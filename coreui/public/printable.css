@media print {
  @page {
    size: portrait;
  }

  .demo-wrap {
    overflow: hidden;
    position: relative;
  }

  .bg-image {
    opacity: 0.3;
    position: absolute;
    left: 10px;
    top: 350px;
    width: 100%;
    height: auto;
  }

  .last-table .main {
    width: 500px;
    font-weight: bold;
    font-size: 20px;
    text-align: end;
    padding-right: 20px;
    padding-top: 20px;
    padding-bottom: 20px;
    background-color: #ecf0f1;
  }

  .middle-table .main {
    width: 100px;
    font-weight: bold;
    font-size: 20px;
    text-align: center;
    background-color: #ecf0f1;
  }

  .middle-table .field {
    width: 200px;
    text-align: end;
    font-weight: bold;
    padding-right: 15px;
  }

  .first-table .main {
    width: 150px;
    font-weight: bold;
    font-size: 20px;
    text-align: center;
    background-color: #ecf0f1;
  }

  .first-table .field {
    text-align: end;
    font-weight: bold;
    padding-top: 5px;
    padding-bottom: 5px;
    font-size: 20px;
    padding-right: 15px;
  }
  .pd{
    padding-top: 10px;
    padding-bottom: 10px;
  }
}
.ahmed {

}

/* .demo-wrap {
    overflow: hidden;
    position: relative;
}

.bg-image {
    opacity: 0.3;
    position: absolute;
    left: 10px;
    top: 350px;
    width: 100%;
    height: auto;
}
.last-table .main {
    width: 500px;
    font-weight: bold;
    font-size: 18px;
    text-align: end;
    padding-right: 20px;
    padding-top: 20px;
    padding-bottom: 20px;
    background-color: #ecf0f1;
  }
  
  .middle-table .main {
    width: 100px;
    font-weight: bold;
    font-size: 19px;
    text-align: center;
    background-color: #ecf0f1;
  }
  
  .first-table .main {
    width: 150px;
    font-weight: bold;
    font-size: 19px;
    text-align: center;
    background-color: #ecf0f1;
  }
  .first-table .field {
    text-align: end;
    font-weight: bold;
    padding-top: 5px;
    padding-bottom: 5px;
    font-size: 20px;
    padding-right: 15px;
  } */