import * as XLSX from "xlsx";

export default {
  /**
   * Read data from an Excel file and convert it to JSON
   * @param {File} file - The Excel file to read
   * @param {Object} options - Reading options
   * @param {String} options.sheet - Sheet name or index (default: first sheet)
   * @param {Boolean} options.header - Whether to use the first row as headers (default: true)
   * @param {Boolean} options.raw - Whether to return raw data or formatted data (default: false)
   * @returns {Promise<Array>} - Array of objects representing the Excel data
   */
  readExcelFile(file, options = {}) {
    return new Promise((resolve, reject) => {
      if (!file) {
        reject(new Error('No file provided'));
        return;
      }

      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          // Default options
          const defaultOptions = {
            sheet: 0,         // First sheet by default
            header: true,     // Use first row as headers
            raw: false        // Return formatted data
          };

          const readOptions = { ...defaultOptions, ...options };

          // Read the Excel file
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });

          // Get the worksheet
          const sheetName = typeof readOptions.sheet === 'number'
            ? workbook.SheetNames[readOptions.sheet]
            : readOptions.sheet || workbook.SheetNames[0];

          const worksheet = workbook.Sheets[sheetName];

          if (!worksheet) {
            reject(new Error(`Sheet "${sheetName}" not found in workbook`));
            return;
          }

          // Convert to JSON
          const jsonOptions = {
            header: readOptions.header ? 1 : undefined,
            raw: readOptions.raw
          };

          const jsonData = XLSX.utils.sheet_to_json(worksheet, jsonOptions);
          resolve(jsonData);
        } catch (error) {
          reject(error);
        }
      };

      reader.onerror = (error) => {
        reject(error);
      };

      reader.readAsArrayBuffer(file);
    });
  }
}
