export default{
    debounce(fn, wait){
        let timer;
        return (...args)=>{
            if(timer) {
                clearTimeout(timer); // clear any pre-existing timer
            }
            // const context = this; // get the current context
          console.log(fn)
            timer = setTimeout(()=>{
                fn.apply(this, args); // call the function if time expires
            }, wait);
        }
    }
}
