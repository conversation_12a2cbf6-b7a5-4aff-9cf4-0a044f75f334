import * as XLSX from 'xlsx'
export default {
    /**
     *
     * @param {string} path
     * @param {FormData} formData
     */
    importFile(path, formData) {
        let file = formData.get('file')
        let fileReader = new FileReader();
        let data = [];
        fileReader.onload = (event) => {
            const buffer = event.target.result;

            const workbook = XLSX.read(buffer, {
                type: "binary",
                cellDates: true,
                dateNF: 'mm/dd/yyyy;@'
            })
            workbook.SheetNames.forEach(sheet => {
                let rowObject = XLSX.utils.sheet_to_row_object_array(
                    workbook.Sheets[sheet]
                );
                let rowString=JSON.stringify(rowObject)
                data.push(rowString);
            });
            formData.append('data', data);
            // console.log(formData.get('data[]'));
            return axios
                .post(path, formData, {
                    headers: {
                        "content-type": "multipart/form-data",
                    },
                    onUploadProgress: function (progressEvent) {
                        this.uploadPercentage = parseInt(
                            Math.round((progressEvent.loaded * 100) / progressEvent.total)
                        );
                    }.bind(this),
                })
                .then((response) => {
                    this.successModal = false;
                    this.updateModal = false;
                    this.progressBar = false;
                    this.flash("File Uploaded Successfully");
                    if (this.hasOwnProperty('getData')) this.getData()
                })
                .catch((error) => {
                    this.progressBar = false;
                    this.successModal = false;
                    this.updateModal = false;
                    if (error.response.data.message) {
                        this.showErrorMessage(error)
                    }
                    if (!error.response.data.failures) return;
                    this.showExcelValidationFailures(error)
                })
        }
        fileReader.readAsBinaryString(file)
    },
}