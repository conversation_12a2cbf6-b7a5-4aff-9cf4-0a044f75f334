<template>
  <c-card>
    <c-card-body>
      <div class="row">
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Actions </template>
            <template #input>
              <v-select v-model="action_id" :options="actions" label="action" :value="0" :reduce="(action) => action.id"
                placeholder="Select Action" class="mt-2" @input="toggleData" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8" v-if="action_id == 2">
          <c-form-group>
            <template #label> Types </template>
            <template #input>
              <v-select v-model="chosen_type_id" :options="chosen_types" label="type" :value="0"
                :reduce="(chosen_type) => chosen_type.id" placeholder="Select Type" class="mt-2" />
            </template>
          </c-form-group>
        </div>

        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Line </template>
            <template #input>
              <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                placeholder="Select Line" class="mt-2" @input="getLineData" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Division </template>
            <template #input>
              <v-select v-model="division_id" :options="divisions" label="name" :value="0"
                :reduce="(division) => division.id" placeholder="Select Division" class="mt-2"
                @input="getLineDivisionBricks" />
            </template>
          </c-form-group>
        </div>

        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Brick</strong>
            </template>
            <template #input>
              <v-select v-model="brick_id" :options="bricks" label="name" :value="0" required
                :reduce="(brick) => brick.id" placeholder="Select Brick" class="mt-2" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8" v-if="checkPermission('list_management_copy_active_from')">
          <c-form-group>
            <template #label>
              <strong>Active From</strong>
            </template>
            <template #input>
              <c-input type="date" class="mt-2" placeholder="Active From" v-model="active_from"></c-input>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8" v-if="action_id == 2 && chosen_type_id == 1">
          <c-form-group>
            <template #label> Account Type </template>
            <template #input>
              <v-select v-model="type_id" :options="types" label="name" :value="0" :reduce="(type) => type.id"
                placeholder="Select Account Type" class="mt-2" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8" v-if="action_id == 2 && chosen_type_id == 2">
          <c-form-group>
            <template #label> Speciality </template>
            <template #input>
              <v-select v-model="speciality_id" :options="specialities" label="name" :value="0"
                :reduce="(speciality) => speciality.id" placeholder="Select Speciality" class="mt-2" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8" v-if="action_id == 2 && chosen_type_id == 3">
          <c-form-group>
            <template #label> Account Class </template>
            <template #input>
              <v-select v-model="acc_class_id" :options="account_classes" label="name" :value="0"
                :reduce="(acc_class) => acc_class.id" placeholder="Select Account Class" class="mt-2" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8" v-if="action_id == 2 && chosen_type_id == 4">
          <c-form-group>
            <template #label> Doctor Class </template>
            <template #input>
              <v-select v-model="doc_class_id" :options="doctor_classes" label="name" :value="0"
                :reduce="(doc_class) => doc_class.id" placeholder="Select Doctor Class" class="mt-2" />
            </template>
          </c-form-group>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" class="text-white" @click="send" style="float: right">save</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["filteredData"],
  data() {
    return {
      type_id: [],
      speciality_id: [],
      division_id: [],
      line_id: null,
      types: [],
      bricks: [],
      brick_id: [],
      specialities: [],
      divisions: [],
      lines: [],
      active_from: null,
      actions: [],
      chosen_types: [],
      action_id: null,
      chosen_type_id: null,
      doc_class_id: null,
      acc_class_id: null,
      doctor_classes: [],
      account_classes: [],
    };
  },
  methods: {
    toggleData() {
      // if (this.action_id == null) {
      this.chosen_type_id = null;
      this.line_id = null;
      this.division_id = null;
      this.brick_id = null;
      this.active_from = null;
      this.type_id = null;
      this.speciality_id = null;
      this.doc_class_id = null;
      this.acc_class_id = null;
      // }
    },
    initialize() {
      axios
        .get("/api/accountsLines/")
        .then((response) => {
          this.lines = response.data.data.lines;
          this.types = response.data.data.account_types;
          this.account_classes = response.data.data.classes;
          this.doctor_classes = response.data.data.classes;
          this.specialities = response.data.data.specialities;
          let data = [
            { id: 1, action: "Copy", permission: 'list_management_copy' },
            { id: 2, action: "Move", permission: 'list_management_move' },
          ];
          let sub_data = [
            { id: 1, type: "Account Type", permission: 'list_management_move_account_type' },
            { id: 2, type: "Speciality", permission: 'list_management_move_speciality' },
            { id: 3, type: "Account Class", permission: 'list_management_move_account_class' },
            { id: 4, type: "Doctor Class", permission: 'list_management_move_doctor_class' },
          ];
          this.actions = data.filter(action => this.checkPermission(action.permission));
          this.chosen_types = sub_data.filter(chosen_type => this.checkPermission(chosen_type.permission));
          // this.action_id = this.actions[0].id;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .get(`/api/getDataLineReport/${this.line_id}`)
        .then((response) => {
          this.divisions = response.data.data.divisions;
          this.types = response.data.data.account_types;
          this.account_classes = response.data.data.classes;
          this.doctor_classes = response.data.data.classes;
          this.specialities = response.data.data.specialities;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineDivisionBricks() {
      axios
        .post(`/api/get-line-division-bricks/`, {
          divisions: [this.division_id],
        })
        .then((response) => {
          this.bricks = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    send() {
      let chosenData = {
        line: this.line_id,
        division: this.division_id,
        brick: this.brick_id,
        action: this.action_id,
        activeFrom: this.active_from,
        chosen_type_id: this.chosen_type_id,
        type: this.type_id,
        speciality: this.speciality_id,
        acc_class: this.acc_class_id,
        doc_class: this.doc_class_id,
      };
      this.$emit("filteredData", { chosenData });
    },
  },
  created() {
    this.initialize();
  },
};
</script>