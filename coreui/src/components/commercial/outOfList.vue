<template>
    <c-card>
        <c-card-header> Out Of List Doctors</c-card-header>
        <c-card-body>
            <button class="btn btn-sm btn-primary" @click="update" color="primary">
                <c-icon name="cib-addthis" />
            </button>

            <p class="d-inline">Add Out Of List Doctors</p>
            <hr />
            <div class="row" v-for="(x, doc) in selected.outOfList" :key="doc">
                <div class="col-lg-3 col-md-3 col-sm-8">
                    <c-input label="Doctor" type="text" placeholder="Enter Doctor"
                        v-model="selected.outOfList[doc].name"></c-input>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                    <c-form-group>
                        <template #label> Speciality </template>
                        <template #input>
                            <v-select v-model="selected.outOfList[doc].speciality_id" :options="specialities"
                                label="name" :value="0" :reduce="(Speciality) => Speciality.id"
                                placeholder="Select Speciality" class="mt-2" />
                        </template>
                    </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                    <c-form-group>
                        <template #label> Cost </template>
                        <template #input>
                            <v-select v-model="selected.outOfList[doc].cost_type_id" :options="doctorSelectedTypes"
                                label="name" :value="0" :reduce="(cost) => cost.id" placeholder="Select Cost Type"
                                class="mt-2" multiple />
                        </template>
                    </c-form-group>
                </div>
                <div class="col-lg-1 col-md-1 col-sm-8">
                    <c-button style="float: right; margin-top: 28px" color="danger"
                        @click="selected.outOfList.splice(doc, 1)"><c-icon
                            name="cib-experts-exchange"></c-icon></c-button>
                </div>
            </div>
        </c-card-body>
    </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { mapState, mapActions } from "vuex";
export default {
    components: {
        vSelect,
    },
    props: {
        id: {
            type: Number,
            required: true,
        },
    },
    data() {
        return {
            doctors: [],
            types: [],
        };
    },
    computed: {
        ...mapState("commercial", ["selected", "specialities"]),
        doctorSelectedTypes() {
            return this.selected.types.length > 0 ? this.selected.types
                .filter(({ type }) => {
                    let exists = false;
                    try {
                        exists = type.cost.name === "per doctor";
                    } catch (e) { }
                    return exists;
                })
                .map((obj) => ({
                    id: obj.type.id,
                    name: obj.type.name,
                })) : [];
        },
    },
    methods: {
        ...mapActions("commercial", ["loadSpecialities"]),
        initialize() {
            this.editOutOfList();
            this.loadSpecialities();
            const doctor = {
                name: null,
                speciality_id: null,
                cost_type_id: [],
            };
            this.selected.outOfList.push(doctor);
            axios
                .post(`/api/doctor-costs/`, {
                    doctors: this.selected.doctors,
                    doctorCosts: this.selected.doctorsCost,
                })
                .then((response) => {
                    this.doctors = response.data.doctors;
                    this.types = response.data.types;
                    this.showTable = true;
                })
                .catch((error) => {
                    this.showErrorMessage(error);
                });
        },
        editOutOfList() {
            axios
                .get(`/api/edit-out_of_list/${this.id}`)
                .then((response) => {
                    this.selected.outOfList = response.data.data;
                })
                .catch((error) => {
                    this.showErrorMessage(error);
                });
        },
        update() {
            const doctor = {
                name: null,
                speciality_id: null,
                cost_type_id: [],
            };
            this.selected.outOfList.push(doctor);
        },
    },
    created() {
        this.initialize();
    },
};
</script>