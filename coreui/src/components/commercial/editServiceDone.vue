<template>
    <div>
        <c-card>
            <c-card-body>
                <div class="row">
                    <div class="col-lg-3 col-md-3 col-sm-8">
                        <label>Invoice Attach</label><br />
                        <input class="m-1" type="file" @change="handleFileUpload($event)" />
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-8">
                        <c-input class="m-1" type="number" label="Amount" v-model="invoice_amount" />
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-8">
                        <c-form-group>
                            <template #label> End Service </template>
                            <template #input>
                                <v-select v-model="type" :options="['Yes', 'No']" :value="0" placeholder="Select paid"
                                    class="mt-2" />
                            </template>
                        </c-form-group>
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-8">
                        <c-input label="Date" type="date" placeholder="Date" v-model="date"></c-input>
                    </div>

                </div>
            </c-card-body>
            <c-card-footer>
                <c-button v-if="is_edit && finance_paid == 'No' && checkPermission('edit_service_completes')"
                    class="m-3 mt-0" @click="save()" color="primary">Update</c-button>
            </c-card-footer>
        </c-card>
        <c-card>
            <c-card-body>
                <c-data-table hover striped sorter tableFilter footer itemsPerPageSelect table-filter pagination
                    thead-top :items="services" :fields="Object.keys(services[0])" :items-per-page="1000"
                    :active-page="1" :responsive="true">
                    <template #invoice_attach="{ item }">
                        <td>
                            <CButton style="padding-top: 10px" small class="btn-link" target="_blank"
                                :href="item.invoice_attach">{{ item.invoice_attach |
                                    fileNameFromPath }}
                            </CButton>
                        </td>
                    </template>
                    <template #actions="{ item }">
                        <td>
                            <div class="row justify-content-center">
                                <c-button color="success" class="btn-sm mt-2 mr-1 text-white"
                                    v-if="item.finance_paid == 'No' && checkPermission('edit_service_completes')"
                                    @click="editService(item)"><c-icon name="cil-pencil" /></c-button>
                            </div>
                        </td>
                    </template>
                </c-data-table>
            </c-card-body>
        </c-card>
    </div>

</template>

<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
    components: {
        vSelect,
    },
    props: {
        id: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            services: [],
            service_id: null,
            invoice_amount: null,
            finance_paid: 'No',
            type: null,
            is_edit: false,
            invoice_attach: null,
            date: null,
        }
    },
    methods: {
        editFormatDate(value) {
            return moment(String(value)).format("YYYY-MM-DD");
        },
        handleFileUpload(event) {
            let formData = new FormData();
            const file = event.target.files[0]; // Get the first selected file
            formData.append("file", file);
            formData.append("folder", "serviceComplete");

            if (file) {
                axios
                    .post("/api/upload/attachment", formData)
                    .then((res) => {
                        this.invoice_attach = res.data.data.url;
                    })
                    .catch((error) => {
                        this.showErrorMessage(error);
                    });
            }
        },
        save() {
            axios
                .post(`/api/save-service-complete/`, {
                    id: this.service_id,
                    invoice_amount: this.invoice_amount,
                    paid: this.type == 'Yes' ? 1 : 0,
                    date: this.date,
                    invoice_attach: this.invoice_attach,

                })
                .then((response) => {
                    this.is_edit = false;
                    this.flash('Service Done Created Successfully');
                    this.initialize();
                })
                .catch((error) => {
                    this.showErrorMessage(error);
                });
        },
        editService(item) {
            this.is_edit = true;
            axios
                .get(`/api/get/service/complete/${item.id}`)
                .then((response) => {
                    console.log(response.data.data);
                    this.service_id = response.data.data.id;
                    this.finance_paid = response.data.data.finance_paid;
                    this.invoice_amount = response.data.data.invoice_amount;
                    this.type = response.data.data.service_done == 1 ? 'Yes' : 'No';
                    this.date = response.data.data.date;
                })
                .catch((error) => {
                    this.showErrorMessage(error);
                });
        },
        initialize() {
            axios
                .get(`/api/service/complete/${this.id}`)
                .then((response) => {
                    this.services = response.data.data;
                })
                .catch((error) => {
                    this.showErrorMessage(error);
                });
        },
    },
    created() {
        this.initialize();
    }
}
</script>