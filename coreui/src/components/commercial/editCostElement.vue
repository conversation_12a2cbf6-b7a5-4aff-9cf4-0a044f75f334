<template>
    <c-card>
        <c-card-body>
            <button class="btn btn-sm btn-primary" @click="update" color="primary">
                <c-icon name="cib-addthis" />
            </button>
            <p class="d-inline">Add Category Costs</p>
            <hr />
            <c-card class="card-style" v-if="selected.categoriesCosts.length > 0">
                <c-card-body v-for="(x, index) in selected.categoriesCosts" :key="index">
                    <div class="row mt-1">
                        <div class="col-lg-2 col-md-2 col-sm-8" v-if="withPayments > 0">
                            <c-form-group>
                                <template #label> Payment Methods </template>
                                <template #input>
                                    <v-select v-model="selected.categoriesCosts[index].payment_id"
                                        :options="paymentMethods" label="name" :value="0"
                                        :reduce="(paymentMethods) => paymentMethods.id"
                                        placeholder="Select Payment Method" class="mt-2" @input="
                                            getCategories(selected.categoriesCosts[index].payment_id)
                                            " />
                                </template>
                            </c-form-group>
                        </div>
                        <div class="col-lg-2 col-md-2 col-sm-8" v-if="withUsers == 1 && withPayments > 0">
                            <c-form-group>
                                <template #label> Employee </template>
                                <template #input>
                                    <v-select v-model="selected.categoriesCosts[index].user_id" :options="custodyUsers"
                                        label="name" :value="0" :reduce="(user) => user.id"
                                        placeholder="Select Employee" class="mt-2" />
                                </template>
                            </c-form-group>
                        </div>
                        <div class="col-lg-2 col-md-2 col-sm-8" v-if="cats.length > 0 || categories.length > 0">
                            <c-form-group>
                                <template #label> Category </template>
                                <template #input>
                                    <v-select v-model="selected.categoriesCosts[index].cat_id"
                                        :options="withPayments > 0 ? cats : categories" label="name" :value="0"
                                        :reduce="(cat) => cat.id" placeholder="Select Category" class="mt-2" @input="
                                            getTypes(
                                                selected.categoriesCosts[index].cat_id,
                                                selected.categoriesCosts[index].type_id
                                            )
                                            " />
                                </template>
                            </c-form-group>
                        </div>
                        <div class="col-lg-2 col-md-2 col-sm-8" v-if="cats.length > 0 || categories.length > 0">
                            <c-form-group>
                                <template #label> Type </template>
                                <template #input>
                                    <v-select v-model="selected.categoriesCosts[index].type_id" :options="types"
                                        label="name" :value="0" :reduce="(type) => type.id" placeholder="Select Type"
                                        class="mt-2" @input="
                                            getTypes(
                                                selected.categoriesCosts[index].cat_id,
                                                selected.categoriesCosts[index].type_id
                                            )
                                            " />
                                </template>
                            </c-form-group>
                        </div>
                        <div class="col-lg-2 col-md-2 col-sm-8" v-if="cats.length > 0 || categories.length > 0">
                            <c-form-group>
                                <template #label> Sub Type </template>
                                <template #input>
                                    <v-select v-model="selected.categoriesCosts[index].sub_type_id" :options="subTypes"
                                        label="name" :value="0" :reduce="(sub_type) => sub_type.id"
                                        placeholder="Select Sub Type" class="mt-2"
                                        @input="getTotalPrice(selected.categoriesCosts[index])" />
                                </template>
                            </c-form-group>
                        </div>
                        <div class="col-lg-2 col-md-2 col-sm-8" v-if="cats.length > 0 || categories.length > 0">
                            <c-input v-model="selected.categoriesCosts[index].time" label="Time" type="time"
                                placeholder="Time" />
                        </div>
                        <div class="col-lg-2 col-md-2 col-sm-8">
                            <c-input label="quantity" type="text" placeholder="Quantity(Supplies)"
                                v-model="selected.categoriesCosts[index].quantity"
                                @input="resetTotalWithQuantity(selected.categoriesCosts[index])" @change="
                                    resetTotalWithQuantity(selected.categoriesCosts[index])
                                    "></c-input>
                        </div>
                        <div class="col-lg-2 col-md-2 col-sm-8">
                            <c-input label="Total" type="text" placeholder="Total"
                                v-model="selected.categoriesCosts[index].total"></c-input>
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-8">
                            <c-input label="Notes" type="text" placeholder="Notes"
                                v-model="selected.categoriesCosts[index].notes"></c-input>
                        </div>
                        <div class="col-lg-1 col-md-1 col-sm-8">
                            <c-button style="float: right; margin-top: 28px" color="danger"
                                @click="selected.categoriesCosts.splice(index, 1)"><c-icon
                                    name="cib-experts-exchange"></c-icon></c-button>
                        </div>
                    </div>
                </c-card-body>
            </c-card>
        </c-card-body>
    </c-card>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { mapActions, mapState } from "vuex";
export default {
    components: {
        vSelect,
    },
    props: {
        id: {
            type: Number,
            required: true,
        },
    },
    data() {
        return {
            types: [],
            subTypes: [],
            price: 0,
            cats: [],
            withPayments: 0,
            withUsers: 0,
        }
    },
    computed: {
        ...mapState("commercial", [
            "selected",
            "paymentMethods",
            "custodyUsers",
            "categories",
        ]),
    },
    methods: {
        ...mapActions("commercial", [
            "loadCategories",
            "loadPaymentMethods",
            "loadCustodyUsers",
        ]),
        initialize() {
            this.loadEditableData();
            axios
                .get(`/api/get-categories-payments`)
                .then((response) => {
                    this.withPayments = response.data.data;
                    if (this.withPayments > 0) {
                        this.loadPaymentMethods();
                        this.loadCustodyUsers();
                        if (this.selected.categoriesCosts.length > 0) {
                            this.selected.categoriesCosts.forEach((costElement) => {
                                this.getCategories(costElement.payment_id);
                                this.getTypes(costElement.cat_id, costElement.type_id);
                                this.getSubTypes(costElement.type_id, costElement.sub_type_id);
                            });
                        }

                    } else {
                        this.loadCategories();
                        if (this.selected.categoriesCosts.length > 0) {
                            this.selected.categoriesCosts.forEach((costElement) => {
                                this.getTypes(costElement.cat_id, costElement.type_id);
                                this.getSubTypes(costElement.type_id, costElement.sub_type_id);
                            });
                        }
                    }
                })
                .catch((error) => {
                    this.showErrorMessage(error);
                });
            const categoriesPrice = {
                payment_id: null,
                user_id: null,
                cat_id: null,
                type_id: null,
                sub_type_id: null,
                time: null,
                notes: null,
                quantity: 0,
                price: 0,
                total: 0,
            };
            this.selected.categoriesCosts.push(categoriesPrice);

        },
        loadEditableData() {
            axios
                .get(`/api/edit-cost-elements/${this.id}`)
                .then((response) => {
                    this.selected.categoriesCosts = response.data.data;
                })
                .catch((error) => {
                    this.showErrorMessage(error);
                });
        },
        getCategories(payment_id) {
            console.log(payment_id);

            axios
                .get(`/api/categories/${payment_id}`)
                .then((response) => {
                    this.cats = response.data.data.categories;
                    this.withUsers = response.data.data.withUsers;
                })
                .catch((error) => {
                    this.showErrorMessage(error);
                });
        },
        getTypes(cat_id, type_id) {
            axios
                .post(`/api/category/${cat_id}/types/`, {
                    cat_id,
                    type_id,
                })
                .then((response) => {
                    this.types = response.data.data.types;
                    this.subTypes = response.data.data.subTypes;
                })
                .catch((error) => {
                    this.showErrorMessage(error);
                });
        },
        getTotalPrice(subType) {
            axios
                .get(`/api/sub-type/${subType.sub_type_id}`)
                .then((response) => {
                    subType.total = Number(response.data.data.price);
                    subType.price = Number(response.data.data.price);
                })
                .catch((error) => {
                    this.showErrorMessage(error);
                });
        },
        resetTotalWithQuantity(cost) {
            cost.total = Number(cost.price) * Number(cost.quantity);
        },
        update() {
            const categoriesPrice = {
                payment_id: null,
                user_id: null,
                cat_id: null,
                type_id: null,
                sub_type_id: null,
                time: null,
                notes: null,
                quantity: 0,
                price: 0,
                total: 0,
            };
            this.selected.categoriesCosts.push(categoriesPrice);
        },

    },
    created() {
        this.initialize();
    }
}
</script>