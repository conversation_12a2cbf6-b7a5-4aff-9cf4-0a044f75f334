<template>
    <div>
        <c-card>
            <c-card-body>
                <div class="row">
                    <div class="col-lg-4 col-md-4 col-sm-8">
                        <label>Partial Attach</label><br />
                        <input class="m-1" type="file" @change="handleFileUpload($event)" />
                    </div>
                    <div class="col-lg-4 col-md-4 col-sm-8">
                        <c-input class="m-1" type="number" label="Amount" v-model="partial_amount" />
                    </div>
                    <div class="col-lg-4 col-md-4 col-sm-8">
                        <c-input label="Date" type="date" placeholder="Date" v-model="date"></c-input>
                    </div>

                </div>
            </c-card-body>
            <c-card-footer>
                <c-button v-if="is_edit && checkPermission('edit_partial_payments')" class="m-3 mt-0" @click="save()"
                    color="primary">Update</c-button>
            </c-card-footer>
        </c-card>
        <c-card>
            <c-card-body>
                <c-data-table hover striped sorter tableFilter footer itemsPerPageSelect table-filter pagination
                    thead-top :items="partial_payments" :fields="Object.keys(partial_payments[0])"
                    :items-per-page="1000" :active-page="1" :responsive="true">
                    <template #partial_attach="{ item }">
                        <td>
                            <CButton style="padding-top: 10px" small class="btn-link" target="_blank"
                                :href="item.partial_attach">{{ item.partial_attach |
                                    fileNameFromPath }}
                            </CButton>
                        </td>
                    </template>
                    <template #actions="{ item }">
                        <td>
                            <div class="row justify-content-center">
                                <c-button color="success" class="btn-sm mt-2 mr-1 text-white"
                                    v-if="item.finance_paid == 'No' && checkPermission('edit_partial_payments')"
                                    @click="editPartial(item)"><c-icon name="cil-pencil" /></c-button>
                            </div>
                        </td>
                    </template>
                </c-data-table>
            </c-card-body>
        </c-card>
    </div>

</template>

<script>
import moment from "moment";
export default {
    props: {
        id: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            partial_payments: [],
            partial_id: null,
            partial_amount: null,
            type: null,
            is_edit: false,
            partial_attach: null,
            date: null,
        }
    },
    methods: {
        editFormatDate(value) {
            return moment(String(value)).format("YYYY-MM-DD");
        },
        handleFileUpload(event) {
            let formData = new FormData();
            const file = event.target.files[0]; // Get the first selected file
            formData.append("file", file);
            formData.append("folder", "partialPayments");

            if (file) {
                axios
                    .post("/api/upload/attachment", formData)
                    .then((res) => {
                        this.partial_attach = res.data.data.url;
                    })
                    .catch((error) => {
                        this.showErrorMessage(error);
                    });
            }
        },
        save() {
            axios
                .put(`/api/partial/payments/${this.partial_id}`, {
                    id: this.partial_id,
                    partial_amount: this.partial_amount,
                    date: this.date,
                    partial_attach: this.partial_attach,

                })
                .then((response) => {
                    this.is_edit = false;
                    this.flash('Partial Payment Updated Successfully');
                    this.initialize();
                })
                .catch((error) => {
                    this.showErrorMessage(error);
                });
        },
        editPartial(item) {
            this.is_edit = true;
            axios
                .get(`/api/partial/payments/${item.id}`)
                .then((response) => {
                    this.partial_id = response.data.data.id;
                    this.partial_amount = response.data.data.partial_amount;
                    this.type = response.data.data.paid == 1 ? 'Yes' : 'No';
                    this.date = this.editFormatDate(response.data.data.date);
                })
                .catch((error) => {
                    this.showErrorMessage(error);
                });
        },
        initialize() {
            axios
                .post(`/api/partial/payments`, {
                    request_id: this.id
                })
                .then((response) => {
                    this.partial_payments = response.data.data;
                })
                .catch((error) => {
                    this.showErrorMessage(error);
                });
        },
    },
    created() {
        this.initialize();
    }
}
</script>