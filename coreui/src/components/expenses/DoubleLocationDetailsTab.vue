<template>
  <c-card>
    <c-card-body>
      <button class="btn btn-sm btn-primary" @click="update" color="primary">
        <c-icon name="cib-addthis" />
      </button>

      <p class="d-inline">Add Expense Request Details</p>
      <hr />
      <div class="row" v-for="(x, index) in selected.details" :key="index">
        <div class="col-lg-2 col-md-2 col-sm-8" v-if="expenseTime != null">
          <c-form-group>
            <template #label>
              <strong>Date</strong>
            </template>

            <template #input>
              <c-input class="mt-2" type="date" :min="minDate" :max="maxDate" placeholder="From"
                v-model="selected.details[index].date" @input="resetData(selected.details[index])"></c-input>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8" v-else>
          <c-form-group>

            <template #label>
              <strong>Date</strong>
            </template>

            <template #input>
              <c-input class="mt-2" type="date" placeholder="From" v-model="selected.details[index].date"
                @change="resetData(selected.details[index])"></c-input>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-form-group>

            <template #label>
              <strong>Expense Type</strong>
            </template>

            <template #input>
              <v-select title="Search for Expense Type" v-model="selected.details[index].type" :options="types"
                label="name" :value="0" class="mt-2" :reduce="(type) => type.id"
                @input="getExpenseType(selected.details[index])"></v-select>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8" v-if="selected.details[index].perLocation == true">
          <c-form-group>

            <template #label>
              <strong>From</strong>
            </template>

            <template #input>
              <v-select title="Search for Source" v-model="selected.details[index].source_id" :options="divisions"
                label="name" :value="0" class="mt-2" :reduce="(division) => division.id"></v-select>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8" v-if="selected.details[index].perLocation == true">
          <c-form-group>

            <template #label>
              <strong>To</strong>
            </template>

            <template #input>
              <v-select title="Search for Destination" v-model="selected.details[index].destination_id"
                :options="divisions" label="name" :value="0" class="mt-2" :reduce="(division) => division.id"
                @input="getKiloMeters(selected.details[index])"></v-select>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8" v-if="selected.details[index].perLocation == true">
          <c-form-group>

            <template #label>
              <strong>Single/Round</strong>
            </template>

            <template #input>
              <v-select title="Search for Distance" v-model="selected.details[index].singleOrRound" :options="distances"
                label="name" :value="0" class="mt-2" :reduce="(distance) => distance.id"
                @input="doublePrice(selected.details[index])"></v-select>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8"
          v-if="selected.details[index].perLocation == true && selected.details[index].singleOrRound == 2">
          <c-form-group>

            <template #label>
              <strong>Over Night</strong>
            </template>

            <template #input>
              <v-select title="Search for Distance" v-model="selected.details[index].overNight" :options="overnights"
                label="name" :value="0" class="mt-2" :reduce="(overNight) => overNight.id"></v-select>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8"
          v-if="selected.details[index].perLocation == true && selected.details[index].singleOrRound == 2">
          <c-form-group>
            <template #label>
              <strong>Return Date</strong>
            </template>

            <template #input>
              <c-input class="mt-2" type="date" placeholder="to" v-model="selected.details[index].toDate"></c-input>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8"
          v-if="selected.details[index].perLocation == true && selected.details[index].singleOrRound != null">
          <c-form-group>

            <template #label>
              <strong>Daily Allowence</strong>
            </template>

            <template #input>
              <v-select title="Search for Meal" v-model="selected.details[index].meal" :options="meals" label="name"
                :value="0" class="mt-2" @input="getMealPrice(selected.details[index])"></v-select>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8" v-if="selected.details[index].perLocation == true">
          <c-input label="KM" type="number" placeholder="KM" v-model="selected.details[index].distance"
            disabled></c-input>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-input label="Amount" type="number" placeholder="Amount" v-model="selected.details[index].amount"
            :disabled="selected.details[index].perLocation == true"></c-input>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-textarea rows="1" @focus="resizeTextarea" @keyup="resizeTextarea" label="Description" type="text"
            placeholder="Description" v-model="selected.details[index].description"></c-textarea>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8" style="margin-top: 10px">
          <input id="file" :data-expense-first="index" type="file" @change="uploadFile" />
          <input :data-expense-first="index" type="file" @change="uploadFile" />
        </div>
        <div class="col-lg-1 col-md-1 col-sm-8">
          <c-button style="float: right; margin-top: 28px" color="danger"
            @click="selected.details.splice(index, 1)"><c-icon name="cib-experts-exchange"></c-icon></c-button>
        </div>
      </div>
    </c-card-body>
  </c-card>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { mapState, mapActions, mapMutations } from "vuex";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      list: 0,
      flag: false,
      meals: [],
      divisions: [],
      // overNights: [
      //   { id: 1, name: 'Same Day' },
      //   { id: 2, name: 'Over Night' }
      // ],
      minDate: null,
      maxDate: null,
      expenseTime: null,
    };
  },
  computed: {
    ...mapState("expenses", ["types", "selected", "distances", "overnights"]),
  },
  emits: ["getDetails"],
  methods: {
    ...mapActions("expenses", ["loadTypes", "loadDistances", "loadOvernights"]),
    ...mapMutations("expenses", ["setDetails", "initDetails"]),
    resetData(expense) {
      expense.type = null;
      expense.source_id = null;
      expense.destination_id = null;
      expense.km_price = null;
      expense.meal = null;
      expense.singleOrRound = null;
      expense.overNight = null;
      expense.perLocation = false;
      expense.amount = 0;
      axios
        .post(`/api/get-meals`, {
          date: expense.date,
          type: expense.type,
        })
        .then((response) => {
          this.meals = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    initialize() {
      this.loadTypes();
      this.loadDistances();
      this.loadOvernights();
      const expense = {
        amount: 0,
        date: null,
        source_id: null,
        destination_id: null,
        distance: null,
        km_price: 0,
        perLocation: false,
        type: null,
        locationId: null,
        meal: null,
        singleOrRound: 1,
        overNight: null,
        toDate: null,
        description: null,
        attachments: [],
      };
      this.selected.details.push(expense);
    },
    getExpenseType(expense) {
      axios
        .get(`/api/expense-type-data/${expense.type}`)
        .then((response) => {
          expense.perLocation = response.data.data.isLocation;
          expense.amount = response.data.data.price;
          this.divisions = response.data.data.divisions;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getKiloMeters(expense) {
      axios
        .post("/api/expense-location-kilometers", {
          from: expense.source_id,
          to: expense.destination_id,
          date: expense.date,
          type_id: expense.type,
        })
        .then((response) => {
          expense.distance = response.data.data.kilo_meter;
          expense.km_price = Number(response.data.data.price);
          expense.amount = Number(response.data.data.price);
          expense.locationId = response.data.data.id;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    doublePrice(expense) {
      this.getKiloMeters(expense);
      if (expense.singleOrRound === 2) {
        expense.amount = Number(expense.amount * 2);
      } if (expense.singleOrRound == null) {
        expense.meal = null;
        expense.amount = Number(expense.km_price);
      }
      if (expense.singleOrRound == 1) {
        expense.meal = null;
        expense.amount = Number(expense.km_price);
      }

    },
    getMealPrice(expense) {
      if (expense.meal == null) {
        expense.amount = Number(expense.km_price);
      } else {
        expense.amount = expense.km_price;
        expense.amount += Number(expense.meal.price);
      }
    },
    resizeTextarea(e) {
      let area = e.target;
      area.style.overflow = "hidden";
      area.style.height = area.scrollHeight + "px";
    },
    update() {
      const expense = {
        amount: 0,
        date: null,
        source_id: null,
        destination_id: null,
        km_price: 0,
        distance: null,
        type: null,
        locationId: null,
        meal: null,
        perLocation: false,
        description: null,
        singleOrRound: 1,
        overNight: null,
        toDate: null,
        attachments: [],
      };
      this.selected.details.push(expense);
    },
    uploadFile(event) {
      const expense =
        this.selected.details[event.target.getAttribute("data-expense-first")];
      const file = event.target.files[0];
      const formData = new FormData();
      formData.append("file", file);
      formData.append("folder", "expenses");
      axios
        .post("/api/upload/attachment", formData)
        .then((res) => {
          expense.attachments.push(res.data.data.path);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getDates() {
      axios
        .get("/api/min-date-expense-header")
        .then((response) => {
          this.minDate = response.data.data.min_date;
          this.maxDate = response.data.data.max_date;
          this.expenseTime = response.data.data.expenseTime;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    }
  },

  created() {
    this.initialize();
    this.getDates();
  },
};
</script>