<template>
  <c-card>
    <c-card-body>
      <div class="row">
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Line</strong>
            </template>
            <template #input>
              <v-select title="Search for Line" v-model="line_id" :options="lines" label="name" :value="0" class="mt-3"
                :reduce="(line) => line.id" @input="getLineData"></v-select>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Divisions</strong>
            </template>
            <template #input>
              <input label="All" v-if="divisions.length != 0" class="m-1" type="checkbox"
                v-model="hasCheckedAllDivisions" title="Check All Divisions" @change="checkAllDivisions" />
              <label v-if="divisions.length != 0" style="font-weight: bold">All</label>
              <v-select title="Search for Division" v-model="div_ids" :options="divisions" label="name" :value="0"
                class="mt-2" multiple :reduce="(division) => division.id" @input="getProducts"></v-select>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Products</strong>
            </template>
            <template #input>
              <input label="All" v-if="products.length != 0" class="m-1" type="checkbox" v-model="hasCheckedAllProducts"
                title="Check All Products" @change="checkAllProducts" />
              <label v-if="products.length != 0" style="font-weight: bold">All</label>
              <v-select title="Search for Products" v-model="product_ids" :options="products" label="name" :value="0"
                class="mt-2" multiple :reduce="(product) => product.id"></v-select>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8" v-if="expenseTime != null">
          <c-form-group>
            <template #label>
              <strong>Date</strong>
            </template>
            <template #input>
              <c-input class="mt-2" type="date" placeholder="From" :min="minDate" :max="maxDate"
                v-model="date"></c-input>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8" v-else>
          <c-form-group>
            <template #label>
              <strong>Date</strong>
            </template>
            <template #input>
              <c-input class="mt-2" type="date" placeholder="From" v-model="date"></c-input>
            </template>
          </c-form-group>
        </div>
      </div>
    </c-card-body>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { mapState, mapMutations, mapActions } from "vuex";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      line_id: [],
      products: [],
      divisions: [],
      div_ids: [],
      product_ids: [],
      date: [],
      minDate: null,
      maxDate: null,
      expenseTime: null,
      hasCheckedAllDivisions: false,
      hasCheckedAllProducts: false,
    };
  },
  computed: {
    ...mapState("expenses", ["lines"]),
  },
  methods: {
    ...mapMutations("expenses", [
      "setSelectedLine",
      "setSelectedDate",
      "setSelectedDivisions",
      "setSelectedProducts",
    ]),
    ...mapActions("expenses", ["loadLines"]),
    initialize() {
      this.loadLines();
      axios
        .get("/api/min-date-expense-header")
        .then((response) => {
          this.minDate = response.data.data.min_header_date;
          this.maxDate = response.data.data.max_header_date;
          this.expenseTime = response.data.data.expenseTime;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .get(`/api/expense-line-data/${this.line_id}`)
        .then((response) => {
          this.divisions = response.data.divisions;
          this.products = response.data.products;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getProducts() {
      axios
        .post(
          `/api/get-expense-products`, { divisions: this.div_ids, line: this.line_id }
        )
        .then((response) => {
          this.products = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllDivisions() {
      if (this.hasCheckedAllDivisions)
        this.div_ids = this.divisions.map((item) => item.id);
      if (this.hasCheckedAllDivisions == false) this.div_ids = [];
    },
    checkAllProducts() {
      if (this.hasCheckedAllProducts)
        this.product_ids = this.products.map((item) => item.id);
      if (this.hasCheckedAllProducts == false) this.product_ids = [];
    },
    checkAlllines() {
      if (this.checkAllLines) {
        this.line_ids = [...this.lines];
      }
      if (this.checkAllLines == false) {
        this.line_ids = [];
      }
    },
    pushSelectedLine(value) {
      this.setSelectedLine(value);
    },
    pushSelectedDate(value) {
      this.setSelectedDate(value);
    },
    pushSelectedDivisions(value) {
      this.setSelectedDivisions(value);
    },
    pushSelectedProducts(value) {
      this.setSelectedProducts(value);
    },
  },
  watch: {
    line_id: {
      handler: "pushSelectedLine",
      deep: true,
      immediate: true,
    },
    date: {
      handler: "pushSelectedDate",
      deep: true,
      immediate: true,
    },
    div_ids: {
      handler: "pushSelectedDivisions",
      deep: true,
      immediate: true,
    },
    product_ids: {
      handler: "pushSelectedProducts",
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.initialize();
  },
};
</script>