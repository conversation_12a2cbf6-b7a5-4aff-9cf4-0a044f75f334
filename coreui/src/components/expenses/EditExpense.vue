<template>
  <c-card>
    <c-card-body>
      <c-card v-if="detail">
        <c-card-body class="scroll">
          <div class="row">
            <div style="width: 10%; padding-left: 8px; margin-top: 15px">
              <!-- <c-input
                class="mt-2"
                type="date"
                placeholder="Date"
                v-model="detail.date"
                disabled
              ></c-input> -->
              <strong class="mt-2"><label>Date</label></strong><br />
              <div class="show">
                <span class="mt-2">{{ detail.date }}</span>
              </div>
            </div>
            <div style="width: 12%; padding-left: 8px; margin-top: 15px">
              <strong class="mt-2"><label>type</label></strong><br />
              <div class="show">
                <span class="mt-2">{{ detail.type }}</span>
              </div>
            </div>
            <!-- <div class="col-3">
              <c-textarea
                rows="1"
                v-model="detail.description"
                label="Description"
                type="text"
                placeholder="Description"
                disabled
              ></c-textarea>
            </div> -->
            <!-- </div> -->
            <!-- <div class="row"> -->
            <div style="width: 12%; padding-left: 8px; margin-top: 15px" v-if="detail.is_location">
              <strong class="mt-2"><label>From</label></strong><br />
              <div class="show">
                <span class="mt-2">{{ detail.from }}</span>
              </div>
              <!-- <c-form-group> -->
              <!-- <template #label>
                  <strong>from</strong>
                </template>
<template #input> -->
              <!-- <v-select
                    title="Source"
                    :options="areas"
                    label="name"
                    :value="0"
                    class="mt-2"
                    v-model="detail.from"
                    :reduce="(from) => from.id"
                    disabled
                  ></v-select>
                </template>
</c-form-group> -->
            </div>
            <div style="width: 12%; padding-left: 8px; margin-top: 15px" v-if="detail.is_location">
              <strong class="mt-2"><label>To</label></strong><br />
              <div class="show">
                <span class="mt-2">{{ detail.to }}</span>
              </div>
              <!-- <c-form-group>
                <template #label>
                  <strong>to</strong>
                </template>
                <template #input>
                  <v-select
                    title="Destination"
                    :options="areas"
                    label="name"
                    :value="0"
                    class="mt-2"
                    v-model="detail.to"
                    :reduce="(to) => to.id"
                    disabled
                  ></v-select>
                </template>
              </c-form-group> -->
            </div>
            <div style="width: 8%; padding-left: 8px; margin-top: 15px" v-if="detail.is_location">
              <strong class="mt-2"><label>Distance</label></strong><br />
              <div class="show">
                <span class="mt-2">{{ detail.km }}</span>
              </div>
              <!-- <c-input
                v-model="detail.km"
                label="Distance"
                type="text"
                placeholder="Distance"
                disabled
              ></c-input> -->
            </div>
            <div class="col-1" v-if="detail.is_location">
              <c-form-group>
                <template #label>
                  <strong>Value</strong>
                </template>
                <template #input>
                  <c-input v-model="detail.km_price" type="number" class="mt-2" placeholder="value"
                    @input="editTotal(detail)" disabled></c-input>
                </template>
              </c-form-group>
            </div>
            <div class="col-2" v-if="detail.is_location">
              <c-form-group>
                <template #label>
                  <strong>Meal</strong>
                </template>
                <template #input>
                  <v-select :options="meals" label="name" :value="0" class="mt-2" v-model="detail.meal"
                    :reduce="(meal) => meal" @input="getMealPrice(detail)"></v-select>
                </template>
              </c-form-group>
            </div>
            <div style="width: 11%; padding-left: 8px; margin-top: 15px" v-if="detail.is_location">
              <strong class="mt-2"><label>Meal Price</label></strong><br />
              <div class="show">
                <span class="mt-2">{{ detail.meal_price }}</span>
              </div>
              <!-- <c-input
                v-model="detail.meal_price"
                label="Meal Price"
                type="text"
                placeholder="Meal price"
                disabled
              ></c-input> -->
            </div>
            <div class="col-1">
              <c-form-group>
                <template #label>
                  <strong>Total</strong>
                </template>
                <template #input>
                  <c-input class="mt-2" v-model="detail.amount" type="text" placeholder="Amount"></c-input>
                </template>
              </c-form-group>
            </div>
          </div>
          <div class="row">
            <div class="col-6">
              <c-textarea rows="3" v-model="detail.edit_notes" label="Notes" type="text"
                placeholder="Notes"></c-textarea>
            </div>
          </div>
        </c-card-body>
      </c-card>
    </c-card-body>
    <c-card-footer>
      <c-button class="text-white" color="primary" @click="update()">Update</c-button>
      <c-button color="default" @click="$router.go(-1)">Cancel</c-button>
    </c-card-footer>
  </c-card>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      detail: null,
      areas: [],
      meals: [],
      types: [],
      setting: null,
    };
  },
  methods: {
    async getPolicies() {
      await axios
        .get("/api/expense-policies/")
        .then((response) => {
          this.setting = response.data.data.setting;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    async initialize() {
      await this.getPolicies();
      if (this.setting == 'Single') {
        await axios
          .get(`/api/edit-expense-statistics-second-version/${this.$route.params.id}`)
          .then((response) => {
            this.detail = response.data.detail;
            this.areas = response.data.divisions;
            this.meals = response.data.meals;
            this.types = response.data.types;
          })
          .catch((error) => {
            this.showErrorMessage(error);
          });
      } else {
        await axios
          .get(`/api/edit-expense-details-second-version/${this.$route.params.id}`)
          .then((response) => {
            this.detail = response.data.detail;
            this.areas = response.data.divisions;
            this.meals = response.data.meals;
            this.types = response.data.types;
          })
          .catch((error) => {
            this.showErrorMessage(error);
          });
      }
    },
    getMealPrice(detail) {
      if (detail.meal == null) {
        detail.meal_price = 0;
        detail.amount = Number(detail.km_price);
      } else {
        detail.amount = 0;
        detail.amount = Number(detail.km_price);
        detail.amount += Number(detail.meal.price);
        detail.meal_price = Number(detail.meal.price);
      }
    },
    editTotal(detail) {
      detail.amount = 0;
      detail.amount = Number(detail.km_price);
      detail.amount += Number(detail.meal.price);
      detail.meal_price = Number(detail.meal.price);
    },
    update() {
      axios
        .put(`/api/expense-stats-second-version/${this.$route.params.id}`, {
          detail: this.detail,
        })
        .then((response) => {
          this.flash("Expense Updated Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
  },
};
</script>

<style scoped>
.show {
  border: 1px solid #e4e7ea;
  padding: 5px;
  border-radius: 3px;
  font-size: 13px;
  background-color: #e4e7ea;
}
</style>