<template>
  <CCol md="12">
    <CCard>
      <CCardHeader> <strong>Create OW & Activities</strong></CCardHeader>
      <CCardBody>
        <c-row>
          <c-col lg="3" md="3" sm="8">
            <CFormGroup>
              <template #label>
                <strong>From Date</strong>
              </template>
              <template #input>
                <input
                  type="date"
                  v-model="from"
                  :max="maxTo"
                  :min="minFrom"
                  class="mt-2 form-control"
                />
              </template>
            </CFormGroup>
          </c-col>
          <c-col lg="3" md="3" sm="8">
            <CFormGroup>
              <template #label>
                <strong>To Date</strong>
              </template>
              <template #input>
                <input
                  type="date"
                  v-model="to"
                  :max="maxTo"
                  :min="minFrom"
                  class="mt-2 form-control"
                />
              </template>
            </CFormGroup>
          </c-col>
        </c-row>
      </CCardBody>
      <CCardFooter>
        <CButton color="primary" @click="show()" style="float: right"
          >Show</CButton
        >
      </CCardFooter>
    </CCard>
  </CCol>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
    components:{
        vSelect
    },
    props:{
        from_date:{
            type:String,
            required:true
        },
        to_date:{
            type:String,
            required:true
        },
        minFrom:{
            type:String,
            required:true
        },
        maxTo:{
            type:String,
            required:true
        },
    },
    data() {
        return {
            from:null,
            to:null
        }
    },
    emits:["submitted"],
    methods: {
        show(){
            this.$emit("submitted",{from:this.from,to:this.to});
        }
    },
    mounted() {
        this.from = this.from_date;
        this.to = this.to_date;
    },
};
</script>

<style>
</style>