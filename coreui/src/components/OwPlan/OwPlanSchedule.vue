<template>
  <c-data-table
  add-table-classes="officeWork-class"
    hover
    striped
    sorter
    tableFilter
    footer
    itemsPerPageSelect
    :items="plans"
    :fields="fields"
    :items-per-page="1000"
    :active-page="1"
    :responsive="true"
    pagination
    thead-top
  >
    <template #day="{ item }">
      <td style="text-align: center; padding-top: 50px">
        <strong  v-if="item.isVacation" :style="{
                  color:'orange',
                }"
        >{{ item.forDisplayDay }}
      </strong>
      <strong v-if="!item.isVacation" >
        {{ item.forDisplayDay }}
      </strong>
      </td>
    </template>
    <template #full_day="{ item }">
      <td style="text-align: center; padding-top: 50px">
        <input
          type="checkbox"
          v-model="item.full_day"
          :disabled="
            item.AM.old || item.PM.old || (!!item.PM.id && !!item.AM.id)
          "
          @change="checkFullDay(item)"
        />
      </td>
    </template>
    <template #AM="{ item }">
      <td>
        <v-select
          v-model="item.AM.id"
          :options="owTypes"
          label="name"
          :value="0"
          :reduce="(type) => type.id"
          placeholder="Select option"
          :disabled="item.AM.old || item.full_day"
        />
        <c-textarea
          v-model="item.AM.notes"
          type="text"
          placeholder="Notes"
          class="mt-1"
          :disabled="item.AM.old || item.full_day"
        />
      </td>
    </template>
    <template #PM="{ item }">
      <td>
        <v-select
          v-model="item.PM.id"
          :options="owTypes"
          label="name"
          :value="0"
          :reduce="(type) => type.id"
          placeholder="Select option"
          :disabled="item.PM.old || item.full_day"
        />
        <c-textarea
          v-model="item.PM.notes"
          type="text"
          placeholder="Notes"
          class="mt-1"
          :disabled="item.PM.old || item.full_day"
        />
      </td>
    </template>
  </c-data-table>
</template>

<script>
import { mapState, mapMutations } from "vuex";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  props: {
    shiftsPlans: {
      type: Object,
      required: true,
    },
    dates: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      shifts: [],
      owTypes: [],
      fields: ["day", "full_day"],
    };
  },
  computed: {
    ...mapState("owplan", ["plans"]),
  },
  methods: {
    ...mapMutations("owplan", ["setOwPlanVisits"]),
    initialize() {
      if (this.shiftsPlans.shifts != null)
        this.shifts = this.shiftsPlans.shifts;
      if (this.shiftsPlans.ow != null) this.owTypes = this.shiftsPlans.ow;
      return axios
        .post("/api/owplanvisits/getOwPlanSchedule", { ...this.dates })
        .then((res) => {
          this.prepareItemsAndFields(res.data.data);
        })
        .catch((error) => this.showErrorMessage(error));
    },
    prepareItemsAndFields(data) {
      let items = Object.values(data);
      this.shifts.forEach((shift) => {
        items = items.map((item) => {
          let dateOnlyString = item.day.split(' ')[0];
          // console.log('dateOnlyString')
          // console.log(dateOnlyString)
          let dateObject = new Date(item.day);
          let dayOfWeek = dateObject.getDay();
          let daysOfWeek = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
          let dayName = daysOfWeek[dayOfWeek];
          // console.log('dayName')
          // console.log(dayName)
          let forDisplayDay = dateOnlyString + ' ' + dayName
          // console.log('forDisplayDay')
          // console.log(forDisplayDay)
          if (!!item[shift.name])
            return {
              ...item,
              forDisplayDay: forDisplayDay,
              [shift.name]: { ...item[shift.name], old: true, full_day: false },
            };
          return {
            ...item,
            forDisplayDay: forDisplayDay,
            [shift.name]: {
              id: null,
              notes: null,
              old: false,
              full_day: false,
            },
          };
        });
        this.fields.push(shift.name);
      });
      this.setOwPlanVisits([...items]);
    },
    checkFullDay(item) {
      if (item.full_day == true) {
        item.PM = { ...item.AM };
      } else {
        let initial = { id: null, note: null, old: false, full_day: false };
        item.PM = { ...initial };
        item.AM = { ...initial };
      }
    },
  },
  created() {
    this.initialize();
  },
};
</script>
<style>
.officeWork-class > thead > tr {
  background-color: #005CC8;
  color: white;
  text-align: center;
}
.officeWork-class > tfoot > tr {
  background-color: #005CC8;
  color: white;
  text-align: center;
}
</style>
