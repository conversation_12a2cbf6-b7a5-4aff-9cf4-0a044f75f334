<template>
  <div class="container">
    <div class="input-row">
      <search-input @search-change="find" />
      <c-button
        v-if="!!contacts.length"
        color="primary"
        @click="call"
        :disabled="empty"
        >Call</c-button
      >
    </div>
    <div class="container">
      <!--v-click-outside="unselect"  -->
      <user-card
        v-for="(user, i) in allContacts"
        :key="i"
        :user="user"
        @change="selectCallee(user)"
        @click.shift="shiftSelect(user.id)"
      />
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";
import Vue from "vue";
import SearchInput from "../common/Search-Input.vue";
import UserCard from "../common/UserCard.vue";
Vue.directive("click-outside", {
  bind(el, binding, vnode) {
    el.clickOutsideEvent = (event) => {
      if (!(el === event.target || el.contains(event.target))) {
        vnode.context[binding.expression](event);
      }
    };
    document.body.addEventListener("click", el.clickOutsideEvent);
  },
  unbind(el) {
    document.body.removeEventListener("click", el.clickOutsideEvent);
  },
});

export default {
  emits: ["setNewCall"],
  components: {
    SearchInput,
    UserCard,
  },
  data() {
    return {
      selectedCallees: new Map(),
      usersToCall: [],
      search: null,
    };
  },
  computed: {
    ...mapState("contacts", ["contacts"]),
    size() {
      return this.usersToCall.length;
    },
    empty() {
      return this.usersToCall.length === 0;
    },
    allContacts() {
      return this.contacts.filter((contact) => {
        if (this.search == "" || this.search == null) return true;
        return contact.name.toLowerCase().includes(this.search);
      });
    },
  },
  methods: {
    ...mapMutations("contacts", ["setContacts"]),
    find(textSearch) {
      this.search = textSearch;
    },
    unselect() {
      if (this.selectedCallees.size != 0) {
        this.selectedCallees.forEach((user) => {
          document.getElementById(user.id).click();
        });
      }
    },
    shiftSelect(id) {
      if (this.selectedCallees.size == 0) return;
      let lastSelectedId = Array.from(this.selectedCallees).pop()[0];

      let shiftedIndex = this.contacts.findIndex((user) => user.id == id);
      let lastSelectedIndex = this.contacts.findIndex(
        (user) => user.id == lastSelectedId
      );

      for (let i of this.selectRange(lastSelectedIndex, shiftedIndex)) {
        let userID = this.contacts[i].id;
        if (this.selectedCallees.get(userID)) continue;
        document.getElementById(userID).click();
      }
      document.getElementById(id).click();
    },
    call() {
      this.$emit("setNewCall", this.usersToCall);
    },
    selectCallee(user) {
      if (this.selectedCallees.has(user.id)) {
        this.selectedCallees.delete(user.id);
        this.usersToCall = [];
        this.selectedCallees.forEach((user) => this.usersToCall.push(user));
        return;
      }

      this.selectedCallees.set(user.id, user);
      this.usersToCall = [];
      this.selectedCallees.forEach((user) => this.usersToCall.push(user));
    },
  },
};
</script>

<style scoped>
@import url("https://fonts.googleapis.com/css2?family=Lato:wght@300;700;900&display=swap");
.container {
  display: flex;
  flex-flow: row wrap;
  --space: 2%;
}

@media (min-width: 550px) {
  .container .option_item {
    flex-basis: calc(calc((100% - (3 * var(--space)))) / 4);
  }
  .container .input-row {
    flex-basis: calc((100% - (13 * var(--space))));
  }
}

@media (min-width: 800px) {
  .container .option_item {
    flex-basis: calc(calc((100% - (4 * var(--space)))) / 5);
  }
  .container .input-row {
    flex-basis: calc((100% - (10 * var(--space))));
  }
}

@media (min-width: 1000px) {
  .container .option_item {
    flex-basis: calc(calc((100% - (8 * var(--space)))) / 9);
  }
  .container .input-row {
    flex-basis: calc((100% - (9 * var(--space))));
  }
}

.input-row {
  margin: 0 0 0 1.3rem;
  padding: 0px;
  display: flex;
  flex-basis: calc((100% - (23 * var(--space))));
  justify-content: space-between;
  align-items: center;
  font-family: "Lato" !important;
}
</style>
