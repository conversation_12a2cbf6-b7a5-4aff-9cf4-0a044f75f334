<template>
  <c-card no-header>
    <c-card-body>
      <div class="row">
        <div class="col-8">
          <c-input
            label="Name"
            type="text"
            placeholder="Name"
            v-model="name"
          >
            <template slot="label">
              Name <span style="color: red">*</span>
            </template>
          </c-input>
        </div>
        <div class="col-4">
          <c-input
            label="Sort"
            type="number"
            placeholder="Sort"
            v-model="sort"
          >
            <template slot="label">
              Sort <span style="color: red">*</span>
            </template>
          </c-input>
        </div>
      </div>

      <CFormGroup v-if="unifiedDistributors.length>0">
              <template #label> Unified Distributor </template>
              <template #input>
                <v-select
                  v-model="unified_distributor_id"
                  :options="unifiedDistributors"
                  label="name"
                  :value="unified_distributor_id"
                  :reduce="(Udistributor) => Udistributor.id"
                  placeholder="Select Unified Distributor"
                  class="mt-2"
                />
              </template>
            </CFormGroup>

      <div class="row">
        <div class="col">
          <c-textarea
            label="Notes"
            type="text"
            placeholder="Notes"
            v-model="notes"
          ></c-textarea>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button
        color="primary"
        class="text-white"
        @click="update"
        :disabled="!(name)"
        >Update</c-button
      >
      <c-button color="default" @click="$router.go(-1)">Cancel</c-button>
    </c-card-footer>
  </c-card>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data(){
    return {
      distributor:null,
      name: null,
      notes: null,
      sort: null,
      unified_distributor_id:null,
      unifiedDistributors:[]
    };
  },
  emits:["getDistributors"],
  methods: {
    getUnifiedDistributors(){
      return axios
        .get("/api/load-distributors")
        .then((response) => {
           this.unifiedDistributors=response.data.result;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    initialize() {
      this.getUnifiedDistributors().then(()=>{

        axios
        .get(`/api/distributors/${this.$route.params.id}/edit`)
        .then((response) => {
          this.distributor_id = response.data.id;
          this.name = response.data.name;
          this.notes = response.data.notes;
          this.sort = response.data.sort;
          this.unified_distributor_id=response.data.unified_distributor_id;
          this.$emit("getDistributors",{ distributor: this.distributor_id });
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      })
    },
    update() {
      axios
        .put(`/api/distributors/${this.distributor_id}`, {
          id: this.distributor_id,
          name: this.name,
          notes: this.notes,
          sort: this.sort,
          unified_distributor_id:this.unified_distributor_id
        })
        .then((response) => {
          this.flash("Distributer Updated Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
  },
};
</script>