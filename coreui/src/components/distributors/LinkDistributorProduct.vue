<template>
  <c-card>
    <c-card-body>
      <c-row>
        <c-col>
          <c-form-group>
            <template #label
              >Product <span style="color: red">*</span></template
            >
            <template #input>
              <v-select
                v-model="selectedProduct"
                :options="products"
                label="name"
                :value="0"
                required
                :reduce="(product) => product.id"
                placeholder="Select Product"
                class="mt-2"
              />
            </template>
          </c-form-group>
        </c-col>
        <c-col>
          <c-input
            label="Code"
            type="text"
            placeholder="Code"
            v-model="code"
            class="mt-2"
          >
            <template slot="label">
              Code <span style="color: red">*</span>
            </template>
          </c-input>
        </c-col>
      </c-row>
      <c-row>
        <c-col>
          <c-input
            label="From Date"
            type="date"
            placeholder="From Date"
            v-model="from"
            class="mt-2"
          >
            <template slot="label">
              From Date <span style="color: red">*</span>
            </template>
          </c-input>
        </c-col>
        <c-col>
          <c-input
            label="to Date"
            type="date"
            placeholder="to Date"
            v-model="to"
            class="mt-2"
          >
            <template slot="label"> To Date </template>
          </c-input>
        </c-col>
      </c-row>
    </c-card-body>
    <c-card-footer>
      <c-button
        color="primary"
        class="text-white"
        v-if="!EditSelectedDistributorProduct"
        :disabled="!(selectedProduct && code && from)"
        @click="store"
        >Create</c-button
      >
      <c-button
        color="primary"
        class="text-white"
        v-if="EditSelectedDistributorProduct"
        :disabled="!(selectedProduct && code && from)"
        @click="update"
        >Update</c-button
      >
      <c-button color="default" @click="$router.go(-1)">Cancel</c-button>
    </c-card-footer>
    <c-data-table
      hover
      striped
      sorter
      tableFilter
      footer
      itemsPerPageSelect
      :items="items"
      :fields="fields"
      :items-per-page="1000"
      :active-page="1"
      :responsive="true"
      table-filter
      pagination
      thead-top
    >
      <template #from_date="{ item }">
        <td>{{ format_date(item.from_date) }}</td>
      </template>
      <template #to_date="{ item }">
        <td>{{ format_date(item.to_date) }}</td>
      </template>
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ items.length }}
        </td>
      </template>
      <template #actions="{ item }">
        <td>
          <div class="row justify-content-center">
            <CButton
              color="success"
              v-if="checkPermission('edit_distributor_products')"
              class="text-white btn-sm mt-2 mr-1"
              @click="edit(item.id)"
              ><i class="cil-pencil"></i><CIcon name="cil-pencil"
            /></CButton>
            <c-button
              color="danger"
              v-if="checkPermission('delete_distributor_products')"
              class="btn-sm mt-2 mr-1"
              @click="
                $root
                  .$confirm('Delete', 'Do you want to delete this record?', {
                    color: 'red',
                    width: 290,
                    zIndex: 200,
                  })
                  .then((confirmed) => {
                    if (confirmed) {
                      deleteDistributorProduct(item);
                    }
                  })
              "
              ><c-icon name="cil-trash"
            /></c-button>
          </div>
        </td>
      </template>
    </c-data-table>
  </c-card>
</template>
<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  name: "LinkDistributorProduct",
  components: {
    vSelect,
  },
  props: {
    distributor: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      items: [],
      fields: [
        "id",
        "product",
        "distributor",
        "code",
        "from_date",
        "to_date",
        "actions",
      ],
      products: [],
      selectedProduct: null,
      EditSelectedDistributorProduct: false,
      code: null,
      to: null,
      from: null,
      distributorProduct: null,
    };
  },
  methods: {
    async initialize() {
      return await this.getProducts();
    },
    format_date(value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    edit_date_format(value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    getDistributorProducts() {
      axios
        .get(`/api/distributor/${this.distributor}/product/mapping`)
        .then((response) => {
          this.items = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    async getProducts() {
      try {
        const res = await axios.get("/api/products");
        this.products = res.data.products;
      } catch (err) {
        this.showErrorMessage(err);
      }
    },
    async store() {
      let request = {
        product_id: this.selectedProduct,
        code: this.code,
        from_date: this.crmDateFormat(this.from),
        to_date: this.toDate ? this.crmDateFormat(this.to) : null,
      };
      try {
        const res = await axios.post(
          `/api/distributor/${this.distributor}/product/mapping`,
          request
        );
        this.flash("Distributor Product Mapping Created Successfully");
        this.reset();
      } catch (err) {
        this.showErrorMessage(err);
      }
      this.getDistributorProducts();
    },
    edit(id) {
      this.EditSelectedDistributorProduct = true;
      axios
        .get(`/api/distributors/${id}/mapping/products`)
        .then((response) => {
          this.distributorProduct = response.data.data.id;
          this.selectedProduct = response.data.data.product_id;
          this.code = response.data.data.code;
          this.from = this.edit_date_format(response.data.data.from_date);
          this.to = this.edit_date_format(response.data.data.to_date);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    reset() {
      this.selectedProduct = null;
      this.code = null;
      this.from = null;
      this.to = null;
    },
    update() {
      let request = {
        product_id: this.selectedProduct,
        code: this.code,
        from_date: this.crmDateFormat(this.from),
        to_date: this.to ? this.crmDateFormat(this.to) : null,
      };
      axios
        .put(
          `/api/distributors/${this.distributorProduct}/mapping/products`,
          request
        )
        .then((response) => {
          this.flash("Distributor Product Mapping Updated Successfully");
          this.getDistributorProducts();
          this.reset();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removeDistributorProduct(item) {
      const index = this.items.findIndex((type) => item.id == type.id);
      this.items.splice(index, 1);
    },
    deleteDistributorProduct(item) {
      axios
        .delete(`/api/distributors/${item.id}/mapping/products`)
        .then((res) => {
          this.removeDistributorProduct(item);
          this.flash("Distributor Product Mapping Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
    this.initialize();
    this.getDistributorProducts();
  },
};
</script>
