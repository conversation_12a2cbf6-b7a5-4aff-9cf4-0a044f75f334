<template>
  <c-card>
    <c-card-body>
      <c-row>
        <div class="col-4">
          <c-form-group>
            <template #label>Line <span style="color: red">*</span></template>
            <template #input>
              <v-select
                v-model="selectedLine"
                :options="lines"
                label="name"
                :value="0"
                required
                :reduce="(line) => line.id"
                placeholder="Select Line"
                class="mt-2"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-4">
          <c-input
            label="From Date"
            type="date"
            placeholder="From Date"
            v-model="from"
            class="mt-2"
          >
            <template slot="label">
              From Date <span style="color: red">*</span>
            </template>
          </c-input>
        </div>
        <div class="col-4">
          <c-input
            label="to Date"
            type="date"
            placeholder="to Date"
            v-model="to"
            class="mt-2"
          >
            <template slot="label"> To Date </template>
          </c-input>
        </div>
      </c-row>
    </c-card-body>
    <c-card-footer>
      <c-button
        color="primary"
        class="text-white"
        v-if="!EditSelectedDistributorLine"
        :disabled="!(selectedLine && from)"
        @click="store"
        >Create</c-button
      >
      <c-button
        color="primary"
        class="text-white"
        v-if="EditSelectedDistributorLine"
        :disabled="!(selectedLine && from)"
        @click="update"
        >Update</c-button
      >
      <c-button color="default" @click="$router.go(-1)">Cancel</c-button>
    </c-card-footer>
    <c-data-table
      hover
      striped
      sorter
      tableFilter
      footer
      itemsPerPageSelect
      :items="items"
      :fields="fields"
      :items-per-page="1000"
      :active-page="1"
      :responsive="true"
      pagination
      thead-top
    >
      <template #from_date="{ item }">
        <td>{{ format_date(item.from_date) }}</td>
      </template>
      <template #to_date="{ item }">
        <td>{{ format_date(item.to_date) }}</td>
      </template>
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ items.length }}
        </td>
      </template>
      <template #actions="{ item }">
        <td>
          <div class="row justify-content-center">
            <CButton
              color="success"
              v-if="checkPermission('edit_distributor_lines')"
              class="text-white btn-sm mt-2 mr-1"
              @click="edit(item.id)"
              ><i class="cil-pencil"></i><CIcon name="cil-pencil"
            /></CButton>
            <c-button
              color="danger"
              v-if="checkPermission('delete_distributor_lines')"
              class="btn-sm mt-2 mr-1"
              @click="
                $root
                  .$confirm('Delete', 'Do you want to delete this record?', {
                    color: 'red',
                    width: 290,
                    zIndex: 200,
                  })
                  .then((confirmed) => {
                    if (confirmed) {
                      deleteDistributorLine(item);
                    }
                  })
              "
              ><c-icon name="cil-trash"
            /></c-button>
          </div>
        </td>
      </template>
    </c-data-table>
  </c-card>
</template>


<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  name: "LinkDistributorLine",
  components: {
    vSelect,
  },
  props: {
    distributor: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      items: [],
      fields: ["id", "distributor", "line", "from_date", "to_date", "actions"],
      lines: [],
      selectedLine: [],
      EditSelectedDistributorLine: false,
      to: null,
      from: null,
      distributorLine: null,
    };
  },
  methods: {
    format_date(value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    edit_date_format(value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    async initialize() {
      return await this.getLines();
    },
    getDistributorLines() {
      axios
        .get(`/api/distributor/${this.distributor}`)
        .then((response) => {
          this.items = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    async getLines() {
      try {
        const res = await axios.get("/api/lines");
        this.lines = res.data.lines;
      } catch (err) {
        this.showErrorMessage(err);
      }
    },
    async store() {
      let request = {
        lines: this.selectedLine,
        from_date: this.crmDateFormat(this.from),
        to_date: this.to? this.crmDateFormat(this.to):null,
      };
      try {
        const res = await axios.post(
          `/api/distributor/${this.distributor}`,
          request
        );
        this.flash("Distributor Line Created Successfully");
        this.reset();
      } catch (err) {
        this.showErrorMessage(err);
      }
      this.getDistributorLines();
    },
    edit(id) {
      this.EditSelectedDistributorLine = true;
      axios
        .get(`/api/distributor/${id}/lines`)
        .then((response) => {
          this.distributorLine = response.data.data.id;
          this.selectedLine = response.data.data.line_id;
          this.from = this.edit_date_format(response.data.data.from_date);
          this.to = this.edit_date_format(response.data.data.to_date);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    reset() {
      this.selectedLine = null;
      this.from = null;
      this.to = null;
    },
    update() {
      let request = {
        line_id: this.selectedLine,
        from_date: this.crmDateFormat(this.from),
        to_date: this.to? this.crmDateFormat(this.to):null,
      };
      axios
        .put(`/api/distributor/${this.distributorLine}/lines`, request)
        .then((response) => {
          this.flash("Distributor Line Updated Successfully");
          this.getDistributorLines();
          this.reset();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removeDistributorLine(item) {
      const index = this.items.findIndex((type) => item.id == type.id);
      this.items.splice(index, 1);
    },
    deleteDistributorLine(item) {
      axios
        .delete(`/api/distributor/${item.id}`)
        .then((res) => {
          this.removeDistributorLine(item);
          this.flash("Distributor Line Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
    this.initialize();
    this.getDistributorLines();
  },
};
</script>
