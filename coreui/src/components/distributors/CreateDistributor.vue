<template>
  <c-card no-header>
    <c-card-body>
      <div class="row">
        <div class="col-8">
          <c-input
            label="Name"
            type="text"
            placeholder="Name"
            v-model="distributor.name"
          >
            <template slot="label">
              Name <span style="color: red">*</span>
            </template>
          </c-input>
        </div>
        <div class="col-4">
          <c-input
            label="Sort"
            type="number"
            placeholder="Sort"
            v-model="distributor.sort"
          >
            <template slot="label">
              Sort <span style="color: red">*</span>
            </template>
          </c-input>
        </div>
      </div>
      <CFormGroup v-if="unifiedDistributors.length>0">
              <template #label> Unified Distributor </template>
              <template #input>
                <v-select
                  v-model="distributor.unified_distributor_id"
                  :options="unifiedDistributors"
                  label="name"
                  :value="0"
                  :reduce="(Udistributor) => Udistributor.id"
                  placeholder="Select Unified Distributor"
                  class="mt-2"
                />
              </template>
            </CFormGroup>

      <div class="row">
        <div class="col">
          <c-textarea
            label="Notes"
            type="text"
            placeholder="Notes"
            v-model="distributor.notes"
          ></c-textarea>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button
        color="primary"
        class="text-white"
        @click="store"
        :disabled="!(distributor.name)"
        >Create</c-button
      >
      <c-button color="default" @click="$router.go(-1)">Cancel</c-button>
    </c-card-footer>
  </c-card>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  name: "CreateDistributor",
  components: {
    vSelect,
  },
  data: () => {
    return {
      distributor: {
        name: null,
        notes: null,
        sort: null,
        unified_distributor_id:null
      },
      distributor_id: null,
      code: null,
      unifiedDistributors:[]
    };
  },
  emits: ["getDistributors"],
  methods: {
    getUnifiedDistributors(){
      axios
        .get("/api/load-distributors")
        .then((response) => {
           this.unifiedDistributors=response.data.result;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    store() {
      let request = {
        name: this.distributor.name,
        notes: this.distributor.notes,
        sort: this.distributor.sort,
        unified_distributor_id:this.distributor.unified_distributor_id
      };
      axios
        .post("/api/distributors", request)
        .then((response) => {
          this.distributor_id = response.data.distributor.id;
          this.distributor = {
            name: "",
            notes: "",
          };
          this.flash("Distributor Created Successfully.");
          this.$emit("getDistributors", { distributor: this.distributor_id });
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created () {
    this.getUnifiedDistributors();
  }
};
</script>
