<template>
  <CModal title="Create Article" size="xl" v-if="dialog" :show.sync="dialog">
    <div class="scroll">
      <div class="row">
        <div class="col-8">
          <CInput label="Name" type="text" placeholder="Name" v-model="name">
            <template slot="label">
              Name <span style="color: red">*</span>
            </template>
          </CInput>
        </div>
      </div>
      <KeywordsInputField ref="keywordsInputField" v-model="keywords" />
      <div class="row">
        <div class="col-8">
          <CSelect placeholder=" " :options="options" :value.sync="selected">
            <template slot="label">
              Parent Topic <span style="color: red">*</span>
            </template>
          </CSelect>
        </div>
      </div>
      <div class="row">
        <div class="col-12">
          <quill-editor
            ref="myQuillEditor"
            v-model="content"
            :options="editorOption"
          />
          <input
            ref="imageInput"
            class="d-none"
            type="file"
            accept="image/*"
            @change="imageUpload"
          />
        </div>
      </div>
    </div>
    <template #footer>
      <CButton @click="close" class="text-white" color="danger"
        >Discard</CButton
      >
      <CButton @click="submit" class="text-white" color="success">Save</CButton>
    </template>
  </CModal>
</template>

<script>
import { quillEditor } from "vue-quill-editor";
import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";
import KeywordsInputField from "../../components/common/KeywordsInputField.vue";
export default {
  components: {
    KeywordsInputField,
    quillEditor,
  },
  emits: ["newArticleHasAdd"],
  props: {
    subTopics: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      name: null,
      selected: null,
      keywords: [],
      content: null,
      delta: null,
      editorOption: {
        theme: "snow",
        modules: {
          toolbar: {
            container: [
              ["bold", "italic", "underline", "strike"], // toggled buttons
              ["blockquote", "code-block"],

              [{ header: 1 }, { header: 2 }], // custom button values
              [{ list: "ordered" }, { list: "bullet" }],
              [{ script: "sub" }, { script: "super" }], // superscript/subscript
              [{ indent: "-1" }, { indent: "+1" }], // outdent/indent
              [{ direction: "rtl" }], // text direction

              [{ size: ["small", false, "large", "huge"] }], // custom dropdown
              [{ header: [1, 2, 3, 4, 5, 6, false] }],

              [{ color: [] }, { background: [] }], // dropdown with defaults from theme
              [{ font: [] }],
              [{ align: [] }],

              ["clean"],
              ["link", "image", "video"],
            ],
            handlers: {
              image: this.insertImage,
            },
          },
        },
        placeholder: "insert text here",
        readOnly: false,
      },
    };
  },
  methods: {
    initialize() {
      this.name = null;
      this.sort = null;
      this.content = null;
      this.keywords = [];
      if (!this.subTopics) {
        return;
      }
      this.selected = this.subTopics.map((topic) => topic.name);
    },
    submit() {
      if (!this.subTopics) {
        this.flash("There is no subtopics to insert new article to.", "error");
        this.close();
        return;
      }
      const parent = this.subTopics.find((topic) => {
        const name = Object.keys(this.selectedObjects).find((topicName) => {
          if (this.selectedObjects[topicName] == true) {
            return topicName;
          }
        });
        if (name == topic.name) {
          return topic;
        }
      });
      if (!parent) {
        this.flash("Should choose the parent of the article.", "error");
        this.close();
        return;
      }
      this.saveNewArticle({
        name: this.name,
        parent: parent,
        keywords: [...this.keywords],
        content: this.delta,
      });

      this.close();
    },
    saveNewArticle(article) {
      axios
        .post(`/api/help/subtopics/${article.parent.id}/articles`, {
          name: article.name,
          content: JSON.stringify(article.content),
        })
        .then((articleRes) => {
          axios
            .post(`/api/help/articles/${articleRes.data.data.id}/keywords`, {
              keywords: [...article.keywords],
            })
            .then((keywordsRes) => {
              this.flash("Article add successfully", "success");
              this.$emit("newArticleHasAdd", ["loadTopics", "loadKeywords"]);
            })
            .catch((error) => {
              this.showErrorMessage(error);
            });
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    insertImage() {
      this.$refs.imageInput.click();
    },
    imageUpload(event) {
      const file = event.target.files[0];
      const formData = new FormData();
      formData.append("file", file);
      formData.append("folder", "help");
      axios
        .post("/api/upload/attachment", formData)
        .then((response) => {
          event.target.value = "";
          const currentIndex = this.editor.selection.lastRange.index;
          this.editor.insertEmbed(
            currentIndex,
            "image",
            response.data.data.url
          );
          this.editor.setSelection(currentIndex + 1, 0);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    open() {
      this.dialog = true;
    },
    close() {
      this.initialize();
      this.$refs.keywordsInputField.initialize();
      this.dialog = false;
    },
  },
  watch: {
    content() {
      this.delta = this.$refs.myQuillEditor.quill.getContents();
    },
  },
  computed: {
    options() {
      if (!this.subTopics) {
        return [];
      }
      return this.subTopics.map((topic) => topic.name);
    },
    selectedObjects() {
      return this.options.reduce((obj, option) => {
        obj[option] = option === this.selected;
        return obj;
      }, {});
    },
    editor() {
      return this.$refs.myQuillEditor.quill;
    },
  },
  created() {
    this.initialize();
  },
};
</script>
<style>
.scroll {
  height: 429px;
  overflow-x: hidden;
  overflow-y: auto;
}
.ql-container {
  height: 20rem;
}
.ql-tooltip.ql-editing {
  margin-left: 9.4rem;
}
</style>
