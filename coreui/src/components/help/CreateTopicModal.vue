<template>
  <CModal title="Create Topic" v-if="dialog" :show.sync="dialog">
    <div class="row">
      <div class="col-8">
        <CInput label="Name" type="text" placeholder="Name" v-model="name">
          <template slot="label">
            Name <span style="color: red">*</span>
          </template>
        </CInput>
      </div>
    </div>
    <div class="row">
      <div class="col-8">
        <CInput label="Sort" type="text" placeholder="Sort" v-model="sort">
          <template slot="label">
            Sort <span style="color: red">*</span>
          </template>
        </CInput>
      </div>
    </div>
    <div class="row">
      <div class="col-8">
        <CSelect
          placeholder="root topic"
          :options="options"
          :value.sync="selected"
        >
          <template slot="label">
            Parent Topic <span style="color: red">*</span>
          </template>
        </CSelect>
      </div>
    </div>
    <template #footer>
      <CButton @click="close" class="text-white" color="danger">Discard</CButton>
      <CButton @click="submit" class="text-white" color="success">Save</CButton>
    </template>
  </CModal>
</template>

<script>
export default {
  emits:['newTopicHasAdd'],
  props: {
    mainTopics:{
      type:Array,
      required:true
    }
  },
  data() {
    return {
      dialog: false,
      name: null,
      sort: null,
      selected: null,
      parent: null,
    };
  },
  methods: {
    initialize() {
      this.name = null;
      this.sort = null;
      if (!this.mainTopics) {
        console.log("this is no good");
        return;
      }
      this.selected = this.mainTopics.map((topic) => topic.name);
    },
    submit() {
      if (this.mainTopics) {
        this.parent = this.mainTopics.find((topic) => {
          const name = Object.keys(this.selectedObjects).find((topicName) => {
            if (this.selectedObjects[topicName] == true) {
              return topicName;
            }
          });
          if (name == topic.name) {
            return topic;
          }
        });
      }

      axios
        .post("/api/help/topics", {
          name: this.name,
          sort: this.sort,
          parent_topic_id: this.parent ? this.parent.id : null,
        })
        .then((res) => {
          this.flash("Topic is add successfully.", "success");
          this.$emit('newTopicHasAdd',["loadTopics","loadSubTopics","loadMainTopics"]);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      this.close();
    },
    open() {
      this.dialog = true;
    },
    close() {
      this.initialize();
      this.dialog = false;
    },
  },
  computed: {
    options() {
      if (!this.mainTopics) {
        return [];
      }
      return this.mainTopics.map((topic) => topic.name);
    },
    selectedObjects() {
      return this.options.reduce((obj, option) => {
        obj[option] = option === this.selected;
        return obj;
      }, {});
    },
  },
  created() {
    this.initialize();
  },
};
</script>
