<template>
  <v-card v-scroll.self="onScroll" class="overflow-y-auto full-width" :max-height="height">
    <v-card-text>
      <v-treeview
        :search="search"
        :filter="filter"
        :items="topics"
        open-on-click
      >
        <template v-slot:prepend="{ item, leaf }">
          <v-icon v-if="item.children" v-text="`mdi-folder-network`" />
          <v-icon
            v-else
            v-text="`mdi-archive`"
            @click="showInPreview(item, leaf)"
          />
        </template>
        <template v-slot:label="{ item, leaf }">
          <h5
            class="mt-2"
            v-text="item.name"
            @click="showInPreview(item, leaf)"
          />
        </template>
      </v-treeview>
    </v-card-text>
  </v-card>
</template>

<script>
export default {
  emits: ["showInPreview"],
  props: {
    topics: {
      type: Array,
      required: true,
    },
    selectKeyword: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      items: [],
      scrollInvoked: 0,
    };
  },
  methods: {
    showInPreview(item, leaf) {
      this.$emit("showInPreview", item, leaf);
    },
    onScroll() {
      this.scrollInvoked++;
    },
  },
  computed: {
    search() {
      if (!this.selectKeyword) return "";
      let searchString = this.selectKeyword.reduce(
        (appendedSearchItems, singleSearchItem) => {
          if (appendedSearchItems == "")
            return (appendedSearchItems += `${singleSearchItem}`);
          return (appendedSearchItems += ` ${singleSearchItem}`);
        },
        ""
      );
      return searchString;
    },
    filter() {
      return (item, search) => {
        let searchResults = false;
        let keywords = [];
        //maintopic
        if (
          Object.getOwnPropertyNames(item).find(
            (key) => key === "parent_topic_id"
          ) !== "parent_topic_id"
        ) {
          const topic = Object.values(item);
          topic.forEach((topicItem) => {
            if (Array.isArray(topicItem)) {
              keywords = topicItem
                .map((subtopic) =>
                  subtopic.children.map((article) =>
                    article.keywords.map((keyword) => keyword.name)
                  )
                )
                .flat(Infinity);
            }
          });
        }
        //subtopic
        if (
          Object.getOwnPropertyNames(item).find(
            (key) => key === "parent_topic_id"
          ) === "parent_topic_id" &&
          Object.keys(item).find((key) => key === "children") === "children"
        ) {
          const subtopic = Object.values(item);
          subtopic.forEach((subtopicItem) => {
            if (Array.isArray(subtopicItem)) {
              keywords = subtopicItem
                .map((article) =>
                  article.keywords.map((keyword) => keyword.name)
                )
                .flat(Infinity);
            }
          });
        }

        //article
        if (
          Object.getOwnPropertyNames(item).find((key) => key === "keywords") ==
          "keywords"
        ) {
          keywords = item.keywords.map((keyword) => keyword.name);
        }

        search = search.split(" ");
        searchResults = keywords.reduce(
          (acc, keyword) => acc || search.includes(keyword),
          false
        );
        return searchResults;
      };
    },
    height() {
      let height=200;
      switch (this.$vuetify.breakpoint.name) {
        case "xs":
          height+=150
          break
        case "sm":
          height+=250
          break
        case "md":
          height+=350
          break
        case "lg":
          height+=450
          break
        case "xl":
          height+=550
          break
      }
      return height
    },
  },
};
</script>
<style scoped>
.full-width{
  width:100vw
}
</style>
