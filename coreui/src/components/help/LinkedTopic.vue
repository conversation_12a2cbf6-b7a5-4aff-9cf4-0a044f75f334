<template>
  <CCol col="12" md="12" class="p-0">
    <CCard v-if="topic">
      <CCardHeader @click="collapsed = !collapsed" class="text-left btn">
        <strong>{{ topic.name }}</strong>
      </CCardHeader>
      <CCollapse :show="!collapsed">
        <CCardBody>
          <CListGroup>
            <CListGroupItem
              v-for="subTopic in getChildrenOfTopic"
              :key="subTopic.id"
            >
              <LinkedArticleOfTopic
                :topic="subTopic"
              />
            </CListGroupItem>
          </CListGroup>
        </CCardBody>
      </CCollapse>
    </CCard>
  </CCol>
</template>
<script>
import { mapState} from "vuex";
import LinkedArticleOfTopic from "./LinkedArticleOfTopic.vue";
export default {
  components: {
    LinkedArticleOfTopic,
  },
  props: {
    topic: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      collapsed: true,
    };
  },
  methods: {
    
    initialize() {
    },

  },
  computed: {
    ...mapState("help", ["subTopics"]),
    getChildrenOfTopic() {
      if(!this.subTopics)return [];
      return this.subTopics.filter(
        (subTopic) => subTopic.parent_topic_id==this.topic.id
      );
    },
  },
  created() {
    this.initialize();
  },
};
</script>
<style scoped>
</style>
