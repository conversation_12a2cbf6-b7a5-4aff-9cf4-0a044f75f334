<template>
  <c-card>
    <c-card-header>Create Target Details</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-4">
          <c-form-group>
            <template #label>
              <strong>Line</strong>
            </template>
            <template #input>
              <v-select
                title="Search for option"
                v-model="line_id"
                :options="lines"
                label="name"
                :value="0"
                class="mt-2"
                :reduce="(line) => line.id"
              ></v-select>
            </template>
          </c-form-group>
        </div>
        <div class="col-4">
          <c-form-group>
            <template #label>
              <strong>Target Type</strong>
            </template>
            <template #input>
              <v-select
                title="Search for option"
                v-model="type_id"
                :options="types"
                label="name"
                :value="0"
                class="mt-2"
                :reduce="(type) => type.id"
              ></v-select>
            </template>
          </c-form-group>
        </div>
        <div class="col-4">
          <c-form-group>
            <template #label>
              <strong>Date</strong>
            </template>
            <template #input>
              <c-input type="date" class="mt-2" placeholder="Date" v-model="date"></c-input>
            </template>
          </c-form-group>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" class="text-white" @click="show" style="float: right"
        >Show</c-button
      >
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      lines: [],
      types: [],
      line_id: null,
      type_id: null,
      date: null,
      showTable:false
    };
  },
  emits: ["getSchedule"],
  methods: {
    initialize() {
      axios
        .get("/api/target-details/data")
        .then((response) => {
          this.lines = response.data.lines;
          this.types = response.data.types;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    show() {
      this.$emit("getSchedule",{line:this.line_id,type:this.type_id,date:this.date});
    },
  },
  created() {
    this.initialize();
  },
};
</script>
