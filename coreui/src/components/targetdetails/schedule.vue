<template>
  <c-card>
    <c-card-header>
      <div class="row">
        <div class="col-md-5"><strong>Target Details</strong></div>
        <div class="col-md-2">
          <strong>{{ crmDateFormat }}</strong>
        </div>
        <div class="col-md-5">
          <c-button color="primary" @click="store()" style="float: right"
            >Save</c-button
          >
        </div>
      </div>
    </c-card-header>
    <c-card-body>
      <div class="target" style="height: auto">
        <table class="table table-striped">
          <thead>
            <th scope="col"><label class="text-white">Division</label></th>
            <th scope="col" v-for="(product, index) in dataCols" :key="index">
              <div style="width: 100px" class="text-center text-white">
                <label>{{ product.name }}</label>
              </div>
            </th>
          </thead>
          <tbody>
            <tr v-for="(division, rowIndex) in dataRows" :key="rowIndex">
              <td>
                <label>{{ division.name }}</label>
              </td>
              <td
                style="width: 50px"
                v-for="(product, colIndex) in dataCols"
                :key="colIndex"
              >
                <c-input
                  type="number"
                  v-model="targetDetails[rowIndex][colIndex].target"
                  @change="setTotal(targetDetails[rowIndex][colIndex].target,colIndex)"
                />
              </td>
            </tr>
            <tr style="background: #2eb85c">
              <td><label class="text-white">Total</label></td>
              <td
                style="width: 50px"
                v-for="(total, colIndex) in holdData"
                :key="colIndex"
              >
                <c-input type="number" :value="total" disabled />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" class="text-white" @click="store()">Save</c-button>
      <c-button color="default" :to="{ name: 'target-details' }"
        >Cancel</c-button
      >
    </c-card-footer>
  </c-card>
</template>
<script>
import moment from "moment";
import Table from "../../views/tables/Table.vue";
export default {
  components: { Table },
  props: {
    dataRows: {
      type: Array,
      required: true,
    },
    dataCols: {
      type: Array,
      required: true,
    },
    settingTarget: {
      type: Array,
      required: true,
    },
    setting:{
      type: Array,
      required: true,
    },
    line: {
      type: Number,
      required: true,
    },
    type: {
      type: Number,
      required: true,
    },
    date: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      targetDetails: null,
      totals: [],
      holdData: [],
    };
  },
  methods: {
    initialize() {
      this.targetDetails = new Array(this.dataRows.length);
      this.totals = new Array(this.dataCols.length).fill(0,0,this.dataCols.length);
      if (this.settingTarget[0].value == "Brick" && this.setting[0].value == "Product") {
        for (let j = 0; j < this.dataRows.length; j++) {
          this.targetDetails[j] = [];
        }
        for (let i = 0; i < this.dataRows.length; i++) {
          for (let j = 0; j < this.dataCols.length; j++) {
            this.targetDetails[i][j] = {
              id: null,
              line_id: this.line,
              type_id: this.type,
              date: this.date,
              div_id: null,
              brick_id: this.dataRows[i].id,
              product_id: this.dataCols[j].id,
              target: null,
            };
          }
        }
      }
      else {
        for (let j = 0; j < this.dataRows.length; j++) {
          this.targetDetails[j] = [];
        }
        for (let i = 0; i < this.dataRows.length; i++) {
          for (let j = 0; j < this.dataCols.length; j++) {
            this.targetDetails[i][j] = {
              id: null,
              line_id: this.line,
              type_id: this.type,
              date: this.date,
              brick_id: null,
              div_id: this.dataRows[i].id,
              product_id: this.dataCols[j].id,
              target: null,
            };
          }
        }
      }
      console.log(this.targetDetails);
    },
    filterData() {
      return this.targetDetails
        .map((item, i) => {
          return item
            .map((el) => {
              if (el.target == null) return null;
              return el;
            })
            .filter((item) => item != null);
        })
        .filter((item) => item.length != 0);
    },
    setTotal(value, index) {
      this.totals[index];
      this.totals[index] += +value;
      this.holdData = [];
      this.holdData = this.totals;
    },
    getTargets() {
      axios
        .post("/api/get-target-details", {
          line: this.line,
          type: this.type,
          date: this.date,
        })
        .then((response) => {
          response.data.targets.forEach((element) => {
            for (let i = 0; i < this.dataRows.length; i++) {
              for (let j = 0; j < this.dataCols.length; j++) {
                if (
                  element.div_id != null &&
                  element.div_id === this.targetDetails[i][j].div_id &&
                  element.product_id === this.targetDetails[i][j].product_id
                ) {
                  this.targetDetails[i][j].id = element.id;
                  this.targetDetails[i][j].target = element.target;
                  this.setTotal(element.target, j);
                }

                if (
                  element.brick_id != null &&
                  element.brick_id === this.targetDetails[i][j].brick_id &&
                  element.product_id === this.targetDetails[i][j].product_id
                ) {
                  this.targetDetails[i][j].id = element.id;
                  this.targetDetails[i][j].target = element.target;
                  this.setTotal(element.target, j);
                }
              }
            }
          });

          this.show = true;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    store() {
      let percent = this.filterData();
      console.log();
      axios
        .post("/api/target-details", {percent})
        .then((response) => {
          this.flash("The Target Details is Created Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  computed: {
    crmDateFormat() {
      return moment(this.date).format("MMMM YYYY");
    },
  },
  created() {
    this.initialize();
    this.getTargets();
  },
};
</script>
<style scoped>
.target {
  overflow-y: auto;
  height: 600px;
}
table {
  display: table;
  white-space: nowrap;
  width: 100%;
}
th {
  opacity: 1;
  position: sticky;
  top: 0;
  background: #2eb85c;
}
.form-group {
  margin-bottom: 0;
}
label {
  font-weight: bolder;
  margin-bottom: 0;
}
</style>