<template>
  <c-card>
    <c-card-header> Create Automatic Plan </c-card-header>
    <c-card-body>
      <c-tabs>
        <c-tab active>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-description" /> Main
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <CFormGroup>
                    <template #label>
                      <strong>Line</strong>
                      <strong><span style="color: red">*</span></strong>
                    </template>
                    <template #input>
                      <v-select v-model="line_id" :options="lines" label="name" :value="0" required
                        :reduce="(line) => line.id" placeholder="Select Line" class="mt-2"
                        @input="getLineDivisions()" />
                    </template>
                  </CFormGroup>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8" v-if="line_id">
                  <c-form-group>
                    <template #label>
                      Division
                      <strong><span style="color: red">*</span></strong></template>
                    <template #input>
                      <input label="All" v-if="divisions.length != 0" class="m-1" type="checkbox" id="divisions"
                        v-model="checkAllDivisions" title="Check All Divisions" @change="checkAllDivs" />
                      <label for="divisions" v-if="divisions.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="division_ids" :options="divisions" label="name" :value="0"
                        :reduce="(division) => division.id" placeholder="Select Divisions" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8" v-if="from_date">
                  <CFormGroup>
                    <template #label>
                      <strong>From Date</strong>
                      <strong><span style="color: red">*</span></strong>
                    </template>
                    <template #input>
                      <input type="date" v-model="from_date" class="form-control mt-2" />
                    </template>
                  </CFormGroup>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8" v-if="to_date">
                  <CFormGroup>
                    <template #label>
                      <strong>To Date</strong>
                      <strong><span style="color: red">*</span></strong>
                    </template>
                    <template #input>
                      <input type="date" v-model="to_date" class="form-control mt-2" />
                    </template>
                  </CFormGroup>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-description" /> Account Types
            & Specialities
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Account Type </template>
                    <template #input>
                      <input label="All" v-if="types.length != 0" class="m-1" type="checkbox" id="types"
                        v-model="allAccountTypes" title="Check All Types" @change="checkAllAccounts" />
                      <label for="types" v-if="types.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="type_ids" :options="types" label="name" :value="0" :reduce="(type) => type.id"
                        placeholder="Select Account Type" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Speciality </template>
                    <template #input>
                      <input label="All" v-if="specialities.length != 0" class="m-1" id="specialities" type="checkbox"
                        v-model="checkAllSpecialities" title="Check All Specialities" @change="checkAllSpech" />
                      <label for="specialities" v-if="specialities.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="speciality_ids" :options="specialities" label="name" :value="0"
                        :reduce="(speciality) => speciality.id" placeholder="Select Speciality" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
      </c-tabs>
    </c-card-body>
    <c-card-footer>
      <CButton :disabled="line_id == null && division_ids.length == 0" color="primary" @click="show()"
        style="float: right">
        Show</CButton>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      line_id: null,
      division_ids: [],
      type_ids: [],
      speciality_ids: [],
      lines: [],
      divisions: [],
      types: [],
      specialities: [],
      checkAllSpecialities: true,
      checkAllDivisions: false,
      allAccountTypes: true,
      from_date: null,
      to_date: null,
    };
  },
  emits: ["Schedule"],
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: null,
          to: null,
          data: ['lines', 'accountTypes']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.types = response.data.data.accountTypes;
          this.type_ids = this.types.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineDivisions() {
      axios
        .get(`/api/get-line-division/${this.line_id}`)
        .then((response) => {
          this.divisions = response.data.divisions;
          this.specialities = response.data.specialities;
          this.speciality_ids = this.specialities.map((item) => item.id);
          this.from_date = response.data.user_plan_start_day;
          // this.min_from_date = response.data.user_plan_start_day;
          this.to_date = response.data.final_user_plan_end_day;
          // this.max_to_date = response.data.final_user_plan_end_day;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllAccounts() {
      if (this.allAccountTypes)
        this.type_ids = this.types.map((item) => item.id);
      if (this.allAccountTypes == false) this.type_ids = null;
    },
    checkAllDivs() {
      if (this.checkAllDivisions) {
        this.division_ids = this.divisions.map((item) => item.id);
      }
      if (this.checkAllDivisions == false) {
        this.division_ids = null;
      }
    },
    checkAllSpech() {
      if (this.checkAllSpecialities)
        this.speciality_ids = this.specialities.map((item) => item.id);
      if (this.checkAllSpecialities == false) this.speciality_ids = null;
    },
    show() {
      let planFilter = {
        line: this.line_id,
        divisions: this.division_ids,
        types: this.type_ids,
        specialities: this.speciality_ids,
        fromDate: this.from_date,
        toDate: this.to_date,
      };
      this.$emit("Schedule", planFilter);
    },
  },
  created() {
    this.initialize();
  },
};
</script>
