<template>
  <nav class="flex">
    <ul class="pagination">
      <li class="page-item">
        <button
          type="button"
          class="page-link"
          v-if="page != 1"
          @click="
            () => {
              page--;
              paginate();
            }
          "
        >
          Previous
        </button>
      </li>
      <li class="page-item">
        <button
          class="page-link"
          v-for="pageNumber in pages.slice(page - 1, page + 5)"
          @click="
            () => {
              page = pageNumber;
              paginate();
            }
          "
          :key="pageNumber"
        >
          {{ pageNumber }}
        </button>
      </li>
      <li class="page-item">
        <button
          type="button"
          class="page-link"
          @click="
            () => {
              page++;
              paginate();
            }
          "
          v-if="page < pages.length"
        >
          Next
        </button>
      </li>
    </ul>
  </nav>
</template>
<script>
export default {
  emits: ["paginated"],
  data() {
    return {
      pages: [],
      page: 1,
      perPage: 9,
    };
  },
  props: {
    data: {
      type: Array,
      required: true,
    },
  },
  methods: {
    setPages() {
      let numberOfPages = Math.ceil(this.data.length / this.perPage);
      for (let index = 1; index <= numberOfPages; index++) {
        this.pages.push(index);
      }
    },
    paginate() {
      let page = this.page;
      let perPage = this.perPage;
      let from = page * perPage - perPage;
      let to = page * perPage;
      this.$emit("paginated", this.data.slice(from, to));
    },
  },
  watch: {
    data() {
      this.setPages();
    },
  },
  filters: {
    trimWords(value) {
      return value.split(" ").splice(0, 20).join(" ") + "...";
    },
  },
  mounted() {
      this.paginate()
  },
};
</script>

<style>
.pagination{
    display: flex;
    justify-items: center;
    justify-content: center;
}
button.page-link {
  display: inline-block;
}
 button.page-link {
    font-size: 20px;
    color: #4189DE;
    font-weight: 500;
}
</style>