<template>
    <div class="search-container">
        <div class="search-input-wrapper">
            <i class="fas fa-search search-icon"></i>
            <input type="text" :value="searchTerm" @input="$emit('update:searchTerm', $event.target.value)"
                class="search-input" placeholder="Search by account, doctor, group..." />
            <button v-if="searchTerm" @click="$emit('clearSearch')" class="clear-search-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
</template>

<script>
export default {
    name: 'PlanVisitsSearch',
    props: {
        searchTerm: {
            type: String,
            required: true
        }
    },
    emits: ['update:searchTerm', 'clearSearch']
}
</script>

<style scoped>
.search-container {
    padding: 16px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 12px;
    color: var(--gray);
    font-size: 14px;
}

.search-input {
    flex-grow: 1;
    padding: 10px 12px 10px 36px;
    /* Adjust left padding for icon */
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(58, 123, 242, 0.2);
}

.clear-search-btn {
    position: absolute;
    right: 10px;
    background: none;
    border: none;
    color: var(--gray);
    cursor: pointer;
    font-size: 14px;
    padding: 4px;
}

.clear-search-btn:hover {
    color: var(--dark);
}
</style>