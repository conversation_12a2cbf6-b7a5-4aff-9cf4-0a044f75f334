<template>
    <div class="card-header">
        <div class="header-title">
            <div class="title-icon">
                <c-icon name="cil-calendar" />
            </div>
            <h2>Plan Visits</h2>
            <div class="period-badge">{{ month }}</div>
        </div>
        <div class="header-actions">
            <button class="btn-save" @click="$emit('save')" :disabled="saving">
                <span v-if="!saving">Save</span>
                <span v-else class="spinner"></span>
            </button>
        </div>
    </div>
</template>

<script>
export default {
    name: 'PlanVisitsHeader',
    props: {
        month: {
            type: String,
            required: true
        },
        saving: {
            type: Boolean,
            default: false
        }
    }
}
</script>

<style scoped>
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: linear-gradient(to right, var(--primary), var(--primary-dark));
    color: white;
}

.header-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.title-icon {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.header-title h2 {
    margin: 0;
    font-weight: 600;
    font-size: 20px;
    letter-spacing: -0.2px;
}

.period-badge {
    background: rgba(255, 255, 255, 0.15);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    margin-left: 10px;
}

.btn-save {
    background: rgba(255, 255, 255, 0.9);
    color: var(--primary-dark);
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-save:hover {
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-save:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Add spinner styles if needed */
.spinner {
    /* Basic spinner example */
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-left-color: var(--primary-dark);
    border-radius: 50%;
    width: 16px;
    height: 16px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
</style>