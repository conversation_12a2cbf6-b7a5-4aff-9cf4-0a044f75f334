<template>
    <div class="stats-bar">
        <div class="stat-item">
            <span class="stat-label">Total List</span>
            <span class="stat-value">{{ totalList }}</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">Planned</span>
            <span class="stat-value">{{ plannedVisits }}</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">Filtered</span>
            <span class="stat-value">{{ filteredVisits }}</span>
        </div>
        <div class="stat-legend">
            <div class="legend-item">
                <span class="color-dot completed"></span>
                <span>Completed</span>
            </div>
            <div class="legend-item">
                <span class="color-dot pending"></span>
                <span>Pending</span>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'PlanVisitsStats',
    props: {
        totalList: {
            type: Number,
            required: true
        },
        plannedVisits: {
            type: Number,
            required: true
        },
        filteredVisits: {
            type: Number,
            required: true
        }
    }
}
</script>

<style scoped>
.stats-bar {
    display: flex;
    background: var(--primary-light);
    padding: 12px 24px;
    justify-content: space-between;
    align-items: center;
}

.stat-item {
    display: flex;
    flex-direction: column;
}

.stat-label {
    font-size: 12px;
    color: var(--primary-dark);
    opacity: 0.8;
}

.stat-value {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-dark);
}

.stat-legend {
    display: flex;
    gap: 16px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: var(--dark);
}

.color-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

.color-dot.completed {
    background: var(--success);
}

.color-dot.pending {
    background: var(--warning);
}
</style>