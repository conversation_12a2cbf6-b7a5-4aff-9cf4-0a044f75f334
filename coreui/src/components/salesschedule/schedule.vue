<template>
    <c-card-body>
      <div class="contribution" style="height: auto">
        <table id="contribution_table" class="table table-striped">
          <thead>
            <th scope="col"><label class="text-white">Division</label></th>
            <th scope="col" v-for="(product, index) in dataCols" :key="index">
              <div style="width: 100px" class="text-center text-white">
                {{ product.name }}
              </div>
            </th>
          </thead>
          <tbody>
            <tr v-for="(division, rowIndex) in dataRows" :key="rowIndex">
              <td>{{ division.name }}</td>
              <td
                style="width: 50px"
                v-for="(product, colIndex) in dataCols"
                :key="colIndex"
              >
                <c-input type="number" v-model="percent[rowIndex][colIndex].contribution" />
              </td>
            </tr>
            <tr>
              <td><label class="text-white">Total</label></td>
              <td>
                
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </c-card-body>
</template>