<template>
  <CRow>
    <CCol col="12" lg="12">
      <c-card>

        <c-card-body>
          <div class="row">
            <div class="col-lg-3 col-md-3 col-sm-8" v-if="!is_edit">
              <c-form-group>
                <template #label> Line </template>
                <template #input>
                  <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                    placeholder="Select Line" class="mt-3" @input="getLineData" />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-2 col-md-2 col-sm-8" v-if="is_edit">
              <c-form-group>
                <template #label> Line </template>
                <template #input>
                  <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                    placeholder="Select Line" class="mt-3" disabled />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-8" v-if="!is_edit">
              <c-form-group>
                <template #label> Division </template>
                <template #input>
                  <input label="All" id="divisons" v-if="divisions.length != 0" class="m-1" type="checkbox"
                    v-model="checkAllDivisions" title="Check All Divisions" @change="checkAllDivision" />
                  <label for="divisions" v-if="divisions.length != 0" style="font-weight: bold">All</label>
                  <v-select v-model="division_id" :options="divisions" label="name" :value="0"
                    :reduce="(division) => division.id" placeholder="Select Division" class="mt-2" multiple />
                </template>
              </c-form-group>
            </div>

            <div class="col-lg-2 col-md-2 col-sm-8" v-if="is_edit">
              <c-form-group>
                <template #label> Division </template>
                <template #input>
                  <v-select v-model="division_id" :options="divisions" label="name" :value="0"
                    :reduce="(division) => division.id" placeholder="Select Division" class="mt-3" disabled />
                </template>
              </c-form-group>
            </div>

            <div class="col-lg-3 col-md-3 col-sm-8">
              <c-input label="From Date" type="date" placeholder="Date" v-model="from_date" class="mt-2"></c-input>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-8">
              <c-input label="To Date" type="date" placeholder="Date" v-model="to_date" class="mt-2"></c-input>
            </div>
            <div class="mt-1" v-if="is_edit">
              <!-- <label for="Update Line Users" style="font-weight: bold">Update Line Users</label> -->
              <c-input label="Update Line Users" style="font-weight: bold" type="checkbox" placeholder="Date"
                @change="toggleUpdateLineUser" class="mt-3"></c-input>
            </div>
          </div>
        </c-card-body>
        <c-card-footer>
          <c-button color="primary" class="text-white" v-if="!is_edit" @click="storeLineDivision()"
            style="float: right">Add</c-button>

          <c-button color="primary" class="text-white" v-if="is_edit" @click="updateLineDivision()"
            style="float: right">Edit</c-button>

          <CButton color="danger" @click="reset" style="float: left">Cancel</CButton>
        </c-card-footer>
      </c-card>


      <c-row>
        <c-col col="12" xl="12">
          <transition name="slide">
            <c-card>
              <c-card-body>
                <c-data-table hover striped sorter tableFilter footer itemsPerPageSelect :items="items" :fields="fields"
                  :items-per-page="1000" :active-page="1" :responsive="true" table-filter pagination thead-top>
                  <template slot="thead-top">
                    <td style="border-top: none"><strong>Total</strong></td>
                    <td style="border-top: none" class="text-xs-right">
                      {{ items.length }}
                    </td>
                  </template>
                  <template #actions="{ item }">
                    <td>
                      <div class="row justify-content-center">
                        <c-button color="success" class="btn-sm mt-2 mr-1 text-white" @click='editData(item)'>
                          <c-icon name="cil-pencil" /></c-button>
                        <c-button color="danger" v-if="checkPermission('delete_currencies')" class="btn-sm mt-2 mr-1"
                          @click="
                            $root
                              .$confirm(
                                'Delete',
                                'Do you want to delete this record?',
                                {
                                  color: 'red',
                                  width: 290,
                                  zIndex: 200,
                                }
                              )
                              .then((confirmed) => {
                                if (confirmed) {
                                  deleteUser(item);
                                }
                              })
                            "><c-icon name="cil-trash" /></c-button>
                      </div>
                    </td>
                  </template>
                </c-data-table>
              </c-card-body>
            </c-card>
          </transition>
        </c-col>
      </c-row>
    </CCol>
  </CRow>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";

export default {
  data() {
    return {
      lines: [],
      divisions: [],
      line_id: [],
      division_id: [],
      checkAllLines: false,
      updateLineUser: false,
      checkAllDivisions: false,
      items: [],
      lineDivisionUserId: null,
      fields: ["id", "employee", "line", "division", 'from_date', "to_date", "actions"],
      userId: null,
      editable_data: [],
      is_edit: false,
      from_date: "",
      to_date: "",
    };
  },
  components: {
    vSelect
  },
  props: ['user_id', 'user'],
  methods: {
    edit_date_format: function (value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    toggleUpdateLineUser() {
      this.updateLineUser = !this.updateLineUser; // Toggle the value
    },
    initialize() {
      axios
        .get("/api/lines-targets-summary/")
        .then((response) => {
          this.lines = response.data.data.lines;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post("/api/line-data-targets-summary/", { lines: [this.line_id] })
        .then((response) => {
          this.divisions = response.data.data.divisions;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    reset() {
      this.line_id = null;
      this.division_id = null;
      this.from_date = null;
      this.to_date = null;
      this.is_edit = false;
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = null;
      this.getLineData();
    },
    checkAllDivision() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = null;
    },
    emitInput() {
      this.$emit('input', {
        basicDataEmpCode: this.code,
        basicDataEmpName: this.name,
        basicDataEmpNameEnglish: this.nameEnglish,
        basicDataEmpNameCRM: this.nameCRM,

      });
    },
    removeUser(item) {
      const index = this.items.findIndex((type) => item.id == type.id);
      this.items.splice(index, 1);
    },
    deleteUser(item) {
      axios
        .delete("/api/user/linedivision", { data: item })
        .then((res) => {
          this.removeUser(item);
          this.flash("User Division Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
    storeLineDivision() {
      axios
        .post("/api/user/linedivision", {
          user_id: this.user_id,
          line: this.line_id,
          division: this.division_id,
          from_date: this.from_date,
          to_date: this.to_date,
        })
        .then((response) => {
          // this.fields = ["id", "user", "line_division_id", 'from_date', "to_date", "actions"];
          this.items = response.data.data;
          this.flash("User Added to Line Division Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    editData(item) {
      this.is_edit = true;
      axios
        .get(`/api/user/linedivision/edit/${item.id}`)
        .then((response) => {
          this.lineDivisionUserId = response.data.lineDivisionUser.id;
          this.line_id = response.data.lineDivisionUser.line_id;
          this.getLineData();
          this.division_id = response.data.lineDivisionUser.line_division_id;
          // console.log(this.division_id);
          this.from_date = this.edit_date_format(response.data.lineDivisionUser.from_date);
          this.to_date = this.edit_date_format(response.data.lineDivisionUser.to_date);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    updateLineDivision() {
      axios
        .put("/api/user/linedivision/", {
          lineDivisionUserId: this.lineDivisionUserId,
          line_id: this.line_id,
          user_id: this.user_id,
          updateLineUser: this.updateLineUser,
          division_id: this.division_id,
          from_date: this.from_date,
          to_date: this.to_date,
        })
        .then((response) => {
          this.is_edit = false;
          this.items = response.data.data;
          this.flash("User Updated to Line Division Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  mounted() {
    this.initialize();
  },
  watch: {
    user_id: function (newVal, oldVal) {
      this.userId = newVal;
    },
    user: function (newVal, oldVal) {
      this.items = newVal;
    },
  },
};
</script>