<template>
  <c-sidebar-brand v-if="hasPermission" to="/dashboard">
    <a
      href="/dashboards/main"
      class="c-sidebar-brand router-link-active"
      target="_self"
    >
      <img
        v-if="logoUrl"
        style="width: 256px; height: 84px"
        class="c-sidebar-brand-full"
        :viewBox="`0 0 ${minimize ? 110 : 556} 134`"
        :src="logoUrl"
      />
    </a>
  </c-sidebar-brand>
</template>

<script>
export default {
  name: 'SidebarBrand',
  props: {
    minimize: {
      type: Boolean,
      default: false
    },
    logoUrl: {
      type: String,
      default: ''
    }
  },
  computed: {
    hasPermission() {
      return this.checkPermission('show_all_logos');
    }
  },
}
</script>
