<script>
import NavDropdown from './NavDropdown';
export default {
  name: 'NavItem',
  props: {
    item: {
      type: Object,
      required: true
    },
    searchQuery: {
      type: String,
      default: ''
    }
  },
  computed: {
    isExternalLink() {
      return this.item.to && this.item.to.indexOf('http') === 0;
    }
  },
  render(h) {
    // Check if user has permission to see this item
    if (this.item.permission && !this.checkPermission(this.item.permission)) {
      return null;
    }

    if (this.item.type === 'dropdown') {
      return h(NavDropdown, {
        props: {
          item: this.item,
          searchQuery: this.searchQuery
        }
      });
    } else {
      // Link item
      return h('c-sidebar-nav-item', {
        props: {
          name: this.item.name,
          to: this.isExternalLink ? undefined : this.item.to,
          href: this.isExternalLink ? this.item.to : undefined,
          icon: this.item.icon,
          target: this.isExternalLink ? '_blank' : undefined
        }
      });
    }
  }
};
</script>
