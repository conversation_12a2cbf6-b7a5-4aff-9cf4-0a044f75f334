<script>
import NavItem from './NavItem';
export default {
  name: 'NavDropdown',
  props: {
    item: {
      type: Object,
      required: true
    },
    searchQuery: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isOpen: false
    };
  },
  created() {
    // Auto-open dropdowns when searching
    this.$watch('searchQuery', (val) => {
      this.isOpen = !!val;
    }, { immediate: true });
  },
  render(h) {
    // Filter children based on permissions
    const children = (this.item.children || [])
      .filter(child => !child.permission || this.checkPermission(child.permission))
      .map(child => h(NavItem, {
        props: {
          item: child,
          searchQuery: this.searchQuery
        }
      }));

    // If no visible children, don't render dropdown
    if (children.length === 0) {
      return null;
    }

    return h('c-sidebar-nav-dropdown', {
      props: {
        name: this.item.name,
        route: this.item.to,
        icon: this.item.icon,
        open: this.isOpen
      },
      class: {
        'c-show': this.isOpen // Add c-show class when dropdown is open
      },
      on: {
        'update:open': (value) => {
          this.isOpen = value;
        },
        click: () => {
          // Toggle dropdown on click
          this.isOpen = !this.isOpen;
        }
      }
    }, children);
  }
};
</script>
