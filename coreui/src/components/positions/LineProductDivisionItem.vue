<template>
  <div>
    <c-col col="1">
      <c-form-group>
        <template #input>
          <input
            class="m-1"
            type="checkbox"
            v-model="selectedLine"
            :id="line.id"
            @change="selectedItems"
          /><label :for="line.id">{{ line.name }}</label>
        </template>
      </c-form-group>
    </c-col>
    <c-col>
      <c-form-group>
        <template #label> Products</template>
        <template #input>
          <v-select
            v-model="products"
            :options="line.products"
            label="name"
            :value="0"
            multiple
            :reduce="(product) => product.id"
            placeholder="Select Product"
            :class="{
              disabled: !selectedLine,
            }"
            @input="selectedItems"
          />
        </template>
      </c-form-group>
    </c-col>
    <c-col>
      <c-form-group>
        <template #label> Divisions</template>
        <template #input>
          <v-select
            v-model="divisions"
            :options="line.divisions"
            label="name"
            :value="0"
            multiple
            :reduce="(division) => division.id"
            placeholder="Select Division"
            :class="{
              disabled: !selectedLine,
            }"
            @input="selectedItems"
          />
        </template>
      </c-form-group>
    </c-col>
  </div>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["push", "pull"],
  props: {
    line: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      selectedLine: false,
      products: [],
      divisions: [],
    };
  },
  methods: {
    selectedItems() {
      if (this.selectedLine === true) {
        this.$emit("push", {
          line: this.line.id,
          products: this.products,
          divisions: this.divisions,
        });
      }
      if (this.selectedLine === false) {
        this.$emit("pull", {
          line: this.line.id,
          products: this.products,
          divisions: this.divisions,
        });
      }
    },
  },
};
</script>


<style scoped>
.disabled {
  pointer-events: none;
  color: #bfcbd9;
  cursor: not-allowed;
  background-image: none;
  background-color: #eef1f6;
  border-color: #d1dbe5;
}
</style>
