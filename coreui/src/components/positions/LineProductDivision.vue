<template>
  <c-card>
    <c-card-body>
      <line-product-division-item class="row" v-for="(line, index) in lines" :key="index" :line="line" @push="insert"
        @pull="remove"></line-product-division-item>
      <c-row>
        <c-col>
          <c-form-group>
            <template #input>
              <input class="m-1" type="checkbox" v-model="doubleChecked" id="doubleChecked" /><label
                for="doubleChecked">Double Plan Visit</label>
            </template>
          </c-form-group>
        </c-col>
      </c-row>
      <c-row>
        <c-form-group>
          <template #input>
            <input class="m-1" type="checkbox" v-model="show_below_users" 
              id="showBelowChecked" />
            <label for="showBelowChecked">Show Below Users</label>
          </template>
        </c-form-group>
      </c-row>
    </c-card-body>
    <c-card-footer><c-button color="primary" class="text-white" @click="submit">Save</c-button></c-card-footer>
  </c-card>
</template>

<script>
import LineProductDivisionItem from "./LineProductDivisionItem.vue";
export default {
  components: {
    LineProductDivisionItem,
  },
  props: {
    lines: {
      type: Array,
      required: true,
    },
    userPosition: {
      type: Number,
      required: true,
    },
    fromDate: {
      type: Date,
      required: true,
    },
  },
  data() {
    return {
      selectedLines: [],
      selectedProducts: [],
      selectedDivisions: [],
      doubleChecked: false,
      show_below_users: true,
    };
  },
  methods: {
    submit() {
      let request = {
        id: this.userPosition,
        lines: this.selectedLines,
        products: this.selectedProducts,
        divisions: this.selectedDivisions,
        from_date: this.fromDate + " 00:00:00",
        double_visit: this.doubleChecked ? 1 : 0,
        show_below_users: this.show_below_users ? 1 : 0,
      };
      axios
        .post("/api/position-settings", request)
        .then(() =>
          this.flash("user position and settings are created successfully .")
        )
        .catch((error) => this.showErrorMessage(error));
    },
    update() { },
    insert(obj) {
      this.selectedLines.find((item) => obj.line === item) === undefined
        ? this.selectedLines.push(obj.line)
        : null;
      obj.products.forEach((element) => {
        this.selectedProducts.find((item) => element === item) === undefined
          ? this.selectedProducts.push(element)
          : null;
      });
      obj.divisions.forEach((element) => {
        this.selectedDivisions.find((item) => element === item) === undefined
          ? this.selectedDivisions.push(element)
          : null;
      });
    },
    remove(obj) {
      this.selectedLines.splice(this.selectedLines.indexOf(obj.line), 1);
      obj.products.forEach((element) => {
        this.selectedProducts.splice(this.selectedProducts.indexOf(element), 1);
      });
      obj.divisions.forEach((element) => {
        this.selectedDivisions.splice(
          this.selectedDivisions.indexOf(element),
          1
        );
      });
    },
  },
};
</script>
