<template>
  <div>
    <c-col>
      <c-row>
        <c-col>
          <c-input label="Name" type="text" placeholder="name" v-model="name"></c-input>
        </c-col>
        <c-col>
          <c-input label="Target Days" type="text" placeholder="Target Days" v-model="target_days"></c-input>
        </c-col>
        <c-col>
          <c-input label="Single Target Days" type="text" placeholder="Single Target Days"
            v-model="single_target_days"></c-input>
        </c-col>
        <c-col>
          <c-input label="Double Target Days" type="text" placeholder="Double Target Days"
            v-model="target_days"></c-input>
        </c-col>
      </c-row>
      <c-row>
        <c-col>
          <c-input label="Note" type="text" placeholder="note" v-model="note"></c-input>
        </c-col>
        <c-col>
          <c-input label="Sort" type="number" placeholder="sort" v-model="sort"></c-input>
        </c-col>
      </c-row>
    </c-col>
    <c-card-footer>
      <c-button color="primary" class="text-white" @click="submit()">Save</c-button>
    </c-card-footer>
  </div>
</template>

<script>
export default {
  data() {
    return {
      name: "",
      sort: "",
      note: "",
      target_days: 0,
      single_target_days: 0,
      double_target_days: 0,
      role_id: null,
      listRoles: [],
    };
  },
  methods: {
    submit() {
      const position = {
        name: this.name,
        target_days: this.target_days,
        single_target_days: this.single_target_days,
        double_target_days: this.double_target_days,
        sort: this.sort,
        note: this.note,
        role_id: this.role_id
      };
      axios
        .post("/api/positions", position)
        .then(() => this.flash("Position Created Successfully"))
        .catch((error) => this.showErrorMessage(error));
    },
  },
};
</script>
