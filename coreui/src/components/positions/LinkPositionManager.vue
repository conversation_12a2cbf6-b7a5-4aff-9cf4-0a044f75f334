<template>
  <div>
    <c-row class="mt-2">
      <c-col>
        <c-form-group>
          <template #label> Position</template>
          <template #input>
            <v-select
              v-model="position"
              :options="positions"
              label="name"
              :value="0"
              required
              :reduce="(position) => position.id"
              placeholder="Select Position"
              class="mt-2"
            />
          </template>
        </c-form-group>
      </c-col>
      <c-col>
        <c-form-group>
          <template #label> Report To</template>
          <template #input>
            <v-select
              v-model="type"
              :options="managerTypes"
              label="name"
              :value="0"
              required
              :reduce="(type) => type"
              placeholder="Select Manager Type"
              class="mt-2"
            />
          </template>
        </c-form-group>
      </c-col>
    </c-row>
    <c-row>
      <c-col>
        <c-input
          label="From"
          type="date"
          placeholder="From"
          v-model="from"
        ></c-input>
      </c-col>
      <c-col>
        <c-input label="To" type="date" placeholder="To" v-model="to"></c-input>
      </c-col>
    </c-row>
    <c-card-footer>
      <c-button color="primary" class="text-white" @click="submit"
        >Save</c-button
      >
    </c-card-footer>
  </div>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { mapState, mapActions, mapMutations } from "vuex";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      position: null,
      type: null,
      from: null,
      to: null,
    };
  },
  computed: {
    ...mapState("positions", ["positions", "managerTypes"]),
  },
  methods: {
    ...mapActions("positions", ["loadPositions", "loadManagerTypes"]),
    // ...mapMutations("positions", ["removeManagerType", "addManagerType"]),
    initialize() {
      this.loadPositions();
      this.loadManagerTypes();
    },
    submit() {
      let positionManager = {
        managerable_id: this.type.type_id,
        managerable_type: this.type.model,
        position_id: this.position,
        from_date: this.crmDateFormat(this.from),
        to_date: this.to ? this.crmDateFormat(this.to) : null,
      };
      axios
        .post("/api/position-managers", positionManager)
        .then(() => this.flash("position Manager Created successfully"))
        .catch((error) => this.showErrorMessage(error));
    },
  },
  created() {
    this.initialize();
  },
};
</script>
