<template>
  <c-card>
    <c-card-header>
      <div class="row">
        <div class="col-md-5"><strong>Contribution</strong></div>
        <div class="col-md-2">
          <strong>{{ crmDateFormat() }}</strong>
        </div>
        <div class="col-md-5">
          <c-button color="primary" @click="store()" style="float: right"
            >Save</c-button
          >
        </div>
      </div>
    </c-card-header>
    <c-card-body>
      <div class="contribution" style="height: auto" v-if="show">
        <table id="contribution_table" class="table table-striped">
          <thead>
            <th scope="col"><label class="text-white">Division</label></th>
            <th scope="col" v-for="(product, index) in dataCols" :key="index">
              <div style="width: 100px" class="text-center text-white">
                <label>{{ product.name }}</label>
              </div>
            </th>
          </thead>
          <tbody>
            <tr v-for="(division, rowIndex) in dataRows" :key="rowIndex">
              <td>
                <label>{{ division.name }}</label>
              </td>
              <td
                style="width: 50px"
                v-for="(product, colIndex) in dataCols"
                :key="colIndex"
              >
                <c-input
                  type="number"
                  v-model="percent[rowIndex][colIndex].contribution"
                  @change="
                    setTotal(percent[rowIndex][colIndex].contribution, colIndex)
                  "
                />
              </td>
            </tr>
            <tr style="background: #2eb85c">
              <td><label class="text-white">Total</label></td>
              <td
                style="width: 50px"
                v-for="(total, colIndex) in holdData"
                :key="colIndex"
              >
                <c-input type="number" :value="total" disabled />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" class="text-white" @click="store()"
        >Save</c-button
      >
      <c-button color="default" :to="{ name: 'contribution' }">Cancel</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import moment from "moment";
import Table from "../../views/tables/Table.vue";
export default {
  components: { Table },
  props: {
    dataRows: {
      type: Array,
      required: true,
    },
    dataCols: {
      type: Array,
      required: true,
    },
    settingContribution: {
      type: Array,
      required: true,
    },
    setting: {
      type: Array,
      required: true,
    },
    line: {
      type: Number,
      required: true,
    },
    type: {
      type: Number,
      required: true,
    },
    date: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      percent: null,
      id: null,
      totals: [],
      division: null,
      product: 0,
      holdData: [],
      show: false,
    };
  },
  methods: {
    initialize() {
      this.percent = new Array(this.dataRows.length);
      this.totals = new Array(this.dataCols.length).fill(
        0,
        0,
        this.dataCols.length
      );
      if (
        this.settingContribution[0].value == "Brick" &&
        this.setting[0].value == "Product"
      ) {
        for (let j = 0; j < this.dataRows.length; j++) {
          this.percent[j] = [];
        }
        for (let i = 0; i < this.dataRows.length; i++) {
          for (let j = 0; j < this.dataCols.length; j++) {
            this.percent[i][j] = {
              id: null,
              line_id: this.line,
              type_id: this.type,
              date: this.date,
              div_id: null,
              brick_id: this.dataRows[i].id,
              product_id: this.dataCols[j].id,
              contribution: null,
            };
          }
        }
      }
      if (
        this.settingContribution[0].value == "Brick" &&
        this.setting[0].value == "Line"
      ) {
        for (let j = 0; j < this.dataRows.length; j++) {
          this.percent[j] = [];
        }
        for (let i = 0; i < this.dataRows.length; i++) {
          for (let j = 0; j < this.dataCols.length; j++) {
            this.percent[i][j] = {
              id: null,
              line_id: this.line,
              type_id: this.type,
              date: this.date,
              div_id: null,
              brick_id: this.dataRows[i].id,
              product_id: null,
              contribution: null,
            };
          }
        }
      }
      if (
        this.settingContribution[0].value == "Division" &&
        this.setting[0].value == "Product"
      ) {
        for (let j = 0; j < this.dataRows.length; j++) {
          this.percent[j] = [];
        }
        for (let i = 0; i < this.dataRows.length; i++) {
          for (let j = 0; j < this.dataCols.length; j++) {
            this.percent[i][j] = {
              id: null,
              line_id: this.line,
              type_id: this.type,
              date: this.date,
              brick_id: null,
              div_id: this.dataRows[i].id,
              product_id: this.dataCols[j].id,
              contribution: null,
            };
          }
        }
      }
      if (
        this.settingContribution[0].value == "Division" &&
        this.setting[0].value == "Line"
      ) {
        for (let j = 0; j < this.dataRows.length; j++) {
          this.percent[j] = [];
        }
        for (let i = 0; i < this.dataRows.length; i++) {
          for (let j = 0; j < this.dataCols.length; j++) {
            this.percent[i][j] = {
              id: null,
              line_id: this.line,
              type_id: this.type,
              date: this.date,
              brick_id: null,
              div_id: this.dataRows[i].id,
              product_id: null,
              contribution: null,
            };
          }
        }
      }
    },
    filterData() {
      return this.percent
        .map((item, i) => {
          return item
            .map((el) => {
              if (el.contribution == null) return null;
              return el;
            })
            .filter((item) => item != null);
        })
        .filter((item) => item.length != 0);
    },
    setTotal(value, index) {
      this.totals[index];
      this.totals[index] += +value;
      this.holdData = [];
      this.holdData = this.totals;
    },
    getContributions() {
      axios
        .post("/api/get-contribution", {
          line: this.line,
          type: this.type,
          date: this.date,
        })
        .then((response) => {
          response.data.contributions.forEach((element) => {
            for (let i = 0; i < this.dataRows.length; i++) {
              for (let j = 0; j < this.dataCols.length; j++) {
                if (
                  element.div_id != null &&
                  element.div_id === this.percent[i][j].div_id &&
                  element.product_id === this.percent[i][j].product_id
                ) {
                  this.percent[i][j].id = element.id;
                  this.percent[i][j].contribution = element.contribution;
                  this.setTotal(element.contribution, j);
                }

                if (
                  element.brick_id != null &&
                  element.brick_id === this.percent[i][j].brick_id &&
                  element.product_id === this.percent[i][j].product_id
                ) {
                  this.percent[i][j].id = element.id;
                  this.percent[i][j].contribution = element.contribution;
                  this.setTotal(element.contribution, j);
                }
              }
            }
          });

          this.show = true;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    store() {
      let percent = this.filterData();
      axios
        .post("/api/contribution", { percent })
        .then((response) => {
          this.flash("The Contribution is Created Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    crmDateFormat() {
      return moment(this.date).format("MMMM YYYY");
    },
  },
  created() {
    this.initialize();
    this.getContributions();
  },
};
</script>
<style scoped>
.contribution {
  overflow-y: auto;
  height: 600px;
}
table {
  display: table;
  white-space: nowrap;
  width: 100%;
}
th {
  opacity: 1;
  position: sticky;
  top: 0;
  background: #2eb85c;
}
.form-group {
  margin-bottom: 0;
}
label {
  font-weight: bolder;
  margin-bottom: 0;
}
</style>