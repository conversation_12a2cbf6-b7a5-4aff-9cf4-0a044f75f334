<template>
  <c-row>
    <c-col col="12" lg="12">
      <c-card no-header>
        <c-card-body>
          <template slot="header"> Coaching </template>
          <c-tabs>
            <c-tab active>
              <template slot="title">
                <c-icon name="cil-chart-pie" /> Types
              </template>
              <types-tab @removeLinkedCategories="SetlinkedCategories" />
            </c-tab>

            <c-tab>
              <template slot="title">
                <c-icon name="cil-chart-pie" /> Categories
              </template>
              <categories-tab
                :linkedCategories="linkedCategories"
              />
            </c-tab>

            <c-tab>
              <template slot="title">
                <c-icon name="cil-chart-pie" /> Questions
              </template>
              <questions-tab />
            </c-tab>

            <c-tab>
              <template slot="title">
                <c-icon name="cil-chart-pie" /> Settings
              </template>
              <settings-tab />
            </c-tab>

          </c-tabs>
        </c-card-body>
      </c-card>
    </c-col>
  </c-row>
</template>

<script>
import TypesTab from "./TypesTab.vue";
import CategoriesTab from "./CategoriesTab.vue";
import QuestionsTab from "./QuestionsTab.vue";
import SettingsTab from "./SettingsTab.vue";

export default {
  name: "Coaching",
  components: {
    TypesTab,
    CategoriesTab,
    QuestionsTab,
    SettingsTab
  },
  data() {
    return {
      linkedCategories: [],
    };
  },
  methods: {
    SetlinkedCategories(linkedCategories) {
      this.linkedCategories = linkedCategories;
    },

  },
};
</script>
