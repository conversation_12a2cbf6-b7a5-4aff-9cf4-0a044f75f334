
<template>
  <c-row>
    <c-col col="12" xl="12">
      <transition name="slide">
        <c-card>
          <c-card-header> Settings </c-card-header>
          <c-card-body>

            <c-data-table
              hover
              striped
              sorter
              tableFilter
              footer
              itemsPerPageSelect
              :items="items"
              :fields="fields"
              :items-per-page="100"
              :active-page="1"
              :responsive="true"
              pagination
              thead-top
            >
              <template slot="thead-top">
                <td style="border-top: none"><strong>Total</strong></td>
                <td style="border-top: none" class="text-xs-right">
                  {{ items.length }}
                </td>
              </template>
              <template #actions="{ item }">
                <td>
                  <div class="row justify-content-center">
                    <c-button
                      color="success"
                      class="btn-sm mt-2"
                      :to="{
                        name: 'EditCoachingSettings',
                        params: { id: item.id },
                      }"
                      ><i class="cil-pencil"></i><c-icon name="cil-pencil"
                    /></c-button>
                  </div>
                </td>
              </template>
            </c-data-table>
          </c-card-body>
        </c-card>
      </transition>
    </c-col>
  </c-row>
</template>

<script>
export default {
  data() {
    return {
      items:[],
      fields: ['name',"value","actions",],
    };
  },
  created() {
    this.initialize();
  },
  methods: {
    initialize() {
      axios.get("/api/coaching-settings")
      .then(response =>{
        this.items = response.data.settings;
      })
      .catch((error) => {
        this.showErrorMessage(error)
      })
    },
  }
};
</script>
