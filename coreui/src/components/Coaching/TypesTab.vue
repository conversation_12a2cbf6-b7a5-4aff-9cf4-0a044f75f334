<template>
  <c-row>
    <c-col col="12" xl="12">
      <transition name="slide">
        <c-card>
          <c-card-body>
            <c-button
              class="text-white"
              color="primary"
              v-if="checkPermission('create_types')"
              :to="{ name: 'CreateCoachingType' }"
              >Create Type</c-button
            >
            <c-dropdown
              placement="bottom-end"
              toggler-text="Tools"
              color="primary"
              class="float-right d-inline-block"
            >
              <c-dropdown-item
                v-if="checkPermission('download_all_templates')"
                @click="templateDownload('Type_Template.xlsx')"
                >Template</c-dropdown-item
              >

              <c-dropdown-item
                v-if="checkPermission('import_types')"
                @click="successModal = true"
                >Upload New</c-dropdown-item
              >

              <c-dropdown-item
                v-if="checkPermission('import_bulk_edit')"
                @click="updateModal = true"
                >Bulk Edit</c-dropdown-item
              >

              <c-dropdown-divider></c-dropdown-divider>

              <c-dropdown-item
                v-if="checkPermission('export_xlsx_types')"
                @click="exportType()"
                >Export to Excel</c-dropdown-item
              >
              <c-dropdown-item
                v-if="checkPermission('export_csv_types')"
                @click="exportCSV()"
                >Export to CSV</c-dropdown-item
              >
              <c-dropdown-item
                v-if="checkPermission('export_pdf_types')"
                @click="exportTypePDF()"
                >Export to PDF</c-dropdown-item
              >
              <c-dropdown-item
                v-if="checkPermission('export_email_types')"
                @click="sendModal = true"
                >Send to Mail</c-dropdown-item
              >
            </c-dropdown>

            <c-modal
              title="Upload Coaching Types"
              color="success"
              :show.sync="successModal"
            >
              <c-input-file
                type="file"
                ref="file"
                id="file"
                name="file_name"
                v-on:change="handleFileUpload"
                placeholder="New file"
              />
              <c-progress
                :value="uploadPercentage"
                color="success"
                animated
                showPercentage
                show-value
                style="height: 15px"
                class="mt-1"
                :max="100"
                v-show="progressBar"
              />
              <template #footer>
                <c-button @click="successModal = false" color="danger"
                  >Discard</c-button
                >
                <c-button
                  @click="importType()"
                  class="text-white"
                  color="success"
                  >Upload</c-button
                >
              </template>
            </c-modal>

            <!-- bulk edit - update modal -->
            <c-modal
              title="Bulk Edit Coaching Types"
              color="success"
              :show.sync="updateModal"
            >
              <c-input-file
                type="file"
                ref="file"
                id="bulkEditFile"
                name="file_name"
                v-on:change="handleFileUpload"
                placeholder="New file"
              />
              <c-progress
                :value="uploadPercentage"
                color="success"
                animated
                showPercentage
                show-value
                style="height: 15px"
                class="mt-1"
                :max="100"
                v-show="progressBar"
              />
              <template #footer>
                <c-button @click="updateModal = false" color="danger"
                  >Discard</c-button
                >
                <c-button
                  @click="importUpdateType()"
                  class="text-white"
                  color="success"
                  >Upload</c-button
                >
              </template>
            </c-modal>

            <c-modal title="Compose" color="success" :show.sync="sendModal">
              <template>
                <div class="form-group">
                  <vue-tags-input
                    v-model="email"
                    :tags="emails"
                    :validation="validation"
                    placeholder="To"
                    :add-on-key="addOnKey"
                    @tags-changed="(newEmails) => (emails = newEmails)"
                  />
                </div>
              </template>
              <c-textarea
                type="text"
                name="text"
                v-model="text"
                placeholder="Message"
              ></c-textarea>
              <template #footer>
                <c-button @click="sendModal = false" color="danger"
                  >Discard</c-button
                >
                <c-button @click="sendTypeMail()" color="success"
                  >Send</c-button
                >
              </template>
            </c-modal>
            <c-data-table
              hover
              striped
              sorter
              tableFilter
              footer
              itemsPerPageSelect
              :items="items"
              :fields="fields"
              :items-per-page="1000"
              :active-page="1"
              :responsive="true"
              pagination
              thead-top
            >
              <template #notes="{ item }">
                <td v-if="item.notes">{{ item.notes.substring(0, 15) }}...</td>
                <td v-else-if="!item.notes"></td>
              </template>
              <template slot="thead-top">
                <td style="border-top: none"><strong>Total</strong></td>
                <td style="border-top: none" class="text-xs-right">
                  {{ items.length }}
                </td>
              </template>
              <template #actions="{ item }">
                <td>
                  <div class="row justify-content-center">
                    <c-button
                      color="primary"
                      class="btn-sm mt-2 mr-1"
                      v-if="checkPermission('show_single_types')"
                      :to="{
                        name: 'ShowCoachingType',
                        params: { id: item.id },
                      }"
                      ><c-icon name="cil-magnifying-glass"
                    /></c-button>
                    <c-button
                      color="success"
                      class="btn-sm mt-2 mr-1"
                      v-if="checkPermission('edit_types')"
                      :to="{
                        name: 'EditCoachingType',
                        params: { id: item.id },
                      }"
                      ><i class="cil-pencil"></i><CIcon name="cil-pencil"
                    /></c-button>
                    <c-button
                      color="danger"
                      v-if="checkPermission('delete_types')"
                      class="btn-sm mt-2 mr-1"
                      @click="
                        $root
                          .$confirm(
                            'Delete',
                            `${
                              item.categories.length > 0
                                ? `This Type is linked to other Sub Categories, linked Categories ${printLinkedCategories(
                                    item.categories
                                  )} will be deleted as well.`
                                : ''
                            }
                            Are you sure you want to delete this record?`,
                            {
                              color: 'red',
                              width: 290,
                              zIndex: 200,
                            }
                          )
                          .then((confirmed) => {
                            if (confirmed) {
                              deleteType(item);
                              $emit('removeLinkedCategories', item.categories);
                            }
                          })
                      "
                      ><c-icon name="cil-trash"
                    /></c-button>
                  </div>
                </td>
              </template>
            </c-data-table>
          </c-card-body>
        </c-card>
      </transition>
    </c-col>
  </c-row>
</template>

<script>
import VueTagsInput from "@johmun/vue-tags-input";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";

export default {
  name: "CoachingTypes",
  components: {
    VueTagsInput,
    vSelect,
  },
  data: () => {
    return {
      uploadPercentage: 0,
      progressBar: false,
      addOnKey: [13, 32, ":", ";"],
      items: [],
      fields: ["id", "name", 'line',  "notes", "sort", "actions"],
      successModal: false,
      updateModal: false,
      sendModal: false,
      file: "",
      myResult: "",
      text: "",
      validation: [
        {
          classes: "email",
          rule: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,24}))$/,
        },
      ],
      email: "",
      emails: [],
    };
  },
  methods: {
    removeType(item) {
      const index = this.items.findIndex((type) => item.id == type.id);
      this.items.splice(index, 1);
    },
    deleteType(item) {
      axios
        .delete(`/api/coaching/types/${item.id}`)
        .then((res) => {
          this.removeType(item);
          this.flash("Coaching Type Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
    importType() {
      this.progressBar = true;
      let formData = new FormData();
      formData.append("file", this.file);
      this.importFile("/api/coaching/importtypes", formData);
      document.getElementById("file").value = "";
      this.successModal = false;
    },

    importUpdateType() {
      this.progressBar = true;
      let formData = new FormData();
      formData.append("file", this.file);
      this.importFile("/api/coaching/importupdatetypes", formData);
      document.getElementById("bulkEditFile").value = "";
      this.updateModal = false;
    },

    handleFileUpload(files, event) {
      this.file = event.target.files[0];
      this.file_name = event.target.files[0].name;
    },
    exportType() {
      this.exportFile("/api/coaching/exporttypes", "Types.xlsx");
    },
    exportCSV() {
      this.exportFile("/api/coaching/exporttypescsv", "Types.csv");
    },
    exportTypePDF() {
      this.exportFile("/api/coaching/exporttypepdf", "types.pdf");
    },
    sendTypeMail() {
      const formData = {
        emails: JSON.stringify(this.emails),
        text: this.text,
      };
      this.sendMail("/api/coaching/sendmailtypes", formData);
      this.successModal = false;
    },
    getData() {
      this.get("/api/coaching/types", "types");
    },
    printLinkedCategories(linkedCategories) {
      let text = "";
      linkedCategories.forEach(function (category) {
        text += category.name + ", ";
      });
      return text;
    },
  },
  created() {
    this.getData();
  },
};
</script>
