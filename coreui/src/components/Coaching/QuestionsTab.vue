<template>
  <c-row>
    <c-col col="12" xl="12">
      <transition name="slide">
        <c-card>
          <c-card-body>
            <c-button
              class="text-white"
              color="primary"
              v-if="checkPermission('create_questions')"
              :to="{ name: 'CreateCoachingQuestion' }"
              >Create Question</c-button
            >
            <c-dropdown
              placement="bottom-end"
              toggler-text="Tools"
              color="primary"
              class="float-right d-inline-block"
            >
              <c-dropdown-item
                v-if="checkPermission('download_all_templates')"
                @click="templateDownload('Question_Template.xlsx')"
                >Template</c-dropdown-item
              >
              <c-dropdown-item
                v-if="checkPermission('import_questions')"
                @click="successModal = true"
                >Upload New</c-dropdown-item
              >

              <c-dropdown-item
                v-if="checkPermission('import_bulk_edit')"
                @click="updateModal = true"
                >Bulk Edit</c-dropdown-item
              >

              <c-dropdown-divider />
              <c-dropdown-item
                v-if="checkPermission('export_xlsx_questions')"
                @click="exportQuestion()"
                >Export to Excel</c-dropdown-item
              >
              <c-dropdown-item
                v-if="checkPermission('export_csv_questions')"
                @click="exportCSV()"
                >Export to CSV</c-dropdown-item
              >
              <c-dropdown-item
                v-if="checkPermission('export_pdf_questions')"
                @click="exportQuestionPDF()"
                >Export to PDF</c-dropdown-item
              >
              <c-dropdown-item
                v-if="checkPermission('export_email_questions')"
                @click="sendModal = true"
                >Send to Mail</c-dropdown-item
              >
            </c-dropdown>

            <c-modal
              title="Upload Coaching Questions"
              color="success"
              :show.sync="successModal"
            >
              <c-input-file
                type="file"
                ref="file"
                id="file"
                name="file_name"
                v-on:change="handleFileUpload"
                placeholder="New file"
              />
              <c-progress
                :value="uploadPercentage"
                color="success"
                animated
                showPercentage
                show-value
                style="height: 15px"
                class="mt-1"
                :max="100"
                v-show="progressBar"
              />
              <template #footer>
                <c-button @click="successModal = false" color="danger"
                  >Discard</c-button
                >
                <c-button
                  @click="importQuestion()"
                  class="text-white"
                  color="success"
                  >Upload</c-button
                >
              </template>
            </c-modal>

            <!-- bulk edit - update modal -->
            <c-modal
              title="Bulk Edit Coaching Questions"
              color="success"
              :show.sync="updateModal"
            >
              <c-input-file
                type="file"
                ref="file"
                id="bulkEditFile"
                name="file_name"
                v-on:change="handleFileUpload"
                placeholder="New file"
              />
              <c-progress
                :value="uploadPercentage"
                color="success"
                animated
                showPercentage
                show-value
                style="height: 15px"
                class="mt-1"
                :max="100"
                v-show="progressBar"
              />
              <template #footer>
                <c-button @click="updateModal = false" color="danger"
                  >Discard</c-button
                >
                <c-button
                  @click="importUpdateQuestion()"
                  class="text-white"
                  color="success"
                  >Upload</c-button
                >
              </template>
            </c-modal>

            <c-modal title="Compose" color="success" :show.sync="sendModal">
              <template>
                <div class="form-group">
                  <vue-tags-input
                    v-model="email"
                    :tags="emails"
                    :validation="validation"
                    placeholder="To"
                    :add-on-key="addOnKey"
                    @tags-changed="newEmails => (emails = newEmails)"
                  />
                </div>
              </template>
              <c-textarea
                type="text"
                name="text"
                v-model="text"
                placeholder="Message"
              ></c-textarea>
              <template #footer>
                <c-button @click="sendModal = false" color="danger"
                  >Discard</c-button
                >
                <c-button @click="sendQuestionMail()" color="success"
                  >Send</c-button
                >
              </template>
            </c-modal>
            <c-data-table
              hover
              striped
              sorter
              tableFilter
              footer
              itemsPerPageSelect
              :items="items"
              :fields="fields"
              :items-per-page="1000"
              :active-page="1"
              :responsive="true"
              pagination
              thead-top
            >
              <template slot="thead-top">
                <td style="border-top: none"><strong>Total</strong></td>
                <td style="border-top: none" class="text-xs-right">
                  {{ items.length }}
                </td>
              </template>

              <template #categories="{ item }">
                <td v-if="item.categories">
                  <li
                    style="list-style-type: none"
                    v-for="category in item.categories"
                    :key="category.id"
                  >
                    {{ category.name }}
                  </li>
                </td>
                <td v-else-if="!item.categories"></td>
              </template>

              <template #actions="{ item }">
                <td>
                  <div class="row justify-content-center">
                    <c-button
                      color="primary"
                      class="btn-sm mt-2 mr-1"
                      v-if="checkPermission('show_single_questions')"
                      :to="{
                        name: 'ShowCoachingQuestion',
                        params: { id: item.id }
                      }"
                      ><c-icon name="cil-magnifying-glass"
                    /></c-button>
                    <c-button
                      color="success"
                      class="btn-sm mt-2 mr-1"
                      v-if="checkPermission('edit_questions')"
                      :to="{
                        name: 'EditCoachingQuestion',
                        params: { id: item.id }
                      }"
                      ><i class="cil-pencil"></i><CIcon name="cil-pencil"
                    /></c-button>
                    <c-button
                      color="danger"
                      v-if="checkPermission('delete_questions')"
                      class="btn-sm mt-2 mr-1"
                      @click="
                        $root
                          .$confirm(
                            'Delete',
                            'Do you want to delete this record?',
                            {
                              color: 'red',
                              width: 290,
                              zIndex: 200
                            }
                          )
                          .then(confirmed => {
                            if (confirmed) {
                              deleteQuestion(item);
                            }
                          })
                      "
                      ><c-icon name="cil-trash"
                    /></c-button>
                  </div>
                </td>
              </template>
            </c-data-table>
          </c-card-body>
        </c-card>
      </transition>
    </c-col>
  </c-row>
</template>

<script>
import VueTagsInput from "@johmun/vue-tags-input";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";

export default {
  name: "QuestionsTab",
  // props: ["linkedQuestions"],
  components: {
    VueTagsInput,
    vSelect
  },
  data: () => {
    return {
      uploadPercentage: 0,
      progressBar: false,
      addOnKey: [13, 32, ":", ";"],
      items: [],
      fields: ["id", "name", "notes", "sort", "categories", "actions"],
      successModal: false,
      updateModal: false,
      sendModal: false,
      file: "",
      myResult: "",
      text: "",
      validation: [
        {
          classes: "email",
          rule: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,24}))$/
        }
      ],
      email: "",
      emails: []
    };
  },
  methods: {
    removeQuestion(item) {
      const index = this.items.findIndex(question => item.id == question.id);
      this.items.splice(index, 1);
    },
    deleteQuestion(item) {
      axios
        .delete(`/api/coaching/questions/${item.id}`)
        .then(res => {
          this.removeQuestion(item);
          this.flash("Coaching Question Deleted Successfully");
        })
        .catch(err => this.flash("ooops something Error"));
    },
    importQuestion() {
      this.progressBar = true;
      let formData = new FormData();
      formData.append("file", this.file);
      this.importFile("/api/coaching/importquestions", formData);
      document.getElementById("file").value = "";
      this.successModal = false;
    },

    importUpdateQuestion() {
      this.progressBar = true;
      let formData = new FormData();
      formData.append("file", this.file);
      this.importFile("/api/coaching/importupdatequestions", formData);
      document.getElementById("bulkEditFile").value = "";
      this.updateModal = false;
    },

    handleFileUpload(files, event) {
      this.file = event.target.files[0];
      this.file_name = event.target.files[0].name;
    },
    exportQuestion() {
      this.exportFile("/api/coaching/exportquestions", "Questions.xlsx");
    },
    exportCSV() {
      this.exportFile("/api/coaching/exportquestionscsv", "Questions.csv");
    },
    exportQuestionPDF() {
      this.exportFile("/api/coaching/exportquestionpdf", "questions.pdf");
    },
    sendQuestionMail() {
      const formData = {
        emails: JSON.stringify(this.emails),
        text: this.text
      };
      this.sendMail("/api/coaching/sendmailquestions", formData);
      this.successModal = false;
    },
    getData() {
      axios
        .get("/api/coaching/questions")
        .then(response => {
          this.items = Object.values(response.data.questions);
        })
        .catch(error => {
          this.showErrorMessage(error);
        });
    }
  },
  watch: {
    // linkedQuestions() {
    //   if (this.linkedQuestions.length !== 0) {
    //     for (let index = 0; index < this.linkedQuestions.length; index++) {
    //       this.removeQuestion(this.linkedQuestions[index]);
    //     }
    //   }
    // },
  },
  created() {
    this.getData();
  }
};
</script>
