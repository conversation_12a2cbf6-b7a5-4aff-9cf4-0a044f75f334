<script>
import LabelDropdown from "./LabelDropdown.vue";

export default {
  name: "RichText",
  components: {LabelDropdown},
  props: {
    uploadFile: {
      type: Function,
      required: true
    },
    updateBody: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      lastSelection: null,
      currentAlignment: 'left',
    }
  },
  methods: {
    saveSelection() {
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        this.lastSelection = selection.getRangeAt(0);
      }
    },

    restoreSelection() {
      if (this.lastSelection) {
        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(this.lastSelection);
      }
    },

    toggleFormat(tag) {
      this.restoreSelection();
      const selection = window.getSelection();
      if (!selection.rangeCount) return;

      const range = selection.getRangeAt(0);
      const ancestor = range.commonAncestorContainer;
      const existingTag = this.findClosestTag(ancestor, tag);

      if (existingTag) {
        // Remove formatting
        const parent = existingTag.parentNode;
        while (existingTag.firstChild) {
          parent.insertBefore(existingTag.firstChild, existingTag);
        }
        parent.removeChild(existingTag);
      } else {
        // Apply formatting
        const newElement = document.createElement(tag);
        range.surroundContents(newElement);
      }

      this.$refs.editor.focus();
      this.updateContent();
    },

    findClosestTag(node, tagName) {
      while (node && node !== this.$refs.editor) {
        if (node.nodeType === 1 && node.tagName.toLowerCase() === tagName) {
          return node;
        }
        node = node.parentNode;
      }
      return null;
    },

    applyAlignment(alignment) {
      this.restoreSelection();
      const selection = window.getSelection();
      if (!selection.rangeCount) return;

      const range = selection.getRangeAt(0);
      let container = range.commonAncestorContainer;

      // Find the block-level element
      while (container && container.nodeType !== 1) {
        container = container.parentNode;
      }

      if (container && container !== this.$refs.editor) {
        container.style.textAlign = alignment;
        this.currentAlignment = alignment;
      }

      this.$refs.editor.focus();
      this.updateContent();
    },

    toggleList(type) {
      this.restoreSelection();
      const selection = window.getSelection();
      if (!selection.rangeCount) return;

      const range = selection.getRangeAt(0);
      let container = range.commonAncestorContainer;

      // Find existing list
      const existingList = this.findClosestTag(container, type);

      if (existingList) {
        // Remove list
        while (existingList.firstChild) {
          const p = document.createElement('p');
          p.innerHTML = existingList.firstChild.innerHTML;
          existingList.parentNode.insertBefore(p, existingList);
          existingList.removeChild(existingList.firstChild);
        }
        existingList.parentNode.removeChild(existingList);
      } else {
        // Create new list
        const list = document.createElement(type);
        const item = document.createElement('li');
        range.surroundContents(item);
        item.parentNode.replaceChild(list, item);
        list.appendChild(item);
      }

      this.$refs.editor.focus();
      this.updateContent();
    },

    applyIndent(increase) {
      this.restoreSelection();
      const selection = window.getSelection();
      if (!selection.rangeCount) return;

      const range = selection.getRangeAt(0);
      let container = range.commonAncestorContainer;

      while (container && container.nodeType !== 1) {
        container = container.parentNode;
      }

      if (container && container !== this.$refs.editor) {
        const currentIndent = parseInt(container.style.marginLeft || '0');
        container.style.marginLeft = `${currentIndent + (increase ? 20 : -20)}px`;
      }

      this.$refs.editor.focus();
      this.updateContent();
    },

    clearFormatting() {
      const editor = this.$refs.editor;
      const text = editor.innerText;
      editor.innerHTML = text;
      this.updateContent();
      this.currentAlignment = 'left';
    },

    updateContent() {
      this.updateBody(this.$refs.editor.innerHTML);
    },

    handleKeyDown(e) {
      // Handle special key combinations if needed
      if (e.key === 'Tab') {
        e.preventDefault();
        this.applyIndent(!e.shiftKey);
      }
    },

    isFormatActive(tag) {
      const selection = window.getSelection();
      if (!selection.rangeCount) return false;

      const range = selection.getRangeAt(0);
      const ancestor = range.commonAncestorContainer;
      return !!this.findClosestTag(ancestor, tag);
    },

    isListActive(type) {
      const selection = window.getSelection();
      if (!selection.rangeCount) return false;

      const range = selection.getRangeAt(0);
      const ancestor = range.commonAncestorContainer;
      return !!this.findClosestTag(ancestor, type);
    },
  }
}
</script>

<template>

  <c-col sm="11" class="ml-auto">
    <div class="toolbar" role="toolbar">
      <div class="btn-group mr-1">
        <c-button
          color="light"
          @click="toggleFormat('strong')"
          :class="{ active: isFormatActive('strong') }"
        >
          <c-icon name="cil-bold"/>
        </c-button>
        <c-button
          color="light"
          @click="toggleFormat('em')"
          :class="{ active: isFormatActive('em') }">
          <c-icon name="cil-italic"/>
        </c-button>
        <c-button
          color="light"
          @click="toggleFormat('u')"
          :class="{ active: isFormatActive('u') }"
        >
          <c-icon name="cil-underline"/>
        </c-button>
      </div>
      <div class="btn-group mr-1">
        <c-button
          color="light"
          @click="applyAlignment('left')"
          :class="{ active: currentAlignment === 'left' }"
        >
          <c-icon name="cil-align-left"
          />
        </c-button>
        <c-button
          color="light"
          @click="applyAlignment('right')"
          :class="{ active: currentAlignment === 'right' }"
        >
          <c-icon name="cil-align-right"
          />
        </c-button>
        <c-button
          color="light"
          @click="applyAlignment('center')"
          :class="{ active: currentAlignment === 'center' }"
        >
          <c-icon name="cil-align-center"
          />
        </c-button>
        <c-button
          color="light"
          @click="applyAlignment('justify')"
          :class="{ active: currentAlignment === 'justify' }"
        >
          <c-icon name="cil-justify-center"
          />
        </c-button>
      </div>
      <div class="btn-group mr-1">
        <c-button
          color="light"
          @click="applyIndent(true)"
        >
          <c-icon name="cil-indent-increase"
          />
        </c-button>
        <c-button
          color="light"
          @click="applyIndent(false)"
        >
          <c-icon name="cil-indent-decrease"
          />
        </c-button>
      </div>
      <div class="btn-group mr-1">
        <c-button
          color="light"
          @click="toggleList('ul')"
          :class="{ active: isListActive('ul') }">
          <c-icon name="cil-list"/>
        </c-button>
        <c-button
          color="light"
          @click="toggleList('ol')"
          :class="{ active: isListActive('ol') }"
        >
          <c-icon name="cil-list-numbered"
          />
        </c-button>
      </div>
      <c-button
        color="light"
        class="mr-1"
        @click="clearFormatting"
      >
        <c-icon name="cil-trash"
        />
      </c-button>
      <c-button color="light" @click="$refs.fileInput.click()">
        <c-icon name="cil-paperclip"/>
      </c-button>
      <input
        ref="fileInput"
        class="d-none"
        type="file"
        @change="uploadFile"
      />
      <LabelDropdown/>
    </div>
    <div class="form-group mt-4">
      <div
        ref="editor"
        class="form-control editor-content"
        contenteditable="true"
        @input="updateContent"
        @keydown="handleKeyDown"
        @mouseup="saveSelection"
        @keyup="saveSelection"
      ></div>
      <slot></slot>
    </div>
  </c-col>

</template>

<style scoped>
.editor-content {
  min-height: 200px;
  height: auto;
  overflow-y: auto;
  padding: 1rem;
  line-height: 1.5;
}

.toolbar .btn-group .c-button.active {
  background-color: #e6e6e6;
  border-color: #adadad;
}

.editor-content:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
</style>
