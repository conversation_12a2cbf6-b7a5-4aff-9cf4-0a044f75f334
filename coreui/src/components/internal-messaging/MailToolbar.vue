<template>
  <div class="toolbar p-3">
    <div class="btn-group mr-1">
      <!-- <c-button color="light" @click="getAllMessage">
        <c-icon name="cil-envelope-closed" />
      </c-button> -->
      <!-- <c-button color="light"
      @click="favoriteMessage()">
        <c-icon name="cil-star" />
      </c-button> -->
      <c-button color="light"
      @click="importantMessage()">
        <c-icon name="cil-bookmark" />
      </c-button>
    </div>

    <!-- <div class="btn-group mr-1">
      <c-button color="light">
        <c-icon name="cil-share" />
      </c-button>
      <c-button color="light">
        <c-icon name="cil-share-all" />
      </c-button>
      <c-button color="light">
        <c-icon name="cil-share-boxed" />
      </c-button>
    </div> -->

    <c-button color="light"
    @click="
    $root
      .$confirm(
        'Delete',
        'Do you want to delete this Message?',
        {
          color: 'red',
          width: 290,
          zIndex: 200,
        }
      )
      .then((confirmed) => {
        if (confirmed) {
          this.deleteMessage();
        }
      })
  ">
      <c-icon name="cil-trash" />

    </c-button>

    <LabelDropdown />

    <div class="btn-group float-right">
      <!-- <CLink  -->
        <c-button
          @click="previousItem()"
          color="light"
        >
<!--          :disable="isPreviousDisabled"-->
<!--          :class="{ disabled: isPreviousDisabled }"-->
          <c-icon name="cil-chevron-left" />
        </c-button>
      <!-- </CLink> -->
      <c-button
        color="light"
        @click="nextItem()"
      >
<!--        :class="{ disabled: isNextDisabled }"-->
<!--        :disable="isNextDisabled"-->
        <c-icon name="cil-chevron-right" />
      </c-button>
    </div>
  </div>
</template>

<script>
import LabelDropdown from "./LabelDropdown";
export default {
  name: "MailToolbar",
  components: {
    LabelDropdown,
  },
  emits:['next','previous'],
  props:{
    messageId:{
      type: Number,
      required:true
    },
    isPreviousDisabled:{
      type: Boolean,
      required:true
    },
    isNextDisabled:{
      type: Boolean,
      required:true
    }
  },
  methods:{
    nextItem(){
      this.$emit('next')
    },
    previousItem(){
      this.$emit('previous')
    },
    deleteMessage() {
      axios
        .put(`/api/update-message/${this.messageId}`,{
          trashed:true
        })
        .then(() => {
          this.flash("Message Deleted Successfully");
        })
        .catch((err) =>
          this.showErrorMessage(err)
        );
    },
    favoriteMessage() {
      axios
        .put(`/api/update-message/${this.messageId}`,{
          favorite:true
        })
        .then(() => {
          this.flash("Message Added to favorites Successfully");
        })
        .catch((err) =>
          this.showErrorMessage(err)
        );
    },
    importantMessage() {
      axios
        .put(`/api/update-message/${this.messageId}`,{
          important:true
        })
        .then(() => {
          this.flash("Message Added to booked mark Successfully");
        })
        .catch((err) =>
          this.showErrorMessage(err)
        );
    },
  },
};
</script>
