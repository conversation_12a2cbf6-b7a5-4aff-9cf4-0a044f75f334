<template>
  <div class="w-100 border shadow-sm mt-3 p-2">
    <div class="row">
      <div class="col-2 c-message-actions">
<!--         <CIcon name="cil-star" class="stretched-link pe-auto" style="color: yellow" /> -->
        <!--      <input type="checkbox" class="d-inline m-1 mt-2" @change="changed"  v-model="selected" :select="select" :value="message.id" style="transform:scale(1.3);" />-->
        <!-- <button class="m-1" @click="importantMessage(message);" v-if="icondition()"><CIcon  name="cil-bookmark" style="color: black"  /></button> -->
        <!-- <button class="m-1" @click="deleteMessage(message);" v-if="dcondition()"><CIcon  name="cil-trash" style="color: red" /></button> -->
      </div>
      <c-link
        :to="{ name: 'Show_Message', params: { id: message.id } }"
        class="c-message col-10 text-decoration-none"
        :class="{ 'c-message-read': !!message.read }"
      >

        <div class="c-message-details row p-3">
          <div class="c-message-headers">
            <div class="c-message-headers-from  font-weight-bold col-2 p-0">{{ message.sender.name }}</div>
            <div class="c-message-headers-subjects mr-1 "> {{ message.subject.substring(0, 10) + ".." }} -</div>
            <div class="c-message-body " v-html="message.body.substring(0,30)+'..'"></div>
            <div class="c-message-headers-dates f-right text-secondary">
              {{ new Date(Date.parse(message.created_at)).toLocaleString() }}
            </div>
          </div>
        </div>
      </c-link>
    </div>
  </div>
</template>

<script>
export default {
  name: "SentMessage",
  props: {
    message: {
      type: Object,
      required: true,
    },
  },
  methods: {
    removePromotionalMaterial(message) {
      const index = this.messages.findIndex(type => message.id === type.id)
      this.messages.splice(index, 1)
    },
    deleteMessage(message) {
      axios
        .put(`/api/update-message/${message.id}`, {
          trashed: true
        })
        .then(() => {
          this.flash("Message Deleted Successfully");
        })
        .catch((err) =>
          this.showErrorMessage(err)
        );
      this.removePromotionalMaterial(message)
    },
    favoriteMessage() {
      axios
        .put(`/api/update-message/${this.$route.params.id}`, {
          favorite: true
        })
        .then(() => {
          this.flash("Message Added to favorites Successfully");
        })
        .catch((err) =>
          this.showErrorMessage(err)
        );
    },
    importantMessage(message) {
      axios
        .put(`/api/update-message/${message.id}`, {
          important: true
        })
        .then(() => {
          this.flash("Message Added to Importants  Successfully");
        })
        .catch((err) =>
          this.showErrorMessage(err)
        );
    },
  }
};
</script>

<style scoped>
.shadow-sm:hover {
  box-shadow: 0 0.5rem 1rem rgb(0 0 21 / 15%) !important;
}

.c-message-headers-dates {
  right: 10px;
  /* float: right; */
  position: absolute;
}



.c-message-bodys {
  color: #888;
}
</style>
