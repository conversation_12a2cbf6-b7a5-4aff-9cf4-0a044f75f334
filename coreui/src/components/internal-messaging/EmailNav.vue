<template>
  <CSidebar :fixed="false" color-scheme="light">
    <CButton class="d-flex m-3" :to="{ name: 'New_Message' }" color="success">
      <CIcon name="cil-inbox" class="c-sidebar-nav-icon" />New Email
    </CButton>
    <CSidebarNav>
      <CSidebarNavItem
        :to="{ name: 'All_Messages' }"
        icon="cil-inbox"
        name="Inbox"
        :badge="{ text: getInboxCount, color: 'danger' }"
      />
      <CSidebarNavItem
        :to="{ name: 'Sent' }"
        :badge="{ text: getSentMessagesCount, color: 'info' }"
        icon="cil-share"
        name="Sent"
      />
      <CSidebarNavItem
        :to="{ name: 'Trash' }"
        :badge="{ text: getTrashMessagesCount, color: 'info' }"
        icon="cil-trash"
        name="Trash"
      />
      <CSidebarNavItem
        :to="{ name: 'Important' }"
        icon="cil-bookmark"
        name="Important"
        :badge="{ text: getImportantMessagesCount, color: 'info' }"
      />
    </CSidebarNav>
  </CSidebar>
</template>

<script>
import { mapState, mapMutations, mapActions, mapGetters } from "vuex";
export default {
  name: "EmailNav",
  computed: {
    ...mapState("internalMessages", ["sentMessages"]),
    ...mapMutations("internalMessages", ["setSentMessages"]),
    ...mapGetters("internalMessages", [
      "getInboxCount",
      "getImportantMessagesCount",
      "getTrashMessagesCount",
      "getStaredMessagesCount",
      "getSentMessagesCount",
    ]),
  },
  methods: {
    ...mapActions("internalMessages", ["loadSentMessages"]),
  },
  created() {
    this.loadSentMessages();
  },
};
</script>
