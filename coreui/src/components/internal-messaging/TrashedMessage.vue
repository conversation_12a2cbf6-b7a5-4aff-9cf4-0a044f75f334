<template>
  <CLink
    :to="{ name: 'Show_Message', params: { id: message.id } }"
    class="c-message"
    :class="{ 'c-message-read': !!message.read }"
    v-if="condition()"
  >
    <div class="c-message-actions">
      <CIcon name="cil-star" style="color: yellow" />
    </div>
    <div class="c-message-details">
      <div class="c-message-headers">
        <div class="c-message-headers-from">{{ message.sender.name }}</div>
        <div class="c-message-headers-date">{{ message.created_at }}</div>
        <div class="c-message-headers-subject">{{ message.subject }}</div>
      </div>
      <div class="c-message-body">
        {{ message.body }}
      </div>
    </div>
  </CLink>
</template>

<script>
export default {
  name: "InboxMessage",
  props: {
    message: {
      type: Object,
      required: true,
    },
    condition: {
      type: Function,
      default: () => true,
    },
  },
};
</script>
