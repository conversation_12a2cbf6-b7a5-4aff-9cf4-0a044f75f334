<template>
  <div class="w-100 border shadow-sm mt-3 p-2">
    <div class="row">
      <div class="col-2 c-message-actions">
        <button class="m-1" @click="importantMessage(message);" v-if="pageName==='important' && message.important && !message.trashed">
          <CIcon name="cil-bookmark" style="color: black"/>
        </button>
        <button class="m-1" @click="deleteMessage(message);" v-if="pageName==='inbox'&& !!message.trashed">
          <CIcon name="cil-trash" style="color: red"/>
        </button>
        <button class="m-1" @click="undoMessage(message);" v-if="pageName==='trashed' && message.trashed">
          <CIcon name="cil-action-undo" style="color: black"/>
        </button>
      </div>
      <c-link
        :to="{ name: 'Show_Message', params: { id: message.id } }"
        class="c-message col-10 text-decoration-none"
        :class="{ 'c-message-read': !!message.read }"
      >

        <div class="c-message-details row p-3">
          <div class="c-message-headers">
            <div class="c-message-headers-froms  font-weight-bold col-2 p-0">{{ message.sender.name }}</div>
            <div class="c-message-headers-subjects mr-1 "> {{
                message.subject.substring(0, 20) + message.subject.length < 20 ? ".." : "  "
              }} -</div>
            <div class="c-message-bodys " v-html="message.body.substring(0, 10) + '..'"> </div>
            <div class="c-message-headers-dates f-right text-secondary">
              {{ new Date(Date.parse(message.created_at)).toLocaleString() }}
            </div>
          </div>
        </div>
      </c-link>
    </div>
  </div>
</template>

<script>
import {mapActions} from "vuex";

export default {
  name: "InboxMessage",
  props: {
    message: {
      type: Object,
      required: true,
    },
    pageName: {
      type: String,
      default: 'inbox'
    }
  },
  methods: {
    ...mapActions("internalMessages", ["removeMessage"]),
    deleteMessage(message) {
      axios
        .put(`/api/update-message/${message.id}`, {
          trashed: true
        })
        .then(() => {
          this.flash("Message Deleted Successfully");
          this.removeMessage(message)
        })
        .catch(
          this.showErrorMessage
        );
    },
    favoriteMessage() {
      axios
        .put(`/api/update-message/${this.$route.params.id}`, {
          favorite: true
        })
        .then(() => {
          this.flash("Message Added to favorites Successfully");
        })
        .catch((err) =>
          this.showErrorMessage(err)
        );
    },
    importantMessage(message) {
      axios
        .put(`/api/update-message/${message.id}`, {
          important: true
        })
        .then(() => {
          this.flash("Message Added to Importants  Successfully");
        })
        .catch((err) =>
          this.showErrorMessage(err)
        );
    },
    undoMessage(message) {
      axios
        .put(`/api/undo-message/${message.id}`)
        .then(() => {
          this.flash("Message Restored to Inbox  Successfully");
        })
        .catch((err) =>
            this.showErrorMessage(err)
          // TODO: REMOVE IT FROM STORE
        );
    },
  },
  mounted() {
  }
};
</script>

<style scoped>
.shadow-sm:hover {
  box-shadow: 0 0.5rem 1rem rgb(0 0 21 / 15%) !important;
}

.c-message-headers-dates {
  right: 18px;
  /* float: right; */
  position: absolute;
}

.c-message-headers-subjects {
  color: #000
}

.c-message-bodys {
  color: #888;
}
</style>
