<template>
  <c-card>
    <c-card-header>Create Double Visit Feedback</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Line </template>
            <template #input>
              <v-select
                v-model="line_id"
                :options="lines"
                label="name"
                :value="0"
                :reduce="(line) => line.id"
                placeholder="Select Line"
                class="mt-2"
                @input="getLineData"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Employee </template>
            <template #input>
              <input
                label="All"
                v-if="users.length != 0"
                id="user"
                class="m-1"
                type="checkbox"
                v-model="checkAllUsers"
                title="Check All Users"
                @change="checkAllEmployees"
              />
              <label
                for="user"
                v-if="users.length != 0"
                style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="user_ids"
                :options="users"
                label="fullname"
                :value="0"
                :reduce="(user) => user.id"
                placeholder="Select Employees"
                class="mt-2"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-input
            label="From Date"
            type="date"
            placeholder="From Date"
            v-model="from_date"
          ></c-input>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-input
            label="to Date"
            type="date"
            placeholder="To Date"
            v-model="to_date"
          ></c-input>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button
        v-if="line_id != null"
        color="primary"
        class="text-white"
        @click="show"
        style="float: right"
        >Show</c-button
      >
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      user_ids: [],
      line_id: null,
      users: [],
      lines: [],
      checkAllUsers: false,
      from_date: moment().startOf('month').format("YYYY-MM-DD"),
      to_date: moment().endOf('month').format("YYYY-MM-DD"),
    };
  },
  methods: {
    initialize() {
      axios
        .get("/api/actual-double-feedback/lines/")
        .then((response) => {
          this.lines = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .get(`/api/actual-double-feedback/lines/${this.line_id}`)
        .then((response) => {
          this.users = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_ids = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_ids = null;
    },
    show() {
      let visitFilter = {
        line: this.line_id,
        users: this.user_ids,
        from_date: this.from_date,
        to_date: this.to_date,
      };
      this.$emit("getSchedule", { visitFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>