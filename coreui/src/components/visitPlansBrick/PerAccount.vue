<template>
  <div class="custom-container">
    <div v-for="(k, index) in days" :key="index">
      <c-card>
        <c-card-header align="center">{{ dayFromDate(index) }}</c-card-header>
        <c-card-body>
          <div>
            <v-select
              v-model="brick[index]"
              :options="bricks"
              label="name"
              :value="0"
              required
              :reduce="(brick) => brick"
              placeholder="Select Brick"
              class="w-100 mt-2"
            />
            <div class="d-flex mt-2 pt-3 pl-3" v-if="brick[index]">
              <input
                class="mt-1 mr-2"
                :id="`${dayFromDate(index)}_all`"
                type="checkbox"
                v-model="checkAll[dayFromDate(index)]"
                @change="selectAll(brick[index].accounts, dayFromDate(index))"
              />
              <label :for="`${dayFromDate(index)}_all`">All</label>
            </div>
          </div>
          <div
            v-if="brick[index]"
            class="mt-2 p-3 overflow-auto"
            style="min-height: 10rem; max-height: 15rem"
          >
            <div v-for="(account, key) in brick[index].accounts" :key="key">
              <div class="d-flex">
                <input
                  class="mt-1 mr-2"
                  :id="`${dayFromDate(index)}_${key}`"
                  type="checkbox"
                  v-model="selectedPlans"
                  :value="newPlan(account, dayFromDate(index))"
                />
                <label :for="`${dayFromDate(index)}_${key}`">{{
                  account.name
                }}</label>
              </div>
            </div>
          </div>
          <v-select
            v-model="otherPlans[index]"
            :options="accounts"
            label="name"
            :value="0"
            required
            :reduce="
              (account) => ({
                line_id: plan.line,
                div_id: plan.division,
                acc: account.id,
                doc: null,
                shift_id: plan.shift,
                date: dayFromDate(index),
                visit_type: plan.visitType,
              })
            "
            multiple
            placeholder="Select Brick"
            class="w-100 mt-2"
          />
        </c-card-body>
      </c-card>
    </div>
  </div>
</template>

<script>
import DateWrapper from "./DateWrapper";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { mapState, mapMutations } from "vuex";
export default DateWrapper.extend({
  components: {
    vSelect,
  },
  data() {
    return {
      bricks: [],
      brick: {},
      selectedPlans: [],
      checkAll: {},
      accounts: [],
      otherPlans: {},
    };
  },
  watch: {
    selectedPlans: {
      handler: "detectAllPlans",
      deep: true,
    },
    otherPlans: {
      handler: "detectAllPlans",
      deep: true,
    },
  },
  computed: {
    ...mapState("visits", ["plan"]),
  },
  methods: {
    ...mapMutations("visits", ["pushToPlans", "setPlans"]),
    newPlan(account, date) {
      return {
        line_id: this.plan.line,
        div_id: this.plan.division,
        acc: account.id,
        doc: null,
        shift_id: this.plan.shift,
        date,
        visit_type: this.plan.visitType,
      };
    },
    detectAllPlans() {
      this.setPlans([]);

      this.selectedPlans.forEach((plan) => {
        this.pushToPlans(plan);
      });

      Object.keys(this.otherPlans).forEach((key) => {
        this.otherPlans[key].forEach((plan) => {
          this.pushToPlans(plan);
        });
      });
    },
    async initialize() {
      try {
        let res = await axios.post(
          `/api/lines/${this.plan.line}/divisions/${this.plan.division}/bricks`,
          { accounts: true }
        );
        this.bricks = res.data.data;
        this.updateAccounts();
      } catch (error) {
        this.showErrorMessage(error);
      }
    },
    updateAccounts() {
      this.bricks.forEach((brick) => {
        brick.accounts.forEach((account) => {
          this.accounts.push(account);
        });
      });
    },
    selectAll(accounts, date) {
      let checked = this.checkAll[date];
      if (!checked) {
        accounts.forEach(() => {
          let index = this.selectedPlans.findIndex((plan) => plan.date == date);
          if (~index) this.selectedPlans.splice(index, 1);
        });
        return;
      }
      accounts.forEach((account) => {
        let plan = this.newPlan(account, date);

        if (
          ~this.selectedPlans.findIndex(
            (item) => item.date == plan.date && item.acc == plan.acc
          )
        ) {
          return;
        }
        this.selectedPlans.push(plan);
      });
    },
  },
  created() {
    this.initialize();
  },
});
</script>
