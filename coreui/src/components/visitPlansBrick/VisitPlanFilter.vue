<template>
  <c-card no-header v-if="plan.minDate">
    <c-card-body>
      <c-row>
        <c-col>
          <c-form-group>
            <template #label>Line</template>
            <template #input>
              <v-select
                v-model="plan.line"
                :options="lines"
                label="name"
                :value="0"
                required
                :reduce="(line) => line.id"
                placeholder="Select Line"
                class="mt-2"
              />
            </template>
          </c-form-group>
        </c-col>
        <c-col>
          <c-form-group>
            <template #label>Division</template>
            <template #input>
              <v-select
                v-model="plan.division"
                :options="filteredDivisions"
                label="name"
                :value="0"
                required
                :reduce="(division) => division.id"
                placeholder="Select Division"
                class="mt-2"
                :disabled="!plan.line"
              />
            </template>
          </c-form-group>
        </c-col>
      </c-row>
      <c-row>
        <c-col>
          <c-form-group>
            <template #label>From Date</template>
            <template #input>
              <c-input
                type="date"
                placeholder="From Date"
                v-model="plan.fromDate"
                :min="plan.minDate"
              />
            </template>
          </c-form-group>
        </c-col>
        <c-col>
          <c-form-group>
            <template #label>To Date</template>
            <template #input>
              <c-input
                type="date"
                placeholder="From Date"
                v-model="plan.toDate"
                :min="plan.fromDate || plan.minDate"
              />
            </template>
          </c-form-group>
        </c-col>
      </c-row>
      <c-row>
        <c-col col="6">
          <c-form-group>
            <template #label>Shift</template>
            <template #input>
              <v-select
                v-model="plan.shift"
                :options="shifts"
                label="name"
                :value="0"
                required
                :reduce="(shift) => shift.id"
                placeholder="Select Shift"
              />
            </template>
          </c-form-group>
        </c-col>
      </c-row>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" :disabled="!planExists" @click="submit"
        >Show</c-button
      >
    </c-card-footer>
  </c-card>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import DateWrapper from "./DateWrapper";
import { mapState, mapMutations, mapGetters } from "vuex";
export default DateWrapper.extend({
  components: {
    vSelect,
  },
  emits: ["submitted"],
  data() {
    return {
      lines: [],
      divisions: [],
      shifts: [],
    };
  },
  computed: {
    ...mapState("visits", ["plan"]),
    ...mapGetters("visits", ["planExists"]),
    filteredDivisions() {
      return this.divisions.filter(
        (division) => division.line_id == this.plan.line
      );
    },
  },
  methods: {
    ...mapMutations("visits", ["setPlan"]),
    async initialize() {
      try {
        let res = await axios.get(`/api/users/${null}/lines`);

        this.lines = res.data.data.lines;
        this.divisions = res.data.data.divisions;
        this.shifts = res.data.data.shifts;
      } catch (err) {
        this.showErrorMessage(err);
      }
    },
    submit() {
      this.$emit("submitted");
    },
  },
  created() {
    this.initialize();
  },
});
</script>
