<template>
  <v-dialog v-model="dialog" :max-width="options.width" :style="{ zIndex: options.zIndex }" @keydown.esc="cancel">
    <v-card>
      <v-toolbar dark :color="options.color" dense flat>
        <v-toolbar-title class="white--text">{{ title }}</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="cancel">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <gmap-map v-if="positions.length > 0" class="mt-3" :center="center" :zoom="18" style="width: 100%; height: 340px">
        <gmap-cluster>
          <gmap-info-window :options="infoOptions" :position="infoWindowPos" :opened="infoWinOpen"
            @closeclick="infoWinOpen = false">
            <div v-html="infoOptions.content"></div>
          </gmap-info-window>
          <gmap-marker v-for="(position, key) in positions" :key="key" :position="position" :clickable="true"
            @click="toggleInfoWindow(position, key)" />
        </gmap-cluster>
      </gmap-map>
      <v-card-text v-else class="pa-4">{{ message }}</v-card-text>
      <v-card-actions class="pt-0">
        <v-spacer></v-spacer>
        <v-btn color="grey" text @click.native="cancel">Cancel</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import { mapActions } from "vuex";
export default {
  data() {
    return {
      center: { lat: 30.0444, lng: 31.2357 },
      dialog: false,
      resolve: null,
      reject: null,
      positions: [],
      title: null,
      message: null,
      options: {
        color: "primary",
        width: 600,
        zIndex: 1000000000,
      },
      infoWindowPos: null,
      infoWinOpen: true,
      currentMidx: null,
      infoOptions: {
        content: "",
        //optional: offset infowindow so it visually sits nicely on top of our marker
        pixelOffset: {
          width: 0,
          height: -35,
        },
      },
    };
  },
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open(title, positions, titles, options = null) {
      this.loadVApp();
      this.dialog = true;
      this.title = title;
      if (positions.length > 0) {
        this.positions = positions;
        this.center = this.positions[0];
        this.options = Object.assign(this.options, options || this.options);
      } else {
        this.message = "Error Has Occurs";
      }
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    agree() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp();
    },
    cancel() {
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp();
    },
    toggleInfoWindow: function (marker, idx) {
      this.center = marker;
      this.infoWindowPos = marker;
      // this.infoOptions.content = marker.position.id;
      this.infoOptions.content = this.getInfoWindowContent(marker);
      //check if its the same marker that was selected if yes toggle
      if (this.currentMidx == idx) {
        this.infoWinOpen = !this.infoWinOpen;
      }
      //if different marker set infowindow to open and reset current marker index
      else {
        this.infoWinOpen = true;
        this.currentMidx = idx;
      }
    },
    getInfoWindowContent(marker) {

      if (marker.key == 'line_division') {
        return `<div class="">
          <div>
            <div>
              <div class="m-2"><span style="font-weight: bold;">${"division_id:"
          }</span>
              ${marker.division_id}

              </div>
              <div class="m-2"><span style="font-weight: bold;">Line: </span>
                ${marker.line}
              </div>
              <div class="m-2"><span style="font-weight: bold;">Division: </span>
                ${marker.name}
              </div>
            </div>
          </div>
        </div>`;
      } else if (marker.hasOwnProperty('manager')) {
        return `<div class="">
          <div>
            <div>
              <div class="m-2"><span style="font-weight: bold;">${"Manager:"
          }</span>
              ${marker.manager}

              </div>
              <div class="m-2"><span style="font-weight: bold;">Account: </span>
                ${marker.account}
              </div>
              <div class="m-2"><span style="font-weight: bold;">Doctor: </span>
                ${marker.doctor}
              </div>
            </div>
          </div>
        </div>`;
      }
      else {
        return `<div class="">
          <div>
            <div>
              <div class="m-2"><span style="font-weight: bold;">${marker.visit_id ? "visit id: " : "account_id:"
          }</span>
              ${marker.visit_id ? marker.visit_id : marker.account_id}

              </div>
              <div class="m-2"><span style="font-weight: bold;">account: </span>
                ${marker.account}
              </div>
              <div class="m-2"><span style="font-weight: bold;">doctor: </span>
                ${marker.doctor}
              </div>
            </div>
          </div>
        </div>`;
      }

    },
  },
};
</script>

<style></style>
