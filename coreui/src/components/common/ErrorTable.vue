<template>
  <c-card v-if="showTable">
    <c-card-header>
      <c-row>
        <c-col>
          Errors
        </c-col>

      </c-row>
    </c-card-header>
    <c-card-body>
      <c-data-table
        hover
        striped
        sorter
        footer
        itemsPerPageSelect
        :items="items"
        :fields="fields"
        :items-per-page="100"
        :active-page="1"
        :responsive="true"
        table-filter
        pagination
      >
      </c-data-table>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" @click="download">Download</c-button>
      <c-button  color="default" @click="cancel">Cancel</c-button>
    </c-card-footer>
  </c-card>
</template>

<script>
export default {
  emits: ['canceled','downloaded'],
  props: {
    items: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      showTable: false,
    }
  },
  computed: {
    fields () {

      return this.items.length==0?[]:Object.keys(this.items[0])
    }
  },
  methods: {
    download () {
      this.downloadXlsx(this.items)
      this.$emit('downloaded')
      this.showTable=false
    },
    show () {
      this.showTable=true
    },
    cancel () {
      this.showTable=false
      this.$emit('canceled')
    }
  }
};
</script>
