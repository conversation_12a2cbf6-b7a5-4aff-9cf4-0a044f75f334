<template>
  <div :for="user.id" class="option_item">
    <input type="checkbox" class="checkbox" :id="user.id" v-on="listeners" />
    <div class="option_inner facebook">
      <div class="tickmark"></div>
      <div class="box">
        <div class="before" :class="user.online ? 'online' : 'offline'"></div>
        <div class="after"></div>
        <div class="content">
          <img :src="user.url || 'img/avatars/male.png'" :alt="user.name" />
        </div>
      </div>

      <div class="name">{{ user.fullname | shortName }}</div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    user: {
      type: Object,
      required: true,
    },
  },
  computed: {
    listeners() {
      return { ...this.$listeners };
    },
  },
};
</script>

<style scoped>
.option_item {
  display: block;
  position: relative;
  min-width: 175px;
  flex-basis: calc(calc(100% - (2 * var(--space))) / 3);
  height: 250px;
  margin: 10px;
  gap: 1rem;
}

.option_item .checkbox {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  min-width: 175px;
  height: 250px;
  z-index: 1;
  opacity: 0;
}

.option_item .option_inner {
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 5px;
  text-align: center;
  padding: 58px 40px;
  color: #585c68;
  display: block;
  border: 5px solid transparent;
  position: relative;
}

.option_item .option_inner .icon {
  overflow: hidden;
  border-radius: 50%;
  margin-bottom: 10px;
}

.option_item .option_inner .icon img {
  max-width: 100%;
  max-height: 100%;
  display: block;
}

.option_item .option_inner .icon .fab {
  font-size: 32px;
}

.option_item .option_inner .name {
  user-select: none;
  text-align: center;
}

.option_item .checkbox:checked ~ .option_inner.facebook {
  border-color: #3b5999;
  color: #3b5999;
}

.option_item .option_inner .tickmark {
  position: absolute;
  top: -1px;
  left: -1px;
  border: 20px solid;
  border-color: #000 transparent transparent #000;
  display: none;
}

.option_item .option_inner .tickmark:before {
  content: "";
  position: absolute;
  top: -18px;
  left: -18px;
  width: 15px;
  height: 5px;
  border: 3px solid;
  border-color: transparent transparent #fff #fff;
  transform: rotate(-45deg);
}

.option_item .checkbox:checked ~ .option_inner .tickmark {
  display: block;
}

.option_item .option_inner.facebook .tickmark {
  border-color: #3b5999 transparent transparent #3b5999;
}

.box {
  position: relative;
  width: 100px;
  height: 100px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  overflow: hidden;
}
.box .before {
  position: absolute;
  inset: 0px 0px;
  transition: 0.5s;
  animation: animate 4s linear infinite;
}

.online {
  background: linear-gradient(315deg, #95ff00, #00d49f);
}
.offline {
  background: linear-gradient(315deg, #ff0000, #bfd400);
}

.box .after {
  position: absolute;
  inset: 4px;
  background: #162052;
  border-radius: 50%;
}

.content {
  position: absolute;
  inset: 5%;
  border-radius: 50%;
  overflow: hidden;
}

.content img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@keyframes animate {
  0% {
    transform: rotate(0deg);
  }
  0% {
    transform: rotate(360deg);
  }
}
</style>
