<template>
  <v-dialog
    v-model="dialog"
    :max-width="options.width"
    :style="{ zIndex: options.zIndex }"
    @keydown.esc="cancel"
  >
    <div class="stars"></div>
    <div class="twinkling"></div>
    <div class="galaxy"></div>
    <div class="lamps-container">
      <div class="lamps">
        <img
          class="vert-move1"
          src="../../../public/img/ramadanImg/lamp-2.png"
        />
        <img
          class="vert-move3"
          src="../../../public/img/ramadanImg/lamp-4.png"
        />
        <img
          class="vert-move2"
          src="../../../public/img/ramadanImg/lamp-3.png"
        />
      </div>
      <div class="lamps-sec">
        <img
          class="vert-move1"
          src="../../../public/img/ramadanImg/lamp-2.png"
        />
        <img
          class="vert-move3"
          src="../../../public/img/ramadanImg/lamp-4.png"
        />
        <img
          class="vert-move2"
          src="../../../public/img/ramadanImg/lamp-3.png"
        />
      </div>
      <div class="lamps-2">
        <h1>Ramadan<span>KAREEM</span></h1>
      </div>
      <div class="ramdan-text">
        <p>
          <strong><span style="font-weight: bold">IT Gates </span></strong
          >Company congratulates
          <strong
            ><span style="font-weight: bold"> {{ name }} </span></strong
          >
          on the advent of the month of Ramadan, may God bring it back to us
          with blessings , happiness and happy new year all
        </p>
      </div>
    </div>
  </v-dialog>
</template>
<script>
import { mapActions } from "vuex";
export default {
  data() {
    return {
      dialog: false,
      resolve: null,
      reject: null,
      items: [],
      fields: [],
      title: null,
      options: {
        color: "primary",
        width: 900,
        zIndex: 1000000000,
      },
      audio: null,
      name: null,
    };
  },
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open(title, options = null) {
      this.dialog = true;
      this.loadVApp();
      var audio = new Audio(title);
      audio.play();
      this.options = Object.assign(this.options, options || this.options);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    agree() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp();
    },
    cancel() {
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp();
    },
    initialize() {
      axios
        .get("/api/companies")
        .then((response) => {
          this.name = response.data.data[0].name;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    generateStars() {
      var $galaxy = $(".galaxy");
      var iterator = 0;

      while (iterator <= 100) {
        var xposition = Math.random();
        var yposition = Math.random();
        var star_type = Math.floor(Math.random() * 3 + 1);
        var position = {
          x: $galaxy.width() * xposition,
          y: $galaxy.height() * yposition,
        };

        $('<div class="star star-type' + star_type + '"></div>')
          .appendTo($galaxy)
          .css({
            top: position.y,
            left: position.x,
          });

        iterator++;
      }
    },
  },
  created() {
    this.initialize();
    this.generateStars();
  },
};
</script>

<style scoped>
* {
  margin: 0;
  padding: 0;
}

.lamps {
  position: absolute;
  z-index: 99;
  top: 53px;
  width: 100%;
  right: 10px;
  text-align: right;
}
img.mubarak {
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 9999;
}
header {
  background-color: rgba(33, 33, 33, 0.9);
  color: #ffffff;
  display: block;
  font: 14px/1.3 Arial, sans-serif;
  height: 50px;
  position: relative;
  z-index: 5;
}
h2 {
  margin-top: 30px;
  text-align: center;
}
header h2 {
  font-size: 22px;
  margin: 0 auto;
  padding: 10px 0;
  width: 80%;
  text-align: center;
}
header a,
a:visited {
  text-decoration: none;
  color: #fcfcfc;
}

@keyframes move-twink-back {
  from {
    background-position: 0 0;
  }
  to {
    background-position: -10000px 5000px;
  }
}

@keyframes move-clouds-back {
  from {
    background-position: 0 0;
  }
  to {
    background-position: 10000px 0;
  }
}

.stars,
.twinkling,
.clouds {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  display: block;
}

.stars {
  background: #39286b url("../../../public/img/ramadanImg/stars.png") repeat top
    center;
  z-index: 0;
}

.twinkling {
  background: transparent url("../../../public/img/ramadanImg/twinklingb.png")
    repeat top center;
  z-index: 1;
  animation: move-twink-back 200s linear infinite;
}
.lamps-2 h1 {
  font-family: "Playball", cursive;
  color: #fff;
  font-size: 76px;
  letter-spacing: 9px;
  text-align: center;
  width: 354px;
  line-height: 44px;
  margin-top: 100px;
  margin-bottom: 15px;
  background-image: url("../../../public/img/ramadanImg/ramadan-kareem.png");
  padding: 0px 0px 166px 0px;
  background-position: center 57px;
  background-repeat: no-repeat;
  background-size: 100%;
}
.lamps-2 h1 span {
  font-size: 22px;
  text-align: center;
  float: left;
  padding-bottom: 10px;
  width: 100%;
}

@keyframes twinkle_one {
  0% {
    opacity: 1;
  }
  30% {
    opacity: 1;
  }
  70% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes twinkle_two {
  0% {
    opacity: 1;
  }
  25% {
    opacity: 0;
  }
  80% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes twinkle_three {
  0% {
    opacity: 1;
  }
  30% {
    opacity: 0;
  }
  40% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.galaxy {
  z-index: 0;
  position: absolute;
  left: 0;
  right: 0;
  width: 1200px;
  height: 600px;
  margin: 0 auto;
}
.star {
  position: absolute;
  background-color: white;
}

.star-type1 {
  width: 4px;
  height: 4px;
  border-radius: 2px;
  box-shadow: 0 0 1px 1px white;
  animation: twinkle_one 3s ease-in-out infinite;
}
.star-type2 {
  width: 4px;
  height: 4px;
  border-radius: 4px;
  box-shadow: 0 0 2px 1px white;
  animation: twinkle_two 12s ease-in-out infinite;
}
.star-type3 {
  width: 4px;
  height: 4px;
  border-radius: 2px;
  box-shadow: 0 0 2px 0 white;
  animation: twinkle_three 7s ease-in-out infinite;
}
img.vert-move {
  animation: mover 1s infinite alternate;
}
img.vert-move {
  animation: mover 1s infinite alternate;
  position: relative;
  top: -101px;
}
img.vert-move1 {
  -webkit-animation: mover 2s infinite alternate;
  animation: mover 2s infinite alternate;
  position: relative;
  top: -41px;
}
img.vert-move1 {
  animation: mover 2s infinite alternate;
}
img.vert-move3 {
  animation: mover 1.5s infinite alternate;
}
img.vert-move3 {
  animation: mover 1.5s infinite alternate;
  top: -140px;
  position: relative;
}
img.vert-move2 {
  -webkit-animation: mover 1.8s infinite alternate;
  animation: mover 1.8s infinite alternate;
}
img.vert-move2 {
  -webkit-animation: mover 1.8s infinite alternate;
  animation: mover 1.8s infinite alternate;
}
@-webkit-keyframes mover {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-10px);
  }
}
@keyframes mover {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-10px);
  }
}
.lamps-3 {
  position: absolute;
  bottom: 0px;
  z-index: 99;
}
.lamps-sec {
  position: absolute;
  left: 25px;
  top: 63px;
  z-index: 9999999;
}
.lamps-2 {
  position: absolute;
  left: 50%;
  top: 62px;
  z-index: 1;
  margin-left: -205px;
}
.ramdan-text {
  position: absolute;
  color: #fff;
  width: 480px;
  font-size: 19px;
  text-align: center;
  font-family: arial;
  margin: 0 auto;
  left: 50%;
  margin-left: -269px;
  z-index: 1;
  top: 67%;
  font-family: "Poppins", sans-serif;
}
.ramdan-text p {
  font-weight: 400;
  font-family: "Roboto Condensed", sans-serif;
}
</style>