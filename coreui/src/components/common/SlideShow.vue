<template>
  <div>
    <cool-light-box
      :items="slides"
      :index="index"
      @close="close(index)"
      @on-change="change"
      @on-change-end="changeEnd"
      @on-open="open"
      :srcMediaType="'mediaType'"
      :fullScreen="fullScreen"
    >
    </cool-light-box>
    <div class="images-wrapper">
      <div
        class="image"
        v-for="(image, imageIndex) in slides"
        :key="imageIndex"
        @click="index = imageIndex"
        :style="{ backgroundImage: 'url(' + image.thumb + ')' }"
      ></div>
    </div>
  </div>
</template>

<script>
import CoolLightBox from "./SlideShow/CoolLightBox.vue";
export default {
  components: {
    CoolLightBox,
  },
  props: {
    slides: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      fullScreen: true,
      items: [
        {
          thumb: "/storage/users/1/profile/images/holder_1668419009.png",
          src: "/storage/presentations/file1.html",
          mediaType: "iframe",
        },
        {
          thumb: "/storage/users/1/profile/images/holder_1668419009.png",
          src: "https://www.youtube.com/watch?v=3T_Jy1CqH9k&ab_channel=KevinPowell",
          mediaType: "video",
        },
        {
          thumb: "/storage/users/1/profile/images/holder_1668419009.png",
          src: "/storage/presentations/file2.html",
          mediaType: "iframe",
        },
        {
          title: "Coffee Machine",
          description: "Need to drink then pay first",
          thumb: "/storage/users/1/profile/images/holder_1668419009.png",
          src: "/storage/presentations/file3.html",
          mediaType: "iframe",
        },
        {
          title: "In nature, nothing is perfect and everything is perfect",
          description: "Photo by Lucas",
          thumb: "/storage/users/1/profile/images/holder_1668419009.png",
          src: "https://www.africau.edu/images/default/sample.pdf",
          mediaType: "iframe",
        },
      ],
      index: null,
    };
  },
  methods: {
    close() {
      this.index = null;
      this.fullScreen = false;
    },
    change(index) {
      console.log("change", this.items[index]);
    },
    changeEnd(index) {
      console.log("change end", this.items[index]);
    },
    open(index) {
      console.log("open", this.items[index]);
    },
  },
  watch:{
    slides(val){
      console.log(val)
    }
  }
};
</script>
<style scoped>
.images-wrapper {
  display: flex;
}
.image {
  padding: 10em;
  background-repeat: no-repeat;
}
</style>
