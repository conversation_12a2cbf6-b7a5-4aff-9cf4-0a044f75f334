<template>
  <v-dialog
    v-model="dialog"
    :max-width="options.width"
    :style="{ zIndex: options.zIndex }"
    @keydown.esc="cancel"
  >
    <v-card>
      <div class="flex">
        <!-- <v-checkbox v-model="landscape" label="Landscape"></v-checkbox> -->
        <v-time-picker :full-width="true" v-model="picker"></v-time-picker>
      </div>
      <v-card-actions class="pt-0">
        <v-spacer></v-spacer>
        <v-btn color="primary" text @click.native="save">Ok</v-btn>
        <v-btn color="grey" text @click.native="cancel">Cancel</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import { mapActions } from "vuex";
export default {
  data() {
    return {
      dialog: false,
      resolve: null,
      reject: null,
      positions: [],
      title: null,
      picker: null,
      item: null,
      landscape: false,
      options: {
        color: "primary",
        width: 290,
        zIndex: 1000000000,
      },
      infoWindowPos: null,
      infoWinOpen: true,
      currentMidx: null,
      infoOptions: {
        content: "",
        //optional: offset infowindow so it visually sits nicely on top of our marker
        pixelOffset: {
          width: 0,
          height: -35,
        },
      },
    };
  },
  emits: ["timeDialog"],
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open(title, data, titles, options = null) {
      this.loadVApp();
      this.dialog = true;
      this.title = title;
      this.item = data;
      this.picker = this.title == 'AM Time' ? data.AM_time : data.PM_time;
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    agree() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp();
    },
    cancel() {
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp();
    },
    save() {
      this.$emit("timeDialog", {
        time: this.picker,
        item: this.item,
      });
      this.picker = null;
      this.cancel();
    },
  },
};
</script>

<style>
</style>