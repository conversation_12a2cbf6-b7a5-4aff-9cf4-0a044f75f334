<template>
  <div>
    <!-- Performance-optimized search container -->
    <div class="search-container-enhanced" v-if="showSearch">
      <div class="search-header">
        <div class="search-label-enhanced">
          <CIcon name="cil-filter" class="search-icon-enhanced" />
          <span>Filter Data</span>
        </div>
        <div class="search-stats" v-if="localSearchTerm">
          <CIcon name="cil-check-circle" class="search-stats-icon" />
          <span class="results-count">{{ totalFilteredItems }}</span>
          <span class="results-text">found of</span>
          <span class="total-count">{{ items.length }}</span>
          <span class="total-text">total</span>
        </div>
      </div>
      <div class="search-input-wrapper-enhanced">
        <CIcon name="cil-magnifying-glass" class="search-icon" />
        <input ref="searchInput" type="text" class="search-input-enhanced" :placeholder="searchPlaceholder"
          v-model="localSearchTerm" @input="debouncedSearch" @keydown.esc="clearSearch" />
        <button v-if="localSearchTerm" class="clear-search-btn-enhanced" @click="clearSearch">
          <CIcon name="cil-x" />
        </button>
        <div v-if="isSearching" class="search-loading">
          <CIcon name="cil-reload" class="loading-spinner" />
        </div>
      </div>
    </div>

    <!-- Loading overlay -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner-container">
        <CIcon name="cil-reload" class="loading-spinner-large" />
        <div class="loading-text">Loading data...</div>
      </div>
    </div>

    <!-- Performance-optimized table container with pagination -->
    <div class="table-container-enhanced" ref="tableContainer">
      <!-- Total Records Row - Positioned directly above table -->
      <div class="total-records-bar" v-if="showTotalBar">
        <div class="total-info-enhanced">
          <div class="total-section">
            <CIcon name="cil-chart-pie" class="total-icon" />
            <span class="total-label">Total Records:</span>
            <span class="total-count-badge">{{ totalFilteredItems }}</span>
          </div>
          <div class="filter-section" v-if="localSearchTerm && totalFilteredItems !== items.length">
            <CIcon name="cil-filter" class="filter-icon" />
            <span class="filter-text">Filtered from {{ items.length }} total</span>
          </div>
          <div class="page-section">
            <CIcon name="cil-layers" class="page-icon" />
            <span class="page-text">Page {{ currentPage }} of {{ totalPages }}</span>
          </div>
        </div>
      </div>

      <div class="table-wrapper">
        <!-- Fixed Headers -->
        <div class="table-header-container">
          <table class="data-table-enhanced header-table">
            <thead class="table-header-enhanced">
              <tr class="header-row">
                <th v-for="column in processedColumns" :key="`header-${column.key}`" class="column-header-enhanced"
                  :class="getHeaderClass(column)" @click="handleHeaderClick(column)">

                  <!-- Select all checkbox -->
                  <template v-if="column.key === 'select'">
                    <div class="select-all-container">
                      <input type="checkbox" v-model="selectAllModel" title="Select All Items"
                        class="select-all-checkbox-enhanced" @click.stop />
                      <span class="select-all-label">All</span>
                    </div>
                  </template>

                  <!-- Regular column header -->
                  <template v-else>
                    <div class="header-content">
                      <span class="header-text">{{ column.label }}</span>
                      <span class="sort-indicator" v-if="column.sortable">
                        <CIcon v-if="sortColumn === column.key"
                          :name="sortDirection === 'asc' ? 'cil-sort-ascending' : 'cil-sort-descending'"
                          class="sort-icon-active" />
                        <CIcon v-else name="cil-swap-vertical" class="sort-icon-inactive" />
                      </span>
                    </div>
                  </template>
                </th>
              </tr>
            </thead>
          </table>
        </div>

        <!-- Scrollable Body -->
        <div class="table-body-container" @scroll="handleHorizontalScroll">
          <table class="data-table-enhanced body-table">
            <!-- Table body with paginated items -->
            <tbody class="table-body-enhanced" :key="paginationKey">
              <tr v-for="(item, index) in getPaginatedItems()" :key="`row-${currentPage}-${pageSize}-${getItemKey(item, index)}`" 
                class="data-row-enhanced" :class="getRowClass(item, index)">
                <!-- Render each column -->
                <td v-for="column in processedColumns" :key="`${getItemKey(item, index)}-${column.key}`" 
                  :class="getCellClass(item, column)">
                  
                  <!-- Checkbox for selection -->
                  <div v-if="column.key === 'select'" class="checkbox-container">
                    <input class="row-checkbox" type="checkbox" v-model="selectedItems" :value="item"
                      title="Select Item" @change="onItemSelectionChanged" />
                  </div>

                  <!-- Custom slot content -->
                  <template v-else-if="$scopedSlots[column.key]">
                    <slot :name="column.key" :item="item" :value="getColumnValue(item, column)" :column="column" :index="index"></slot>
                  </template>

                  <!-- Default rendering -->
                  <span v-else>{{ formatColumnValue(item, column) }}</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Enhanced Pagination Controls -->
    <div class="pagination-container-enhanced" v-if="showPagination">
      <div class="pagination-wrapper">
        <!-- Left section: Page size selector -->
        <div class="page-size-section">
          <div class="page-size-label">
            <CIcon name="cil-list" class="page-size-icon" />
            <span>Show</span>
          </div>
          <select
            class="page-size-select"
            :value="pageSize"
            @change="onPageSizeChange"
          >
            <option v-for="size in pageSizeOptions" :key="size" :value="size">{{ size }}</option>
          </select>
          <span class="page-size-text">per page</span>
        </div>

        <!-- Center section: Navigation controls -->
        <div class="pagination-navigation">
          <button class="nav-btn nav-btn-first" :disabled="currentPage === 1" @click="goToPage(1)" title="First Page">
            <CIcon name="cil-media-skip-backward" />
          </button>

          <button class="nav-btn nav-btn-prev" :disabled="currentPage === 1" @click="goToPage(currentPage - 1)"
            title="Previous Page">
            <CIcon name="cil-chevron-left" />
          </button>

          <!-- Page numbers -->
          <div class="page-numbers">
            <template v-for="(page, index) in getVisiblePages()">
              <button v-if="page !== '...'" :key="`page-${index}`" class="page-btn"
                :class="{ 'page-btn-active': page === currentPage }" @click="goToPage(page)">
                {{ page }}
              </button>
              <span v-else :key="`ellipsis-${index}`" class="page-ellipsis">...</span>
            </template>
          </div>

          <button class="nav-btn nav-btn-next" :disabled="currentPage === totalPages" @click="goToPage(currentPage + 1)"
            title="Next Page">
            <CIcon name="cil-chevron-right" />
          </button>

          <button class="nav-btn nav-btn-last" :disabled="currentPage === totalPages" @click="goToPage(totalPages)"
            title="Last Page">
            <CIcon name="cil-media-skip-forward" />
          </button>
        </div>

        <!-- Right section: Page info -->
        <div class="page-info-section">
          <div class="page-info-text">
            <span class="current-range">
              {{ ((currentPage - 1) * pageSize + 1) }}-{{ Math.min(currentPage * pageSize, totalFilteredItems) }}
            </span>
            <span class="range-separator">of</span>
            <span class="total-items">{{ totalFilteredItems }}</span>
            <span class="items-text">items</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance info (development only) -->
    <div v-if="showPerformanceInfo" class="performance-info">
      <small>
        Last render: {{ lastRenderTime.toFixed(2) }}ms |
        Total: {{ totalFilteredItems }} |
        Page: {{ paginatedItems.length }} |
        Current: {{ currentPage }}/{{ totalPages }}
      </small>
    </div>
  </div>
</template>

<script>
import moment from "moment";

export default {
  name: 'GenericDataTable',
  props: {
    // Core data props
    items: {
      type: Array,
      required: true,
      default: () => []
    },
    columns: {
      type: Array,
      required: true,
      validator: (columns) => {
        return columns.every(col => 
          typeof col === 'object' && 
          col.key && 
          col.label
        );
      }
    },
    
    // Configuration props
    searchTerm: {
      type: String,
      default: ''
    },
    searchPlaceholder: {
      type: String,
      default: 'Search in all fields...'
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    showTotalBar: {
      type: Boolean,
      default: true
    },
    showPagination: {
      type: Boolean,
      default: true
    },
    
    // Selection props
    selectable: {
      type: Boolean,
      default: false
    },
    selectedItems: {
      type: Array,
      default: () => []
    },
    selectAll: {
      type: Boolean,
      default: false
    },
    
    // Pagination props
    initialPageSize: {
      type: Number,
      default: 25
    },
    pageSizeOptions: {
      type: Array,
      default: () => [10, 25, 50, 100, 200]
    },
    
    // Performance props
    isLoading: {
      type: Boolean,
      default: false
    },
    
    // Styling props
    tableHeight: {
      type: String,
      default: '600px'
    },
    
    // Item key function for unique identification
    itemKey: {
      type: [String, Function],
      default: 'id'
    }
  },
  
  data() {
    return {
      localSelectedItems: [...this.selectedItems],
      sortColumn: null,
      sortDirection: 'asc',
      localSearchTerm: this.searchTerm,

      // Performance optimization properties
      isSearching: false,
      searchDebounceTimer: null,

      // Pagination properties
      currentPage: 1,
      pageSize: this.initialPageSize,

      // Total filtered items count
      totalFilteredItems: 0,

      // Performance monitoring
      renderStartTime: 0,
      lastRenderTime: 0
    };
  },

  mounted() {
    this.initializeComponent();
    this.setupPerformanceMonitoring();

    // Synchronize column widths after component is mounted
    this.$nextTick(() => {
      this.synchronizeColumnWidths();
    });
  },

  beforeDestroy() {
    this.cleanup();
  },

  computed: {
    // Process columns to add selection column if needed
    processedColumns() {
      let columns = [...this.columns];

      // Add selection column if selectable
      if (this.selectable) {
        columns.unshift({
          key: 'select',
          label: 'Select',
          sortable: false,
          width: '60px'
        });
      }

      // Ensure all columns have required properties
      return columns.map(col => ({
        key: col.key,
        label: col.label || this.formatFieldName(col.key),
        sortable: col.sortable !== false, // Default to true unless explicitly false
        width: col.width || 'auto',
        type: col.type || 'text',
        format: col.format || null
      }));
    },

    // Filtered items - simplified without problematic caching
    filteredItems() {
      const currentSearchTerm = this.localSearchTerm || this.searchTerm;

      let result;
      if (!currentSearchTerm) {
        result = this.sortedItems;
      } else {
        result = this.performOptimizedSearch(currentSearchTerm);
      }

      // Update total count
      this.totalFilteredItems = result.length;
      return result;
    },

    // Sorted items - simplified without problematic caching
    sortedItems() {
      return this.performOptimizedSort([...this.items]);
    },

    // Paginated items for current page
    paginatedItems() {
      const currentPage = this.currentPage;
      const pageSize = this.pageSize;
      const filteredItems = this.filteredItems;

      const startIndex = (currentPage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      return filteredItems.slice(startIndex, endIndex);
    },

    // Calculate total pages
    totalPages() {
      const totalItems = this.totalFilteredItems;
      const pageSize = this.pageSize;
      return Math.max(1, Math.ceil(totalItems / pageSize));
    },

    // Force reactivity key
    paginationKey() {
      return `${this.currentPage}-${this.pageSize}-${this.totalFilteredItems}`;
    },

    // Performance monitoring
    showPerformanceInfo() {
      return process.env.NODE_ENV === 'development' && this.lastRenderTime > 0;
    },

    // Selection computed properties
    isAllSelected() {
      if (!this.selectable || this.filteredItems.length === 0) return false;
      return this.filteredItems.every(item =>
        this.localSelectedItems.some(selected => this.getItemKey(selected) === this.getItemKey(item))
      );
    },

    selectAllModel: {
      get() {
        return this.isAllSelected;
      },
      set(value) {
        if (value) {
          // Select all filtered items
          this.localSelectedItems = [...this.filteredItems];
        } else {
          // Deselect all items
          this.localSelectedItems = [];
        }
        this.onItemSelectionChanged();
      }
    }
  },

  watch: {
    selectedItems: {
      handler(newVal) {
        this.localSelectedItems = [...newVal];
      },
      deep: true
    },

    searchTerm: {
      handler(newVal) {
        this.localSearchTerm = newVal;
      },
      immediate: true
    },

    selectAll: {
      handler(newVal) {
        if (this.selectable && newVal !== this.isAllSelected) {
          this.selectAllModel = newVal;
        }
      },
      immediate: true
    },

    // Watch for items changes to ensure real-time updates
    items: {
      handler(newItems) {
        // Update total count immediately
        this.totalFilteredItems = newItems.length;

        // Adjust current page if needed
        const maxPage = Math.ceil(this.totalFilteredItems / this.pageSize);
        if (this.currentPage > maxPage && maxPage > 0) {
          this.currentPage = maxPage;
        }

        // Synchronize column widths when data changes
        this.$nextTick(() => {
          this.synchronizeColumnWidths();
        });
      },
      deep: true,
      immediate: true
    },

    // Watch pagination changes to force reactivity
    currentPage: {
      handler() {
        this.$forceUpdate();
      }
    },

    pageSize: {
      handler() {
        this.$forceUpdate();

        // Synchronize column widths when page size changes
        this.$nextTick(() => {
          this.synchronizeColumnWidths();
        });
      }
    },

    // Watch paginatedItems to synchronize column widths when displayed data changes
    paginatedItems: {
      handler() {
        this.$nextTick(() => {
          this.synchronizeColumnWidths();
        });
      }
    }
  },

  methods: {
    // Initialize component
    initializeComponent() {
      // Set default sort column
      if (!this.sortColumn && this.processedColumns.length > 0) {
        const defaultSortColumn = this.processedColumns.find(col => col.sortable && col.key !== 'select');
        if (defaultSortColumn) {
          this.sortColumn = defaultSortColumn.key;
          this.$emit('sort', { column: this.sortColumn, direction: this.sortDirection });
        }
      }
    },

    // Setup performance monitoring
    setupPerformanceMonitoring() {
      if (process.env.NODE_ENV === 'development') {
        this.renderStartTime = performance.now();
      }
    },

    // Cleanup event listeners
    cleanup() {
      if (this.searchDebounceTimer) {
        clearTimeout(this.searchDebounceTimer);
      }
    },

    // Get unique key for item
    getItemKey(item, index = null) {
      if (typeof this.itemKey === 'function') {
        return this.itemKey(item, index);
      }
      return item[this.itemKey] || index;
    },

    // Optimized search with indexing
    performOptimizedSearch(searchTerm) {
      const term = searchTerm.toLowerCase();
      const searchableColumns = this.processedColumns.filter(col => col.key !== 'select' && col.sortable);

      return this.items.filter(item => {
        return searchableColumns.some(column => {
          const value = this.getColumnValue(item, column);
          if (value === null || value === undefined) return false;
          return String(value).toLowerCase().includes(term);
        });
      });
    },

    // Optimized sorting with better performance
    performOptimizedSort(itemsToSort) {
      if (!this.sortColumn || itemsToSort.length === 0) return itemsToSort;

      const column = this.processedColumns.find(col => col.key === this.sortColumn);
      if (!column) return itemsToSort;

      const direction = this.sortDirection;
      const modifier = direction === 'asc' ? 1 : -1;

      return itemsToSort.sort((a, b) => {
        const aValue = this.getColumnValue(a, column);
        const bValue = this.getColumnValue(b, column);

        // Handle null/undefined values
        if (aValue === null || aValue === undefined) return 1 * modifier;
        if (bValue === null || bValue === undefined) return -1 * modifier;

        // Numeric comparison
        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return (aValue - bValue) * modifier;
        }

        // Date comparison
        if (column.type === 'date') {
          const dateA = new Date(aValue);
          const dateB = new Date(bValue);
          return (dateA.getTime() - dateB.getTime()) * modifier;
        }

        // String comparison with locale
        const strA = String(aValue);
        const strB = String(bValue);
        return strA.localeCompare(strB, undefined, { numeric: true }) * modifier;
      });
    },

    // Get column value from item
    getColumnValue(item, column) {
      if (typeof column.key === 'function') {
        return column.key(item);
      }

      // Support nested properties with dot notation
      const keys = column.key.split('.');
      let value = item;
      for (const key of keys) {
        if (value && typeof value === 'object') {
          value = value[key];
        } else {
          return null;
        }
      }
      return value;
    },

    // Format column value for display
    formatColumnValue(item, column) {
      const value = this.getColumnValue(item, column);

      if (value === null || value === undefined) {
        return '';
      }

      // Apply custom format function if provided
      if (column.format && typeof column.format === 'function') {
        return column.format(value, item);
      }

      // Apply built-in formatting based on type
      switch (column.type) {
        case 'date':
          return this.formatDate(value);
        case 'currency':
          return this.formatCurrency(value);
        case 'number':
          return this.formatNumber(value);
        default:
          return String(value);
      }
    },

    // Format field name (convert snake_case to Title Case)
    formatFieldName(field) {
      if (!field) return '';
      const withSpaces = field.replace(/[_-]/g, ' ');
      return withSpaces.replace(/\b\w/g, c => c.toUpperCase());
    },

    // Format date
    formatDate(value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD HH:mm:ss");
      }
      return '';
    },

    // Format currency
    formatCurrency(value) {
      if (typeof value === 'number') {
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(value);
      }
      return String(value);
    },

    // Format number
    formatNumber(value) {
      if (typeof value === 'number') {
        return new Intl.NumberFormat().format(value);
      }
      return String(value);
    },

    // Header click handler
    handleHeaderClick(column) {
      if (column.key === 'select' || !column.sortable) return;
      this.handleSort(column.key);
    },

    // Sort handler
    handleSort(columnKey) {
      if (this.sortColumn === columnKey) {
        this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        this.sortColumn = columnKey;
        this.sortDirection = 'asc';
      }

      this.$emit('sort', { column: this.sortColumn, direction: this.sortDirection });
    },

    // Debounced search for better performance
    debouncedSearch() {
      this.isSearching = true;

      if (this.searchDebounceTimer) {
        clearTimeout(this.searchDebounceTimer);
      }

      this.searchDebounceTimer = setTimeout(() => {
        this.$emit('search', this.localSearchTerm);
        this.isSearching = false;
        // Reset to first page when search changes
        this.currentPage = 1;
      }, 300);
    },

    // Clear search
    clearSearch() {
      this.localSearchTerm = '';
      this.$emit('search', '');

      // Focus back to search input
      this.$nextTick(() => {
        if (this.$refs.searchInput) {
          this.$refs.searchInput.focus();
        }
      });
    },

    // Pagination methods
    goToPage(page) {
      page = parseInt(page);
      if (page < 1) page = 1;
      if (page > this.totalPages) page = this.totalPages;

      if (this.currentPage !== page) {
        this.currentPage = page;
        this.$emit('page-change', page);

        this.$nextTick(() => {
          this.$forceUpdate();
        });
      }
    },

    onPageSizeChange(event) {
      const newPageSize = parseInt(event.target.value);
      this.pageSize = newPageSize;
      this.handlePageSizeChange();
      this.$emit('page-size-change', newPageSize);

      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },

    handlePageSizeChange() {
      // Adjust current page if needed
      const maxPage = Math.ceil(this.totalFilteredItems / this.pageSize);

      if (this.currentPage > maxPage && maxPage > 0) {
        this.currentPage = maxPage;
      } else if (this.currentPage < 1) {
        this.currentPage = 1;
      }
    },

    // Method version of paginatedItems (for better performance)
    getPaginatedItems() {
      const startIndex = (this.currentPage - 1) * this.pageSize;
      const endIndex = startIndex + this.pageSize;
      return this.filteredItems.slice(startIndex, endIndex);
    },

    // Get visible page numbers for pagination
    getVisiblePages() {
      const pages = [];
      const totalPages = this.totalPages;
      const currentPage = this.currentPage;

      if (totalPages <= 7) {
        // Show all pages if total is 7 or less
        for (let i = 1; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // Always show first page
        pages.push(1);

        if (currentPage > 4) {
          pages.push('...');
        }

        // Show pages around current page
        const start = Math.max(2, currentPage - 1);
        const end = Math.min(totalPages - 1, currentPage + 1);

        for (let i = start; i <= end; i++) {
          if (i !== 1 && i !== totalPages) {
            pages.push(i);
          }
        }

        if (currentPage < totalPages - 3) {
          pages.push('...');
        }

        // Always show last page
        if (totalPages > 1) {
          pages.push(totalPages);
        }
      }

      return pages;
    },

    // Selection methods
    onItemSelectionChanged() {
      this.$emit('selection-change', this.localSelectedItems);
      this.$emit('update:selectedItems', this.localSelectedItems);
    },

    // Styling methods
    getHeaderClass(column) {
      const classes = ['sortable-header'];

      if (column.key === 'select') {
        classes.push('select-all-header');
      } else if (column.sortable) {
        if (this.sortColumn === column.key) {
          classes.push(this.sortDirection === 'asc' ? 'sorted-asc' : 'sorted-desc');
        }
      }

      return classes;
    },

    getRowClass(item, index) {
      const classes = [];

      if (index % 2 === 0) {
        classes.push('even-row');
      } else {
        classes.push('odd-row');
      }

      if (this.selectable && this.localSelectedItems.some(selected =>
        this.getItemKey(selected) === this.getItemKey(item))) {
        classes.push('selected-row');
      }

      return classes;
    },

    getCellClass(item, column) {
      const classes = {};

      // Add column-specific classes
      if (column.key === 'select') {
        classes['select-cell'] = true;
      }

      // Add type-specific classes
      if (column.type) {
        classes[`${column.type}-cell`] = true;
      }

      return classes;
    },

    // Handle horizontal scroll events
    handleHorizontalScroll(event) {
      const bodyContainer = event.target;
      const scrollLeft = bodyContainer.scrollLeft;
      const maxScrollLeft = bodyContainer.scrollWidth - bodyContainer.clientWidth;

      // Find the header container and synchronize its scroll position
      const headerContainer = this.$el.querySelector('.table-header-container');
      if (headerContainer) {
        headerContainer.scrollLeft = scrollLeft;
      }

      // Add visual indicators if needed
      if (scrollLeft > 0) {
        bodyContainer.classList.add('scrolled-left');
      } else {
        bodyContainer.classList.remove('scrolled-left');
      }

      if (scrollLeft < maxScrollLeft) {
        bodyContainer.classList.add('can-scroll-right');
      } else {
        bodyContainer.classList.remove('can-scroll-right');
      }
    },

    // Synchronize column widths between header and body tables
    synchronizeColumnWidths() {
      const headerTable = this.$el.querySelector('.header-table');
      const bodyTable = this.$el.querySelector('.body-table');

      if (!headerTable || !bodyTable) return;

      const headerCols = headerTable.querySelectorAll('th');
      const firstBodyRow = bodyTable.querySelector('tbody tr');

      // If there's no data in the body, we can't synchronize
      if (!firstBodyRow) return;

      const bodyCols = firstBodyRow.querySelectorAll('td');

      // Ensure we have the same number of columns
      if (headerCols.length !== bodyCols.length) return;

      // Calculate and apply widths
      for (let i = 0; i < headerCols.length; i++) {
        // Get the maximum width between header and body column
        const headerWidth = headerCols[i].getBoundingClientRect().width;
        const bodyWidth = bodyCols[i].getBoundingClientRect().width;
        const maxWidth = Math.max(headerWidth, bodyWidth);

        // Apply the maximum width to both columns
        headerCols[i].style.width = `${maxWidth}px`;
        bodyCols[i].style.width = `${maxWidth}px`;
      }
    }
  },

  emits: [
    'search',
    'sort',
    'selection-change',
    'update:selectedItems',
    'page-change',
    'page-size-change'
  ]
};
</script>

<style scoped>
/* Enhanced Search Container Styles */
.search-container-enhanced {
  margin: 20px 0;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
  transition: box-shadow 0.3s ease, transform 0.3s ease;
  contain: layout style;
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.search-label-enhanced {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 700;
  color: #2d3748;
  font-size: 16px;
}

.search-icon-enhanced {
  color: #667eea;
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.search-stats {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #718096;
  font-weight: 500;
  background: rgba(102, 126, 234, 0.1);
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.search-stats-icon {
  color: #10b981;
  width: 16px;
  height: 16px;
}

.results-count {
  color: #667eea;
  font-weight: 700;
  font-size: 16px;
}

.results-text {
  color: #718096;
  font-weight: 400;
}

.total-count {
  color: #4a5568;
  font-weight: 600;
  font-size: 15px;
}

.total-text {
  color: #718096;
  font-weight: 400;
}

.search-input-wrapper-enhanced {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
  will-change: transform, opacity;
  transform: translateZ(0);
  transition: transform 0.3s ease;
  contain: layout style;
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #667eea;
  font-size: 16px;
  width: 16px;
  height: 16px;
  z-index: 2;
}

.search-input-enhanced {
  width: 100%;
  padding: 16px 50px 16px 48px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), border-color 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background: white;
  color: #2d3748;
  will-change: transform, border-color, box-shadow;
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-font-smoothing: subpixel-antialiased;
}

.search-input-enhanced:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2), 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px) translateZ(0);
}

.search-input-enhanced::placeholder {
  color: #a0aec0;
  font-weight: 400;
}

.clear-search-btn-enhanced {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  border: none;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.clear-search-btn-enhanced:hover {
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.5);
}

.search-loading {
  position: absolute;
  right: 50px;
  top: 50%;
  transform: translateY(-50%);
  color: #667eea;
}

.loading-spinner {
  animation: spin 1s linear infinite;
  width: 16px;
  height: 16px;
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

@keyframes spin {
  from {
    transform: rotate(0deg) translateZ(0);
  }
  to {
    transform: rotate(360deg) translateZ(0);
  }
}

/* Loading overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border-radius: 16px;
}

.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-spinner-large {
  animation: spin 1s linear infinite;
  width: 32px;
  height: 32px;
  color: #667eea;
}

.loading-text {
  color: #4a5568;
  font-weight: 600;
  font-size: 16px;
}

/* Total Records Bar Styles */
.total-records-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 24px;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-bottom: none;
  margin-bottom: 0;
}

.total-info-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.total-section,
.filter-section,
.page-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.total-icon,
.filter-icon,
.page-icon {
  width: 18px;
  height: 18px;
  color: rgba(255, 255, 255, 0.9);
}

.total-label,
.filter-text,
.page-text {
  font-weight: 600;
  font-size: 14px;
}

.total-count-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 6px 16px;
  border-radius: 20px;
  font-weight: 700;
  font-size: 16px;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Enhanced Table Container Styles with Performance Optimizations */
.table-container-enhanced {
  position: relative;
  overflow: visible;
  max-height: 650px;
  height: 850px;
  border-radius: 16px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  background: white;
  border: 1px solid #e2e8f0;
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform: translateZ(0);
  -webkit-font-smoothing: subpixel-antialiased;
  contain: content;
  min-height: 400px;
  margin-bottom: 0;
}

/* Responsive adjustments for different screen sizes */
@media (max-width: 1200px) {
  .table-container-enhanced {
    height: 600px;
  }
  .table-wrapper {
    max-height: 550px;
    height: 550px;
    min-height: 550px;
  }
}

@media (max-width: 992px) {
  .table-container-enhanced {
    height: 550px;
  }
  .table-wrapper {
    max-height: 500px;
    height: 500px;
    min-height: 500px;
  }
}

@media (max-width: 768px) {
  .table-container-enhanced {
    height: 500px;
  }
  .table-wrapper {
    max-height: 450px;
    height: 450px;
    min-height: 450px;
  }
  .total-info-enhanced {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  .table-body-container::-webkit-scrollbar {
    height: 8px;
    width: 8px;
  }
}

@media (max-width: 576px) {
  .table-container-enhanced {
    height: 450px;
  }
  .table-wrapper {
    max-height: 400px;
    height: 400px;
    min-height: 400px;
  }
  .table-body-container::-webkit-scrollbar {
    height: 6px;
    width: 6px;
  }
}

.table-wrapper {
  position: relative;
  min-height: 600px;
  height: 600px;
  width: 100%;
  -webkit-overflow-scrolling: touch;
  border-radius: 0 0 16px 16px;
  margin-top: 0;
  padding-top: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Fixed Header Container */
.table-header-container {
  position: relative;
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  flex-shrink: 0;
  z-index: 100;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.table-header-container::-webkit-scrollbar {
  display: none;
}

.header-table {
  width: 100%;
  table-layout: fixed;
  min-width: max-content;
}

/* Scrollable Body Container */
.table-body-container {
  flex: 1;
  overflow-x: auto;
  overflow-y: auto;
  width: 100%;
  scrollbar-width: thin;
  scrollbar-color: #667eea #f1f5f9;
  position: relative;
}

.body-table {
  width: 100%;
  table-layout: fixed;
  min-width: max-content;
}

/* Webkit scrollbar styling for horizontal scroll */
.table-body-container::-webkit-scrollbar {
  height: 12px;
  width: 12px;
}

.table-body-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 6px;
}

.table-body-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 6px;
  border: 2px solid #f1f5f9;
}

.table-body-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.table-body-container::-webkit-scrollbar-corner {
  background: #f1f5f9;
}

/* Scroll indicators for body container */
.table-body-container.scrolled-left::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 12px;
  width: 20px;
  background: linear-gradient(to right, rgba(0,0,0,0.1), transparent);
  pointer-events: none;
  z-index: 10;
}

.table-body-container.can-scroll-right::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 12px;
  width: 20px;
  background: linear-gradient(to left, rgba(0,0,0,0.1), transparent);
  pointer-events: none;
  z-index: 10;
}

.data-table-enhanced {
  width: 100%;
  min-width: max-content;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 14px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  background: white;
  table-layout: auto;
}

.header-table,
.body-table {
  width: 100%;
  min-width: max-content;
}

.header-table th,
.body-table td {
  width: auto;
  white-space: nowrap;
}

.table-header-enhanced {
  position: relative;
  z-index: 100;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  will-change: transform;
  transform: translateZ(0);
  contain: layout style;
  transition: box-shadow 0.3s ease;
  margin-top: 0;
  padding-top: 0;
}

.column-header-enhanced {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  color: white;
  padding: 16px 20px;
  text-align: center;
  font-weight: 700;
  font-size: 14px;
  white-space: nowrap;
  border-bottom: 2px solid #1a202c;
  border-top: none;
  transition: background-color 0.3s ease, transform 0.2s ease;
  user-select: none;
  position: relative;
  will-change: transform, background-color;
  transform: translateZ(0);
  contain: layout style;
  backface-visibility: hidden;
  cursor: pointer;
}

.column-header-enhanced:hover {
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
  transform: translateY(-1px) translateZ(0);
}

.column-header-enhanced.select-all-header {
  cursor: default;
}

.column-header-enhanced.select-all-header:hover {
  transform: none;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.header-text {
  font-weight: 700;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.sort-indicator {
  display: flex;
  align-items: center;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.column-header-enhanced:hover .sort-indicator {
  opacity: 1;
}

.sort-icon-active {
  color: #fbbf24;
  width: 16px;
  height: 16px;
}

.sort-icon-inactive {
  color: rgba(255, 255, 255, 0.6);
  width: 16px;
  height: 16px;
}

.select-all-container {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.select-all-checkbox-enhanced {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #10b981;
  transform: scale(1.2);
}

.select-all-label {
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table-body-enhanced {
  background: white;
}

.data-row-enhanced {
  transition: background-color 0.2s ease, transform 0.1s ease;
  will-change: background-color;
  contain: layout style;
}

.data-row-enhanced:nth-child(even) {
  background-color: #f8fafc;
}

.data-row-enhanced:hover {
  background-color: #edf2f7;
  transform: translateZ(0);
}

.data-row-enhanced.selected-row {
  background-color: #e6fffa !important;
  border-left: 4px solid #10b981;
}

.data-row-enhanced td {
  will-change: contents;
  contain: layout style;
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
  vertical-align: middle;
}

.checkbox-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.row-checkbox {
  width: 16px;
  height: 16px;
  cursor: pointer;
  accent-color: #10b981;
  transform: scale(1.1);
}

/* Enhanced Pagination Controls */
.pagination-container-enhanced {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 0 0 16px 16px;
  padding: 20px 24px;
  border-top: 1px solid #e2e8f0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  margin-top: 0;
}

.pagination-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.page-size-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-size-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  color: #4a5568;
  font-size: 14px;
}

.page-size-icon {
  width: 16px;
  height: 16px;
  color: #667eea;
}

.page-size-select {
  padding: 8px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #2d3748;
  font-weight: 600;
  cursor: pointer;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.page-size-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.page-size-text {
  color: #718096;
  font-weight: 500;
  font-size: 14px;
}

.pagination-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 2px solid #e2e8f0;
  background: white;
  color: #4a5568;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.nav-btn:hover:not(:disabled) {
  border-color: #667eea;
  background: #667eea;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f7fafc;
  color: #a0aec0;
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 4px;
}

.page-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
  padding: 0 12px;
  border: 2px solid #e2e8f0;
  background: white;
  color: #4a5568;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 14px;
}

.page-btn:hover {
  border-color: #667eea;
  background: #f0f4ff;
  color: #667eea;
  transform: translateY(-1px);
}

.page-btn-active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.page-btn-active:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.page-ellipsis {
  padding: 0 8px;
  color: #a0aec0;
  font-weight: 600;
}

.page-info-section {
  display: flex;
  align-items: center;
}

.page-info-text {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #4a5568;
  font-weight: 500;
}

.current-range {
  font-weight: 700;
  color: #2d3748;
}

.range-separator {
  color: #718096;
}

.total-items {
  font-weight: 700;
  color: #667eea;
}

.items-text {
  color: #718096;
}

/* Performance info */
.performance-info {
  margin-top: 16px;
  padding: 12px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  color: #4a5568;
  font-family: 'Monaco', 'Menlo', monospace;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

/* Responsive pagination adjustments */
@media (max-width: 768px) {
  .pagination-wrapper {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .pagination-navigation {
    justify-content: center;
  }

  .page-info-section {
    justify-content: center;
  }

  .nav-btn,
  .page-btn {
    width: 36px;
    height: 36px;
    min-width: 36px;
  }
}

@media (max-width: 576px) {
  .page-numbers {
    gap: 2px;
  }

  .nav-btn,
  .page-btn {
    width: 32px;
    height: 32px;
    min-width: 32px;
    font-size: 12px;
  }

  .page-size-section {
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }
}

/* Optimized rendering for table cells */
.search-container-enhanced,
.table-container-enhanced,
.pagination-container-enhanced {
  contain: layout style paint;
}

.data-row-enhanced td {
  contain: layout style;
}
</style>
