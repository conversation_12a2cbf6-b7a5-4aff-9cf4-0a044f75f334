<template>
  <div class="scroll-test-container">
    <h2>Enhanced Horizontal Scrolling Test</h2>
    
    <!-- Test with many columns -->
    <div class="test-section">
      <h3>Test 1: Wide Table with Many Columns</h3>
      <GenericDataTable
        :items="wideTableItems"
        :columns="wideTableColumns"
        :selectable="true"
        :show-search="true"
        :show-total-bar="true"
        :show-pagination="true"
        @horizontal-scroll="onHorizontalScroll"
      />
    </div>

    <!-- Test with long content -->
    <div class="test-section">
      <h3>Test 2: Table with Long Content</h3>
      <GenericDataTable
        :items="longContentItems"
        :columns="longContentColumns"
        :selectable="true"
        :show-search="true"
        :show-total-bar="true"
        :show-pagination="true"
        @horizontal-scroll="onHorizontalScroll"
      />
    </div>

    <!-- Scroll information display -->
    <div class="scroll-info" v-if="scrollInfo">
      <h4>Scroll Information</h4>
      <div class="info-grid">
        <div class="info-item">
          <label>Scroll Left:</label>
          <span>{{ scrollInfo.scrollLeft }}px</span>
        </div>
        <div class="info-item">
          <label>Max Scroll:</label>
          <span>{{ scrollInfo.maxScrollLeft }}px</span>
        </div>
        <div class="info-item">
          <label>Scroll Width:</label>
          <span>{{ scrollInfo.scrollWidth }}px</span>
        </div>
        <div class="info-item">
          <label>Client Width:</label>
          <span>{{ scrollInfo.clientWidth }}px</span>
        </div>
        <div class="info-item">
          <label>Scroll Percentage:</label>
          <span>{{ scrollInfo.scrollPercentage.toFixed(1) }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import GenericDataTable from "./GenericDataTable.vue";

export default {
  name: 'GenericDataTableScrollTest',
  components: {
    GenericDataTable
  },
  
  data() {
    return {
      scrollInfo: null,
      
      // Wide table with many columns
      wideTableItems: [
        {
          id: 1,
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '******-0123',
          address: '123 Main St, Anytown, USA',
          company: 'Acme Corporation',
          position: 'Software Engineer',
          department: 'Engineering',
          salary: 75000,
          startDate: '2023-01-15',
          manager: 'Jane Smith',
          skills: 'JavaScript, Vue.js, Node.js',
          projects: 'Project Alpha, Project Beta',
          status: 'Active',
          notes: 'Excellent performance, team player'
        },
        {
          id: 2,
          name: 'Jane Smith',
          email: '<EMAIL>',
          phone: '******-0124',
          address: '456 Oak Ave, Somewhere, USA',
          company: 'Tech Solutions Inc',
          position: 'Senior Developer',
          department: 'Engineering',
          salary: 85000,
          startDate: '2022-03-20',
          manager: 'Bob Johnson',
          skills: 'Python, Django, PostgreSQL',
          projects: 'Project Gamma, Project Delta',
          status: 'Active',
          notes: 'Leadership qualities, mentor to junior developers'
        },
        {
          id: 3,
          name: 'Bob Johnson',
          email: '<EMAIL>',
          phone: '******-0125',
          address: '789 Pine Rd, Elsewhere, USA',
          company: 'Innovation Labs',
          position: 'Engineering Manager',
          department: 'Engineering',
          salary: 95000,
          startDate: '2021-06-10',
          manager: 'Alice Brown',
          skills: 'Team Management, Architecture, DevOps',
          projects: 'Project Epsilon, Project Zeta',
          status: 'Active',
          notes: 'Strong technical background, excellent manager'
        }
      ],

      wideTableColumns: [
        { key: 'name', label: 'Full Name', sortable: true },
        { key: 'email', label: 'Email Address', sortable: true },
        { key: 'phone', label: 'Phone Number', sortable: true },
        { key: 'address', label: 'Home Address', sortable: true },
        { key: 'company', label: 'Company Name', sortable: true },
        { key: 'position', label: 'Job Position', sortable: true },
        { key: 'department', label: 'Department', sortable: true },
        { key: 'salary', label: 'Annual Salary', sortable: true, type: 'currency' },
        { key: 'startDate', label: 'Start Date', sortable: true, type: 'date' },
        { key: 'manager', label: 'Direct Manager', sortable: true },
        { key: 'skills', label: 'Technical Skills', sortable: true },
        { key: 'projects', label: 'Current Projects', sortable: true },
        { key: 'status', label: 'Employment Status', sortable: true },
        { key: 'notes', label: 'Performance Notes', sortable: true }
      ],

      // Long content items
      longContentItems: [
        {
          id: 1,
          title: 'Very Long Title That Extends Beyond Normal Column Width',
          description: 'This is an extremely long description that contains a lot of text and should demonstrate how the table handles content that is much wider than the typical column width. It includes multiple sentences and detailed information.',
          category: 'Long Category Name That Extends',
          tags: 'tag1, tag2, tag3, tag4, tag5, tag6, tag7, tag8, tag9, tag10',
          url: 'https://example.com/very-long-url-path/that-extends/beyond/normal/width/and-contains/multiple/segments'
        },
        {
          id: 2,
          title: 'Another Extremely Long Title With Even More Text Content',
          description: 'Another very detailed description with extensive content that should test the horizontal scrolling capabilities of the enhanced data table component.',
          category: 'Another Long Category',
          tags: 'javascript, vue, component, table, scrolling, horizontal, test, demo, example, long-content',
          url: 'https://another-example.com/with/even/longer/path/structure/for/testing/purposes'
        }
      ],

      longContentColumns: [
        { key: 'title', label: 'Long Title Column', sortable: true },
        { key: 'description', label: 'Detailed Description', sortable: true },
        { key: 'category', label: 'Category Name', sortable: true },
        { key: 'tags', label: 'Associated Tags', sortable: true },
        { key: 'url', label: 'Full URL Path', sortable: true }
      ]
    };
  },

  methods: {
    onHorizontalScroll(scrollData) {
      this.scrollInfo = scrollData;
      console.log('Horizontal scroll event:', scrollData);
    }
  }
};
</script>

<style scoped>
.scroll-test-container {
  padding: 20px;
  max-width: 100%;
  overflow: hidden;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background: #f8fafc;
}

.test-section h3 {
  margin-top: 0;
  color: #2d3748;
  border-bottom: 2px solid #667eea;
  padding-bottom: 8px;
}

.scroll-info {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border: 2px solid #667eea;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 250px;
  z-index: 1000;
}

.scroll-info h4 {
  margin: 0 0 12px 0;
  color: #2d3748;
  font-size: 16px;
}

.info-grid {
  display: grid;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  border-bottom: 1px solid #e2e8f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item label {
  font-weight: 600;
  color: #4a5568;
  font-size: 14px;
}

.info-item span {
  font-weight: 500;
  color: #667eea;
  font-size: 14px;
}

@media (max-width: 768px) {
  .scroll-info {
    position: relative;
    top: auto;
    right: auto;
    margin-top: 20px;
  }
}
</style>
