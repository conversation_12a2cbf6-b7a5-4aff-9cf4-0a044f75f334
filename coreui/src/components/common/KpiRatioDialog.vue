<template>
  <v-dialog v-model="dialog" :max-width="options.width" :style="{ zIndex: options.zIndex }" @keydown.esc="cancel">
    <v-card>
      <v-toolbar dark :color="options.color" dense flat>
        <v-toolbar-title class="white--text">{{ title }}</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="cancel">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <c-card class="card-table">
        <c-card-body class="card-scroll">
          <div class="row">
            <div class="col">
              <CInput label="From" type="number" placeholder="From" v-model="from" />
            </div>
            <div class="col">
              <CInput label="TO" type="number" placeholder="TO" v-model="to" />
            </div>
            <div class="col">
              <CInput label="Value" type="number" placeholder="Value" v-model="value" />
            </div>
          </div>
        </c-card-body>
        <c-card-footer>
          <CButton class="text-white" color="primary" @click="save">Save</CButton>
        </c-card-footer>
      </c-card>
    </v-card>
  </v-dialog>
</template>

<script>
import { mapActions } from "vuex";
export default {
  data() {
    return {
      dialog: false,
      resolve: null,
      reject: null,
      item: null,
      from: null,
      to: null,
      value: null,
      title: null,
      options: {
        color: "primary",
        width: 1400,
        zIndex: 1000000000,
      },
      readMore: {},
    };
  },
  emits: ["downloaded"],
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open(title, item, options = null) {
      this.loadVApp(); ``
      this.dialog = true;
      this.title = title;
      this.item = item;
      console.log(this.item);

      this.options = Object.assign(this.options, options || this.options);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    agree() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp();
    },
    cancel() {
      this.from = null;
      this.to = null;
      this.value = null;
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp();
    },
    save() {
      axios
        .post(`/api/save-kpi-percent/`, {
          kpiRatio: this.item,
          from: this.from,
          to: this.to,
          value: this.value,
        })
        .then((response) => {
          this.flash('Kpi Percent Saved Successfully');
          this.cancel();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
};
</script>

<style scoped>
.card-table {
  position: relative;
}

.card-scroll {
  max-height: 450px;
  overflow: auto;
  margin-top: 20px;
}
</style>