<template>
  <div class="row">
    <div class="col-8">
      <CInput
        class=""
        label="Keywords"
        type="text"
        placeholder="Keywords"
        v-model="newKeywordName"
        @keyup.enter="addKeyword"
        @input="handleInput"
      >
        <template slot="label">
          Keywords <span style="color: red">*</span>
        </template>
      </CInput>
      <div class="ml-1 row">
        <div
          class="mb-1 mr-1 chip"
          v-for="(keyword, index) in keywords"
          :key="index"
        >
          {{ keyword }}
          <button class="custom-btn" @click="removeKeyword(index)">
            <CIcon class="icon" name="cil-x-circle" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  prop: ["value"],
  data() {
    return {
      newKeywordName: null,
      keywords: [],
    };
  },
  methods: {
    initialize(){
      this.newKeywordName=null
      this.keywords=[]
    },
    addKeyword() {
      const keyword = this.newKeywordName;
      this.keywords.push(keyword);
      this.newKeywordName = null;
    },
    removeKeyword(index) {
      this.keywords.splice(index, 1);
    },
    handleInput(e) {
      this.$emit("input", this.keywords);
    },
  },
  created() {
    this.initialize()
  }
};
</script>

<style scoped>
.chip {
  background-color: hsl(231, 44%, 56%);
  color: white;
  border-radius: 0.2rem;
  padding: 0.2rem;
  text-align: center;
}
.custom-btn {
  background: transparent;
  border: none;
  outline: none;
}
.icon{
  color:white;
}
</style>
