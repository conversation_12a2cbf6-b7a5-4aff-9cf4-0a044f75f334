<template>
  <v-dialog v-model="dialog" :max-width="options.width" :style="{ zIndex: options.zIndex }" @keydown.esc="cancel">
    <v-card>
      <v-toolbar dark :color="options.color" dense flat>
        <v-toolbar-title class="white--text">{{ title }}</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="cancel">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <c-card class="card-table">
        <c-card-body class="card-scroll">
          <div class="row">
            <div class="col">
              <!-- <template> -->
              <c-input label="Quantity" type="text" v-model="quantity" />
              <!-- </template> -->
            </div>
          </div>
        </c-card-body>
        <c-card-footer>
          <c-button title="edit" color="success" style="float: right" square @click="save()">Save
          </c-button>
        </c-card-footer>
      </c-card>
    </v-card>
  </v-dialog>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      dialog: false,
      resolve: null,
      reject: null,
      quantity: 0,
      item: null,
      fields: [],
      title: null,
      options: {
        color: "primary",
        width: 800,
        zIndex: 1000000000,
      },
    };
  },
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open(title, data, fields = [], options = null) {
      this.loadVApp();
      this.dialog = true;
      this.title = title;
      this.item = data;
      this.quantity = data.quantity;
      this.options = Object.assign(this.options, options || this.options);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    agree() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp();
    },
    cancel() {
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp();
    },
    save() {
      console.log(this.item);
      
      axios
        .post(`/api/edit-samples`, {
          id: this.item.id,
          quantity: this.quantity,
        })
        .then((response) => {
          this.flash("Samples Updated Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      this.cancel();
    },
  },
};
</script>

<style scoped>
.card-table {
  position: relative;
}

.card-scroll {
  max-height: 450px;
  overflow: auto;
  margin-top: 20px;
}
</style>