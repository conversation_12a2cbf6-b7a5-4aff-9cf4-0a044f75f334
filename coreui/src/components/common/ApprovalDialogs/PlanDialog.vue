<template>
  <v-dialog v-model="dialog" :max-width="options.width" :style="{ zIndex: options.zIndex }" @keydown.esc="cancel">
    <v-card>
      <v-toolbar dark :color="options.color" dense flat>
        <v-toolbar-title class="white--text">{{ title }}</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="cancel">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <c-card class="card-table">
        <c-card-body class="card-scroll">
          <c-form-group>
            <template #input>
              <input class="m-1" id="checkAllVisits" type="checkbox" v-model="checkAllVisits" title="Check All"
                @change="checkAllApproval" />
              <label for="checkAllVisits">Select All</label>
            </template>
          </c-form-group>
          <c-data-table hover header tableFilter striped sorter footer :items="items" :fields="fields"
            :items-per-page="500" :active-page="1" :responsive="true" pagination thead-top>
            <template slot="thead-top">
              <td style="border-top: none"><strong>Total</strong></td>
              <td style="border-top: none" class="text-xs-right">
                {{ items.length }}
              </td>
            </template>
            <template #s="{ item }">
              <td>
                <input class="m-1" type="checkbox" v-model="selected" :value="item" title="Check One Visit" />
              </td>
            </template>
            <template #pharmacies="{ item }">
              <td v-if="item.pharmacies == 0">
                <strong style="color: red">{{ item.pharmacies }}</strong>
              </td>
              <td v-else>
                <strong style="color: green">{{ item.pharmacies }}</strong>
              </td>
            </template>
            <template #date="{ item }">
              <td>{{ item.date }}</td>
            </template>
            <template #f_u="{ item }">
              <td>
                <v-tooltip bottom color="primary">
                  <template v-slot:activator="{ on, attrs }">
                    <c-button color="primary" class="btn-sm mt-2 mr-1 text-white" v-bind="attrs" v-on="on">
                      <c-icon name="cil-user-follow" />
                    </c-button>
                  </template>
                  <span>{{ item.f_u }}</span>
                </v-tooltip>
              </td>
            </template>
            <template #actions="{ item }">
              <td style="width: 500px">
                <div class="row" style="margin-left: 0px; margin-right: 0px">
                  <div class="col">
                    <v-select v-model="item.reason_id" :options="item.reasons" :reduce="(reason) => reason.id"
                      label="reason" :value="0" placeholder="Select Reason" v-if="item.approval == null" class="mt-0" />
                  </div>
                </div>
              </td>
            </template>
          </c-data-table>
        </c-card-body>
        <c-card-footer>
          <c-button title="Approve" color="primary" style="float: right" square @click="acceptApprovals()">Approve
          </c-button>
          <c-button class="mr-2" style="float: right" title="Reject" color="danger" square
            @click="rejectApprovals()">Disapprove
          </c-button>
        </c-card-footer>
      </c-card>
    </v-card>
  </v-dialog>
</template>

<script>
import { mapActions } from "vuex";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      dialog: false,
      resolve: null,
      reload: null,
      widget: null,
      reject: null,
      items: [],
      userData: {},
      fields: [],
      title: null,
      options: {
        color: "primary",
        width: 1300,
        zIndex: 1000000000,
      },
      reasons: [],
      selected: [],
      checkAllVisits: false,
    };
  },
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open(title, data, fields = [], userData, widget = null, reloadFn = null, options = null) {
      this.loadVApp();
      this.dialog = true;
      this.title = title;
      this.items = data;
      this.userData = userData;
      if (fields.length == 0) {
        this.fields = Object.keys(this.items[0]);
      } else {
        this.fields = fields;
      }
      this.widget = widget;
      if (reloadFn) {
        this.reload = reloadFn; // Store the passed function
      }
      this.options = Object.assign(this.options, options || this.options);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    agree() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp();
    },
    cancel() {
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp();
    },
    checkAllApproval() {
      this.selected = [];
      if (this.checkAllVisits) {
        for (let i in this.items) {
          this.selected.push(this.items[i]);
        }
      }
    },
    reloadWidget() {
      if (this.reload && typeof this.reload === 'function') {
        this.reload(this.widget.id);
      }
    },
    getVisits(userData) {
      axios
        .post(`/api/getPlanVisitsForApproval`, {
          user: userData.id,
          line: userData.line,
          flag: 1,
        })
        .then((response) => {
          if (this.title == "Visit Approvals") this.items = response.data.plans;
          else this.items = response.data.ow;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    acceptApprovals() {
      let visits = this.selected.map((item) => {
        return {
          visitable_id: item.plan,
          visitable_type: item.visitable_type,
          reason_id: item.reason_id,
        };
      });
      axios
        .post("/api/plan/accept/", {
          visits,
        })
        .then(() => {
          this.selected = [];
          this.checkAllVisits = false;
          this.getVisits(this.userData);
          this.reloadWidget();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    rejectApprovals() {
      let plans = this.selected.map((item) => {
        return {
          visitable_id: item.plan,
          visitable_type: item.visitable_type,
          reason_id: item.reason_id,
        };
      });
      axios
        .post("/api/plan/reject/", {
          plans,
        })
        .then((response) => {
          this.selected = [];
          this.checkAllVisits = false;
          this.getVisits(this.userData);
          this.reloadWidget();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
};
</script>

<style scoped>
.card-table {
  position: relative;
}

.card-scroll {
  max-height: 450px;
  overflow: auto;
  margin-top: 20px;
}
</style>