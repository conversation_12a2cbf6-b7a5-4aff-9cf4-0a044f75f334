<template>
  <v-dialog v-model="dialog" :max-width="options.width" :style="{ zIndex: options.zIndex }" @keydown.esc="cancel">
    <v-card>
      <v-toolbar dark :color="options.color" dense flat>
        <v-toolbar-title class="white--text">{{ title }}</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="cancel">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <c-card class="card-table">
        <c-card-body class="card-scroll">
          <c-form-group>
            <template #input>
              <input class="m-1" id="checkAllCommercials" type="checkbox" v-model="checkAllCommercials"
                title="Check All" @change="checkAllApproval" />
              <label for="checkAllCommercials">Select All</label>
            </template>
          </c-form-group>
          <c-data-table hover header add-table-classes="commercial-dialog-classes" tableFilter striped sorter footer
            :items="items" :fields="fields" :items-per-page="500" :active-page="1" :responsive="true" pagination
            thead-top>
            <template slot="thead-top">
              <td style="border-top: none"><strong>Total</strong></td>
              <td style="border-top: none" class="text-xs-right">
                {{ items.length }}
              </td>
            </template>
            <template #s="{ item }">
              <td>
                <input class="m-1" type="checkbox" v-model="selected" :value="item" title="Check One" />
              </td>
            </template>
            <template #desc="{ item }">
              <td @click="showMore(item.id)" v-if="!readMore[item.id]">
                {{ item.desc.substring(0, 30) + ".." }} <br /><span style="color: blue; cursor: pointer">show
                  more</span>
              </td>
              <td @click="showLess(item.id)" v-if="readMore[item.id]">
                {{ item.desc }} <br />
                <span style="color: blue; cursor: pointer">show less</span>
              </td>
            </template>
            <template #approvals="{ item }">
              <td>
                <CButton color="warning" variant="outline" square size="sm" @click="showApprovalDetails(item)">
                  show
                </CButton>
              </td>
            </template>
            <template #prod="{ item }">
              <td>
                <CButton color="dark" variant="outline" square size="sm" @click="showProductDetails(item)">
                  show
                </CButton>
              </td>
            </template>
            <template #doctor="{ item }">
              <td>
                <CButton color="primary" variant="outline" square size="sm" @click="showDoctorDetails(item)">
                  Show
                </CButton>
              </td>
            </template>

            <template #emp="{ item }">
              <td>
                <CButton color="success" variant="outline" square size="sm" @click="showEmployeeDetails(item)">
                  show
                </CButton>
              </td>
            </template>
            <template #date="{ item }">
              <td>{{ item.date }}</td>
            </template>
            <template #fb="{ item }">
              <td @click="showMoreFeedback(item.id)" v-if="!readMoreFeedback[item.id]">
                {{ item.fb.substring(0, 15) + ".." }} <br /><span style="color: blue; cursor: pointer">show more</span>
              </td>
              <td @click="showLessFeedback(item.id)" v-if="readMoreFeedback[item.id]">
                {{ item.fb }} <br />
                <span style="color: blue; cursor: pointer">show less</span>
              </td>
            </template>
            <template #reason="{ item }">
              <td style="width: 700px">
                <div class="row" style="margin-left: 0px; margin-right: 0px">
                  <div class="col">
                    <v-select v-model="item.reason_id" :options="item.reasons" :reduce="(reason) => reason.id"
                      label="reason" :value="0" placeholder="Select Reason" class="mt-0" />
                  </div>
                </div>
              </td>
            </template>
            <template #comment="{ item }">
              <td>
                <div class="row" style="margin-left: 0px; margin-right: 0px">
                  <div class="col">
                    <c-textarea class="mt-2" type="text" placeholder="Comment" v-model="item.comment"></c-textarea>
                  </div>
                </div>
              </td>
            </template>
          </c-data-table>
        </c-card-body>
        <c-card-footer>
          <c-button title="Approve" color="primary" style="float: right" square @click="acceptApprovals()">Approve
          </c-button>
          <c-button class="mr-2" style="float: right" title="Reject" color="danger" square
            @click="rejectApprovals()">Disapprove
          </c-button>
        </c-card-footer>
      </c-card>
    </v-card>
  </v-dialog>
</template>

<script>
import { mapActions } from "vuex";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      dialog: false,
      resolve: null,
      reject: null,
      items: [],
      userData: {},
      fields: [],
      title: null,
      options: {
        color: "primary",
        width: 1500,
        zIndex: 1000000000,
      },
      reasons: [],
      selected: [],
      checkAllCommercials: false,
      readMore: {},
      readMoreFeedback: {},
    };
  },
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open(title, data, fields = [], userData, options = null) {
      this.loadVApp();
      this.dialog = true;
      this.title = title;
      this.items = data;
      this.userData = userData;
      if (fields.length == 0) {
        this.fields = Object.keys(this.items[0]);
      } else {
        this.fields = fields;
      }
      this.options = Object.assign(this.options, options || this.options);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    agree() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp();
    },
    cancel() {
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp();
    },
    showMore(id) {
      this.$set(this.readMore, id, true);
    },
    showLess(id) {
      this.$set(this.readMore, id, false);
    },
    showMoreFeedback(id) {
      this.$set(this.readMoreFeedback, id, true);
    },
    showLessFeedback(id) {
      this.$set(this.readMoreFeedback, id, false);
    },
    showProductDetails(item) {
      axios
        .post("/api/get-products-data", item)
        .then((response) => {
          const products = response.data.data;
          this.$root.$table("Commercial Product Details", products);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    showApprovalDetails(item) {
      axios
        .post("/api/commercial-approval-data", item)
        .then((response) => {
          const approvals = response.data.data;
          this.$root.$table("Commercial Approval", approvals);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    showDoctorDetails(item) {
      axios
        .post("/api/get-doctors-data", item)
        .then((response) => {
          const doctors = response.data.data.doctors;
          const outOfList = response.data.data.outOfList;
          this.$root.$table("Commercial Doctor Details", doctors, [],
            outOfList);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    showEmployeeDetails(item) {
      axios
        .post("/api/get-users-data", item)
        .then((response) => {
          const employees = response.data.data;
          this.$root.$table("Commercial Employee Details", employees);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllApproval() {
      this.selected = [];
      if (this.checkAllCommercials) {
        for (let i in this.items) {
          this.selected.push(this.items[i]);
        }
      }
    },
    getCommercials(userData) {
      axios
        .post(`/api/get-commercials`, {
          user: userData.id,
          flag: 1,
        })
        .then((response) => {
          this.items = response.data.data.commercials;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    acceptApprovals() {
      let commercials = this.selected.map((item) => {
        return {
          visitable_id: item.commercial,
          visitable_type: item.visitable_type,
          reason_id: item.reason_id,
          required: item.required,
          comment: item.comment,
        };
      });
      axios
        .post("/api/commercial/accept/", {
          commercials,
        })
        .then(() => {
          this.selected = [];
          this.checkAllCommercials = false;
          this.getCommercials(this.userData);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    rejectApprovals() {
      let commercials = this.selected.map((item) => {
        return {
          visitable_id: item.commercial,
          visitable_type: item.visitable_type,
          reason_id: item.reason_id,
          required: item.required,
          comment: item.comment,
        };
      });
      axios
        .post("/api/commercial/reject/", {
          commercials,
        })
        .then((response) => {
          this.selected = [];
          this.checkAllCommercials = false;
          this.getCommercials(this.userData);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
};
</script>

<style>
.card-table {
  position: relative;
}

.card-scroll {
  max-height: 450px;
  overflow: auto;
  margin-top: 20px;
}

.commercial-dialog-classes>tbody>tr>td {
  width: 300px;
}

.commercial-dialog-classes>thead>tr>th:nth-last-child(3)>div {
  width: 300px;
}
</style>