<template>
  <v-dialog
    v-model="dialog"
    :max-width="options.width"
    :style="{ zIndex: options.zIndex }"
    @keydown.esc="cancel"
  >
    <v-card>
      <v-toolbar dark :color="options.color" dense flat>
        <v-toolbar-title class="white--text">{{ title }}</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="cancel">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <c-card class="card-table">
        <c-card-body class="card-scroll">
          <c-form-group>
            <template #input>
              <input
                class="m-1"
                id="checkAllVacations"
                type="checkbox"
                v-model="checkAllVacations"
                title="Check All"
                @change="checkAllApproval"
              />
              <label for="checkAllVacations">Select All</label>
            </template>
          </c-form-group>
          <c-data-table
            hover
            header
            tableFilter
            striped
            sorter
            footer
            :items="items"
            :fields="fields"
            :items-per-page="500"
            :active-page="1"
            :responsive="true"
            pagination
            thead-top
          >
            <template slot="thead-top">
              <td style="border-top: none"><strong>Total</strong></td>
              <td style="border-top: none" class="text-xs-right">
                {{ items.length }}
              </td>
            </template>
            <template #s="{ item }">
              <td>
                <input
                  class="m-1"
                  type="checkbox"
                  v-model="selected"
                  :value="item"
                  title="Check One"
                />
              </td>
            </template>
            <template #date="{ item }">
              <td>{{ item.date }}</td>
            </template>
            <template #actions="{ item }">
              <td style="width: 300px;">
                <div class="row" style="margin-left: 0px; margin-right: 0px">
                  <div class="col">
                    <v-select
                      v-model="item.reason_id"
                      :options="item.reasons"
                      :reduce="(reason) => reason.id"
                      label="reason"
                      :value="0"
                      placeholder="Select Reason"
                      v-if="item.approval == null"
                      class="mt-0"
                    />
                  </div>
                </div>
              </td>
            </template>
            <template #approvals="{ item }">
              <td>
                <CButton
                  color="warning"
                  variant="outline"
                  square
                  size="sm"
                  @click="showApprovalDetails(item)"
                >
                  show
                </CButton>
              </td>
            </template>
          </c-data-table>
        </c-card-body>
        <c-card-footer>
          <c-button
            title="Approve"
            color="primary"
            style="float: right"
            square
            @click="acceptApprovals()"
            >Approve
          </c-button>
          <c-button
            class="mr-2"
            style="float: right"
            title="Reject"
            color="danger"
            square
            @click="rejectApprovals()"
            >Disapprove
          </c-button>
        </c-card-footer>
      </c-card>
    </v-card>
  </v-dialog>
</template>

<script>
import { mapActions } from "vuex";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      dialog: false,
      resolve: null,
      reject: null,
      items: [],
      userData: {},
      fields: [],
      title: null,
      options: {
        color: "primary",
        width: 1000,
        zIndex: 1000000000,
      },
      reasons: [],
      selected: [],
      checkAllVacations: false,
    };
  },
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open(title, data, fields = [], userData, options = null) {
      this.loadVApp();
      this.dialog = true;
      this.title = title;
      this.items = data;
      this.userData = userData;
      if (fields.length == 0) {
        this.fields = Object.keys(this.items[0]);
      } else {
        this.fields = fields;
      }
      this.options = Object.assign(this.options, options || this.options);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    agree() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp();
    },
    cancel() {
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp();
    },
    checkAllApproval() {
      this.selected = [];
      if (this.checkAllVacations) {
        for (let i in this.items) {
          this.selected.push(this.items[i]);
        }
      }
    },
    showApprovalDetails(item) {
      axios
        .post("/api/vacation-approval-data", item)
        .then((response) => {
          const approvals = response.data.data;
          this.$root.$table("Vacation Approval", approvals);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getVacations(userData) {
      axios
        .post(`/api/get-vacations`, {
          user: userData.id,
          flag: 1,
        })
        .then((response) => {
          this.items = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    acceptApprovals() {
      let vacations = this.selected.map((item) => {
        return {
          visitable_id: item.vacation,
          visitable_type: item.visitable_type,
          reason_id: item.reason_id,
          required: item.required,
        };
      });
      axios
        .post("/api/vacation/accept/", {
          vacations,
        })
        .then(() => {
          this.selected = [];
          this.checkAllVacations = false;
          if (this.title == "Visit Approvals") this.getVisits(this.userData);
          else this.getVacations(this.userData);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    rejectApprovals() {
      let vacations = this.selected.map((item) => {
        return {
          visitable_id: item.vacation,
          visitable_type: item.visitable_type,
          reason_id: item.reason_id,
          required: item.required,
        };
      });
      axios
        .post("/api/vacation/reject/", {
          vacations,
        })
        .then((response) => {
          this.selected = [];
          this.checkAllVacations = false;
          this.getVacations(this.userData);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
};
</script>

<style scoped>
.card-table {
  position: relative;
}
.card-scroll {
  max-height: 450px;
  overflow: auto;
  margin-top: 20px;
}
</style>