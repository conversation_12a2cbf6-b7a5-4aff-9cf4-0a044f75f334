<template>
  <v-dialog
    v-model="dialog"
    :max-width="options.width"
    :style="{ zIndex: options.zIndex }"
    @keydown.esc="cancel"
  >
    <v-card>
      <v-toolbar dark :color="options.color" dense flat>
        <v-toolbar-title class="white--text">{{ title }}</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="cancel">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <c-card class="card-table">
        <c-card-body class="card-scroll">
          <div class="row">
            <div class="col-6">
              <c-form-group>
                <template #label>
                  <strong>Division</strong>
                </template>
                <template #input>
                  <v-select
                    placeholder="Search for Line"
                    v-model="division_id"
                    :options="divisions"
                    label="name"
                    :value="0"
                    class="mt-2"
                    :reduce="(line) => line.id"
                  ></v-select>
                </template>
              </c-form-group>
            </div>
          </div>    
        </c-card-body>
        <c-card-footer
          ><c-button
            style="float: right"
            class="text-white"
            color="primary"
            @click="save()"
            >Save</c-button
          >
        </c-card-footer>
      </c-card>
    </v-card>
  </v-dialog>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { mapActions } from "vuex";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      dialog: false,
      resolve: null,
      reject: null,
      divisions: [],
      fields: [],
      division_id: null,
      option_id: [],
      title: null,
      options: {
        color: "primary",
        width: 900,
        height: 600,
        zIndex: 1000000000,
      },
      readMore: {},
    };
  },
  emits: ["getVisitDivision"],
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open(title, data, fields = [], options = null) {
      this.loadVApp();
      this.dialog = true;
      this.title = title;
      this.divisions = data;
      this.fields = fields;
      this.options = Object.assign(this.options, options || this.options);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    agree() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp();
    },
    cancel() {
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp();
    },
    save() {
      this.$emit("getVisitDivision", this.division_id);
    },
  },
};
</script>

<style scoped>
.card-table {
  position: relative;
}
.card-scroll {
  max-height: 450px;
  overflow: auto;
  margin-top: 20px;
}
</style>