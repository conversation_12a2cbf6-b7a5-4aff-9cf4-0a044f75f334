<template>
  <v-dialog v-model="dialog" :max-width="options.width" :style="{ zIndex: options.zIndex }" @keydown.esc="cancel">
    <v-card>
      <v-toolbar dark :color="options.color" dense flat>
        <v-toolbar-title class="white--text">{{ title }}</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="cancel">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <c-card class="card-table">
        <c-card-body class="card-scroll">
          <div style="float: right; width: 300px" v-if="mainApproval != null">
            <Strong>Commercial Approval:</Strong>
            <c-form-group>
              <template #input>
                <v-select title="Search for option" v-model="mainApproval" :options="types" label="name" :value="0"
                  class="mt-3" :reduce="(type) => type.status"></v-select>
              </template>
            </c-form-group>
          </div>
          <c-data-table hover header tableFilter striped sorter footer :items="items" :fields="fields"
            :items-per-page="500" :active-page="1" :responsive="true" pagination thead-top>
            <template slot="thead-top">
              <td style="border-top: none"><strong>Total</strong></td>
              <td style="border-top: none" class="text-xs-right">
                {{ items.length }}
              </td>
            </template>
            <template #status="{ item }">
              <td>
                <strong v-if="item.status == 1" style="color: green">Approved</strong>
                <strong v-if="item.status == 0" style="color: red">Dispproved</strong>
                <strong v-if="item.status == null" style="color: blue">Pending</strong>
              </td>
            </template>
            <template #delete="{ item }">
              <td>
                <div class="row justify-content-center" style="margin-bottom: 10px">
                  <c-button color="danger" class="btn-sm mt-2 mr-1" v-if="checkPermission('reset_commercial_approvals')"
                    @click="
                      $root
                        .$confirm(
                          'Delete',
                          'Do you want to delete this record?',
                          {
                            color: 'red',
                            width: 290,
                            zIndex: 200,
                          }
                        )
                        .then((confirmed) => {
                          if (confirmed) {
                            deleteCommercialApproval(item);
                          }
                        })
                      "><c-icon name="cil-trash" /></c-button>
                </div>
              </td>
            </template>
            <template #actions="{ item }">
              <td>
                <div class="row justify-content-center" style="margin-bottom: 10px">
                  <c-form-group>
                    <template #input>
                      <v-select title="Search for option" v-model="item.status" :options="types" label="name" :value="0"
                        class="mt-3" :reduce="(type) => type.status"></v-select>
                    </template>
                  </c-form-group>
                </div>
              </td>
            </template>
          </c-data-table>
        </c-card-body>
        <c-card-footer>
          <c-button color="primary" @click="update" style="float: right">Update</c-button>
        </c-card-footer>
      </c-card>
    </v-card>
  </v-dialog>
</template>

<script>
import { mapActions } from "vuex";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      dialog: false,
      resolve: null,
      reject: null,
      items: [],
      types: [
        { status: 1, name: "Approved" },
        { status: 0, name: "Dispproved" },
      ],
      mainApproval: null,
      fields: [],
      title: null,
      options: {
        color: "primary",
        width: 1400,
        zIndex: 1000000000,
      },
      readMore: {},
    };
  },
  emits: ["downloaded"],
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open(title, data, fields = [], mainApproval, options = null) {
      this.loadVApp();
      this.dialog = true;
      this.title = title;
      this.items = data;
      if (fields.length == 0) {
        this.fields = Object.keys(this.items[0]);
      } else {
        this.fields = fields;
      }
      this.mainApproval = mainApproval;
      this.options = Object.assign(this.options, options || this.options);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },

    agree() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp();
    },
    cancel() {
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp();
    },
    removeCommercialApproval(item) {
      const index = this.items.findIndex((type) => item.id == type.id);
      this.items.splice(index, 1);
    },
    deleteCommercialApproval(item) {
      axios
        .delete(`/api/commercial/approval/detail/${item.id}`)
        .then((res) => {
          this.removeCommercialApproval(item);
          this.flash("Commercial Approval Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
    update() {
      axios
        .post(`/api/update-commercial-flow`, {
          approvals: this.items,
          mainApproval: this.mainApproval,
        })
        .then((res) => {
          this.flash("Manager Approval Updated Successfully");
          this.cancel();
        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
  },
};
</script>

<style scoped>
.card-table {
  position: relative;
}

.card-scroll {
  max-height: 450px;
  overflow: auto;
  margin-top: 20px;
}
</style>