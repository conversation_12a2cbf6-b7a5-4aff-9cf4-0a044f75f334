<template>
  <div>
    <h2>Generic Data Table Examples</h2>
    
    <!-- Example 1: Basic Table -->
    <div class="example-section">
      <h3>Example 1: Basic Table</h3>
      <GenericDataTable
        :items="basicItems"
        :columns="basicColumns"
        :selectable="false"
        :show-search="true"
        :show-total-bar="true"
        :show-pagination="true"
      />
    </div>

    <!-- Example 2: Table with Selection -->
    <div class="example-section">
      <h3>Example 2: Table with Selection</h3>
      <GenericDataTable
        :items="basicItems"
        :columns="basicColumns"
        :selectable="true"
        :selected-items="selectedItems"
        @selection-change="onSelectionChange"
      />
      <div class="selection-info">
        <p>Selected Items: {{ selectedItems.length }}</p>
        <pre>{{ JSON.stringify(selectedItems.map(item => item.name), null, 2) }}</pre>
      </div>
    </div>

    <!-- Example 3: Table with Custom Slots -->
    <div class="example-section">
      <h3>Example 3: Table with Custom Slots</h3>
      <GenericDataTable
        :items="advancedItems"
        :columns="advancedColumns"
        :selectable="true"
      >
        <!-- Custom status rendering -->
        <template #status="{ value }">
          <span :class="getStatusClass(value)">
            <CIcon :name="getStatusIcon(value)" />
            {{ value }}
          </span>
        </template>

        <!-- Custom actions column -->
        <template #actions="{ item }">
          <div class="action-buttons">
            <CButton color="primary" size="sm" @click="editItem(item)">
              <CIcon name="cil-pencil" />
            </CButton>
            <CButton color="danger" size="sm" @click="deleteItem(item)">
              <CIcon name="cil-trash" />
            </CButton>
          </div>
        </template>

        <!-- Custom date formatting -->
        <template #created_at="{ value }">
          <span class="date-formatted">{{ formatRelativeDate(value) }}</span>
        </template>

        <!-- Custom progress bar -->
        <template #progress="{ value }">
          <div class="progress-container">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: value + '%' }"></div>
            </div>
            <span class="progress-text">{{ value }}%</span>
          </div>
        </template>
      </GenericDataTable>
    </div>

    <!-- Example 4: Performance Table (Large Dataset) -->
    <div class="example-section">
      <h3>Example 4: Performance Table (Large Dataset)</h3>
      <div class="performance-controls">
        <CButton @click="generateLargeDataset(1000)" color="info">Generate 1K Items</CButton>
        <CButton @click="generateLargeDataset(10000)" color="warning">Generate 10K Items</CButton>
        <CButton @click="generateLargeDataset(50000)" color="danger">Generate 50K Items</CButton>
      </div>
      <GenericDataTable
        :items="performanceItems"
        :columns="performanceColumns"
        :selectable="true"
        :is-loading="isGenerating"
        :initial-page-size="50"
        :page-size-options="[25, 50, 100, 200, 500]"
        search-placeholder="Search in large dataset..."
      />
    </div>
  </div>
</template>

<script>
import GenericDataTable from "./GenericDataTable.vue";
import moment from "moment";

export default {
  name: 'GenericDataTableExample',
  components: {
    GenericDataTable
  },
  
  data() {
    return {
      selectedItems: [],
      isGenerating: false,
      
      // Basic example data
      basicItems: [
        { id: 1, name: 'John Doe', email: '<EMAIL>', role: 'Admin', created_at: '2023-01-15' },
        { id: 2, name: 'Jane Smith', email: '<EMAIL>', role: 'User', created_at: '2023-02-20' },
        { id: 3, name: 'Bob Johnson', email: '<EMAIL>', role: 'Manager', created_at: '2023-03-10' },
        { id: 4, name: 'Alice Brown', email: '<EMAIL>', role: 'User', created_at: '2023-04-05' },
        { id: 5, name: 'Charlie Wilson', email: '<EMAIL>', role: 'Admin', created_at: '2023-05-12' }
      ],
      
      basicColumns: [
        { key: 'name', label: 'Full Name', sortable: true },
        { key: 'email', label: 'Email Address', sortable: true },
        { key: 'role', label: 'Role', sortable: true },
        { key: 'created_at', label: 'Created Date', sortable: true, type: 'date' },
        { key: 'a7a', label: 'Created Date', sortable: true, type: 'date' },
        { key: 'ahmed', label: 'Created Date', sortable: true, type: 'date' },
        { key: 'said', label: 'Created Date', sortable: true, type: 'date' },
      ],

      // Advanced example data
      advancedItems: [
        { 
          id: 1, 
          name: 'Project Alpha', 
          status: 'Active', 
          progress: 75, 
          created_at: '2023-01-15T10:30:00Z',
          budget: 50000
        },
        { 
          id: 2, 
          name: 'Project Beta', 
          status: 'Pending', 
          progress: 30, 
          created_at: '2023-02-20T14:15:00Z',
          budget: 75000
        },
        { 
          id: 3, 
          name: 'Project Gamma', 
          status: 'Completed', 
          progress: 100, 
          created_at: '2023-03-10T09:45:00Z',
          budget: 120000
        },
        { 
          id: 4, 
          name: 'Project Delta', 
          status: 'Cancelled', 
          progress: 15, 
          created_at: '2023-04-05T16:20:00Z',
          budget: 25000
        }
      ],

      advancedColumns: [
        { key: 'name', label: 'Project Name', sortable: true },
        { key: 'status', label: 'Status', sortable: true },
        { key: 'progress', label: 'Progress', sortable: true },
        { key: 'created_at', label: 'Created', sortable: true, type: 'date' },
        { key: 'budget', label: 'Budget', sortable: true, type: 'currency' },
        { key: 'actions', label: 'Actions', sortable: false }
      ],

      // Performance testing data
      performanceItems: [],
      performanceColumns: [
        { key: 'id', label: 'ID', sortable: true },
        { key: 'name', label: 'Name', sortable: true },
        { key: 'category', label: 'Category', sortable: true },
        { key: 'value', label: 'Value', sortable: true, type: 'number' },
        { key: 'created_at', label: 'Created', sortable: true, type: 'date' }
      ]
    };
  },

  methods: {
    onSelectionChange(selectedItems) {
      this.selectedItems = selectedItems;
      console.log('Selection changed:', selectedItems);
    },

    getStatusClass(status) {
      const classes = {
        'Active': 'status-active',
        'Pending': 'status-pending',
        'Completed': 'status-completed',
        'Cancelled': 'status-cancelled'
      };
      return classes[status] || 'status-default';
    },

    getStatusIcon(status) {
      const icons = {
        'Active': 'cil-media-play',
        'Pending': 'cil-clock',
        'Completed': 'cil-check-circle',
        'Cancelled': 'cil-x-circle'
      };
      return icons[status] || 'cil-info';
    },

    formatRelativeDate(dateString) {
      return moment(dateString).fromNow();
    },

    editItem(item) {
      alert(`Edit item: ${item.name}`);
    },

    deleteItem(item) {
      if (confirm(`Delete item: ${item.name}?`)) {
        console.log('Delete item:', item);
      }
    },

    generateLargeDataset(count) {
      this.isGenerating = true;
      
      // Simulate async data generation
      setTimeout(() => {
        const categories = ['Electronics', 'Clothing', 'Books', 'Home', 'Sports', 'Toys'];
        const items = [];
        
        for (let i = 1; i <= count; i++) {
          items.push({
            id: i,
            name: `Item ${i}`,
            category: categories[Math.floor(Math.random() * categories.length)],
            value: Math.floor(Math.random() * 1000) + 1,
            created_at: moment().subtract(Math.floor(Math.random() * 365), 'days').toISOString()
          });
        }
        
        this.performanceItems = items;
        this.isGenerating = false;
        
        console.log(`Generated ${count} items for performance testing`);
      }, 100);
    }
  }
};
</script>

<style scoped>
.example-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.example-section h3 {
  margin-top: 0;
  color: #2d3748;
  border-bottom: 2px solid #667eea;
  padding-bottom: 8px;
}

.selection-info {
  margin-top: 16px;
  padding: 16px;
  background: #f7fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.selection-info pre {
  background: white;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  font-size: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.date-formatted {
  color: #667eea;
  font-weight: 500;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  font-weight: 600;
  color: #4a5568;
  min-width: 35px;
}

.status-active {
  color: #10b981;
  font-weight: 600;
}

.status-pending {
  color: #f59e0b;
  font-weight: 600;
}

.status-completed {
  color: #059669;
  font-weight: 600;
}

.status-cancelled {
  color: #dc2626;
  font-weight: 600;
}

.performance-controls {
  margin-bottom: 16px;
  display: flex;
  gap: 12px;
}
</style>
