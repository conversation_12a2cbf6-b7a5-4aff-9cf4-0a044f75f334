<template>
  <v-dialog
    v-model="dialog"
    :max-width="options.width"
    :style="{ zIndex: options.zIndex }"
    @keydown.esc="cancel"
  >
    <v-card>
      <v-toolbar dark :color="options.color" dense flat>
        <v-toolbar-title class="white--text">{{ title }}</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="cancel">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <c-card>
        <c-card-header>
          <div
            v-if="displayAlert == true"
            class="custom-alert alert alert-info alert-dismissible fade show"
            role="alert"
          >
            <strong>Great !</strong> {{ message }}
            <button
              @click="displayAlert = flase"
              type="button"
              class="btn-close"
              data-bs-dismiss="alert"
              aria-label="Close"
            >
              X
            </button>
          </div>
          <c-button
            style="background-color: darkviolet; float: right"
            class="text-white"
            v-if="checkPermission('edit_expenses')"
            @click="editExpense()"
            >Reload</c-button
          >
          <c-button
            style="background-color: blue; float: right"
            class="text-white mr-2"
            v-if="checkPermission('create_approve_expense')"
            @click="approveExpense"
            >Approve</c-button
          >
          <c-button
            style="background-color: red; float: right"
            class="text-white mr-2"
            v-if="checkPermission('create_disapprove_expense')"
            @click="
              $root
                .$confirm(
                  'Delete',
                  'Do you want to Disapprove these records?',
                  {
                    color: 'red',
                    width: 290,
                    zIndex: 200,
                  }
                )
                .then((confirmed) => {
                  if (confirmed) {
                    disapproveExpense();
                  }
                })
            "
            >Disapprove</c-button
          >
        </c-card-header>
        <c-card-body id="printDataTable">
          <div class="userCard">
            <strong>Emp: {{ expenseUser }}</strong> <br />
            <strong>Code: {{ code }}</strong> <br />
            <strong>Lines: {{ lines }}</strong>
          </div>

          <c-data-table
            hover
            header
            striped
            :items="items"
            :fields="fields"
            :items-per-page="1000"
            :active-page="1"
            :responsive="true"
            pagination
            thead-top
          >
            <template slot="thead-top">
              <td style="border-top: none"><strong>Total</strong></td>
              <td style="border-top: none" class="text-xs-right">
                {{ items.length }}
              </td>
            </template>
            <template #file="{ item }">
              <td v-if="item.file.length > 0">
                <div class="row" v-for="(x, index) in item.file" :key="index">
                  <CButton
                    style="padding-top: 10px"
                    small
                    class="btn-link"
                    target="_blank"
                    :href="x"
                    >{{ x | fileNameFromPath }}
                  </CButton>
                </div>
              </td>
              <td v-else></td>
            </template>
            <template #date="{ item }">
              <td
                v-if="item.color"
                :style="`backgroundColor: ${item.color}`"
                style="color: white"
              >
                {{ crmDateFormat(item.date, "YYYY-MM-DD") }}
              </td>
              <td v-else>{{ crmDateFormat(item.date, "YYYY-MM-DD") }}</td>
            </template>
            <template #edited_by="{ item }">
              <td v-if="item.edited_by">
                <v-chip :style="{ backgroundColor: 'green', color: 'white' }">
                  {{ item.edited_by }}
                </v-chip>
              </td>
              <td v-else></td>
            </template>
            <template #division="{ item }">
              <td>
                <strong
                  :style="{
                    color: item.color,
                  }"
                  >{{ item.division }}</strong
                >
              </td>
            </template>
            <template #status="{ item }">
              <td>
                <strong v-if="item.status == 1" style="color: green"
                  >Approved</strong
                >
                <strong v-if="item.status == 0" style="color: red"
                  >Dispproved</strong
                >
                <strong v-if="item.status == null" style="color: blue"
                  >Pending</strong
                >
                <strong v-if="item.status == ''"></strong>
              </td>
            </template>
            <template #actions="{ item }">
              <td>
                <div class="row justify-content-center">
                  <c-button
                    color="success"
                    class="btn-sm mt-2 mr-1 text-white"
                    v-if="checkPermission('edit_expenses')"
                    :to="{
                      name: 'edit-expense-stats',
                      params: { id: item.id },
                    }"
                    target="_blank"
                    ><c-icon name="cil-pencil"
                  /></c-button>

                  <c-button
                    style="background-color: lightsalmon"
                    class="btn-sm mt-2 mr-1 text-white"
                    v-if="checkPermission('edit_expenses') && item.edited_by"
                    @click="getEditedData(item)"
                    ><c-icon name="cib-angellist"
                  /></c-button>
                  <c-button
                    color="danger"
                    class="btn-sm mt-2 mr-1 text-white"
                    v-if="checkPermission('delete_expenses')"
                    @click="
                      $root
                        .$confirm(
                          'Delete',
                          'Do you want to delete this record?',
                          {
                            color: 'red',
                            width: 290,
                            zIndex: 200,
                          }
                        )
                        .then((confirmed) => {
                          if (confirmed) {
                            deleteExpenseDetail(item);
                          }
                        })
                    "
                    ><c-icon name="cil-trash"
                  /></c-button>
                </div>
              </td>
            </template>
          </c-data-table>
          <c-button
            style="background-color: darkviolet; float: right"
            class="text-white"
            v-if="checkPermission('edit_expenses')"
            @click="editExpense()"
            >Reload</c-button
          >
          <c-button
            style="background-color: blue; float: right"
            class="text-white mr-2"
            v-if="checkPermission('create_approve_expense')"
            @click="approveExpense"
            >Approve</c-button
          >
          <c-button
            style="background-color: red; float: right"
            class="text-white mr-2"
            v-if="checkPermission('create_disapprove_expense')"
            @click="
              $root
                .$confirm(
                  'Delete',
                  'Do you want to Disapprove these records??',
                  {
                    color: 'red',
                    width: 290,
                    zIndex: 200,
                  }
                )
                .then((confirmed) => {
                  if (confirmed) {
                    disapproveExpense();
                  }
                })
            "
            >Disapprove</c-button
          >
          <div class="totalCard">
            <strong>Total Amount: {{ total }}</strong> <br />
          </div>
        </c-card-body>
        <c-card-footer>
          <div style="display: flex; justify-content: center">
            <c-button
              color="primary"
              title="Print"
              style="width: 50px; height: 50px"
              class="px-2 m-1 text-white btn-lg"
              @click="getPrintedData"
              ><c-icon name="cil-print" size="xl"
            /></c-button>
            <!-- target="_blank"
              :to="{ name: 'expense-statistics-second-version-print' }" -->
            <c-button
              color="success"
              title="Download Excel"
              style="width: 50px; height: 50px"
              class="px-2 m-1 text-white btn-lg"
              @click="download"
              ><c-icon name="cib-experts-exchange" size="xl"
            /></c-button>
            <c-button
              color="dark"
              title="Download CSV"
              style="width: 50px; height: 50px"
              class="px-2 m-1 text-white btn-lg"
              @click="downloadCsv"
            >
              <c-icon name="cibC" size="xl" />
            </c-button>
            <c-button
              class="px-2 m-1 text-white btn-lg"
              title="Download PDF"
              style="width: 50px; height: 50px"
              color="danger"
              @click="createPDF"
              ><c-icon name="cibAdobeAcrobatReader" size="xl"
            /></c-button>
          </div>
        </c-card-footer>
      </c-card>
    </v-card>
  </v-dialog>
</template>

<script>
import download from "../download-reports/download.vue";
import { Amiri } from "../../assets/fonts/Amiri-Regular-normal";
import jsPDF from "jspdf";
import "jspdf-autotable";
import { mapActions } from "vuex";
import { capitalize } from "../../filters";
export default {
  components: {
    download,
  },
  data() {
    return {
      dialog: false,
      resolve: null,
      reject: null,
      displayAlert: false,
      message: "",
      items: [],
      fields: [],
      expenseIds: [],
      code: null,
      expenseData: {},
      record: null,
      is_required: 0,
      column: null,
      lines: null,
      total: null,
      expenseUser: null,
      title: null,
      options: {
        color: "primary",
        width: 1500,
        zIndex: 1000000000,
      },
      readMore: {},
    };
  },
  emits: ["downloaded"],
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open(
      title,
      data,
      expenseData,
      item,
      column,
      lines = [],
      expenseUser = null,
      total,
      is_required = null,
      code = null,
      fields = [],
      options = null
    ) {
      this.loadVApp();
      this.dialog = true;
      this.title = title;
      this.items = data;
      this.expenseData = expenseData;
      this.record = item;
      this.column = column;
      this.lines = lines;
      this.expenseUser = expenseUser;
      this.total = total;
      this.code = code;
      this.is_required = is_required;
      if (fields.length == 0) {
        this.fields = Object.keys(this.items[0]);
      } else {
        this.fields = fields;
      }
      this.options = Object.assign(this.options, options || this.options);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    getPrintedData() {
      axios
        .post(`/api/print-expenses`, {
          listFilter: this.expenseData,
          user: this.record.id,
          column: this.column,
        })
        .then((response) => {
          this.items = response.data.data.data;
          this.total = response.data.data.total;
          this.fields = response.data.data.fields;
          this.expenseUser = response.data.data.user;
          this.code = response.data.data.code;
          this.lines = response.data.data.lines;
          this.is_required = response.data.data.is_required;
          this.print();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    editExpense() {
      axios
        .post(`/api/show-expense-statistics-second-version`, {
          listFilter: this.expenseData,
          user: this.record.id,
          column: this.column,
        })
        .then((response) => {
          this.items = response.data.data.data;
          this.fields = response.data.data.fields;
          this.expenseUser = response.data.data.user;
          this.code = response.data.data.code;
          this.lines = response.data.data.lines;
          this.is_required = response.data.data.is_required;
          this.total = response.data.data.total;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removeExpense(item) {
      const index = this.items.findIndex((type) => item.id == type.id);
      this.items.splice(index, 1);
    },
    deleteExpenseDetail(item) {
      axios
        .delete(`/api/expense-stats-second-version/${item.id}`)
        .then((res) => {
          this.message = "Expense Detail Deleted Successfully";
          // this.removeExpense(item);
          this.editExpense();
        })
        .catch((err) => this.showErrorMessage(err));
    },
    getEditedData(item) {
      axios
        .get(`/api/edited-expense-users-notes/${item.id}`)
        .then((response) => {
          const data = response.data.data;
          this.$root.$table("Expenses Edited:", data);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    approveExpense() {
      axios
        .post(`/api/approve-expense-second-version`, {
          details: this.items,
          is_required: this.is_required,
        })
        .then((response) => {
          this.message = "Expenses Approved Successfully";
          this.displayAlert = true;
          this.editExpense();
          // this.flash("Data Approved Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    disapproveExpense() {
      axios
        .post(`/api/disapprove-expense-second-version`, {
          details: this.items,
          is_required: this.is_required,
        })
        .then((response) => {
          this.message = "Expenses Disapproved Successfully";
          this.displayAlert = true;
          this.editExpense();
          // this.flash("Data Approved Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    showMore(id) {
      this.$set(this.readMore, id, true);
    },
    showLess(id) {
      this.$set(this.readMore, id, false);
    },
    agree() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp();
    },
    cancel() {
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp();
    },
    print() {
      this.$htmlToPaper("printDataTable");
    },
    download() {
      this.downloadXlsx(this.items, this.title.concat(".xlsx"));
      this.$emit("downloaded");
    },
    downloadCsv() {
      this.downloadXlsx(this.items, this.title.concat(".csv"));
      this.$emit("downloaded");
    },
    createPDF() {
      let pdfName = this.title;
      const columns = this.fields.map((field) => {
        return {
          title: capitalize(field),
          dataKey: field,
        };
      });
      const body = this.items;
      const doc = new jsPDF({ filters: ["ASCIIHexEncode"] });

      doc.addFileToVFS("Amiri-Regular.ttf", Amiri);
      doc.addFont("Amiri-Regular.ttf", "Amiri", "normal");
      doc.setFont("Amiri");
      doc.setFontSize(10);
      doc.autoTable({
        columns,
        body,
        margin: { top: 10 },
        showHead: "firstPage",
        styles: {
          lineColor: "#c7c7c7",
          lineWidth: 0,
          cellPadding: 2,
          font: "Amiri",
        },
      });
      doc.save(pdfName + ".pdf");
    },
  },
};
</script>

<style scoped>
.card-table {
  position: relative;
}
.card-scroll {
  max-height: 450px;
  overflow: auto;
  margin-top: 20px;
}
.userCard {
  width: 300px;
  height: 100px;
  float: right;
  border-radius: 5px;
  color: white;
  padding: 15px;
  background-color: blue;
  animation-name: userCard;
  animation-duration: 4s;
}
.totalCard {
  width: 300px;
  height: 50px;
  float: left;
  border-radius: 5px;
  color: white;
  padding: 15px;
  background-color: brown;
  animation-name: totalCard;
  animation-duration: 4s;
}

@keyframes totalCard {
  0% {
    background-color: blue;
  }
  25% {
    background-color: brown;
  }
  50% {
    background-color: red;
  }
  100% {
    background-color: green;
  }
}

@keyframes userCard {
  0% {
    background-color: blue;
  }
  25% {
    background-color: yellow;
  }
  50% {
    background-color: red;
  }
  100% {
    background-color: green;
  }
}
</style>