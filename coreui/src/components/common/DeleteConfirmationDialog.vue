<template>
  <CModal title="Delete?" color="danger" :show.sync="dialog">
    <p>{{ confirmDeleteMsg }}</p>
    <template #footer>
      <CButton @click="close" color="default">Cancel</CButton>
      <CButton @click="deleteItem" color="danger">Delete</CButton>
    </template>
  </CModal>
</template>

<script>
export default {
  data() {
    return {
      dialog: false,
      confirmDeleteMsg: localStorage.getItem("confirm_delete"),
      deletedItem:null,
    };
  },
  methods: {
    open() {
      this.dialog = true;
    },
    close() {
      this.dialog = false;
    },
    deleteItem() {
      this.$root.$emit("DeleteConfirmed",this.deletedItem);
      this.close();
    }
  },
  mounted() {
    this.$root.$on('alertDeleteConfirm',(item)=>{
      this.deletedItem=item
      this.open();
    })
  },
};
</script>
