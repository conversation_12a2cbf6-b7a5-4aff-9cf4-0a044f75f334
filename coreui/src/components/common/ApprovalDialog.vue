<template>
  <v-dialog
    v-model="dialog"
    :max-width="options.width"
    :style="{ zIndex: options.zIndex }"
    @keydown.esc="cancel"
  >
    <v-card>
      <v-toolbar dark :color="options.color" dense flat>
        <v-toolbar-title class="white--text">{{ title }}</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="cancel">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <c-card class="card-table">
        <c-card-body class="card-scroll">
          <c-data-table
            id="printTable"
            hover
            header
            tableFilter
            striped
            sorter
            footer
            :items="items"
            :fields="fields"
            :items-per-page="500"
            :active-page="1"
            :responsive="true"
            pagination
            thead-top
          >
            <template slot="thead-top">
              <td style="border-top: none"><strong>Total</strong></td>
              <td style="border-top: none" class="text-xs-right">
                {{ items.length }}
              </td>
            </template>
            <template #file="{ item }">
              <td v-if="item.file.length > 0">
                <div class="row" v-for="(x, index) in item.file" :key="index">
                  <CButton
                    style="padding-top: 10px"
                    small
                    class="btn-link"
                    target="_blank"
                    :href="x"
                    >{{ x | fileNameFromPath }}
                  </CButton>
                </div>
              </td>
              <td v-else></td>
            </template>
            <template #doctor="{ item }">
              <td style="border-top: none" class="text-xs-right">
                <strong style="color: blue; cursor: pointer"
                  ><a @click="$root.$doctor('Doctor Data', item.doctor_id)">{{
                    item.doctor
                  }}</a></strong
                >
              </td>
            </template>
            <template #account="{ item }">
              <td style="border-top: none" class="text-xs-right">
                <strong style="color: blue; cursor: pointer"
                  ><a
                    @click="$root.$account('Account Data', item.account_id)"
                    >{{ item.account }}</a
                  ></strong
                >
              </td>
            </template>
            <template #date="{ item }">
              <td>
                {{ crmDateFormat(item.date, "YYYY-MM-DD") }}
              </td>
            </template>
            <template #status="{ item }">
              <td>
                <strong v-if="item.status == 1" style="color: green"
                  >Approved</strong
                >
                <strong v-if="item.status == 0" style="color: red"
                  >Dispproved</strong
                >
                <strong v-if="item.status == null" style="color: blue"
                  >Pending</strong
                >
              </td>
            </template>
            <template #approvals="{ item }">
              <td>
                <CButton
                  color="success"
                  variant="outline"
                  square
                  size="sm"
                  @click="showApprovalDetails(item)"
                >
                  show
                </CButton>
              </td>
            </template>
            <template #division="{ item }">
              <td>
                <strong
                  :style="{
                    color: item.color,
                  }"
                  >{{ item.division }}</strong
                >
              </td>
            </template>
            <template #visit_date="{ item }">
              <td @click="showMore(item.id)" v-if="!readMore[item.id]">
                {{ item.visit_date.substring(0, 15) + ".." }} <br /><span
                  style="color: blue; cursor: pointer"
                  >more</span
                >
              </td>
              <td @click="showLess(item.id)" v-if="readMore[item.id]">
                {{ item.visit_date }} <br />
                <span style="color: blue; cursor: pointer">less</span>
              </td>
            </template>
            <template #actions="{ item }">
              <td>
                <c-button
                  color="success"
                  class="btn-sm mt-2 mr-1 text-white"
                  v-if="checkPermission('edit_expenses')"
                  :to="{ name: 'edit-expense-stats', params: { id: item.id } }"
                  target="_blank"
                  ><c-icon name="cil-pencil"
                /></c-button>
              </td>
            </template>
          </c-data-table>
        </c-card-body>
        <c-card-footer>
          <download
            @getPrint="print"
            @getxlsx="download"
            @getpdf="createPDF"
            @getcsv="downloadCsv"
            :fields="fields"
            :data="items"
            :name="title"
          />
        </c-card-footer>
      </c-card>
    </v-card>
  </v-dialog>
</template>

<script>
import download from "../download-reports/download.vue";
import { Amiri } from "../../assets/fonts/Amiri-Regular-normal";
import jsPDF from "jspdf";
import "jspdf-autotable";
import { mapActions } from "vuex";
import { capitalize } from "../../filters";
export default {
  components: {
    download,
  },
  data() {
    return {
      dialog: false,
      resolve: null,
      reject: null,
      items: [],
      fields: [],
      title: null,
      options: {
        color: "primary",
        width: 1400,
        zIndex: 1000000000,
      },
      readMore: {},
    };
  },
  emits: ["downloaded"],
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open(title, data, fields = [], options = null) {
      this.loadVApp();
      this.dialog = true;
      this.title = title;
      this.items = data;
      if (fields.length == 0) {
        this.fields = Object.keys(this.items[0]);
      } else {
        this.fields = fields;
      }
      this.options = Object.assign(this.options, options || this.options);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    showMore(id) {
      this.$set(this.readMore, id, true);
    },
    showLess(id) {
      this.$set(this.readMore, id, false);
    },
    agree() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp();
    },
    cancel() {
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp();
    },
    print() {
      this.$htmlToPaper("printTable");
    },
    download() {
      this.downloadXlsx(this.items, this.title.concat(".xlsx"));
      this.$emit("downloaded");
    },
    downloadCsv() {
      this.downloadXlsx(this.items, this.title.concat(".csv"));
      this.$emit("downloaded");
    },
    createPDF() {
      let pdfName = this.title;
      const columns = this.fields.map((field) => {
        return {
          title: capitalize(field),
          dataKey: field,
        };
      });
      const body = this.items;
      const doc = new jsPDF({ filters: ["ASCIIHexEncode"] });

      doc.addFileToVFS("Amiri-Regular.ttf", Amiri);
      doc.addFont("Amiri-Regular.ttf", "Amiri", "normal");
      doc.setFont("Amiri");
      doc.setFontSize(10);
      doc.autoTable({
        columns,
        body,
        margin: { top: 10 },
        showHead: "firstPage",
        styles: {
          lineColor: "#c7c7c7",
          lineWidth: 0,
          cellPadding: 2,
          font: "Amiri",
        },
      });
      doc.save(pdfName + ".pdf");
    },
    showApprovalDetails(item) {
      axios
        .post("/api/expense-approvals", item)
        .then((response) => {
          const approvals = response.data.data;
          this.$root.$table("Expense Approval Flow", approvals);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
};
</script>

<style scoped>
.card-table {
  position: relative;
}
.card-scroll {
  max-height: 450px;
  overflow: auto;
  margin-top: 20px;
}
</style>