<template>
  <input type="checkbox" v-model="switchValue" @change="submit" />
</template>

<script>
export default {
  props: {
    value: {
      type: Object,
      required: true,
    },
    trueValue: {
      type: Object,
      required: true,
    },
    falseValue: {
      type: Object,
      required: true,
    },
    except: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      switchValue: false,
      expectedTrueValue: null,
      acceptedValue: null,
    };
  },
  methods: {
    initialize() {
      Object.keys(this.value).forEach((key) => {
        if (!this.arrayHas<PERSON>ey(key)) {
          this.acceptedValue = {
            ...this.acceptedValue,
            [key]: this.value[key],
          };
        }
      });
      Object.keys(this.trueValue).forEach((key) => {
        if (!this.arrayHasKey(key)) {
          this.expectedTrueValue = {
            ...this.expectedTrueValue,
            [key]: this.trueValue[key],
          };
        }
      });
    },
    array<PERSON><PERSON><PERSON><PERSON>(key) {
      return this.except.find((item) => item == key) == key;
    },
    submit() {
      let value=this.switchValue?this.trueValue:this.falseValue
      this.$emit("input",value)
    },
  },
  mounted() {
    this.switchValue = this.isEqual(this.acceptedValue, this.expectedTrueValue);
  },
  async created() {
    this.initialize();
  },
};
</script>