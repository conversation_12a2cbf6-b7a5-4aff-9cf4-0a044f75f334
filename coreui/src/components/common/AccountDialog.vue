<template>
  <v-dialog v-model="dialog" :max-width="options.width" :style="{ zIndex: options.zIndex }" @keydown.esc="cancel">
    <v-card>
      <v-toolbar dark :color="options.color" dense flat>
        <v-toolbar-title class="white--text">{{ title }}</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="cancel">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <c-card v-if="account">
        <c-card-header>
          <span class="text-secondary">#{{ account_id }} </span>
          <strong>{{ account.name }}</strong>
          <span v-if="account.inactive_date == null"
            class="bg-success rounded text-white ml-2 mt-2 p-1 pl-3 pr-3 font-bold">Active</span>
          <span v-else class="bg-danger rounded text-white ml-2 mt-2 p-1 pl-3 pr-3 font-bold">In Active</span>
        </c-card-header>
        <c-card-body>
          <div class="row">
            <div
              class="col-sm-2 text-whit justify-content-center text-center border border-dark bg-transparet shadow-sm"
              style="background: #c2d9f0">
              <div class="p-3 w-100 h-100 text-center d-flex align-items-center m-auto justify-content-center">
                <CIcon class="custom_icon user-icon" name="cil-building" />
              </div>
            </div>
            <div class="col-sm-10">
              <h3 class="text-primary font-weight-bold">
                <CIcon class="custom_icon" name="cil-room" />
                {{ account.type }}
              </h3>
              <!-- <h4>
                {{ doctor.speciality }}
              </h4> -->
              <h5>
                <CIcon class="custom_icon mr-1" name="cil-phone" />
                {{ mobile }} <span class="mr-3 ml-3 text-secondary">|</span>
                <CIcon class="custom_icon mr-1" name="cil-envelope-closed" />
                <!-- {{ show }} -->
              </h5>
              <h5>
                <CIcon class="custom_icon mr-1" name="cil-map" />
                {{ account.address }}
              </h5>

            </div>
          </div>
        </c-card-body>
        <c-card-footer> </c-card-footer>
      </c-card>
      <c-card>
        <!-- <CCardHeader> Account Id: {{ $route.params.id }} </CCardHeader> -->
        <CCardBody>
          <CTabs>
            <CTab active>
              <template slot="title">
                <CIcon class="custom_icon" name="cil-description" /> Information
              </template>
              <CDataTable striped small fixed :items="account_items" :fields="fields">
                <template slot="value" slot-scope="data">
                  <strong>{{ data.item.value }}</strong>
                </template>
              </CDataTable>
            </CTab>

            <CTab>
              <template slot="title">
                <CIcon class="custom_icon" name="cib-letterboxd" /> Account
                Lines
              </template>

              <CDataTable hover striped sorter tableFilter footer itemsPerPageSelect :items="account_line_items"
                :fields="account_lines_fields" :items-per-page="100" :active-page="1" :responsive="true"
                :key="tableKeys.accountLines" pagination>
                <template #from_date="{ item }">
                  <td>{{ new Date(Date.parse(item.from_date)).toDateString() }}</td>
                </template>
                <template #to_date="{ item }">
                  <td>{{ new Date(Date.parse(item.to_date)).toDateString() }}</td>
                </template>
              </CDataTable>
            </CTab>

            <CTab>
              <template slot="title">
                <CIcon class="custom_icon" name="cil-people" /> Account Doctors
              </template>
              <CDataTable hover striped sorter footer itemsPerPageSelect :items="account_doctor_items"
                :fields="account_doctors_fields" :items-per-page="100" :active-page="1" :responsive="true"
                :table-filter="searchFilters.accountDoctors" pagination :key="tableKeys.accountDoctors">
                <template #from_date="{ item }">
                  <td>{{ new Date(Date.parse(item.from_date)).toDateString() }}</td>
                </template>
                <template #to_date="{ item }">
                  <td>{{ new Date(Date.parse(item.to_date)).toDateString() }}</td>
                </template>

              </CDataTable>
            </CTab>

            <c-tab>
              <template slot="title">
                <CIcon class="custom_icon" name="cil-clock" /> Visits
              </template>
              <v-container>
                <v-row style="justify-content: space-around" class="mt-2 px-16">
                  <c-button v-for="(role, index) in roles" :key="index" class="mt-1 btn btn-primary"
                    @click="dispalyVisits(role)">
                    {{ role.name }}
                  </c-button>
                </v-row>
              </v-container>
              <c-data-table v-if="visits.length > 0" hover striped sorter footer itemsPerPageSelect :items="visits"
                :fields="visitFields" :items-per-page="100" :active-page="1" :responsive="true" tableFilter pagination
                thead-top :key="tableKeys.visits">
                <template slot="thead-top">
                  <td style="border-top: none"><strong>Total</strong></td>
                  <td style="border-top: none" class="text-xs-right">
                    {{ visits.length }}
                  </td>
                </template>
                <template #employee="{ item }">
                  <td :style="{
                    color: item.color,
                  }">
                    <strong>{{ item.employee }}</strong>
                  </td>
                </template>
                <template #division="{ item }">
                  <td :style="{
                    color: item.color,
                  }">
                    <strong>{{ item.division }}</strong>
                  </td>
                </template>
                <template #visit_date="{ item }">
                  <td>
                    <v-chip :style="{ backgroundColor: colorfulDate(item), color: 'white' }">
                      {{ item.visit_date }}
                    </v-chip>
                  </td>
                </template>
              </c-data-table>
            </c-tab>

            <CTab>
              <template slot="title">
                <CIcon class="custom_icon" name="cil-color-palette" /> Requests
              </template>
              <CDataTable hover striped sorter footer itemsPerPageSelect :items="commercials"
                :fields="Object.keys(commercials[0] || {})" :items-per-page="100" :active-page="1" :responsive="true"
                pagination :key="tableKeys.commercials">
                <template #from_date="{ item }">
                  <td>{{ new Date(Date.parse(item.from_date)).toDateString() }}</td>
                </template>
                <template #to_date="{ item }">
                  <td>{{ new Date(Date.parse(item.to_date)).toDateString() }}</td>
                </template>

              </CDataTable>
            </CTab>

            <CTab>
              <template slot="title">
                <CIcon class="custom_icon" name="cil-satelite" /> Social
              </template>
              <CDataTable hover striped sorter footer itemsPerPageSelect :items="social_items" :fields="socials_fields"
                :items-per-page="100" :active-page="1" :responsive="true" tableFilter pagination
                :key="tableKeys.socials">
              </CDataTable>
            </CTab>

            <CTab>
              <template slot="title">
                <CIcon class="custom_icon" name="cil-chart-pie" /> Map
              </template>
              <div class="col">
                <GmapMap :center="center" :zoom="30" style="width: 100%; height: 440px" map-style-id="roadmap"
                  :options="mapOptions" ref="mapRef" @click="handleMapClick">
                  <GmapMarker :position="marker.position" :clickable="true" :draggable="true" @drag="handleMarkerDrag"
                    @click="panToMarker" />
                </GmapMap>
                <button @click="geolocate">Detect Location</button>

                <p>Selected Position: {{ marker.position }}</p>
              </div>
            </CTab>
            <CTab>
              <template slot="title">
                <CIcon class="custom_icon" name="cil-animal" /> Pharmacies
              </template>
              <CDataTable hover striped sorter footer itemsPerPageSelect :items="pharmacies" :fields="pharmaciesFields"
                :items-per-page="100" :active-page="1" :responsive="true" tableFilter pagination
                :key="tableKeys.socials">
              </CDataTable>
            </CTab>
          </CTabs>
        </CCardBody>
        <CCardFooter>
          <CButton color="primary" @click="$router.go(-1)">Back</CButton>
        </CCardFooter>
      </c-card>
    </v-card>
  </v-dialog>
</template>

<script>
import { mapActions } from "vuex";
export default {
  data() {
    return {
      dialog: false,
      resolve: null,
      reject: null,
      account: null,
      searchFilters: {
        accountItems: "",
        accountLines: "",
        accountDoctors: "",
        socials: "",
        visits: "",
      },
      tableKeys: {
        accountItems: Date.now(),
        accountLines: Date.now(),
        accountDoctors: Date.now(),
        socials: Date.now(),
        visits: Date.now(),
      },
      account_id: null,
      account_items: [],
      account_line_items: [],
      account_doctor_items: [],
      social_items: [],
      roles: [],
      visits: [],
      visitFields: [
        "id",
        "visit_date",
        "line",
        "division",
        "employee",
        "account",
        "brick",
        "doctor",
        "speciality",
        "acc_type",
        "type",
      ],
      fields: [{ key: "key" }, { key: "value" }],
      account_lines_fields: [
        "id",
        "line_name",
        "line_division_name",
        "brick_name",
        "class",
        "from_date",
        "to_date",
      ],
      account_doctors_fields: ["id", "doctor_name", "from_date", "to_date"],
      socials_fields: ["id", "account_name", "social_name", "link"],
      pharmaciesFields: [],
      pharmacies: [],
      center: { lat: 45.508, lng: -73.587 },
      marker: { position: { lat: 10, lng: 10 } },
      mapOptions: {
        disableDefaultUI: true,
      },
      commercial_requests: [],
      mobile: null,
      email: null,
      positions: [],
      items: [],
      commercials: [],
      accounts_fields: ["id", "line", "division", "account", "type", "doctor"],

      title: null,
      options: {
        color: "primary",
        width: 1400,
        zIndex: **********,
      },
    };
  },
  emits: ["downloaded"],
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    resetFilters() {
      this.searchFilters = {
        accountItems: "",
        accountLines: "",
        accountDoctors: "",
        socials: "",
        visits: "",
        commercials: ""
      };

      // Force re-render of tables
      this.tableKeys = {
        accountItems: Date.now(),
        accountLines: Date.now(),
        accountDoctors: Date.now(),
        socials: Date.now(),
        visits: Date.now(),
        commercials: Date.now(),
      };
    },
    colorfulDate(item) {
      if (!this.monthColors) {
        this.monthColors = {}; // Store assigned colors for each month
        const uniqueMonths = [...new Set(this.visits.map(obj => obj.visit_date.split('-').slice(0, 2).join('-')))];

        const colorsArray = ['navy', 'indigo', 'yellowgreen', 'teal', 'steelblue', 'seagreen', 'purple', 'orange', 'brown', 'red', 'pink', 'cyan'];

        uniqueMonths.forEach((month, index) => {
          this.monthColors[month] = colorsArray[index % colorsArray.length]; // Assign colors
        });
      }

      const monthKey = item.visit_date.split('-').slice(0, 2).join('-'); // Extract "YYYY-MMM" as key
      return this.monthColors[monthKey] || 'transparent';
    },
    open(title, data, fields = [], options = null) {
      this.loadVApp();
      this.dialog = true;
      this.title = title;
      this.account_id = data;
      axios
        .get(`/api/accounts/${this.account_id}`)
        .then((response) => {
          //const items = response.data.divisions;
          this.account = response.data.account;

          this.account_line_items = response.data.account_lines;
          this.account_doctor_items = response.data.account_doctors;
          this.commercials = response.data.commercialDoctors;
          this.pharmacies = response.data.pharmacies;
          this.pharmaciesFields = this.pharmacies.length > 0? Object.keys(response.data.pharmacies[0]): [];
          this.social_items = response.data.socials;
          this.roles = response.data.roles;
          this.visits = response.data.visits;
          const account_items = Object.entries(response.data.account);
          this.account_items = account_items.map(([key, value]) => {
            return { key: key, value: value };
          });
          console.log(this.account.name);
          console.log(this.account_line_items);
          console.log(this.account_doctor_items);
          console.log(this.commercials);
          console.log(this.account_line_items);
          console.log(this.pharmacies);
          this.resetFilters();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      this.options = Object.assign(this.options, options || this.options);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    dispalyVisits(role) {
      axios
        .post(`/api/accounts/${this.account_id}/visits/${role.id}`)
        .then((response) => {
          this.visits = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    format_date: function (value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    geolocate: function () {
      navigator.geolocation.getCurrentPosition((position) => {
        this.marker.position = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
        };
      });
      this.panToMarker();
    },
    //sets the position of marker when dragged
    handleMarkerDrag(e) {
      this.marker.position = { lat: e.latLng.lat(), lng: e.latLng.lng() };
    },

    //Moves the map view port to marker
    panToMarker() {
      this.$refs.mapRef.panTo(this.marker.position);
      this.$refs.mapRef.setZoom(18);
    },

    //Moves the marker to click position on the map
    handleMapClick(e) {
      this.marker.position = { lat: e.latLng.lat(), lng: e.latLng.lng() };
    },
    agree() {
      this.resolve(true);
      this.dialog = false;
      this.resetFilters();
      this.unLoadVApp();
    },
    cancel() {
      this.resolve(false);
      this.dialog = false;
      this.items = [];
      this.account_items = [],
        this.account_line_items = [],
        this.account_doctor_items = [],
        this.unLoadVApp();
    },
  },
};
</script>

<style scoped>
.card-table {
  position: relative;
}

.card-scroll {
  max-height: 450px;
  overflow: auto;
  margin-top: 20px;
}
</style>
