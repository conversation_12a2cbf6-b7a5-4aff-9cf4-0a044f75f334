<template>
  <div class="row">
    <div class="col">
      <template> <label> Attachments </label></template>
      <br />
      <template>
        <input
          type="file"
          id="files"
          multiple
          ref="file_input"
          @change="handleFilesUpload"
        />
        <p
          style="margin-left: 20px"
          v-if="files_length != 0"
          class="custom-file-upload"
        >
          <b>List of files</b>
        </p>
        <ul style="margin-left: 50px">
          <li v-for="(file, key) in files" :key="key">
            {{ file.name }}
          </li>
        </ul>
      </template>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      files: [],
      files_length: 0,
    };
  },
  emits: ["getAttachments"],
  methods: {
    handleFilesUpload() {
      var files = this.$refs.file_input.files;
      this.files_length = files.length;
      var data = new FormData();
      for (var i = 0; i < files.length; i++) {
        data.append("input_name[]", files[i]);
      }
      this.files = data.getAll("input_name[]");
      this.$emit("getAttachments", {
          files: this.files,
          file_length: this.files_length,
        });

    },
  },
};
</script>