<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  name: "yearSelection",
  components:{
    vSelect
  },
  props:{
    years: Array,
    year: Number,
  },
  computed:{
    selectedYear: {
      get() {
        return this.year;
      },
      set(value) {
        this.$emit('update:year', value);
      }
    },
  },
  methods:{
    handleYearChange(newYear) {
      console.log(newYear);
      this.$emit('update:year', newYear);
    },
  }
}
</script>

<template>
  <v-select
    title="Search for Year"
    v-model="selectedYear"
    :options="years"
    class="my-2 "
    @input="handleYearChange"
  ></v-select>
</template>

