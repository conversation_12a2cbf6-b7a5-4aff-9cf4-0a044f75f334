<template>
  <v-dialog v-model="dialog" :max-width="options.width" :style="{ zIndex: options.zIndex }" @keydown.esc="cancel">
    <v-card>
      <v-toolbar dark :color="options.color" dense flat>
        <v-toolbar-title class="white--text">{{ title }}</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="cancel">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <c-card class="card-table">
        <c-card-body class="card-scroll">
          <c-data-table id="printTable" hover header tableFilter striped sorter footer :items="items" :fields="fields"
            :items-per-page="500" :active-page="1" :responsive="true" pagination thead-top>
            <template slot="thead-top">
              <td style="border-top: none"><strong>Total</strong></td>
              <td style="border-top: none" class="text-xs-right">
                {{ items.length }}
              </td>
            </template>
            <template #actions="{ item }">
              <td>
                <c-button color="danger" class="btn-sm mr-1" v-if="checkPermission('delete_quiz')" @click="
                  $root
                    .$confirm('Delete', 'Do you want to delete this record?', {
                      color: 'red',
                      width: 290,
                      zIndex: 200,
                    })
                    .then((confirmed) => {
                      if (confirmed) {
                        deleteQuiz(item);
                      }
                    })
                  "><c-icon name="cil-trash" /></c-button>
              </td>
            </template>
          </c-data-table>
          <br />

        </c-card-body>

      </c-card>
    </v-card>
  </v-dialog>
</template>

<script>
import "jspdf-autotable";
import { mapActions } from "vuex";
export default {
  data() {
    return {
      dialog: false,
      resolve: null,
      reject: null,
      items: [],
      object: null,
      column: null,
      fields: [],
      outOfList: [],
      quizResultData: {},
      outOfListFields: [],
      title: null,
      options: {
        color: "primary",
        width: 1400,
        zIndex: 1000000000,
      },
      readMore: {},
    };
  },
  emits: ["downloaded"],
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open(title, data, fields = [], item = null, request, column = null, options = null) {
      this.loadVApp();
      this.dialog = true;
      this.title = title;
      this.items = data;
      this.object = item;
      this.column = column;
      this.quizResultData = request;
      if (fields.length == 0) {
        this.fields = Object.keys(this.items[0]);
      } else {
        this.fields = fields;
      }
      this.options = Object.assign(this.options, options || this.options);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    removeQuiz(item) {
      const index = this.items.findIndex(type => item.id == type.id)
      this.items.splice(index, 1)
    },
    deleteQuiz(item) {
      axios
        .delete(`/api/quiz/${item.quiz_id}/${item.emp_id}`)
        .then((res) => {
          this.removeQuiz(item);
          this.getQuizzes();
          this.flash("Quiz Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
    getQuizzes() {
      axios
        .post(`/api/show-quiz-results`, {
          column: field,
          Quizzes_ids: item.Quizzes_ids,
          user_id: item.id,
          from: this.quizResultData.from,
          to: this.quizResultData.to
        })
        .then((response) => {
          this.items = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    agree() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp();
    },
    cancel() {
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp();
    },

  },
};
</script>

<style scoped>
.card-table {
  position: relative;
}

.card-scroll {
  max-height: 450px;
  overflow: auto;
  margin-top: 20px;
}
</style>