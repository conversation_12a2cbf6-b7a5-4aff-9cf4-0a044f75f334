<script>
export default {
  name: "pagination",
  props: {
    currentPage: Number,
    totalPages: Number,
    pageSize: Number,
  },
  emits:['next','previous','update:currentPage'],
  computed: {
    selectedCurrentPage:{
      get: function() {
        return this.currentPage;
      },
      set: function(value) {
        this.$emit('update:currentPage', value);
      }
    },
    selectedPageSize:{
      get: function() {
        return this.pageSize;
      },
      set: function(value) {
        this.$emit('update:pageSize', value);
      }
    }
  }
}
</script>

<template>
  <div class="pagination">
    <c-button @click="$emit('previous')" :disabled="currentPage === 1">Previous</c-button>
    <span>Page {{ currentPage }} of {{ totalPages }}</span>
    <c-button @click="$emit('next')" :disabled="currentPage === totalPages">Next</c-button>
    <select v-model="selectedPageSize" @change="selectedCurrentPage = 1">
      <option :value="10">10 per page</option>
      <option :value="20">20 per page</option>
      <option :value="50">50 per page</option>
    </select>
  </div>
</template>

<style scoped>
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.pagination button {
  padding: 5px 10px;
  border: 1px solid #ddd;
  background-color: white;
  cursor: pointer;
  border-radius: 3px;
}

.pagination button:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.pagination select {
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 3px;
}
</style>
