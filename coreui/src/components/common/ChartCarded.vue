<template>
  <div :class="`col-sm-12 col-md-6 col-lg-${loadedWidget.cols}`">
    <CCard>
      <CCardHeader>
        <strong>{{ loadedWidget.name }}</strong>
        <c-button @click="reload(loadedWidget.id)" style="float: right;background-color: blueviolet;"
          class="text-white"><c-icon name="cil-reload" /></c-button>
      </CCardHeader>
      <CCardBody>
        <component :is="widgetComponent" v-bind="currentProps" />
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
import DataTable from "../dashboard/DataTable.vue";
import DataTabs from "../dashboard/DataTabs.vue";
import DataImage from '../dashboard/DataImage.vue'
import DataList from "../dashboard/DataList.vue";
import DataListView from "../dashboard/DataListView.vue";
import { capitalize } from "../../filters";

export default {
  components: {
    DataTable,
    DataTabs,
    DataImage,
    DataList,
    DataListView
  },
  props: {
    widget: {
      type: Object,
      required: true,
    },
  },
  provide() {
    return {
      reload: this.reload
    }
  },
  data() {
    return {
      loadedWidget: null,
      labelOption: {
        show: true,
        formatter: (item) => {
          return item.val;
        },
        fontSize: 16,
        rich: {
          name: {},
        },
      },
      chartOption: null,
    };
  },
  computed: {
    widgetComponent() {
      switch (this.loadedWidget.type.name) {
        case 'table': return 'data-table';
        case 'tabs': return 'data-tabs';
        case 'image': return 'data-image';
        case 'list': return 'date-list';
        case 'view_list': return 'data-list-view';
        default: return 'e-charts';
      }
    },
    currentProps() {
      if (this.widgetComponent == 'e-charts') {
        return {
          autoResize: true,
          option: this.chartOption,
          style: {
            height: '20rem'
          }
        }
      }

      return {
        widget: this.loadedWidget
      }
    }
  },
  methods: {
    reload(id) {
      axios
        .get(`/api/widgets/${id}`)
        .then((response) => {
          this.loadedWidget = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    initialize() {
      const key = `fill${capitalize(this.loadedWidget.type.name).trimEnd()}Data`;
      this[key]();
    },
    fillPieData() {
      this.chartOption = this.pieInit();
      this.fillRoundedData(this.chartOption);
    },
    fillDonutData() {
      this.chartOption = this.donutInit();
      this.fillRoundedData(this.chartOption);
    },
    fillRoundedData(shape) {
      shape.legend.data = Object.keys(this.loadedWidget.data);
      for (const label of shape.legend.data) {
        shape.series[0].data.push({
          value: this.loadedWidget.data[label],
          name: label,
        });
      }
    },
    pieInit() {
      return {
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b} : {c}",
        },
        legend: {
          orient: "vertical",
          left: "left",
          data: [],
        },
        toolbox: {
          show: true,
          orient: "vertical",
          left: "right",
          top: "center",
          feature: {
            mark: { show: true },
            dataView: { show: true, readOnly: false },
            saveAsImage: { show: true },
          },
        },
        series: [
          {
            name: this.loadedWidget.name,
            type: "pie",
            radius: "50%",
            center: ["50%", "50%"],
            data: [],
            emphasis: {
              itemStyle: {
                shadowBlur: 5,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
    },
    donutInit() {
      return {
        tooltip: {
          trigger: "item",
        },
        legend: {
          top: "5%",
          left: "center",
          data: [],
        },
        toolbox: {
          show: true,
          orient: "vertical",
          left: "right",
          top: "center",
          feature: {
            mark: { show: true },
            dataView: { show: true, readOnly: false },
            saveAsImage: { show: true },
          },
        },
        series: [
          {
            name: this.loadedWidget.name,
            type: "pie",
            radius: ["40%", "70%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: "#fff",
              borderWidth: 2,
            },
            label: {
              show: false,
              position: "center",
              formatter: (item) => {
                return item.value;
              },
            },
            emphasis: {
              label: {
                show: true,
                fontSize: "40",
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: [],
          },
        ],
      };
    },
    barInit() {
      return {
        legend: {},
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        toolbox: {
          show: true,
          orient: "vertical",
          left: "right",
          top: "center",
          feature: {
            mark: { show: true },
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ["line", "bar", "stack"] },
            restore: { show: true },
            saveAsImage: { show: true },
          },
        },
        dataset: {
          dimensions: [],
          source: [],
        },
        xAxis: [{ type: "category", axisTick: { show: false } }],
        yAxis: [{ type: "value" }],
        series: [
          {
            type: "bar",
            emphasis: {
              focus: "series",
            },
            label: this.labelOption
          },
          {
            type: "bar",
            emphasis: {
              focus: "series",
            },
            label: this.labelOption
          },
          {
            type: "bar",
            emphasis: {
              focus: "series",
            },
            label: this.labelOption
          },
        ],
      };
    },
    fillBarData() {
      this.chartOption = this.barInit();
      const [first] = this.loadedWidget.data;
      if (!first) return;
      console.log(first);
      console.log(this.loadedWidget.data);
      this.chartOption.dataset.dimensions = Object.keys(first);
      this.chartOption.dataset.source = this.loadedWidget.data;
    },
    fillTableData() {
    },
    fillImageData() {
    },
    fillListData() {
    },
    fillView_listData() {
    },
    fillStack_barData() {

      const list = this.loadedWidget.data;
      const types = Object.keys(list[0].data) ?? [];

      const defaultReduce = types.map(type => (
        {
          name: capitalize(type).replace(' ', ''),
          type: 'bar',
          stack: 'total',
          barWidth: '60%',
          label: {
            show: true,
          },
          data: [],
        }
      ));

      const dataXAxis = list.map(item => item.name);

      const series = list.reduce((acc, item) => {
        acc = acc.map(el => {
          const sub = el.name;
          el.data.push(item.data[sub])
          return el;
        })
        return acc;
      }, defaultReduce)

      this.chartOption = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // Use axis to trigger tooltip
            type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
          }
        },
        toolbox: {
          show: true,
          orient: "vertical",
          left: "right",
          top: "center",
          feature: {
            mark: { show: true },
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ["line", "bar", "stack"] },
            restore: { show: true },
            saveAsImage: { show: true },
          },
        },
        legend: {},
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dataXAxis,
          // axisLabel: {
          //   inside: true,
          //   color: '#fff'
          // },
          axisLabel: { interval: 0, rotate: 30 }
        },
        yAxis: {
          type: 'value',
        },
        series
      }
    }
  },
  created() {
    this.loadedWidget = {
      ...this.widget
    };

    if (this.loadedWidget.type.name !== "table" && this.loadedWidget.type.name !== "tabs" && this.loadedWidget.type.name !== "image") {
      this.initialize();
    }
  },
};
</script>
