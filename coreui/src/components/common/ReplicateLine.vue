<template>
  <v-dialog
    v-model="dialog"
    :max-width="options.width"
    :style="{ zIndex: options.zIndex }"
    @keydown.esc="cancel"
  >
    <v-card>
      <v-toolbar dark :color="options.color" dense flat>
        <v-toolbar-title class="white--text">{{ title }}</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="cancel">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <c-card class="card-table">
        <c-card-body class="card-scroll">
          <div class="row">
            <div class="col-6">
              <c-form-group>
                <template #label>
                  <strong>Line</strong>
                </template>
                <template #input>
                  <v-select
                    placeholder="Search for Line"
                    v-model="line_id"
                    :options="lines"
                    label="name"
                    :value="0"
                    class="mt-2"
                    :reduce="(line) => line.id"
                  ></v-select>
                </template>
              </c-form-group>
            </div>
            <div class="col-6">
              <c-form-group>
                <template #label>
                  <strong>Options</strong>
                </template>
                <template #input>
                  <v-select
                    placeholder="Search for Options..."
                    v-model="option_id"
                    :options="fields"
                    label="name"
                    :value="0"
                    :reduce="(field) => field.id"
                    class="mt-2"
                    multiple
                  ></v-select>
                </template>
              </c-form-group>
            </div>
          </div>
          <br />
          <br />
          <br />
          <br />
          <br />
          <br />
          <br />
          <div class="row">
            <div class="col-6">
              <CInput
                label="Line Name"
                type="text"
                placeholder="Name"
                v-model="line_name"
              >
                <template slot="label">
                  Line Name <span style="color: red">*</span>
                </template>
              </CInput>
            </div>
          </div>
        </c-card-body>
        <c-card-footer
          ><c-button
            style="float: right"
            class="text-white"
            color="primary"
            @click="save()"
            >Save</c-button
          >
        </c-card-footer>
      </c-card>
    </v-card>
  </v-dialog>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { mapActions } from "vuex";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      dialog: false,
      resolve: null,
      reject: null,
      lines: [],
      fields: [],
      line_id: null,
      line_name: null,
      option_id: [],
      title: null,
      options: {
        color: "primary",
        width: 900,
        height: 600,
        zIndex: 1000000000,
      },
      readMore: {},
    };
  },
  emits: ["downloaded"],
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open(title, data, fields = [], options = null) {
      this.loadVApp();
      this.dialog = true;
      this.title = title;
      this.lines = data;
      this.fields = fields;
      this.options = Object.assign(this.options, options || this.options);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    showMore(id) {
      this.$set(this.readMore, id, true);
    },
    showLess(id) {
      this.$set(this.readMore, id, false);
    },
    agree() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp();
    },
    cancel() {
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp();
    },
    save() {
      axios
        .post(`/api/replicate-line/`, {
          line: this.line_id,
          options: this.option_id,
          line_name: this.line_name,
        })
        .then((response) => {
          this.flash("Line Replicated Successfully");
          this.line_id = null;
          this.option_id = [];
          this.line_name = null;
          this.open();
          this.getData();
        })
        .catch((err) => this.showErrorMessage(err));
    },
    getData() {
      this.get("/api/lines", "lines");
    },
  },
};
</script>

<style scoped>
.card-table {
  position: relative;
}
.card-scroll {
  max-height: 450px;
  overflow: auto;
  margin-top: 20px;
}
</style>