<template>
  <v-dialog v-model="dialog" :max-width="options.width" :style="{ zIndex: options.zIndex }" @keydown.esc="cancel">
    <v-card>
      <v-toolbar dark :color="options.color" dense flat>
        <v-toolbar-title class="white--text">{{ title }}</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="cancel">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <c-card class="card-table">
        <c-card-body class="card-scroll">
          <c-textarea label="Clarification" type="text" placeholder="Clarification"
            v-model="clarification"></c-textarea>
        </c-card-body>
        <c-card-footer>
          <CButton class="text-white" color="primary" @click="save">Save</CButton>
        </c-card-footer>
      </c-card>
    </v-card>
  </v-dialog>
</template>

<script>
import { mapActions } from "vuex";
export default {
  data() {
    return {
      dialog: false,
      resolve: null,
      reject: null,
      item: null,
      reload: null,
      widget: null,
      clarification: null,
      title: null,
      options: {
        color: "primary",
        width: 1400,
        zIndex: 1000000000,
      },
      readMore: {},
    };
  },
  emits: ["downloaded"],
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open(title, data, widget = null, reloadFn = null, options = null) {
      this.loadVApp();``
      this.dialog = true;
      this.title = title;
      this.item = data;
      this.widget = widget;
      if (reloadFn) {
        this.reload = reloadFn; // Store the passed function
      }

      this.options = Object.assign(this.options, options || this.options);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    agree() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp();
    },
    cancel() {
      this.clarification = null;
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp();
    },
    save() {
      axios
        .post(`/api/plan-clarification/`, {
          plan_id: this.item[0].id,
          clarification: this.clarification,
        })
        .then((response) => {
          this.clarification = null;
          this.flash('Clarification Saved Successfully');
          this.cancel();
          if (this.reload && typeof this.reload === 'function') {
            this.reload(this.widget.id);
          }
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
};
</script>

<style scoped>
.card-table {
  position: relative;
}

.card-scroll {
  max-height: 450px;
  overflow: auto;
  margin-top: 20px;
}
</style>