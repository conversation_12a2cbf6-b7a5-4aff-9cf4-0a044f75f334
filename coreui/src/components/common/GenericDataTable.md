# GenericDataTable Component

A comprehensive, reusable data table component built for Vue.js applications with CoreUI. This component provides all the essential features needed for modern data tables while maintaining high performance and flexibility.

## Features

### Core Features
- ✅ **Sticky/Frozen Headers** - Headers remain visible when scrolling
- ✅ **Advanced Search** - Debounced search with performance optimization
- ✅ **Sorting** - Multi-column sorting with visual indicators
- ✅ **Selection** - Row selection with "select all" functionality
- ✅ **Pagination** - Attractive pagination with customizable page sizes
- ✅ **Performance Optimized** - Handles large datasets efficiently (3M+ records)
- ✅ **Real-time Updates** - Reactive to data changes
- ✅ **Responsive Design** - Works on all screen sizes

### Styling Features
- ✅ **Custom Styling** - White headers with contrasting text
- ✅ **Professional Design** - Modern, attractive appearance
- ✅ **Smooth Animations** - Enhanced user experience
- ✅ **Horizontal Scroll** - Visible scrollbar for wide tables
- ✅ **Visual Indicators** - Sort icons, loading states, etc.

### Developer Features
- ✅ **Slot-based Customization** - Custom cell rendering
- ✅ **Event-driven** - Comprehensive event system
- ✅ **TypeScript Ready** - Full prop validation
- ✅ **Performance Monitoring** - Development mode performance tracking

## Basic Usage

```vue
<template>
  <GenericDataTable
    :items="tableData"
    :columns="tableColumns"
    :selectable="true"
    @selection-change="handleSelectionChange"
  />
</template>

<script>
import GenericDataTable from '@/components/common/GenericDataTable.vue';

export default {
  components: {
    GenericDataTable
  },
  data() {
    return {
      tableData: [
        { id: 1, name: 'John Doe', email: '<EMAIL>', role: 'Admin' },
        { id: 2, name: 'Jane Smith', email: '<EMAIL>', role: 'User' }
      ],
      tableColumns: [
        { key: 'name', label: 'Full Name', sortable: true },
        { key: 'email', label: 'Email Address', sortable: true },
        { key: 'role', label: 'Role', sortable: true }
      ]
    };
  },
  methods: {
    handleSelectionChange(selectedItems) {
      console.log('Selected:', selectedItems);
    }
  }
};
</script>
```

## Props

### Core Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `items` | Array | `[]` | Array of data objects to display |
| `columns` | Array | `[]` | Column configuration array |
| `searchTerm` | String | `''` | External search term |
| `selectedItems` | Array | `[]` | Currently selected items |
| `selectable` | Boolean | `false` | Enable row selection |
| `isLoading` | Boolean | `false` | Show loading state |

### Display Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `showSearch` | Boolean | `true` | Show search input |
| `showTotalBar` | Boolean | `true` | Show total records bar |
| `showPagination` | Boolean | `true` | Show pagination controls |
| `searchPlaceholder` | String | `'Search in all fields...'` | Search input placeholder |

### Pagination Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `initialPageSize` | Number | `25` | Initial page size |
| `pageSizeOptions` | Array | `[10, 25, 50, 100, 200]` | Available page sizes |

### Advanced Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `itemKey` | String/Function | `'id'` | Unique key for items |
| `tableHeight` | String | `'600px'` | Table container height |

## Column Configuration

Each column object supports the following properties:

```javascript
{
  key: 'field_name',        // Required: Field key or path (supports dot notation)
  label: 'Display Name',    // Required: Column header text
  sortable: true,           // Optional: Enable sorting (default: true)
  type: 'text',            // Optional: Data type (text, date, number, currency)
  width: 'auto',           // Optional: Column width
  format: (value, item) => value  // Optional: Custom formatter function
}
```

### Column Types
- `text` - Default text rendering
- `date` - Automatic date formatting
- `number` - Number formatting with locale
- `currency` - Currency formatting

## Custom Slots

Use scoped slots to customize cell rendering:

```vue
<GenericDataTable :items="items" :columns="columns">
  <!-- Custom status rendering -->
  <template #status="{ value, item, column, index }">
    <span :class="getStatusClass(value)">{{ value }}</span>
  </template>

  <!-- Custom actions column -->
  <template #actions="{ item }">
    <CButton @click="editItem(item)">Edit</CButton>
    <CButton @click="deleteItem(item)">Delete</CButton>
  </template>

  <!-- Custom date formatting -->
  <template #created_at="{ value }">
    {{ formatDate(value) }}
  </template>
</GenericDataTable>
```

## Events

| Event | Payload | Description |
|-------|---------|-------------|
| `search` | `searchTerm` | Fired when search term changes |
| `sort` | `{ column, direction }` | Fired when column is sorted |
| `selection-change` | `selectedItems[]` | Fired when selection changes |
| `page-change` | `pageNumber` | Fired when page changes |
| `page-size-change` | `pageSize` | Fired when page size changes |

## Advanced Examples

### Custom Cell Rendering
```vue
<GenericDataTable :items="projects" :columns="projectColumns">
  <template #progress="{ value }">
    <div class="progress-bar">
      <div class="progress-fill" :style="{ width: value + '%' }"></div>
    </div>
  </template>
</GenericDataTable>
```

### Performance Optimization
```vue
<GenericDataTable
  :items="largeDataset"
  :columns="columns"
  :initial-page-size="100"
  :page-size-options="[50, 100, 200, 500]"
  :item-key="item => item.unique_id"
/>
```

### Real-time Updates
```vue
<GenericDataTable
  :items="liveData"
  :columns="columns"
  :is-loading="isRefreshing"
  @search="handleSearch"
  @sort="handleSort"
/>
```

## Migration from VisitsReportTable

To migrate from the original VisitsReportTable:

1. Replace the component import
2. Convert `fields` array to `columns` configuration
3. Move custom cell rendering to scoped slots
4. Update event handlers

### Before (VisitsReportTable)
```vue
<VisitsReportTable
  :items="visits"
  :fields="['name', 'date', 'status']"
  :readMore="readMoreState"
  @toggleDetails="handleToggleDetails"
/>
```

### After (GenericDataTable)
```vue
<GenericDataTable
  :items="visits"
  :columns="visitColumns"
  :selectable="true"
>
  <template #status="{ value }">
    <span :class="getStatusClass(value)">{{ value }}</span>
  </template>
</GenericDataTable>
```

## Performance Tips

1. **Use `item-key` function** for complex unique identification
2. **Limit initial page size** for large datasets
3. **Implement server-side filtering** for very large datasets
4. **Use slots sparingly** - only when needed for custom rendering
5. **Debounce external search** to avoid excessive API calls

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Dependencies

- Vue.js 2.6+
- CoreUI Vue components
- Moment.js (for date formatting)

## License

This component is part of the ProCRM project and follows the same licensing terms.
