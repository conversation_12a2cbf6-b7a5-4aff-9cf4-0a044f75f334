<template>
  <v-dialog v-model="dialog" :max-width="options.width" :style="{ zIndex: options.zIndex }" @keydown.esc="cancel">
    <v-card>
      <v-toolbar dark :color="options.color" dense flat>
        <v-toolbar-title class="white--text">{{ title }}</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="cancel">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <c-card>
        <c-card-header>
          <span class="text-secondary">#{{ $route.params.id }}</span>
          <strong>{{ doctor.name }}</strong>
          <span v-if="doctor.inactive_date == null"
            class="bg-success rounded text-white ml-2 mt-2 p-1 pl-3 pr-3 font-bold">Active</span>
          <span v-else class="bg-danger rounded text-white ml-2 mt-2 p-1 pl-3 pr-3 font-bold">In Active</span>
        </c-card-header>
        <c-card-body>
          <div class="row">
            <div
              class="col-sm-2 text-whit justify-content-center text-center border border-dark bg-transparet shadow-sm"
              style="background: #c2d9f0">
              <div class="p-3 w-100 h-100 text-center d-flex align-items-center m-auto justify-content-center">
                <CIcon class="custom_icon user-icon" name="cil-user" />
              </div>
            </div>
            <div class="col-sm-10">
              <h3 class="text-primary font-weight-bold">Specialist</h3>
              <h4>
                <CIcon class="custom_icon" name="cilPuzzle" />
                {{ doctor.speciality }}
              </h4>
              <h5>
                <CIcon class="custom_icon mr-1" name="cil-phone" />
                {{ mobile }} <span class="mr-3 ml-3 text-secondary">|</span>
                <CIcon class="custom_icon mr-1" name="cil-envelope-closed" />
                {{ show }}
              </h5>
              <div class="row align-items-center justify-content-center">
                <div class="col-3 font-weight-bold text-secondary h5">
                  Classification
                </div>
                <div class="progress col-6 p-0">
                  <div v-if="doctor.class == 'A'" class="progress-bar bg-success" role="progressbar" style="width: 98%"
                    aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
                  <div v-if="doctor.class == 'B'" class="progress-bar bg-primary" role="progressbar" style="width: 70%"
                    aria-valuenow="70" aria-valuemin="0" aria-valuemax="100"></div>
                  <div v-if="doctor.class == 'C'" class="progress-bar bg-danger" role="progressbar" style="width: 50%"
                    aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <div class="col-3 h5 text-dark">{{ doctor.class }}</div>
              </div>
            </div>
          </div>
        </c-card-body>
        <c-card-footer> </c-card-footer>
      </c-card>
      <c-card>
        <c-card-body>
          <c-tabs>
            <c-tab active>
              <template slot="title">
                <CIcon class="custom_icon" name="cil-description" /> Information
              </template>
              <c-data-table striped small fixed :items="items" :fields="fields">
                <template slot="value" slot-scope="data">
                  <td>
                    <strong>{{ data.item.value }}</strong>
                  </td>
                </template>
              </c-data-table>
            </c-tab>

            <c-tab>
              <template slot="title">
                <CIcon class="custom_icon" name="cil-menu" /> Lines
              </template>
              <c-data-table hover striped sorter tableFilter footer itemsPerPageSelect :items="accounts"
                :fields="accounts_fields" :items-per-page="100" :active-page="1" :responsive="true"
                :key="tableKeys.lines" pagination thead-top>
                <!-- <template slot="thead-top">
                  <td style="border-top: none"><strong>Total</strong></td>
                  <td style="border-top: none" class="text-xs-right">
                    {{ accounts.length }}
                  </td>
                </template> -->
              </c-data-table>
            </c-tab>
            <c-tab>
              <template slot="title">
                <CIcon class="custom_icon" name="cil-map" /> Locations
              </template>
              <gmap-map v-if="positions.length > 0" class="mt-3" :center="center" :zoom="18"
                style="width: 100%; height: 340px">
                <gmap-cluster>
                  <gmap-marker v-for="(position, key) in positions" :key="key" :position="position" :clickable="true" />
                </gmap-cluster>
              </gmap-map>
            </c-tab>
            <c-tab>
              <template slot="title">
                <CIcon class="custom_icon" name="cil-clock" /> Visits
              </template>
              <v-container>
                <v-row style="justify-content: space-around" class="mt-2 px-16">
                  <c-button v-for="(role, index) in roles" :key="index" class="mt-1 btn btn-primary"
                    @click="dispalyVisits(role)">
                    {{ role.name }}
                  </c-button>
                </v-row>
              </v-container>
              <c-data-table v-if="visits.length > 0" hover striped sorter tableFilter footer itemsPerPageSelect
                :items="visits" :fields="visitFields" :items-per-page="100" :active-page="1" :responsive="true"
                :key="tableKeys.visits" pagination thead-top>
                <template slot="thead-top">
                  <td style="border-top: none"><strong>Total</strong></td>
                  <td style="border-top: none" class="text-xs-right">
                    {{ visits.length }}
                  </td>
                </template>
                <template #employee="{ item }">
                  <td :style="{
                    color: item.color,
                  }">
                    <strong>{{ item.employee }}</strong>
                  </td>
                </template>
                <template #division="{ item }">
                  <td :style="{
                    color: item.color,
                  }">
                    <strong>{{ item.division }}</strong>
                  </td>
                </template>
                <template #visit_date="{ item }">
                  <td>
                    <v-chip :style="{ backgroundColor: colorfulDate(item), color: 'white' }">
                      {{ item.visit_date }}
                    </v-chip>
                  </td>
                </template>
              </c-data-table>
            </c-tab>
            <c-tab>
              <template slot="title">
                <CIcon class="custom_icon" name="cil-chart-pie" />Requests
              </template>
              <c-data-table hover striped sorter tableFilter footer itemsPerPageSelect :items="commercial_requests"
                :items-per-page="100" :active-page="1" :responsive="true" pagination :key="tableKeys.requests" thead-top
                id="print">
                <template slot="thead-top">
                  <td style="border-top: none"><strong>Total</strong></td>
                  <td style="border-top: none" class="text-xs-right">
                    {{ commercial_requests.length }}
                  </td>
                </template>
              </c-data-table>
            </c-tab>
          </c-tabs>
        </c-card-body>
        <c-card-footer>
          <c-button color="primary" @click="cancel">Back</c-button>
          <download @getPrint="print" @getxlsx="download" @getpdf="createPDF" @getcsv="downloadCsv"
            :fields="visitFields" :data="visits" :name="name" />
        </c-card-footer>
      </c-card>
    </v-card>
  </v-dialog>
</template>

<script>
import download from "../../components/download-reports/download.vue";
import { mapActions } from "vuex";
import jsPDF from "jspdf";
import "jspdf-autotable";
import { capitalize } from "../../filters";
export default {
  components: {
    download,
  },
  data() {
    return {
      dialog: false,
      resolve: null,
      reject: null,
      show: null,
      doctor: {
        name: "",
        active_date: "",
        inactive_date: "",
        speciality: "",
        class: "",
      },
      searchFilters: {
        lines: "",
        requests: "",
        visits: "",
      },
      tableKeys: {
        lines: Date.now(),
        requests: Date.now(),
        visits: Date.now(),
      },
      doctor_id: null,
      accounts: [],
      roles: [],
      doctors: [],
      visits: [],
      visitFields: [
        "id",
        "visit_date",
        "line",
        "division",
        "employee",
        "account",
        "brick",
        "doctor",
        "speciality",
        "acc_type",
        "type",
      ],
      commercial_requests: [],
      mobile: null,
      email: null,
      positions: [],
      items: [],
      accounts_fields: ["id", "line", "division", "account", "type", "doctor"],
      social_items: [],
      fields: [{ key: "key" }, { key: "value" }],
      socials_fields: ["id", "doctor_name", "social_name", "link"],
      center: { lat: 0, lng: 0 },
      title: null,
      options: {
        color: "primary",
        width: 1400,
        zIndex: **********,
      },
      name: "visits",
    };
  },
  emits: ["downloaded"],
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    print() {
      this.$htmlToPaper("print");
    },
    download() {
      this.downloadXlsx(this.visits, "visits.xlsx");
      this.$emit("downloaded");
    },
    downloadCsv() {
      this.downloadXlsx(this.visits, "visits.csv");
      this.$emit("downloaded");
    },
    createPDF() {
      let pdfName = this.name;
      const columns = this.visitFields.map((field) => {
        return {
          title: capitalize(field),
          dataKey: field,
        };
      });
      const body = this.visits;
      const doc = new jsPDF({ filters: ["ASCIIHexEncode"] });

      doc.addFileToVFS("Amiri-Regular.ttf", Amiri);
      doc.addFont("Amiri-Regular.ttf", "Amiri", "normal");
      doc.setFont("Amiri");
      doc.setFontSize(10);
      doc.autoTable({
        columns,
        body,
        margin: { top: 10 },
        showHead: "firstPage",
        styles: {
          lineColor: "#c7c7c7",
          lineWidth: 0,
          cellPadding: 2,
          font: "Amiri",
        },
      });
      doc.save(pdfName + ".pdf");
    },
    resetFilters() {
      this.searchFilters = {
        lines: "",
        requests: "",
        visits: "",
      };
      // Force re-render of tables
      this.tableKeys = {
        lines: Date.now(),
        requests: Date.now(),
        visits: Date.now(),
      };
    },
    open(title, data, fields = [], options = null) {
      this.loadVApp();
      this.dialog = true;
      this.title = title;
      this.doctor_id = data;
      axios
        .get(`/api/doctors/${this.doctor_id}`)
        .then((response) => {
          this.doctor = response.data.doctor;
          this.visits = response.data.visits;
          this.doctors = response.data.doctors;
          this.commercial_requests = response.data.doctors;
          this.positions = response.data.locations;
          this.center = this.positions[0];
          this.accounts = response.data.accounts;
          this.roles = response.data.roles;
          this.mobile =
            response.data.doctor.mobile == null
              ? "--"
              : response.data.doctor.mobile;
          this.show =
            response.data.doctor.show == null
              ? "--"
              : response.data.doctor.show;
          const items = Object.entries(response.data.doctor);
          this.items = items.map(([key, value]) => {
            return { key: key, value: value };
          });
          this.social_items = response.data.socials;
          this.resetFilters();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      this.options = Object.assign(this.options, options || this.options);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    dispalyVisits(role) {
      axios
        .post(`/api/doctors/${this.doctor_id}/visits/${role.id}`)
        .then((response) => {
          this.visits = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    colorfulDate(item) {
      // // Extract unique visit dates
      // let uniqueDatesArray = [...new Set(this.visits.map(obj => obj.visit_date))];

      // // Color palette
      // const colorsArray = ['indigo', 'yellowgreen', 'teal', 'steelblue', 'seagreen', 'purple', 'orange', 'brown', 'red', 'pink', 'cyan'];

      // // Assign a unique color for each visit date
      // const myDatesHashTable = {};

      // uniqueDatesArray.forEach((date, index) => {
      //   myDatesHashTable[date] = colorsArray[index % colorsArray.length]; // Cycle colors if needed
      // });
      // console.log(myDatesHashTable[item.visit_date]);

      // return myDatesHashTable[item.visit_date] || 'transparent';
      if (!this.monthColors) {
        this.monthColors = {}; // Store assigned colors for each month
        const uniqueMonths = [...new Set(this.visits.map(obj => obj.visit_date.split('-').slice(0, 2).join('-')))];

        const colorsArray = ['navy', 'indigo', 'yellowgreen', 'teal', 'steelblue', 'seagreen', 'purple', 'orange', 'brown', 'red', 'pink', 'cyan'];

        uniqueMonths.forEach((month, index) => {
          this.monthColors[month] = colorsArray[index % colorsArray.length]; // Assign colors
        });
      }

      const monthKey = item.visit_date.split('-').slice(0, 2).join('-'); // Extract "YYYY-MMM" as key
      return this.monthColors[monthKey] || 'transparent';
    },
    agree() {
      this.resolve(true);
      this.resetFilters();
      this.dialog = false;
      this.unLoadVApp();
    },
    cancel() {
      this.resetFilters();
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp();
    },
  },
};
</script>

<style scoped>
.card-table {
  position: relative;
}

.card-scroll {
  max-height: 450px;
  overflow: auto;
  margin-top: 20px;
}
</style>