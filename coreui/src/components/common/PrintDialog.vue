<template>
  <v-dialog
    v-model="dialog"
    :max-width="options.width"
    :style="{ zIndex: options.zIndex }"
    @keydown.esc="cancel"
  >
    <v-card>
      <v-toolbar dark :color="options.color" dense flat>
        <v-toolbar-title class="white--text">{{ title }}</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="cancel">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <c-card class="card-table">
        <c-card-header
          ><c-button
            color="warning"
            title="Print"
            style="float: right"
            class="btn-sm mr-1 text-white"
            @click="print"
            ><c-icon name="cil-print" /></c-button
        ></c-card-header>
        <c-card-body class="card-scroll" id="printTable">
          <div class="row">
            <div class="col-6">
              <img v-if="gemstoneUrl" :src="gemstoneUrl" style="float: left" />
            </div>
            <div class="col-6">
              <img
                v-if="url"
                style="width: 256px; height: 84px; float: right"
                class="c-sidebar-brand-full"
                :src="url"
              />
            </div>
          </div>
          <c-data-table
            hover
            header
            striped
            footer
            :items="items"
            :fields="fields"
            :responsive="true"
            thead-top
          >
            <template slot="thead-top">
              <td style="border-top: none"><strong>Total</strong></td>
              <td style="border-top: none" class="text-xs-right">
                {{ items.length }}
              </td>
            </template>

            <template #status="{ item }">
              <td>
                <strong v-if="item.status == 1" style="color: green"
                  >Approved</strong
                >
                <strong v-if="item.status == 0" style="color: red"
                  >Dispproved</strong
                >
                <strong v-if="item.status == null" style="color: blue"
                  >Pending</strong
                >
              </td>
            </template>

            <template #division="{ item }">
              <td>
                <strong
                  :style="{
                    color: item.color,
                  }"
                  >{{ item.division }}</strong
                >
              </td>
            </template>
          </c-data-table>
          <div class="totalCard">
            <strong>Total Amount: {{ totalAmount.toLocaleString() }}</strong>
            <br />
          </div>
        </c-card-body>
      </c-card>
    </v-card>
  </v-dialog>
</template>

<script>
import { mapActions } from "vuex";
export default {
  data() {
    return {
      dialog: false,
      resolve: null,
      reject: null,
      items: [],
      fields: [],
      totalAmount: 0,
      url: null,
      gemstoneUrl: null,
      title: null,
      options: {
        color: "primary",
        width: 1400,
        zIndex: 1000000000,
      },
      readMore: {},
    };
  },
  emits: ["downloaded"],
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open(title, data, fields = [], options = null) {
      this.loadVApp();
      this.getItgatesLogo();
      this.getLogo();
      this.dialog = true;
      this.title = title;
      this.totalAmount = 0;
      this.items = data;
      this.items.forEach((item) => (this.totalAmount += Number(item.total)));
      if (fields.length == 0) {
        this.fields = Object.keys(this.items[0]);
      } else {
        this.fields = fields;
      }
      this.options = Object.assign(this.options, options || this.options);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    showMore(id) {
      this.$set(this.readMore, id, true);
    },
    showLess(id) {
      this.$set(this.readMore, id, false);
    },
    agree() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp();
    },
    cancel() {
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp();
    },
    getLogo() {
      axios
        .get(`/api/companies/1`)
        .then((response) => {
          this.url = response.data.data.company.url;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getItgatesLogo() {
      axios
        .get(`/api/logo/3`)
        .then((response) => {
          this.gemstoneUrl = response.data.data.url;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    print() {
      this.$htmlToPaper("printTable");
    },
  },
};
</script>

<style scoped>
.card-table {
  position: relative;
}
.card-scroll {
  max-height: 450px;
  overflow: auto;
  margin-top: 20px;
}
.totalCard {
  width: 300px;
  height: 50px;
  float: left;
  border-radius: 5px;
  color: white;
  padding: 15px;
  background-color: brown;
  animation-name: totalCard;
  animation-duration: 4s;
}

@keyframes totalCard {
  0% {
    background-color: blue;
  }
  25% {
    background-color: brown;
  }
  50% {
    background-color: red;
  }
  100% {
    background-color: green;
  }
}
</style>