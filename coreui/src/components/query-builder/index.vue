<template>
  <c-form-group>
    <template #input>
      <c-row>
        <v-select
          title="Search for option"
          v-model="option"
          :options="options"
          label="name"
          class="mt-2"
          :reduce="(option) => option.name"
          @option:selected="updateOption"
        />
      </c-row>
      <c-row>
        <c-col col="5">
          <v-select
            title="Search for option"
            v-model="selectedTable"
            :options="tables"
            label="name"
            class="mt-2"
            :reduce="(table) => table.index"
            @option:selected="resetColumns"
          />
        </c-col>
        <c-col col="5">
          <v-select
            v-if="selectedTable != null"
            title="Search for option"
            v-model="selectedColumns"
            multiple
            :options="tables[selectedTable].columns"
            label="Field"
            :value="0"
            class="mt-2"
            :reduce="(column) => column.Field"
            @option:selected="newSelection"
          />
        </c-col>
        <c-col col="2">
          <c-button @click="done">Ok</c-button>
        </c-col>
      </c-row>
    </template>
  </c-form-group>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      query: {},
      options: [
        { slug: "Where", name: "where" },
        // { slug: "Where", name: "where" },
      ],
      option: null,
      tables: [],
      selectedTable: null,
      selectedColumns: [],
    };
  },
  methods: {
    async initialize() {
      try {
        let res = await axios.get("/api/tables");
        this.tables = res.data.data;
      } catch (e) {
        this.showErrorMessage(e);
      }
    },
    resetColumns() {
      this.query.from = this.tables[this.selectedTable].name;
      this.selectedColumns = [];
    },
    newSelection() {
      this.query.select = this.selectedColumns;
    },
    updateOption() {},
    done() {
      this.$emit("ok", this.query);
    },
  },
  created() {
    this.initialize();
  },
};
</script>
