<template>
  <div class="select-items" v-show="show">
    <div
      class="item"
      @click="
        () => {
          all = !all;
          selectAll();
        }
      "
    >
      <input type="checkbox" v-model="all" />
      <span>{{ all ? "Select None" : "Select All" }}</span>
    </div>
    <div
      class="item"
      v-for="(column, index) in columns"
      :key="index"
      @click="selectColumn(index)"
    >
      <input type="checkbox" :value="column" v-model="selectedColumns" />
      <span>{{ column.Field | slugRemove | capitalize }}</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    columns: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      show: false,
      all: false,
      selectedColumns: [],
    };
  },
  methods: {
    selectColumn(index) {
      if (
        ~this.selectedColumns.findIndex(
          (item) => this.columns[index].Field == item.Field
        )
      ) {
        this.selectedColumns = this.selectedColumns.filter(
          (item) => item.Field != this.columns[index].Field
        );
      } else {
        this.selectedColumns.push(this.columns[index]);
      }

      this.$emit("input", this.selectedColumns);
    },
    selectAll() {
      this.selectedColumns = [];
      if (this.all) {
        this.columns.forEach((item) => {
          this.selectedColumns.push(item);
        });
      }
      this.$emit("input", this.selectedColumns);
    },
    reset() {
      this.all = false;
      this.selectAll();
    },
    toggle() {
      this.show = !this.show;
    },
    close() {
      this.show = false;
    },
  },
};
</script>
<style scoped>
.select-items {
  height: 10rem;
  overflow-y: scroll;
}
.item {
  display: flex;
  gap: 0.5rem;
  padding: 0.5rem;
  cursor: pointer;
}
.selected {
  border: 1px solid transparent;
  border-radius: 0.2rem;
  background-color: #509ee3;
  color: white;
}
.item:hover {
  border: 1px solid transparent;
  border-radius: 0.2rem;
  background-color: #509ee3;
  color: white;
}
.item:hover .icon {
  color: white;
}
.icon {
  color: #b8bbc3;
}
</style>
