<template>
  <v-card class="mx-4 p-4" color="#ecf5fc">
    <v-row>
      <!-- tables -->
      <v-select
        v-if="table != null"
        v-model="table"
        :items="tables"
        filled
        dense
        clearable
        offset-y
        searchable
        label="Table"
      >
        <template v-slot:item="{ attrs, on, item }">
          <v-list-item v-bind="attrs" v-on="on">
            <v-list-item-content>
              <v-list-item-title>
                {{ item.name | slugRemove | capitalize }}</v-list-item-title
              >
            </v-list-item-content>
          </v-list-item>
        </template>
        <template v-slot:selection="{ item, attrs, on }">
          <v-list-item v-bind="attrs" v-on="on">
            <v-list-item-content>
              <v-list-item-title>
                {{ item.name | slugRemove | capitalize }}</v-list-item-title
              >
            </v-list-item-content>
          </v-list-item>
        </template>
      </v-select>
      <v-spacer></v-spacer>
      <!-- columns -->
      <v-select
        v-if="selectedTable != null"
        v-model="selectedColumns"
        :items="columns"
        offset-y
        label="Columns"
        filled
        dense
        clearable
        chips
        multiple
      >
        <template v-slot:item="{ item, attrs, on }">
          <v-list-item v-on="on" v-bind="attrs" #default="{ active }">
            <v-list-item-action>
              <v-checkbox :input-value="active"></v-checkbox>
            </v-list-item-action>
            <v-list-item-content>
              <v-list-item-title>
                <v-row no-gutters align="center">
                  <span>{{ item.Field | slugRemove }}</span>
                </v-row>
              </v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </template>
        <template v-slot:selection="{ item }">
          <v-chip :color="item.color" small>{{
            item.Field | slugRemove
          }}</v-chip>
        </template>
      </v-select>
    </v-row>
  </v-card>
</template>

<script>
export default {
  data() {
    return {
      tables: [],
      selectedColumns: [],
      table: null,
    };
  },
  computed: {
    selectedTable() {
      return this.tables.find((table) => table.value == this.table);
    },
    columns() {
      let columns = [];
      this.selectedTable == null
        ? (columns = [])
        : (columns = this.selectedTable.columns);

      return columns.map((columns, index) => {
        return { ...columns, value: index };
      });
    },
  },
  methods: {
    async initialize() {
      try {
        let res = await axios.get("/api/tables");
        this.tables = res.data.data;
        this.table = this.tables[0];
      } catch (e) {
        this.showErrorMessage(e);
      }
    },
    toggle() {
      this.$nextTick();
    },
    log() {
      console.log(this.table.columns);
    },
  },
  watch: {
    table: {
      handler: "log",
    },
  },
  created() {
    this.initialize();
  },
};
</script>
