<template>
  <div v-if="table != null">
    <!-- table and column selection -->
    <div class="flex">
      <div class="color-primary space-1">Data</div>
      <svg
        viewBox="0 0 32 32"
        width="16"
        height="16"
        fill="currentcolor"
        role="img"
        aria-label="close icon"
        data-testid="remove-step"
      >
        <path
          d="M4 8 L8 4 L16 12 L24 4 L28 8 L20 16 L28 24 L24 28 L16 20 L8 28 L4 24 L12 16 z "
        ></path>
      </svg>
    </div>
    <div class="flex gap-4">
      <div class="space-1">
        <div class="border rounded-sm color-primary">
          <div
            class="
              flex
              bg-gray
              color-white
              align-items-center
              border
              rounded-sm
              padding-2
              color-primary
              gap-o1
            "
          >
            <div
              class="
                color-white
                bg-primary
                border
                rounded-sm-tl
                padding-1
                cursor-pointer
              "
              @click="$refs.tableSelect.toggle()"
            >
              <div class="flex">
                <div>{{ table.name | slugRemove | capitalize }}</div>
                <span class="hide"> </span>
              </div>
            </div>
            <div
              class="
                color-white
                bg-primary
                border
                rounded-sm-rb
                padding-1
                cursor-pointer
              "
              @click="$refs.columnsSelect.toggle()"
            >
              <div>
                <div>
                  <svg
                    viewBox="0 0 32 32"
                    width="14"
                    height="14"
                    fill="currentcolor"
                    role="img"
                    aria-label="table icon"
                  >
                    <path
                      d="M11.077 11.077h9.846v9.846h-9.846v-9.846zm11.077 11.077H32V32h-9.846v-9.846zm-11.077 0h9.846V32h-9.846v-9.846zM0 22.154h9.846V32H0v-9.846zM0 0h9.846v9.846H0V0zm0 11.077h9.846v9.846H0v-9.846zM22.154 0H32v9.846h-9.846V0zm0 11.077H32v9.846h-9.846v-9.846zM11.077 0h9.846v9.846h-9.846V0z"
                    ></path>
                  </svg>
                </div>
                <span class="hide"></span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="align-items-center flex space-2">
        <button class="padding-2" @click="submit">
          <div>
            <svg
              viewBox="0 0 32 32"
              width="14"
              height="14"
              fill="currentcolor"
              role="img"
              aria-label="play icon"
            >
              <path
                d="M2 2.432v27.136a2 2 0 0 0 2.986 1.74L28.929 17.74a2 2 0 0 0 0-3.48L4.986.692A2 2 0 0 0 2 2.432z"
              ></path>
            </svg>
          </div>
        </button>
      </div>
    </div>
    <!-- pop-up menu table -->
    <table-select
      v-model="table"
      :tables="tables"
      @change="$refs.columnsSelect.reset()"
      ref="tableSelect"
      class="
        border
        rounded-sm
        padding-2
        bg-white
        layer-4
        absolute
        top-0
        left-0
        translate-1
      "
    />
    <column-select
      v-model="columns"
      :columns="table.columns"
      ref="columnsSelect"
      class="
        border
        rounded-sm
        padding-2
        bg-white
        layer-4
        absolute
        top-0
        left-0
        translate-2
      "
    />
    <div class="flex">
      <span class="color-primary space-1">Filter</span>
      <svg
        viewBox="0 0 32 32"
        width="16"
        height="16"
        fill="currentcolor"
        role="img"
        aria-label="close icon"
        data-testid="remove-step"
      >
        <path
          d="M4 8 L8 4 L16 12 L24 4 L28 8 L20 16 L28 24 L24 28 L16 20 L8 28 L4 24 L12 16 z "
        ></path>
      </svg>
    </div>
    <div class="flex gap-4">
      <div class="space-1"></div>
    </div>
    <common-actions />
  </div>
</template>

<script>
import TableSelect from "./TableSelect";
import ColumnSelect from "./ColumnSelect";
import CommonActions from "./CommonActions";
export default {
  components: {
    ColumnSelect,
    TableSelect,
    CommonActions,
  },
  data() {
    return {
      tables: [],
      columns: [],
      table: null,
      query: null,
    };
  },
  methods: {
    async initialize() {
      try {
        let res = await axios.get("/api/tables");
        this.tables = res.data.data;
        this.table = this.tables[0];
      } catch (e) {
        this.showErrorMessage(e);
      }
    },
    submit() {
      this.query = {
        from: this.table.name,
        select: this.columns.map((item) => item.Field),
      };
      this.$emit("query", this.query);
    },
  },
  created() {
    this.initialize();
  },
};
</script>
<style scoped>
.flex {
  display: flex;
  --space: 2%;
}

.row {
  flex-flow: row wrap;
}
.layer-1 {
  z-index: 1;
}
.layer-2 {
  z-index: 2;
}
.layer-3 {
  z-index: 3;
}
.layer-4 {
  z-index: 4;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.top-0 {
  top: 0;
}
.top-1 {
  top: 0.5rem;
}
.left-0 {
  left: 0;
}
.left-1 {
  left: 0.5rem;
}

.translate-1 {
  transform: translateX(3rem) translateY(7rem) translateZ(0rem);
}
.translate-2 {
  transform: translateX(7rem) translateY(7rem) translateZ(0rem);
}

.gap-o1 {
  gap: 0.1rem;
}
.gap-1 {
  gap: 0.5rem;
}
.gap-2 {
  gap: 1rem;
}
.gap-4 {
  gap: 2rem;
}
.align-items-center {
  align-items: center;
}
.space-1 {
  flex-basis: calc(75% - var(--space));
}
.space-2 {
  flex-basis: calc(25% - var(--space));
}
.padding-o1 {
  padding: 0.1rem;
}
.padding-1 {
  padding: 0.5rem;
}
.padding-2 {
  padding: 1rem;
}
.margin-y-2 {
  padding: 1rem 0;
}
.border {
  border: 1px solid transparent !important;
}
.rounded-sm {
  border-radius: 0.5rem !important;
}
.rounded-sm-tl {
  border-radius: 0.3rem 0 0 0.3rem;
}
.rounded-sm-rb {
  border-radius: 0 0.3rem 0.3rem 0;
}
.cursor-pointer {
  cursor: pointer;
}
.color-red {
  color: red;
}
.color-white {
  color: white;
}
.color-yellow {
  color: yellow;
}
.bg-red {
  background-color: red;
}
.bg-yellow {
  background-color: yellow;
}
.bg-white {
  background-color: white;
}
.color-primary {
  color: #509ee3;
}
.bg-primary {
  background-color: #509ee3 !important;
}
.bg-gray {
  background-color: #ecf5fc !important;
}
.color-purple {
  color: #7172ad;
}
.bg-purple {
  background-color: #e0e0ec;
}

.bg-green {
  background-color: #d8e9c5;
}
.color-green {
  color: #88bf4d;
}
.bg-blue {
  background-color: #f2f7fd;
}
.color-blue {
  color: #93a1ab;
}
</style>
