<template>
  <div class="flex gap-1 margin-y-2">
    <button
      class="padding-2 color-purple bg-purple rounded-sm"
      v-if="actions.filter"
      @click="filter"
    >
      <div>
        <svg
          viewBox="0 0 32 32"
          width="18"
          height="18"
          fill="currentcolor"
          role="img"
          aria-label="filter icon"
        >
          <path
            d="M3.556 4h24.888a3.556 3.556 0 1 1 0 7.111H3.556a3.556 3.556 0 1 1 0-7.111zm4 11.556h16.888a2.222 2.222 0 1 1 0 4.444H7.556a2.222 2.222 0 0 1 0-4.444zM12 24.444h8a2.222 2.222 0 0 1 0 4.445h-8a2.222 2.222 0 0 1 0-4.445z"
          ></path>
        </svg>
        <div>Filter</div>
      </div>
    </button>
    <button
      class="padding-2 rounded-sm bg-green color-green"
      v-if="actions.summarize"
      @click="summarize"
    >
      <div>
        <svg
          viewBox="0 0 32 32"
          width="18"
          height="18"
          fill="currentcolor"
          role="img"
          aria-label="sum icon"
        >
          <path
            d="M3 27.41l1.984 4.422L27.895 32l.04-5.33-17.086-.125 8.296-9.457-.08-3.602L11.25 5.33H27.43V0H5.003L3.08 4.51l10.448 10.9z"
          ></path>
        </svg>
        <div>Summarize</div>
      </div>
    </button>
    <button
      class="padding-2 rounded-sm bg-blue color-primary"
      @click="join"
      v-if="actions.join"
    >
      <div>
        <svg
          viewBox="0 0 32 32"
          width="18"
          height="18"
          fill="currentcolor"
          role="img"
          aria-label="join_left_outer icon"
        >
          <path
            d="M17.077 7.148A10.285 10.285 0 0 1 21.818 6C27.441 6 32 10.477 32 16s-4.559 10-10.182 10c-2.038 0-3.936-.588-5.528-1.6l.454-.31c.283-.194.555-.4.816-.617a8.842 8.842 0 0 0 4.258 1.083c4.832 0 8.738-3.836 8.738-8.556 0-4.72-3.906-8.556-8.738-8.556a8.865 8.865 0 0 0-3.546.733 10.65 10.65 0 0 0-1.195-1.03zM15.71 24.399A10.268 10.268 0 0 1 10.182 26C4.559 26 0 21.523 0 16S4.559 6 10.182 6c1.712 0 3.325.415 4.74 1.148-2.643 1.957-4.241 5.022-4.241 8.352 0 3.468 1.733 6.649 4.575 8.59l.454.31zM16 8c2.418 1.651 4 4.395 4 7.5 0 3.105-1.582 5.849-4 7.5-2.418-1.651-4-4.395-4-7.5 0-3.105 1.582-5.849 4-7.5z"
          ></path>
        </svg>
        <div>Join data</div>
      </div>
    </button>
    <button
      class="padding-2 rounded-sm"
      @click="customColumn"
      v-if="actions.customColumn"
    >
      <div>
        <svg
          viewBox="0 0 32 32"
          width="18"
          height="18"
          fill="currentcolor"
          role="img"
          aria-label="add_data icon"
        >
          <path
            d="M0 8h5.926v6.208H0V8zm7.704 0h5.926v6.208H7.704V8zM0 16.07h5.926v6.209H0V16.07zm7.704 0h5.926v6.209H7.704V16.07zm7.703 0h5.926v6.209h-5.926V16.07zM0 24.142h5.926v6.208H0v-6.208zm7.704 0h5.926v6.208H7.704v-6.208zm7.703 0h5.926v6.208h-5.926v-6.208zM23.08 8.08V3h4.064v5.08h5.08v4.063h-5.08v5.08h-4.064v-5.08H18V8.079h5.08z"
          ></path>
        </svg>
        <div>Custom column</div>
      </div>
    </button>
    <button class="padding-2 rounded-sm" @click="sort" v-if="actions.sort">
      <div>
        <svg
          class="Icon Icon-smartscalar e621b520 css-4g44w1 e621b521"
          viewBox="0 0 32 32"
          width="18"
          height="18"
          fill="currentcolor"
          role="img"
          aria-label="smartscalar icon"
        >
          <path
            d="M9.806 9.347v13.016h-2.79V9.593L3.502 14.12a1.405 1.405 0 0 1-1.957.254 1.372 1.372 0 0 1-.256-1.937L7.418 4.54a1.404 1.404 0 0 1 2.219.008l6.08 7.953a1.372 1.372 0 0 1-.27 1.935c-.615.46-1.49.34-1.955-.268l-3.686-4.82zM24.806 23.016V13h-2.79v9.77l-3.514-4.527a1.405 1.405 0 0 0-1.957-.254 1.372 1.372 0 0 0-.256 1.937l6.129 7.897c.56.723 1.663.72 2.219-.008l6.08-7.953a1.372 1.372 0 0 0-.27-1.935 1.405 1.405 0 0 0-1.955.268l-3.686 4.82z"
          ></path>
        </svg>
        <div>Sort</div>
      </div>
    </button>
    <button
      class="padding-2 rounded-sm"
      @click="rowLimit"
      v-if="actions.rowLimit"
    >
      <div>
        <svg
          class="Icon Icon-list e621b520 css-4g44w1 e621b521"
          viewBox="0 0 32 32"
          width="18"
          height="18"
          fill="currentcolor"
          role="img"
          aria-label="list icon"
        >
          <path
            d="M3 8 A3 3 0 0 0 9 8 A3 3 0 0 0 3 8 M12 6 L28 6 L28 10 L12 10z M3 16 A3 3 0 0 0 9 16 A3 3 0 0 0 3 16 M12 14 L28 14 L28 18 L12 18z M3 24 A3 3 0 0 0 9 24 A3 3 0 0 0 3 24 M12 22 L28 22 L28 26 L12 26z"
          ></path>
        </svg>
        <div>Row limit</div>
      </div>
    </button>
  </div>
</template>
<script>
export default {
  data() {
    return {
      actions: {
        filter: true,
        join: true,
        customColumn: true,
        sort: true,
        rowLimit: true,
        summarize: true,
      },
    };
  },
  methods: {
    filter() {
      this.$emit("filter");
      for (const key in this.actions) {
        if (key == "rowLimit" || key == "summarize" || key == "sort") continue;
        this.actions[key] = false;
      }
    },
    summarize() {
      this.$emit("summarize");
    },
    join() {
      this.$emit("join");
    },
    customColumn() {
      this.$emit("customColumn");
    },
    sort() {
      this.$emit("sort");
    },
    rowLimit() {
      this.$emit("rowLimit");
    },
  },
};
</script>
