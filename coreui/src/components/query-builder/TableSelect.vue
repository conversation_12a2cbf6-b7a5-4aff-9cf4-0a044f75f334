<template>
  <div class="select-items" v-show="show">
    <div
      class="item"
      v-for="(table, index) in tables"
      :key="index"
      @click="selectTable(index)"
      :class="value == table ? 'selected' : ''"
    >
      <span>
        <svg
          class="icon"
          viewBox="0 0 32 32"
          width="18"
          height="18"
          fill="currentcolor"
          role="img"
          aria-label="table2 icon"
        >
          <path
            d="M1.6 0h28.8A1.6 1.6 0 0 1 32 1.6v28.8a1.6 1.6 0 0 1-1.6 1.6H1.6A1.6 1.6 0 0 1 0 30.4V1.6A1.6 1.6 0 0 1 1.6 0zm1.6 3.2v6.4h6.4V3.2H3.2zm9.6 0v6.4h16V3.2h-16zm-9.6 9.6v6.4h6.4v-6.4H3.2zm9.6 0v6.4h16v-6.4h-16zm-9.6 9.6v6.4h6.4v-6.4H3.2zm9.6 0v6.4h16v-6.4h-16z"
          ></path></svg
      ></span>
      <span>{{ table.name | slugRemove | capitalize }}</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: Object,
      default: null,
    },
    tables: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      show: false,
    };
  },
  methods: {
    selectTable(index) {
      this.$emit("input", this.tables[index]);
      this.$emit("change");
      this.close();
    },
    toggle() {
      this.show = !this.show;
    },
    close() {
      this.show = false;
    },
  },
};
</script>

<style scoped>
.select-items {
  height: 10rem;
  overflow-y: scroll;
}
.item {
  display: flex;
  gap: 0.5rem;
  padding: 0.5rem;
  cursor: pointer;
}
.item:hover {
  border: 1px solid transparent;
  border-radius: 0.2rem;
  background-color: #509ee3;
  color: white;
}
.selected {
  border: 1px solid transparent;
  border-radius: 0.2rem;
  background-color: #509ee3;
  color: white;
}
.item:hover .icon {
  color: white;
}
.icon {
  color: #b8bbc3;
}
</style>
