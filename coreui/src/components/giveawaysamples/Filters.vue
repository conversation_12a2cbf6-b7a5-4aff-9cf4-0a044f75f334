<template>
  <c-col col="12" lg="12">
    <c-card>
      <c-card-header> Givaways</c-card-header>
      <c-card-body>
        <c-card>
          <c-card-body>
            <div class="form-row form-group">
              <div class="col-4">
                <c-form-group>
                  <template #label>
                    <strong>Line</strong>
                  </template>
                  <template #input>
                    <v-select
                      v-model="listFilter.line_id"
                      :options="lines"
                      label="name"
                      :value="0"
                      required
                      :reduce="(line) => line.id"
                      placeholder="Select option"
                      class="mt-2"
                      @input="getLineDivisions"
                    />
                  </template>
                </c-form-group>
              </div>
              <div class="col-4">
                <c-form-group>
                  <template #label>
                    <strong>Division</strong>
                  </template>
                  <template #input>
                    <input
                      label="All"
                      v-if="divisions.length != 0"
                      class="m-1"
                      type="checkbox"
                      v-model="checkAllDivisions"
                      title="Check All Divisions"
                      @change="checkAllDivision"
                    />
                    <label
                      v-if="divisions.length != 0"
                      style="font-weight: bold"
                      >All</label
                    >
                    <!-- style="max-height: 200px; overflow:auto;"
                        selectionType="counter"  -->
                    <v-select
                      v-model="listFilter.div_id"
                      :options="divisions"
                      label="name"
                      id="custom-v-select"
                      :value="0"
                      :reduce="(division) => division.id"
                      placeholder="Select option"
                      class="form-multi-select mt-2 overview-hidden v-select"
                      multiple
                      transition
                      autoscroll
                    >
                    </v-select>
                  </template>
                </c-form-group>
              </div>
              <div class="col-4">
                <c-input
                  label="Date"
                  type="date"
                  placeholder="Date"
                  v-model="listFilter.date"
                ></c-input>
              </div>
            </div>
          </c-card-body>
        </c-card>
      </c-card-body>
      <c-card-footer>
        <c-button
          color="primary"
          class="text-white"
          @click="show()"
          :disabled="!(listFilter.line_id && listFilter.div_id)"
          style="float: right"
          >Show</c-button
        >
      </c-card-footer>
    </c-card>
  </c-col>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      listFilter: {
        line_id: null,
        div_id: null,
        date: null,
      },
      lines: [],
      divisions: [],

      checkAllLines: false,
      checkAllDivisions: false,
    };
  },
  emits: ["getList"],
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: null,
          to: null,
          data: ['lines']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineDivisions() {
      axios
        .post(`/api/get-lines-data-with-dates`, {
          lines: [this.listFilter.line_id],
          from: null,
          to: null,
          data: ['divisions']
        })
        .then((response) => {
          this.divisions = response.data.data.divisions;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },

    checkAllDivision() {
      if (this.checkAllDivisions)
        this.listFilter.div_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.listFilter.div_id = null;
    },

    show() {
      this.$emit("getList", this.listFilter);
    },
  },
  created() {
    this.initialize();
  },
};
</script>
<!-- <style scoped>
.vs__selected-options {
  max-height: 200px !important;
  overflow: auto !important;
}
.style-chooser .vs__search::placeholder,
.style-chooser .vs__dropdown-toggle,
.style-chooser .vs__dropdown-menu {
  background: #dfe5fb;
  border: none;
  color: #394066;
  text-transform: lowercase;
  font-variant: small-caps;
}

.style-chooser .vs__clear,
.style-chooser .vs__open-indicator {
  fill: #394066;
}
</style> -->
