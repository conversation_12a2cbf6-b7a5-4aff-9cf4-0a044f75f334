<template>
  <c-card>
    <c-card-header>Active and Inactive Accounts</c-card-header>
    <c-card-body>
      <c-tabs>
        <c-tab active>
          <template slot="title">
            <c-icon name="cil-task" class="custom_icons" /> Line & Divisions
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Line </template>
                    <template #input>
                      <v-select
                        v-model="line_id"
                        :options="lines"
                        label="name"
                        :value="0"
                        :reduce="(line) => line.id"
                        placeholder="Select Line"
                        class="mt-2"
                        @input="getLineData"
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Division </template>
                    <template #input>
                      <input
                        label="All"
                        id="division"
                        v-if="divisions.length != 0"
                        class="m-1"
                        type="checkbox"
                        v-model="checkAllDivisions"
                        title="Check All Divisions"
                        @change="checkAllDivs"
                      />
                      <label
                        for="division"
                        v-if="divisions.length != 0"
                        style="font-weight: bold"
                        >All</label
                      >
                      <v-select
                        v-model="division_ids"
                        :options="divisions"
                        label="name"
                        :value="0"
                        :reduce="(division) => division.id"
                        placeholder="Select Division"
                        class="mt-2"
                        multiple
                        @input="getLineDivisionBricks"
                      />
                    </template>
                  </c-form-group>
                </div>
                
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label>
                      <strong>Brick</strong>
                    </template>
                    <template #input>
                      <input
                        label="All"
                        v-if="bricks.length != 0"
                        class="m-1"
                        type="checkbox"
                        id="bricks"
                        v-model="checkAllBricks"
                        title="Check All Bricks"
                        @change="checkAllBrick"
                      />
                      <label
                        for="bricks"
                        v-if="bricks.length != 0"
                        style="font-weight: bold"
                        >All</label
                      >
                      <v-select
                        v-model="brick_id"
                        :options="bricks"
                        label="name"
                        :value="0"
                        required
                        :reduce="(brick) => brick.id"
                        placeholder="Select Brick"
                        class="mt-2"
                        multiple
                      />
                    </template>
                  </c-form-group>
                </div>
                <div
                  v-if="checkPermission('show_settings_active_inactive_accounts')"
                  class="col-lg-3 col-md-3 col-sm-8"
                >
                  <c-form-group>
                    <template #label> Actions </template>
                    <template #input>
                      <v-select
                        v-model="action"
                        :options="actions"
                        label="action"
                        :value="0"
                        :reduce="(action) => action.id"
                        placeholder="Select Action"
                        class="mt-2"
                      />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon name="cil-chart-pie" class="custom_icons" /> Account Types &
            Specialities
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Account Type </template>
                    <template #input>
                      <input
                        label="All"
                        v-if="types.length != 0"
                        class="m-1"
                        type="checkbox"
                        v-model="allAccountTypes"
                        title="Check All Types"
                        @change="checkAllAccounts"
                      />
                      <label v-if="types.length != 0" style="font-weight: bold"
                        >All</label
                      >
                      <v-select
                        v-model="type_ids"
                        :options="types"
                        label="name"
                        :value="0"
                        :reduce="(type) => type.id"
                        placeholder="Select Account Type"
                        class="mt-2"
                        multiple
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Speciality </template>
                    <template #input>
                      <input
                        label="All"
                        v-if="specialities.length != 0"
                        class="m-1"
                        type="checkbox"
                        v-model="checkAllSpecialities"
                        title="Check All Specialities"
                        @change="checkAllSpech"
                      />
                      <label
                        v-if="specialities.length != 0"
                        style="font-weight: bold"
                        >All</label
                      >
                      <v-select
                        v-model="speciality_ids"
                        :options="specialities"
                        label="name"
                        :value="0"
                        :reduce="(speciality) => speciality.id"
                        placeholder="Select Speciality"
                        class="mt-2"
                        multiple
                      />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
      </c-tabs>
    </c-card-body>
    <c-card-footer>
      <c-button
        v-if="
          line_id != null && type_ids.length != 0 && speciality_ids.length != 0
        "
        color="primary"
        class="text-white"
        @click="show"
        style="float: right"
        >Show</c-button
      >
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      type_ids: [],
      speciality_ids: [],
      division_ids: [],
      line_id: null,
      types: [],
      bricks: [],
      brick_id: [],
      specialities: [],
      divisions: [],
      lines: [],
      checkAllBricks: true,
      allAccountTypes: true,
      checkAllSpecialities: true,
      checkAllDivisions: false,
      actions: [
        { id: 1, action: "Active" },
        { id: 2, action: "Inactive" },
      ],
      action: null,
    };
  },
  methods: {
    initialize() {
      axios
        .get("/api/accountsLines/")
        .then((response) => {
          this.lines = response.data.data.lines;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .get(`/api/getDataLineReport/${this.line_id}`)
        .then((response) => {
          this.divisions = response.data.data.divisions;
          this.types = response.data.data.account_types;
          this.specialities = response.data.data.specialities;
          this.type_ids = this.types.map((item) => item.id);
          this.speciality_ids = this.specialities.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineDivisionBricks() {
      axios
        .post(`/api/get-line-division-bricks/`, {
          divisions: this.division_ids,
        })
        .then((response) => {
          this.bricks = response.data.data;
          this.brick_id = this.bricks.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllBrick() {
      if (this.checkAllBricks)
        this.brick_id = this.bricks.map((item) => item.id);
      if (this.checkAllBricks == false) this.brick_id = null;
    },
    checkAllAccounts() {
      if (this.allAccountTypes)
        this.type_ids = this.types.map((item) => item.id);
      if (this.allAccountTypes == false) this.type_ids = null;
    },
    checkAllDivs() {
      if (this.checkAllDivisions) {
        this.division_ids = this.divisions.map((item) => item.id);
        this.getLineDivisionBricks();
      }
      if (this.checkAllDivisions == false) {
        this.division_ids = null;
        this.brick_id = null;
        this.checkAllBrick = false;
      }
    },
    checkAllSpech() {
      if (this.checkAllSpecialities)
        this.speciality_ids = this.specialities.map((item) => item.id);
      if (this.checkAllSpecialities == false) this.speciality_ids = null;
    },
    show() {
      let listFilter = {
        line: this.line_id,
        divisions: this.division_ids,
        bricks: this.brick_id,
        action: this.action,
        types: this.type_ids,
        specialities: this.speciality_ids,
      };
      this.$emit("getSchedule", { listFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>