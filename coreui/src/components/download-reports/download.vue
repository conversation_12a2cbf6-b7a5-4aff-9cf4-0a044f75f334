<template>
  <div style="display: flex; justify-content: center">
    <c-button
      color="primary"
      title="Print"
      style="width: 50px; height: 50px"
      class="px-2 m-1 text-white btn-lg"
      @click="print"
      ><c-icon name="cil-print" size="xl"
    /></c-button>
    <c-button
      color="success"
      title="Download Excel"
      style="width: 50px; height: 50px"
      class="px-2 m-1 text-white btn-lg"
      @click="download"
      ><c-icon name="cib-experts-exchange" size="xl"
    /></c-button>
    <c-button
      color="dark"
      title="Download CSV"
      style="width: 50px; height: 50px"
      class="px-2 m-1 text-white btn-lg"
      @click="downloadCsv"
    >
        <c-icon name="cibC" size="xl" />
    </c-button>
    <c-button
      class="px-2 m-1 text-white btn-lg"
      title="Download PDF"
      style="width: 50px; height: 50px"
      color="danger"
      @click="createPDF"
      ><c-icon name="cibAdobeAcrobatReader" size="xl"
    /></c-button>
  </div>
</template>
<script>
export default {
  emits: ["getPrint", "getxlsx", "getpdf" , "getcsv"],
  props: {
    fields: {
      type: Array,
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
  },
  methods: {
    print() {
      this.$emit("getPrint");
    },
    download() {
      this.$emit("getxlsx");
    },
    downloadCsv() {
      this.$emit("getcsv");
    },
    createPDF() {
      this.$emit("getpdf");
    },
  },
};
</script>
