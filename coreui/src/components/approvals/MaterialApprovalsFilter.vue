<template>
  <c-card>
    <c-card-header> Material Approvals </c-card-header>
    <c-card-body>
      <div class="form-row form-group">
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Line</strong> <strong><span style="color:red">*</span></strong>
            </template>
            <template #input>
              <input label="All" v-if="lines.length != 0" id="line" class="m-1" type="checkbox" v-model="checkAllLines"
                title="Check All lines" @change="checkAllLine" />
              <label for="line" v-if="lines.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="material_approval.line_id" :options="lines" label="name" :value="0" required
                :reduce="(line) => line.id" placeholder="Select Lines" class="mt-2" multiple
                @input="getLineEmployees()" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Employee</strong> <strong><span style="color:red">*</span></strong>
            </template>
            <template #input>
              <input v-if="users.length != 0" class="m-1" id="users" type="checkbox" v-model="checkAllEmployees"
                title="Check All Employees" @change="checkAll" />
              <label for="users" v-if="users.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="material_approval.users_id" :options="users" multiple :reduce="(user) => user.id"
                label="fullname" :value="0" placeholder="Select Employee" class="mt-3" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Material Types</strong>
            </template>
            <template #input>
              <input v-if="types.length != 0" class="m-1" id="types" type="checkbox" v-model="checkAllTypes"
                title="Check All Types" @change="checkAllType" />
              <label for="types" v-if="types.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="material_approval.material_type_id" :options="types" label="name" :value="0" required
                multiple :reduce="(type) => type.id" placeholder="Select Material Type" class="mt-2" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>From Date</strong>
            </template>
            <template #input>
              <c-input type="date" placeholder="From Date" class="mt-2" v-model="material_approval.from_date"></c-input>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>To Date</strong>
            </template>
            <template #input>
              <c-input type="date" placeholder="To Date" class="mt-2" v-model="material_approval.to_date"></c-input>
            </template>
          </c-form-group>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" class="text-white" @click="show()" style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      material_approval: {
        line_id: [],
        users_id: [],
        material_type_id: [],
        from_date: moment().startOf('year').format("YYYY-MM-DD"),
        to_date: moment().endOf('year').format("YYYY-MM-DD"),
      },
      lines: [],
      types: [],
      models: [],
      users: [],
      payments: [],
      checkAllEmployees: true,
      checkAllLines: false,
      checkAllTypes: false,
    };
  },
  emits: ["getMaterials"],
  methods: {
    initialize() {
      axios
        .get("/api/material-report-lines")
        .then((response) => {
          this.lines = response.data.data.lines;
          this.types = response.data.data.materialTypes;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineEmployees() {
      axios
        .post("/api/material-approval-employees", this.material_approval)
        .then((response) => {
          this.users = response.data.filtered_users;
          this.material_approval.users_id = this.users.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) {
        this.material_approval.line_id = this.lines.map((item) => item.id);
      }
      if (this.checkAllLines == false) this.material_approval.line_id = [];
      this.getLineEmployees();
    },
    checkAll() {
      if (this.checkAllEmployees) {
        this.material_approval.users_id = this.users.map((item) => item.id);
      }
      if (this.checkAllEmployees == false)
        this.material_approval.users_id = [];
    },
    checkAllType() {
      if (this.checkAllTypes) {
        this.material_approval.material_type_id = this.types.map(
          (item) => item.id
        );
      }
      if (this.checkAllTypes == false)
        this.material_approval.material_type_id = [];
    },
    show() {
      this.$emit("getMaterials", this.material_approval);
    },
  },
  created() {
    this.initialize();
  },
};
</script>
