<template>
  <c-card>
    <c-card-header> Expense Approvals </c-card-header>
    <c-card-body>
      <div class="form-row form-group">
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Line</strong>
            </template>
            <template #input>
              <input label="All" v-if="lines.length != 0" id="line" class="m-1" type="checkbox" v-model="checkAllLines"
                title="Check All lines" @change="checkAllLine" />
              <label for="line" v-if="lines.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="expense_approval.line_id" :options="lines" label="name" :value="0" required
                :reduce="(line) => line.id" placeholder="Select Line" class="mt-2" multiple
                @input="getLineEmployees()" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Employee</strong>
            </template>
            <template #input>
              <input v-if="users.length != 0" class="m-1" id="users" type="checkbox" v-model="checkAllEmployees"
                title="Check All Employees" @change="checkAll" />
              <label for="users" v-if="users.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="expense_approval.users_id" :options="users" multiple :reduce="(user) => user.id"
                label="fullname" :value="0" placeholder="Select Employees" class="mt-2" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Expense Types</strong>
            </template>
            <template #input>
              <input v-if="types.length != 0" class="m-1" id="types" type="checkbox" v-model="checkAllTypes"
                title="Check All Types" @change="checkAllType" />
              <label for="types" v-if="types.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="expense_approval.expense_type_id" :options="types" label="name" :value="0" required
                multiple :reduce="(type) => type.id" placeholder="Select expense Type" class="mt-2" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>From Date</strong>
            </template>
            <template #input>
              <c-input type="date" placeholder="From Date" v-model="expense_approval.from_date"></c-input>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>To Date</strong>
            </template>
            <template #input>
              <c-input type="date" placeholder="To Date" v-model="expense_approval.to_date"></c-input>
            </template>
          </c-form-group>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" class="text-white" @click="show()" style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      expense_approval: {
        line_id: [],
        type: null,
        users_id: null,
        expense_type_id: null,
        from_date: moment().startOf('month').format("YYYY-MM-DD"),
        to_date: moment().endOf('month').format("YYYY-MM-DD"),
      },
      lines: [],
      types: [],
      models: [],
      users: [],
      checkAllEmployees: false,
      checkAllLines: false,
      checkAllTypes: false,
    };
  },
  emits: ["getExpenses"],
  methods: {
    initialize() {
      axios
        .get("/api/planapprovals")
        .then((response) => {
          this.lines = response.data.lines;
          this.types = response.data.expenseTypes;
          this.models = response.data.models;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) {
        this.expense_approval.line_id = this.lines.map((item) => item.id);
      }
      if (this.checkAllLines == false) this.expense_approval.line_id = null;
      this.getLineEmployees();
    },
    getLineEmployees() {
      axios
        .post("/api/expense-approval-employees", this.expense_approval)
        .then((response) => {
          this.users = response.data.filtered_users;
          this.expense_approval.users_id = this.users.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAll() {
      if (this.checkAllEmployees) {
        this.expense_approval.users_id = this.users.map((item) => item.id);
      }
      if (this.checkAllEmployees == false)
        this.expense_approval.users_id = null;
    },
    checkAllType() {
      if (this.checkAllTypes) {
        this.expense_approval.expense_type_id = this.types.map(
          (item) => item.id
        );
      }
      if (this.checkAllTypes == false)
        this.expense_approval.expense_type_id = null;
    },
    show() {
      this.$emit("getExpenses", this.expense_approval);
    },
  },
  created() {
    this.initialize();
  },
};
</script>
