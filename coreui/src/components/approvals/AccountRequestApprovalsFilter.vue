<template>
  <c-card>
    <c-card-header> Account Request Approvals </c-card-header>
    <c-card-body>
      <div class="form-row form-group">
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Line</strong>
            </template>
            <template #input>
              <input
                label="All"
                v-if="lines.length != 0"
                id="line"
                class="m-1"
                type="checkbox"
                v-model="checkAllLines"
                title="Check All lines"
                @change="checkAllLine"
              />
              <label
                for="line"
                v-if="lines.length != 0"
                style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="account_request_approval.line_id"
                :options="lines"
                label="name"
                :value="0"
                required
                :reduce="(line) => line.id"
                placeholder="Select Line"
                class="mt-2"
                multiple
                @input="getLineEmployees()"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Employee</strong>
            </template>
            <template #input>
              <input
                v-if="users.length != 0"
                class="m-1"
                id="users"
                type="checkbox"
                v-model="checkAllEmployees"
                title="Check All Employees"
                @change="checkAll"
              />
              <label
                for="users"
                v-if="users.length != 0"
                style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="account_request_approval.users_id"
                :options="users"
                multiple
                :reduce="(user) => user.id"
                label="fullname"
                :value="0"
                placeholder="Select Employees"
                class="mt-2"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>From Date</strong>
            </template>
            <template #input>
              <c-input
                type="date"
                placeholder="From Date"
                v-model="account_request_approval.from_date"
              ></c-input>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>To Date</strong>
            </template>
            <template #input>
              <c-input
                type="date"
                placeholder="To Date"
                v-model="account_request_approval.to_date"
              ></c-input>
            </template>
          </c-form-group>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button
        color="primary"
        class="text-white"
        @click="show()"
        style="float: right"
        >Show</c-button
      >
    </c-card-footer>
  </c-card>
</template>
<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      account_request_approval: {
        line_id: [],
        users_id: null,
        from_date: moment().startOf('month').format("YYYY-MM-DD"),
        to_date: moment().endOf('month').format("YYYY-MM-DD"),
      },
      lines: [],
      models: [],
      users: [],
      checkAllEmployees: false,
      checkAllLines:false,
      checkAllTypes: false,
    };
  },
  emits: ["getRequests"],
  methods: {
    initialize() {
      axios
        .get("/api/planapprovals")
        .then((response) => {
          this.lines = response.data.lines;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) {
        this.account_request_approval.line_id = this.lines.map((item) => item.id);
      }
      if (this.checkAllLines == false) this.account_request_approval.line_id = null;
      this.getLineEmployees();
    },
    getLineEmployees() {
      axios
        .post("/api/account/request/users", this.account_request_approval)
        .then((response) => {
          this.users = response.data.filtered_users;
          this.account_request_approval.users_id = this.users.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAll() {
      if (this.checkAllEmployees) {
        this.account_request_approval.users_id = this.users.map((item) => item.id);
      }
      if (this.checkAllEmployees == false)
        this.account_request_approval.users_id = null;
    },
    show() {
      this.$emit("getRequests", this.account_request_approval);
    },
  },
  created() {
    this.initialize();
  },
};
</script>
