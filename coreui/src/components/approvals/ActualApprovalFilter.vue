<template>
  <c-card>
    <c-card-header> Actual Visits Approvals </c-card-header>
    <c-card-body>
      <div class="form-row form-group">
        <div class="col-4">
          <c-form-group>
            <template #label>
              <strong>Line</strong>
            </template>
            <template #input>
              <v-select
                v-model="actual_approval.line_id"
                :options="lines"
                label="name"
                :value="0"
                required
                :reduce="(line) => line.id"
                placeholder="Select Line"
                class="mt-2"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-4">
          <c-form-group>
            <template #label>
              <strong>Type</strong>
            </template>
            <template #input>
              <v-select
                required
                v-model="actual_approval.type"
                :options="models"
                :reduce="(model) => model.type"
                label="name"
                :value="0"
                placeholder="Select Type"
                class="mt-2"
                @input="getLineEmployees()"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-4">
          <c-form-group>
            <template #label>
              <strong>Employee</strong>
            </template>
            <template #input>
              <input
                v-if="users.length != 0"
                class="m-1"
                type="checkbox"
                v-model="checkAllEmployees"
                title="Check All Employees"
                @change="checkAll"
              />
              <v-select
                v-model="actual_approval.users_id"
                :options="users"
                multiple
                :reduce="(user) => user.id"
                label="fullname"
                :value="0"
                placeholder="Select Employee"
                class="mt-2"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-6">
          <c-form-group>
            <template #label>
              <strong>From Date</strong>
            </template>
            <template #input>
              <c-input
                type="date"
                placeholder="From Date"
                :min="minDate"
                v-model="actual_approval.from_date"
              ></c-input>
            </template>
          </c-form-group>
        </div>
        <div class="col-6">
          <c-form-group>
            <template #label>
              <strong>To Date</strong>
            </template>
            <template #input>
              <c-input
                type="date"
                placeholder="To Date"
                v-model="actual_approval.to_date"
              ></c-input>
            </template>
          </c-form-group>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button
        color="primary"
        class="text-white"
        @click="show()"
        style="float: right"
        >Show</c-button
      >
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      actual_approval: {
        line_id: "",
        type: "",
        users_id: "",
        from_date: new Date().toISOString().slice(0, 10),
        to_date: new Date().toISOString().slice(0, 10),
      },
      lines: [],
      models: [],
      users: [],
      checkAllEmployees: false,
      minDate:null
    };
  },
  emits: ["getVisits"],
  methods: {
    
    async initialize() {
      await axios
        .get("/api/planapprovals")
        .then((response) => {
          this.lines = response.data.lines;
          this.models = response.data.models;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
        await axios
        .get("/api/min-date/")
        .then((response) => {
          this.minDate = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineEmployees() {
      axios
        .post("/api/actual-approval-employees", this.actual_approval)
        .then((response) => {
          this.users = response.data.filtered_users;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAll() {
      if (this.checkAllEmployees) {
        this.actual_approval.users_id = this.users.map((item) => item.id);
      }
      if (this.checkAllEmployees == false) this.actual_approval.users_id = null;
    },
    show() {
      this.$emit("getVisits", this.actual_approval);
    },
  },
  created() {
    this.initialize();
  },
};
</script>
