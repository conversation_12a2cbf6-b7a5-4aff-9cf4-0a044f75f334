<template>
  <c-card>
    <c-card-header> Active & Inactive Approvals </c-card-header>
    <c-card-body>
      <div class="form-row form-group">
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Line</strong>
            </template>
            <template #input>
              <v-select
                v-model="active_inactive_approval.line_id"
                :options="lines"
                label="name"
                :value="0"
                required
                :reduce="(line) => line.id"
                placeholder="Select Line"
                class="mt-2"
                @input="getLineEmployees()"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Employee</strong>
            </template>
            <template #input>
              <input
                v-if="users.length != 0"
                class="m-1"
                type="checkbox"
                v-model="checkAllEmployees"
                title="Check All Employees"
                @change="checkAll"
              />
              <v-select
                v-model="active_inactive_approval.users_id"
                :options="users"
                multiple
                :reduce="(user) => user.id"
                label="fullname"
                :value="0"
                placeholder="Select Employee"
                class="mt-2"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>From Date</strong>
            </template>
            <template #input>
              <c-input
                type="date"
                placeholder="From Date"
                v-model="active_inactive_approval.from_date"
              ></c-input>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>To Date</strong>
            </template>
            <template #input>
              <c-input
                type="date"
                placeholder="To Date"
                v-model="active_inactive_approval.to_date"
              ></c-input>
            </template>
          </c-form-group>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button
        color="primary"
        class="text-white"
        @click="show()"
        style="float: right"
        >Show</c-button
      >
    </c-card-footer>
  </c-card>
</template>
<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      active_inactive_approval: {
        line_id: "",
        users_id: "",
        from_date: moment().startOf('month').format("YYYY-MM-DD"),
        to_date: moment().endOf('month').format("YYYY-MM-DD"),
      },
      lines: [],
      users: [],
      checkAllEmployees: false,
    };
  },
  emits: ["getActiveInactive"],
  methods: {
    initialize() {
      axios
        .get("/api/planapprovals")
        .then((response) => {
          this.lines = response.data.lines;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineEmployees() {
      axios
        .post("/api/active-inactive-users", this.active_inactive_approval)
        .then((response) => {
          this.users = response.data.filtered_users;
          this.active_inactive_approval.users_id = this.users.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAll() {
      if (this.checkAllEmployees) {
        this.active_inactive_approval.users_id = this.users.map((item) => item.id);
      }
      if (this.checkAllEmployees == false) this.active_inactive_approval.users_id = null;
    },
    show() {
      this.$emit("getActiveInactive", this.active_inactive_approval);
    },
  },
  created() {
    this.initialize();
  },
};
</script>
