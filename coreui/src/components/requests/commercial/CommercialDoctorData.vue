<template>
  <div class="row">
    <div class="col-12">
      <c-card>
        <div style="float: right; overflow: hidden;">
          <CButton color="success" class="btn mt-2 mr-1 text-white" style="float: right" @click="show_roi">Show ROI
          </CButton>
        </div>
        <c-card-body>
          <c-data-table v-if="selected.selectedDoctors.length > 0" hover striped sorter tableFilter footer
            itemsPerPageSelect :items="selected.selectedDoctors" :fields="Object.keys(selected.selectedDoctors[0])"
            :items-per-page="1000" :active-page="1" :responsive="true" pagination thead-top>
            <template #national_id="{ item }">
              <td>
                <c-input type="number" placeholder="National ID" v-model="item.national_id"></c-input>
              </td>
            </template>
            <template #bank_account_number="{ item }">
              <td>
                <c-input type="text" placeholder="Bank Account Number" v-model="item.bank_account_number"></c-input>
              </td>
            </template>
            <template #bank_name="{ item }">
              <td>
                <c-input type="text" placeholder="Bank Name" v-model="item.bank_name"></c-input>
              </td>
            </template>
          </c-data-table>
        </c-card-body>
      </c-card>
      <c-card>
        <c-card-header>Linked Pharmacies</c-card-header>
        <c-card-body>
          <c-data-table add-table-classes="linked-classes" v-if="selected.selectedDoctors.length > 0" hover striped
            sorter tableFilter footer itemsPerPageSelect :items="selected.doctorPharmacies" :fields="fields"
            :items-per-page="1000" :active-page="1" :responsive="true" pagination thead-top>
            <template #actions="{ item }">
              <td>
                <c-button color="warning" @click="openDoctorsDialog(item)" v-if="item.pharmacies.length == 0"
                  class="px-2 m-1 text-white btn-sm">
                  <c-icon name="cil-description" size="xl" />
                </c-button>
                <add-pharmacies ref="addPharmacies"></add-pharmacies>
              </td>
            </template>
          </c-data-table>
        </c-card-body>
      </c-card>
    </div>
  </div>

</template>
<script>
import AddPharmacies from "./doctors/pharmacies.vue";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { mapGetters, mapActions, mapState } from "vuex";
export default {
  components: {
    vSelect, AddPharmacies
  },
  data() {
    return {
      doctors: [],
      fields: ["account", "doctor", "actions"],
    };
  },
  computed: {
    // ...mapState("commercial", ["paymentMethods", "custodyUsers"]),
    ...mapState("commercial", ["selected", "distributors"]),
    ...mapGetters("commercial", ["selectedDoctorsCount", "selectedDoctorsDataCount"]),
  },
  methods: {
    ...mapActions("commercial", ["loadDoctorData", "loadDoctors", "loadDistributors", "loadDoctorPharmacies"]),
    openDoctorsDialog(item) {
      this.$refs.addPharmacies.open('Linked Pharmacy', item);
    },
    savePharmacies(item) {
      console.log(this.selected.doctorPharmacies);
    },
    initialize() {
      this.loadDoctorData();
      this.loadDoctorPharmacies();
      this.loadDistributors();
    },
    show_roi() {
      let products = this.selected.products.filter(
        (product) => product.id !== undefined
      );
      let types = this.selected.types
        .filter((item) => item.type != null)
        .map(({ type, amount }) => ({ id: type.id, amount }));

      let costElements = this.selected.categoriesCosts.filter(
        (item) => item.total > 0
      );
      axios
        .post("/api/show-roi", {
          users: this.selected.usersCost,
          doctors: this.selected.doctorsCost,
          categoriesCosts: costElements,
          types,
          products,
        })
        .then((response) => {
          const roi = response.data.data;
          this.$root.$table("RoI : ", roi);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    async loadEssentials() {
      await Promise.all([this.loadDoctorData(), this.loadDoctors()]);
    },
  },
  created() {
    this.initialize();
  },
  watch: {
    selectedDoctorsCount: {
      handler: "loadDoctorData",
    },
    selectedDoctorsDataCount: {
      handler: "loadDoctorPharmacies",
    },
  },
};
</script>
<style>
.linked-classes>tbody>tr>td {
  width: 100px;
}

.linked-classes>thead>tr>th:not(:first-of-type)>div {
  width: 100px;
}

.linked-classes>thead>tr>th>div {
  width: max-content;
}
</style>
