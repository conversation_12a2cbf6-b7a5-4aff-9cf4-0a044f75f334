<template>
  <div class="col">
    <c-button @click="initialize" color="primary">Show Selected Doctors</c-button>
    <table class="table table-striped" style="margin-top: 20px" v-if="showTable">
      <thead>
        <th>#</th>
        <th>Account</th>
        <th>Selected Doctors</th>
        <th>Speciality</th>
        <th>Mobile</th>
        <th>Address</th>
        <!-- <th v-for="({ type },typeIndex) in doctorSelectedTypes" :key="typeIndex">{{type.name}}</th> -->
      </thead>
      <tbody v-for="(doctor, index) in selected.doctorInfo" :key="index">
        <tr>
          <td>
            <label>{{ ++index }}</label>
          </td>
          <td>
            <label>{{ doctor.account.name }}</label>
          </td>
          <td>
            <label>{{ doctor.name }}</label>
          </td>
          <td>
            <label>{{ doctor.speciality }}</label>
          </td>
          <td>
            <c-input style="width: 100px" type="text" v-model="doctor.mobile" />
          </td>
          <td>
            <c-textarea type="textarea" placeholder="Address" v-model="doctor.account.address"></c-textarea>
          </td>
        </tr>
      </tbody>
      <tfoot>
        <c-button color="primary" style="float: right;"></c-button>
      </tfoot>
    </table>
    <c-card v-if="showTable">
      <c-card-header> Out Of List Doctors</c-card-header>
      <c-card-body>
        <button class="btn btn-sm btn-primary" @click="update" color="primary">
          <c-icon name="cib-addthis" />
        </button>

        <p class="d-inline">Add Out Of List Doctors</p>
        <hr />
        <div class="row" v-for="(x, doc) in selected.outOfList" :key="doc">
          <div class="col-lg-3 col-md-3 col-sm-8">
            <c-input label="Doctor" type="text" placeholder="Enter Doctor"
              v-model="selected.outOfList[doc].name"></c-input>
          </div>
          <div class="col-lg-3 col-md-3 col-sm-8">
            <c-form-group>
              <template #label> Speciality </template>
              <template #input>
                <v-select v-model="selected.outOfList[doc].speciality_id" :options="specialities" label="name"
                  :value="0" :reduce="(Speciality) => Speciality.id" placeholder="Select Speciality" class="mt-2" />
              </template>
            </c-form-group>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-8">
            <c-form-group>
              <template #label> Cost </template>
              <template #input>
                <v-select v-model="selected.outOfList[doc].cost_type_id" :options="doctorSelectedTypes" label="name"
                  :value="0" :reduce="(cost) => cost.id" placeholder="Select Cost Type" class="mt-2" multiple />
              </template>
            </c-form-group>
          </div>
          <div class="col-lg-1 col-md-1 col-sm-8">
            <c-button style="float: right; margin-top: 28px" color="danger"
              @click="selected.outOfList.splice(doc, 1)"><c-icon name="cib-experts-exchange"></c-icon></c-button>
          </div>
        </div>
      </c-card-body>
    </c-card>
  </div>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { mapState, mapActions } from "vuex";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      doctors: [],
      types: [],
      month_before: [],
      month_after: [],
      now: null,
      showTable: false,
      doctor_products: [],
    };
  },
  computed: {
    ...mapState("commercial", ["selected", "specialities"]),
    doctorSelectedTypes() {
      return this.selected.types
        .filter(({ type }) => {
          let exists = false;
          try {
            exists = type.cost.name === "per doctor";
          } catch (e) { }
          return exists;
        })
        .map((obj) => ({
          id: obj.type.id,
          name: obj.type.name,
        }));
    },
  },
  methods: {
    ...mapActions("commercial", ["loadSpecialities"]),
    initialize() {
      this.loadSpecialities();
      const doctor = {
        name: null,
        speciality_id: null,
        cost_type_id: [],
      };
      this.selected.outOfList.push(doctor);
      axios
        .post(`/api/doctor-costs/`, {
          doctors: this.selected.doctors,
          doctorCosts: this.selected.doctorsCost,
        })
        .then((response) => {
          this.selected.doctorInfo = response.data.doctors;
          console.log(this.selected.doctorInfo);
          this.types = response.data.types;
          this.showTable = true;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },

    update() {
      const doctor = {
        name: null,
        speciality_id: null,
        cost_type_id: [],
      };
      this.selected.outOfList.push(doctor);
    },
  },
};
</script>
<style scoped>
.doctors {
  overflow-y: auto;
  height: 600px;
}

table {
  display: table;
  white-space: nowrap;
  /* width: 100%; */
}

thead {
  opacity: 1;
  position: sticky;
  top: 0;
  color: white;
  background: #2eb85c;
}

thead.second {
  opacity: 1;
  position: sticky;
  top: 0;
  color: white;
  background: #032363;
}

.form-group {
  margin-bottom: 0;
}

label {
  font-weight: bolder;
  margin-bottom: 0;
}
</style>