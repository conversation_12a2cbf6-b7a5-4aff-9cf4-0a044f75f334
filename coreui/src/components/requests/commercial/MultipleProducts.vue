<template>
  <div
    v-if="selectedLinesCount > 0"
    class="products col-12"
    style="height: auto"
  >
    <table class="products table table-striped">
      <thead>
        <th>
          <c-form-group>
            <template #input>
              <input
                v-if="products.length != 0"
                type="checkbox"
                v-model="hasAllChecked"
                title="Check All Products"
                @change="checkAllProduct"
              />
            </template>
          </c-form-group>
        </th>
        <th>
          <label v-if="products.length != 0" style="font-weight: bold"
            >Products</label
          >
        </th>
        <th>units</th>
        <th>Ratio</th>
        <th v-for="(pharmacy, index) in numberOfPharmacies" :key="index">
          Pharmacy {{ pharmacy }}
        </th>
      </thead>
      <tbody v-if="selectedFlag">
        <tr v-for="(product, index) in products" :key="index">
          <td style="width: 10%">
            <input
              type="checkbox"
              :true-value="product.id"
              :false-value="undefined"
              v-model="selected.products[index].id"
              title="Check Product"
            />
          </td>
          <td style="width: 30%">
            <strong>{{ product.name }}</strong>
          </td>
          <td style="width: 10%; padding: 0.5rem">
            <c-input
              style="width: 100px"
              type="number"
              placeholder="units"
              v-model="selected.products[index].units"
            />
          </td>
          <td style="width: 10%; padding: 0.5rem">
            <c-input
              style="width: 100px"
              type="number"
              placeholder="Ratio"
              v-model="selected.products[index].ratio"
            />
          </td>
          <td v-for="(phar, iter) in numberOfPharmacies" :key="iter">
            <!-- v-if="" -->
            <v-select
              title="Pharmacy"
              v-model="selectedPharma[index][iter]"
              :options="pharmacies"
              label="name"
              :value="0"
              :reduce="(pharmacy) => pharmacy.id"
            ></v-select>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
<script>
import { mapState, mapMutations, mapActions, mapGetters } from "vuex";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      numberOfPharmacies: 4,
      hasAllChecked: false,
      selectedFlag: false,
      pharmacy: 1,
      selectedPharma: [],
      settings: null,
    };
  },
  computed: {
    ...mapState("commercial", ["selected", "products", "pharmacies"]),
    ...mapGetters("commercial", [
      "selectedLinesCount",
      "selectedDivisionsCount",
      "productsCount",
      "pharmaciesCount",
    ]),
  },
  methods: {
    ...mapMutations("commercial", [
      "setSelectedProducts",
      "setSelectedSingleProduct",
      "setSelectedSingleProductPharmacies",
    ]),
    ...mapActions("commercial", [
      "updatePharmacies",
      "loadPharmacies",
      "loadProducts",
      'loadDoctors'
    ]),
    updateDisplay() {
      this.selectedPharma = [];
      this.products.forEach((item) => {
        this.selectedPharma.push({});
      });
      this.initSelectedProducts();
    },
    initSelectedProducts() {
      this.selectedFlag = false;
      this.products.forEach((_, index) => {
        this.setSelectedSingleProduct({
          id: index,
          data: { pharmacies: [] },
        });
      });
      this.selectedFlag = true;
    },
    checkAllProduct() {
      this.initSelectedProducts();
      if (this.hasAllChecked) {
        this.setSelectedProducts(
          this.products.map((product) => ({
            id: product.id,
            units: 0,
            ratio: 0,
            pharmacies: [],
          }))
        );
        if(this.settings.appearance){
          this.updatePharmacies(this.selectedPharma);
        }
      }
    },
    async loadEssentials(){
      await Promise.all([
        this.loadPharmacies(),
        this.loadDoctors()
      ])
    }
  },

  async created() {
    try {
      await this.loadProducts();
      await axios.get("/api/get-commercial-pharmacies-settings").then((res) => {
        this.settings = res.data.data;
        this.numberOfPharmacies = this.settings.appearance ? 4 : 0;
      });
      if(this.settings.appearance){
        await this.loadPharmacies();
      }
    } catch (e) {
      this.showErrorMessage(e);
    }
  },
  watch: {
    selectedPharma: {
      handler: "updatePharmacies",
      deep: true,
    },
    selectedLinesCount: {
      handler: "loadProducts",
    },
    selectedDivisionsCount: {
      handler: "loadPharmacies",
    },
    productsCount: {
      handler: "updateDisplay",
    },
    pharmaciesCount: {
      handler: "updateDisplay",
    },
  },
};
</script>
<style scoped>
table {
  display: table;
  white-space: nowrap;
  width: 100%;
}
.products {
  overflow-y: auto;
  /* height: 600px; */
}
thead {
  opacity: 1;
  position: sticky;
  top: 0;
  color: white;
  background: #2eb85c;
}
.form-group {
  margin-bottom: 0;
}
label {
  font-weight: bolder;
  margin-bottom: 0;
}
</style>
