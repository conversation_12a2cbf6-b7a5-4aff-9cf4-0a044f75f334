<template>
  <c-card>
    <c-card-body>
      <button class="btn btn-sm btn-primary" @click="update" color="primary">
        <c-icon name="cib-addthis" />
      </button>
      <p class="d-inline">Add Payment Method</p>
      <c-card>
        <c-card-body>
          <div
            class="row mt-1"
            v-for="(x, index) in selected.payments"
            :key="index"
          >
            <div class="col-lg-2 col-md-2 col-sm-8">
              <c-form-group>
                <template #label> Method </template>
                <template #input>
                  <v-select
                    v-model="selected.payments[index].payment_method_id"
                    :options="paymentMethods"
                    label="name"
                    :value="0"
                    :reduce="(method) => method.id"
                    placeholder="Select Category"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-2 col-md-2 col-sm-8">
              <c-form-group>
                <template #label> Employee </template>
                <template #input>
                  <v-select
                    v-model="selected.payments[index].user_id"
                    :options="custodyUsers"
                    label="name"
                    :value="0"
                    :reduce="(user) => user.id"
                    placeholder="Select Employee"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-1 col-md-1 col-sm-8">
              <c-input
                label="Amount"
                type="number"
                placeholder="Amount"
                v-model="selected.payments[index].amount"
              ></c-input>
            </div>
            <div class="col-lg-2 col-md-2 col-sm-8">
              <c-input
                label="Date"
                type="date"
                placeholder="Date"
                v-model="selected.payments[index].date"
              ></c-input>
            </div>
            <div class="col-lg-2 col-md-2 col-sm-8">
              <c-form-group>
                <template #label> Pay Type </template>
                <template #input>
                  <v-select
                    v-model="selected.payments[index].type"
                    :options="['Yes', 'No', 'Partial']"
                    :value="0"
                    placeholder="Select pay type"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-1 col-md-1 col-sm-8">
              <c-input
                label="Ref No"
                type="number"
                placeholder="Ref No"
                v-model="selected.payments[index].ref_no"
              ></c-input>
            </div>
            <div class="col-lg-1 col-md-1 col-sm-8">
              <c-button
                style="float: right; margin-top: 28px"
                color="danger"
                @click="selected.payments.splice(index, 1)"
                ><c-icon name="cib-experts-exchange"></c-icon
              ></c-button>
            </div>
          </div>
        </c-card-body>
      </c-card>
    </c-card-body>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { mapActions, mapState } from "vuex";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
    };
  },
  computed: {
    ...mapState("commercial", ["paymentMethods","custodyUsers"]),
    ...mapState("commercial", ["selected"]),
  },
  methods: {
    ...mapActions("commercial", ["loadPaymentMethods","loadCustodyUsers"]),
    initialize() {
      this.loadPaymentMethods();
      this.loadCustodyUsers();

      const method = {
        payment_method_id: null,
        amount: 0,
        date: null,
        user_id: null,
        ref_no: 0,
        description: null,
        type: null,
      };
      this.selected.payments.push(method);
    },
    update() {
      const method = {
        payment_method_id: null,
        amount: null,
        date: null,
        user_id: null,
        ref_no: 0,
        description: null,
        type: null,
      };
      this.selected.payments.push(method);
      console.log(this.selected.categoriesCosts);
    },
  },
  created() {
    this.initialize();
  },
};
</script>
