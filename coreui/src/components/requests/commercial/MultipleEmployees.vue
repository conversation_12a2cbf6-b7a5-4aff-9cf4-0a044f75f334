<template>
  <div class="employees col-12" style="height: auto">
    <div style="height: 40px">
      <label style="float: left; padding-top: 10px">Filter: </label>
      <c-input style="width: 200px; float: left; padding-left: 20px" class="mt-1 flex" type="text" placeholder="Search"
        v-model="filter" />
    </div>
    <table class="table table-striped" style="margin-top: 20px">
      <thead>
        <th>
          <c-form-group>
            <template #input>
              <input v-if="users.length != 0" type="checkbox" v-model="hasAllChecked" title="Check All Users"
                @change="checkAllUser" />
            </template>
          </c-form-group>
        </th>
        <th>
          <label v-if="users.length != 0" style="font-weight: bold; cursor: pointer">Employee Code</label>
        </th>
        <th>
          <label v-if="users.length != 0" style="font-weight: bold; cursor: pointer">Employee Name</label>
        </th>
        <th>
          <label v-if="users.length != 0" style="font-weight: bold; cursor: pointer">Employee Mobile</label>
        </th>
        <th class="text-center" v-for="({ type }, costIndex) in employeeSelectedTypes" :key="costIndex">
          <span v-if="type">{{ type.name }}</span>
        </th>
      </thead>
      <tr v-for="(user, index) in filteredRows" :key="index">
        <td style="width: 10%">
          <input type="checkbox" :value="user.id" v-model="selected.users" title="Check Employee"
            @change="toggleSelectionUser(user)" />
        </td>
        <td>
          <strong>{{ user.emp_code }}</strong>
        </td>
        <td>
          <strong>{{ user.fullname }}</strong>
        </td>
        <td>
          <strong>{{ user.mobile }}</strong>
        </td>
        <td class="text-center" v-for="({ type }, costIndex) in employeeSelectedTypes" :key="costIndex">
          <input v-if="userIsSelected(user)" type="checkbox" placeholder="Cost" v-model="selected.usersCost[user.id]"
            :value="type.id" :disabled="!userIsSelected(user)" />
        </td>
      </tr>
    </table>
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      users: [],
      filter: "",
      hasAllChecked: false,
    };
  },
  computed: {
    ...mapState("commercial", ["selected"]),
    employeeSelectedTypes() {
      return this.selected.types.filter(({ type }) => {
        let exists = false;
        try {
          exists = type.cost.name === "per employee";
        } catch (e) { }
        return exists;
      });
    },
    filteredRows() {
      return this.users.filter((row) => {
        const employees = row.fullname.toString().toLowerCase();
        const codes = row.emp_code.toString().toLowerCase();
        const mobiles = row.mobile.toString().toLowerCase();
        const searchTerm = this.filter.toLowerCase();
        return (
          employees.includes(searchTerm) ||
          codes.includes(searchTerm) ||
          mobiles.includes(searchTerm)
        );
      });
    },
  },
  methods: {
    ...mapMutations("commercial", [
      "setSelectedUsers",
      "setUsersCost",
      "setSelectedSingleUserCost",
      "setSelectedUsersCost",
      "deleteSelectedSingleUserCost",
    ]),
    initialize() {
      let lines = this.selected.lines.map((item) => item.id);
      axios
        .post(`/api/commercial-users/`, { lines })
        .then((response) => {
          this.users = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    userIsSelected(user) {
      return this.selected.users.find((id) => id === user.id) === user.id;
    },
    checkAllUser() {
      this.setSelectedUsers([]);
      // set to empty object
      this.setSelectedUsersCost({});
      if (this.hasAllChecked) {
        this.setSelectedUsers(this.users.map((user) => user.id));
        // init every single usersCost for empty array
        this.users.forEach((user) => {
          if (this.selected.usersCost[user.id] === undefined)
            this.setSelectedSingleUserCost({ id: user.id, data: [] });
        });
      }
    },
    toggleSelectionUser(user) {
      if (this.selected.usersCost[user.id] === undefined)
        this.setSelectedSingleUserCost({ id: user.id, data: [] });

      let index = this.selected.users.findIndex((id) => user.id === id);
      if (index === -1) {
        this.deleteSelectedSingleUserCost({ id: user.id });
      }
    },
  },
  created() {
    this.initialize();
  },
};
</script>

<style scoped>
.employees {
  overflow-y: auto;
  height: 600px;
}

table {
  display: table;
  white-space: nowrap;
  width: 100%;
}

thead {
  opacity: 1;
  position: sticky;
  top: 0;
  color: white;
  background: #2eb85c;
}

.form-group {
  margin-bottom: 0;
}

label {
  font-weight: bolder;
  margin-bottom: 0;
}
</style>
