<template>
  <div class="col-lg-4 col-md-4 col-sm-8">
    <c-form-group>
      <template #label> Line </template>
      <template #input>
        <input
          label="All"
          v-if="lines.length != 0"
          class="m-1"
          type="checkbox"
          v-model="checkAllLines"
          title="Check All Lines"
          @change="checkAlllines"
        />
        <label v-if="lines.length != 0" style="font-weight: bold">All</label> <strong><span style="color:red">*</span></strong>
        <v-select
          v-model="line_ids"
          :options="lines"
          label="name"
          :value="0"
          :reduce="(line) => line"
          placeholder="Select Line"
          class="mt-2"
          multiple
        />
      </template>
    </c-form-group>
  </div>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { mapActions, mapState, mapMutations } from "vuex";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      line_ids: [],
      checkAllLines: false,
    };
  },
  computed: {
    ...mapState("commercial", ["lines"]),
  },
  methods: {
    ...mapActions("commercial", ["loadLines"]),
    ...mapMutations("commercial", ["setSelectedLines"]),
    initialize() {
      this.loadLines();
    },
    pushSelectedLines(value) {
      this.setSelectedLines(value);
    },
    checkAlllines() {
      if (this.checkAllLines) {
        this.line_ids = [...this.lines];
      }
      if (this.checkAllLines == false) {
        this.line_ids = [];
      }
    },
  },
  watch: {
    line_ids: {
      handler: "pushSelectedLines",
      deep: true,
    },
  },
  created() {
    this.initialize();
  },
};
</script>