<template>
  <c-card>
    <c-card-body>
      <div class="row">
        <commercial-doctors
          v-if="selectedLinesCount !== 0 && selectedDivisionsCount !== 0"
        />
      </div>
    </c-card-body>
  </c-card>
</template>
<script>
import { mapMutations, mapGetters, mapState } from "vuex";
import CommercialDoctors from "../CommercialDoctors.vue";
export default {
  components: {
    CommercialDoctors,
  },
  computed: {
    ...mapGetters("commercial", [
      "selectedLinesCount",
      "selectedDivisionsCount",
    ]),
  },
};
</script>