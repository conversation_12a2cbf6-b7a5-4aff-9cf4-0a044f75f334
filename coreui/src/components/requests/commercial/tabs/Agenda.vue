<template>
  <c-card
    v-if="selected.details.length !== 0 && flag"
    @keyup.enter="initialize"
  >
    <c-card-body>
      <div class="row" v-for="(x, index) in selected.details" :key="index">
        <div class="col-lg-2 col-md-4 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Speaker Type</strong>
            </template>
            <template #input>
              <v-select
                title="Search for option"
                v-model="selected.details[index].type"
                :options="speakerTypes"
                label="name"
                :value="0"
                class="mt-2"
                :reduce="(speakerType) => speakerType.id"
                @input="getSpeakers"
              ></v-select>
            </template>
          </c-form-group>
        </div>
        <div
          class="col-lg-2 col-md-4 col-sm-8"
          v-if="selected.details[index].type == 1"
        >
          <c-form-group>
            <template #label>
              <strong>Speaker</strong>
            </template>
            <template #input>
              <v-select
                title="Search for option"
                v-model="selected.details[index].speaker"
                :options="doctors"
                label="name"
                :value="0"
                class="mt-2"
                :reduce="(speaker) => speaker.name"
              ></v-select>
            </template>
          </c-form-group>
        </div>
        <div
          class="col-lg-2 col-md-4 col-sm-8"
          v-if="selected.details[index].type == 2"
        >
          <c-form-group>
            <template #label>
              <strong>Speaker</strong>
            </template>
            <template #input>
              <v-select
                title="Search for option"
                v-model="selected.details[index].speaker"
                :options="users"
                label="name"
                :value="0"
                class="mt-2"
                :reduce="(speaker) => speaker.name"
              ></v-select>
            </template>
          </c-form-group>
        </div>
        <div
          class="col-lg-2 col-md-4 col-sm-8"
          v-if="selected.details[index].type == 3"
        >
          <c-form-group>
            <template #label>
              <strong>Speaker</strong>
            </template>
            <template #input>
              <c-input
                class="mt-2"
                type="text"
                placeholder="Speaker"
                v-model="selected.details[index].speaker"
              ></c-input>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Date</strong>
            </template>
            <template #input>
              <c-input
                class="mt-2"
                type="date"
                placeholder="From"
                :min="minDate"
                v-model="selected.details[index].date"
              ></c-input>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>From Time</strong>
            </template>
            <template #input>
              <!-- <vue-timepicker
                fixed-dropdown-button
                hide-clear-button
                class="mt-2"
                type="date"
                placeholder="From"
                format="hh:mm A"
                input-width="120px"
                close-on-complete
                v-model="selected.details[index].from"
                ><template v-slot:dropdownButton>
                  <c-icon name="cil-pencil" /></template
              ></vue-timepicker> -->
              <c-input
                v-model="selected.details[index].from"
                type="time"
                placeholder="Time"
                class="mt-2"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>To Time</strong>
            </template>
            <template #input>
              <!-- <vue-timepicker
                fixed-dropdown-button
                hide-clear-button
                class="mt-2"
                type="date"
                placeholder="to"
                input-width="120px"
                format="hh:mm A"
                close-on-complete
                v-model="selected.details[index].to"
                ><template v-slot:dropdownButton>
                  <c-icon name="cil-pencil" /></template
              ></vue-timepicker> -->
              <c-input
                v-model="selected.details[index].to"
                type="time"
                placeholder="Time"
                class="mt-2"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-8">
          <c-input
            label="Topic"
            type="text"
            placeholder="Topic"
            v-model="selected.details[index].topic"
          ></c-input>
        </div>
      </div>
    </c-card-body>
  </c-card>
</template>
<script>
import VueTimepicker from "vue2-timepicker/src/vue-timepicker.vue";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { mapState, mapActions, mapMutations } from "vuex";
export default {
  components: {
    vSelect,
    VueTimepicker,
  },
  data() {
    return {
      list: 0,
      type_id: null,
      doctors: [],
      users: [],
      flag: true,
      minDate: null,
    };
  },
  computed: {
    ...mapState("commercial", ["speakerTypes", "selected"]),
  },
  methods: {
    ...mapActions("commercial", ["loadSpeakerTypes"]),
    ...mapMutations("commercial", ["initDetails"]),
    initialize() {
      this.loadSpeakerTypes();
      this.flag = false;
      this.list++;
      this.initDetails(this.list);
      this.flag = true;
      axios
        .get("/api/min-date/")
        .then((response) => {
          this.minDate = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },

    getSpeakers() {
      axios
        .get(`/api/commercial-speakers`)
        .then((response) => {
          this.doctors = response.data.data.doctors;
          this.users = response.data.data.users;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
  },
};
</script>