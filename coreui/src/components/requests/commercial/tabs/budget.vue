<template>
  <c-card>
    <c-card-body>
      <c-data-table
        hover
        striped
        sorter
        footer
        :items="budgets"
        :fields="fields"
        itemsPerPageSelect
        :items-per-page="100"
        :active-page="1"
        :responsive="true"
        table-filter
        pagination
      >
        <template slot="thead-top">
          <td style="border-top: none"><strong>Total</strong></td>
          <td style="border-top: none" class="text-xs-right">
            {{ budgets.length }}
          </td>
        </template>
      </c-data-table>
    </c-card-body>
  </c-card>
</template>
<script>
import { mapState, mapActions } from "vuex";
export default {
  data() {
    return {
      fields: ["id", "product", "pro_code", "budget", "consumed", "remained"],
    };
  },
  computed: {
    ...mapState("commercial", ["budgets"]),
  },
  methods: {
    ...mapActions("commercial", ["loadBudgets"]),
    initialize() {
      this.loadBudgets();
    },
  },
  created() {
    this.initialize();
  },
};
</script>
