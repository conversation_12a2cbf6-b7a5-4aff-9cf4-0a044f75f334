<template>
  <c-card>
    <c-card-body>
      <div class="row">
        <multiple-products v-if="selectedLinesCount > 0" />
      </div>
    </c-card-body>
  </c-card>
</template>
<script>
import MultipleProducts from "../MultipleProducts.vue";
import { mapGetters } from "vuex";
export default {
  components: {
    MultipleProducts,
  },
  computed: {
    ...mapGetters("commercial", [
      "selectedLinesCount",
      "selectedDivisionsCount",
    ]),
  },
};
</script>
