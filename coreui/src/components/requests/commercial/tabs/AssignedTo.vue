<template>
    <c-card>
      <c-card-body>
        <div class="row">
          <multiple-employees v-if="selectedLinesCount!==0" />
        </div>
      </c-card-body>
    </c-card>
</template>
<script>
import MultipleEmployees from "../MultipleEmployees.vue";
import {mapGetters} from "vuex"
export default {
  components: {
    MultipleEmployees,
  },
  computed: {
    ...mapGetters("commercial", ["selectedLinesCount"])
  }
};
</script>
