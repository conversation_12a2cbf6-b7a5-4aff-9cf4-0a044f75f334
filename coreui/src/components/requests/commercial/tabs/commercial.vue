<template>
  <c-card>
    <c-card-body>
      <div class="row">
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> <strong>Request Type </strong> <strong><span style="color:red">*</span></strong> </template>
            <template #input>
              <v-select
                v-model="requestType"
                :options="requestTypes"
                label="name"
                :value="0"
                :reduce="(requestType) => requestType"
                placeholder="Select Request Type"
                class="mt-3"
              />
            </template>
          </c-form-group>
        </div>
        <multiple-lines />
        <multiple-divisions v-if="selectedLinesCount !== 0" />
      </div>
      <div class="row">
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>From Date</strong> <strong><span style="color:red">*</span></strong>
            </template>
            <template #input>
              <c-input
                class="mt-3"
                type="date"
                placeholder="From"
                v-model="dateInterval.fromDate"
                ></c-input>
              </template>
            </c-form-group>
          </div>
          <!-- :min="minDate" -->
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>To Date</strong> <strong><span style="color:red">*</span></strong>
            </template>
            <template #input>
              <c-input
                class="mt-3"
                type="date"
                placeholder="To"
                v-model="dateInterval.toDate"
                ></c-input>
              </template>
            </c-form-group>
          </div>
        </div>
        <!-- :min="minDate" -->
      <div class="col-lg-12 col-md-4 col-sm-8">
        <label>Description </label><strong><span style="color:red">*</span></strong>
        <c-textarea
          placeholder="Description"
          v-model="location"
        ></c-textarea>
      </div>
      <div class="col-lg-12 col-md-4 col-sm-8">
        <GmapAutocomplete
          @place_changed="setPlace"
          style="
            width: 200px;
            height: 30px;
            border-radius: 2%;
            margin-right: 20px;
          "
        />
        <c-button color="primary" @click="addMarker">Add</c-button>
      </div>
      <div class="col-lg-12 col-md-4 col-sm-8">
        <gmap-map :center="center" :zoom="7" style="width: 100%; height: 440px">
          <gmap-cluster>
            <gmap-marker :position="center" />
          </gmap-cluster>
        </gmap-map>
      </div>
    </c-card-body>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import MultipleLines from "../MultipleLines.vue";
import MultipleDivisions from "../MultipleDivisions.vue";
import { mapState, mapMutations, mapGetters, mapActions } from "vuex";
export default {
  components: {
    vSelect,
    MultipleLines,
    MultipleDivisions,
  },
  data() {
    return {
      minDate: "",
      requestType: null,
      location: null,
      gpsLocation: { lat: 0, lng: 0 },
      center: { lat: 0, lng: 0 },
      currentPlace: null,
      markers: [],
      places: [],
      address: "",
    };
  },
  computed: {
    ...mapState("commercial", ["dateInterval", "requestTypes"]),
    ...mapGetters("commercial", ["selectedLinesCount"]),
  },
  mounted() {
    this.geolocate();
  },
  methods: {
    ...mapActions("commercial", ["loadRequestTypes"]),
    ...mapMutations("commercial", [
      "setLocation",
      "setGpsLocation",
      "setSelectedRequestType",
    ]),
    initialize() {
      this.loadRequestTypes();
      axios
        .get("/api/min-date/")
        .then((response) => {
          this.minDate = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    pushSelectedRequestType(value) {
      this.setSelectedRequestType(value);
    },
    setPlace(place) {
      this.currentPlace = place;
    },
    addMarker() {
      if (this.currentPlace) {
        const marker = {
          lat: this.currentPlace.geometry.location.lat(),
          lng: this.currentPlace.geometry.location.lng(),
        };
        this.markers.push({ position: marker });
        this.places.push(this.currentPlace);
        this.center = marker;
        this.gpsLocation = marker;
        this.currentPlace = null;
      }
    },
    geolocate: function () {
      navigator.geolocation.getCurrentPosition((position) => {
        this.center = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
        };
      });
    },
  },
  watch: {
    location(value) {
      this.setLocation(value);
    },
    gpsLocation(value) {
      this.setGpsLocation(value);
    },
    requestType: {
      handler: "pushSelectedRequestType",
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.initialize();
  },
};
</script>
