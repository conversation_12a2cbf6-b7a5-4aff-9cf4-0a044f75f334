<template>
  <div class="doctors row">
    <div v-if="selected.requestType.value == 'multiple'" class="doctors col-12" style="height: auto; padding-top: 20px">
      <div style="height: 40px">
        <label style="float: left; padding-top: 10px">Filter: </label>
        <c-input style="width: 200px; float: left; padding-left: 20px" class="mt-1 flex" type="text"
          placeholder="Search" v-model="filter" />
      </div>
      <table class="table table-striped doctors" style="margin-top: 20px">
        <thead>
          <th>
            <c-form-group>
              <template #input>
                <input v-if="doctors.length != 0" type="checkbox" v-model="hasAllChecked" title="Check All Doctors"
                  @change="checkAllDoctor" />
              </template>
            </c-form-group>
          </th>
          <th>
            <label v-if="doctors.length != 0" style="font-weight: bold; cursor: pointer">Doctor Id</label>
          </th>
          <th>
            <label v-if="doctors.length != 0" style="font-weight: bold; cursor: pointer">Accounts</label>
          </th>
          <th>
            <label v-if="doctors.length != 0" style="font-weight: bold; cursor: pointer">Doctors</label>
          </th>
          <th>
            <label v-if="doctors.length != 0" style="font-weight: bold; cursor: pointer">Divisions</label>
          </th>
          <th>
            <label v-if="doctors.length != 0" style="font-weight: bold; cursor: pointer">Specialities</label>
          </th>
          <th>
            <label v-if="doctors.length != 0" style="font-weight: bold; cursor: pointer">No Visits</label>
          </th>
          <th class="text-center" v-for="({ type }, costIndex) in doctorSelectedTypes" :key="costIndex">
            <span v-if="type">{{ type.name }}</span>
          </th>
        </thead>
        <tbody>
          <tr v-for="(doctor, index) in doctors" :key="index">
            <td style="width: 10%">
              <input type="checkbox" :value="doctor.id" v-model="selected.doctors" title="Check Doctor"
                @change="toggleSelectionDoctor(doctor)" />
            </td>
            <td>
              <strong>{{ doctor.doctor_id }}</strong>
            </td>
            <td>
              <strong>{{ doctor.account }}</strong>
            </td>
            <td>
              <strong>{{ doctor.doctor }}</strong>
            </td>
            <td>
              <strong>{{ doctor.division }}</strong>
            </td>
            <td>
              <strong>{{ doctor.speciality }}</strong>
            </td>
            <td>
              <strong>{{ doctor.no_visits }}</strong>
            </td>
            <td class="text-center" v-for="({ type }, costIndex) in doctorSelectedTypes" :key="costIndex">
              <input v-if="doctorIsSelected(doctor)" type="checkbox" placeholder="Cost"
                v-model="selected.doctorsCost[doctor.id].types" :value="type.id"
                :disabled="!doctorIsSelected(doctor)" />
            </td>
          </tr>
        </tbody>
      </table>
      <c-pagination v-if="doctors.length != 0" :activePage.sync="page" @update:activePage="initialize()"
        :pages="total" />
      <!-- <nav class="flex">
        <ul class="pagination">
          <li class="page-item">
            <button
              type="button"
              class="page-link"
              v-if="page != 1"
              @click="page--"
            >
              Previous
            </button>
          </li>
          <li class="page-item">
            <button
              class="page-link"
              v-for="pageNumber in pages.slice(page - 1, page + 5)"
              @click="page = pageNumber"
              :key="pageNumber"
            >
              {{ pageNumber }}
            </button>
          </li>
          <li class="page-item">
            <button
              type="button"
              class="page-link"
              @click="page++"
              v-if="page < pages.length"
            >
              Next
            </button>
          </li>
        </ul>
      </nav> -->
    </div>
    <single-doctor v-if="selected.requestType.value === 'single'" />
  </div>
</template>

<script>
import singleDoctor from "./../../requests/commercial/doctors/singleDoctor.vue";
import { mapState, mapMutations, mapGetters, mapActions } from "vuex";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";

export default {
  components: {
    vSelect,
    singleDoctor,
  },
  data() {
    return {
      doctors: [],
      filter: "",
      hasAllChecked: false,
      // pages: [],
      // page: 1,
      // perPage: 100,
      flag: false,
      countDivs: 0,
      page: 1,
      total: 0,
      totalData: 0,
      previousTimeout: null,
    };
  },
  computed: {
    ...mapState("commercial", ["selected"]),
    ...mapGetters("commercial", [
      "selectedLinesCount",
      "selectedDivisionsCount",
      "selectedDoctorsCount"
    ]),
    doctorSelectedTypes() {
      return this.selected.types.filter(({ type }) => {
        let exists = false;
        try {
          exists = type.cost.name === "per doctor";
        } catch (e) { }
        return exists;
      });
    },
    filteredRows() {
      return this.doctors.filter((doctor) => {
        const doctorIds = doctor.doctor_id.toString().toLowerCase();
        const doctors = doctor.doctor.toString().toLowerCase();
        const divisions = doctor.division.toString().toLowerCase();
        const accounts = doctor.account.toString().toLowerCase();
        const specialities = doctor.speciality.toString().toLowerCase();
        const searchTerm = this.filter.toLowerCase();
        return (
          doctorIds.includes(searchTerm) ||
          doctors.includes(searchTerm) ||
          divisions.includes(searchTerm) ||
          accounts.includes(searchTerm) ||
          specialities.includes(searchTerm)
          // divisions.includes(searchDivision) ||
          // specialities.includes(searchSpeciality)
        );
      });
    },
    // displayedDoctors() {
    //   return this.paginate(this.doctors);
    // },
  },
  methods: {
    ...mapMutations("commercial", [
      "setSelectedDoctors",
      "setDoctorsCost",
      "setSelectedSingleDoctorCost",
      "setSelectedDoctorsCost",
      "deleteSelectedSingleDoctorCost",
      "setSelectedDivisions",
    ]),
    ...mapActions("commercial", [
      "loadPharmacies",
      "loadDoctors",
      "loadDoctorData",
    ]),
    async loadEssentials() {
      await Promise.all([
        this.loadPharmacies(),
        this.loadDoctorData(),
        this.loadDoctors(),
      ]);
    },
    initialize() {
      let lines = this.selected.lines.map((item) => item.id);
      let divisions = this.selected.divisions;
      axios
        .post("/api/commercial-doctors/index?page=" + this.page, { lines, divisions, query: this.filter })
        .then((response) => {
          this.doctors = response.data.data.data;
          this.total = response.data.data.last_page;
          this.totalData = response.data.data.total;
          console.log(this.doctors);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    timeoutClear() {
      if (this.previousTimeout) {
        clearTimeout(this.previousTimeout);
        this.previousTimeout = null;
      }
    },
    doctorIsSelected(doctor) {
      return this.selected.doctors.find((id) => id === doctor.id) === doctor.id;
    },
    checkAllDoctor() {
      this.setSelectedDoctors([]);
      // set to empty object
      this.setSelectedDoctorsCost({});
      if (this.hasAllChecked) {
        this.setSelectedDoctors(this.doctors.map((doctor) => doctor.id));
        // init every single doctorsCost for empty array
        this.doctors.forEach((doctor) => {
          if (this.selected.doctorsCost[doctor.id] === undefined)
            this.setSelectedSingleDoctorCost({
              id: doctor.id,
              account_id: doctor.account_id,
              types: [],
            });
        });
      }
    },
    toggleSelectionDoctor(doctor) {
      console.log("before", this.selected.doctorsCost);
      if (this.selected.doctorsCost[doctor.id] === undefined){
        this.setSelectedSingleDoctorCost({
          id: doctor.id,
          account_id: doctor.account_id,
          types: [],
        });
      }else{
        this.deleteSelectedSingleDoctorCost(doctor);
        return ;
      }
      let index = this.selected.doctors.findIndex((id) => doctor.id === id);
      if (index === -1) {
        ``;
        this.setSelectedSingleDoctorCost({
          id: doctor.id,
          account_id: doctor.account_id,
        });
      }
    },
    setPages() {
      let numberOfPages = Math.ceil(this.doctors.length / this.perPage);
      for (let index = 1; index <= numberOfPages; index++) {
        this.pages.push(index);
      }
    },
    // paginate(doctors) {
    //   let page = this.page;
    //   let perPage = this.perPage;
    //   let from = page * perPage - perPage;
    //   let to = page * perPage;
    //   return doctors.slice(from, to);
    // },
  },
  watch: {
    // doctors() {
    //   this.setPages();
    // },
    filter() {
      this.timeoutClear();
      const id = setTimeout(() => this.initialize(), 500);
      this.previousTimeout = id;
    },
    selectedDivisionsCount: {
      handler: "initialize",
    },
  },
  // filters: {
  //   trimWords(value) {
  //     return value.split(" ").splice(0, 20).join(" ") + "...";
  //   },
  // },
  created() {
    this.initialize();
  },
};
</script>

<style scoped>
.doctors {
  overflow-y: auto;
  height: 600px;
}

table {
  display: table;
  white-space: nowrap;
  width: 100%;
}

thead {
  opacity: 1;
  position: sticky;
  top: 0;
  color: white;
  background: #2eb85c;
}

.form-group {
  margin-bottom: 0;
}

label {
  font-weight: bolder;
  margin-bottom: 0;
}

.pagination {
  display: flex;
  justify-items: center;
  justify-content: center;
}

button.page-link {
  display: inline-block;
}

button.page-link {
  font-size: 20px;
  color: #4189de;
  font-weight: 500;
}
</style>
