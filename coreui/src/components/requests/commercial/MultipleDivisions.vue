<template>
  <div class="col-lg-4 col-md-4 col-sm-8">
    <c-form-group>
      <template #label> Division </template>
      <template #input>
        <input label="All" v-if="divisions.length != 0" class="m-1" type="checkbox" v-model="hasAllChecked"
          title="Check All Divisions" @change="checkAllDivisions" />
        <label v-if="divisions.length != 0" style="font-weight: bold">All</label> <strong><span style="color:red">*</span></strong>
        <v-select v-model="selected.divisions" :options="divisions" label="name" :value="0"
          :reduce="(division) => division.id" placeholder="Select Division" class="mt-2" multiple />
      </template>
    </c-form-group>
  </div>
</template>
<script>
import { mapState, mapMutations, mapGetters } from 'vuex'
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      divisions: [],
      hasAllChecked: false,
    };
  },
  computed: {
    ...mapState('commercial', ['selected']),
    ...mapGetters("commercial", [
      "selectedLinesCount",
    ]),
  },
  methods: {
    ...mapMutations("commercial", [
      "setSelectedDivisions",
    ]),
    initialize() {
      let lines = this.selected.lines.map(item => item.id);
      axios
        .post(`/api/commercial-divisions/`, { lines })
        .then((response) => {
          this.divisions = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    // pushSelectedDivisions(value){
    //   this.setSelectedDivisions(value);
    // },
    checkAllDivisions() {
      this.setSelectedDivisions([])
      if (this.hasAllChecked)
        this.setSelectedDivisions(this.divisions.map(division => division.id));
      //   this.division_ids = this.divisions.map((item) => item.id);
      // if (this.checkAllDivisions == false) this.division_ids = null;
    },
  },
  created() {
    this.initialize();
  },
  watch: {
    selectedLinesCount: {
      handler: "initialize",
    },
    //   selected:{
    //     handler:'initialize',
    //     deep:true,
    //     immediate:true
    //   },
  },
};
</script>
