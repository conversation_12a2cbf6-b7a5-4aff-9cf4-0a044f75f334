<template>
  <div class="col-12">
    <div class="col-5">
      <c-form-group>
        <template #label> Doctor </template>
        <template #input>
          <v-select v-model="doctor" :options="doctors" label="account_doctor_name" placeholder="Select Doctors"
            class="mt-2" @input="selectedDoctor" />
        </template>
      </c-form-group>
    </div>
    <!-- <div class="col-5" v-if="selected.doctors.length===1"> -->
    <div class="col-5">
      <c-form-group>
        <template #label> Types </template>
        <template #input>
          <v-select v-model="selectedTypes" :options="doctorSelectedTypes" label="name" :value="0"
            :reduce="(type) => type.id" placeholder="Select option" class="mt-2" multiple
            @option:selected="updateSelectedTypes" @option:deselected="updateSelectedTypes" />
        </template>
      </c-form-group>
    </div>
  </div>
</template>
<script>
import { mapState, mapMutations, mapActions, mapGetters } from "vuex";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      doctors: [],
      hasAllChecked: false,
      doctor: null,
      selectedTypes: [],
    };
  },
  computed: {
    ...mapState("commercial", ["selected"]),
    ...mapGetters("commercial", ["selectedDoctorsCount"]),
    doctorSelectedTypes() {
      return this.selected.types
        .filter(({ type }) => {
          let exists = false;
          try {
            exists = type.cost.name === "per doctor";
          } catch (e) { }
          return exists;
        })
        .map(({ type }) => type);
    },
  },
  methods: {
    ...mapMutations("commercial", [
      "setSelectedDoctors",
      "setDoctorsCost",
      "setSelectedSingleDoctorCost",
      "setSelectedDoctorsCost",
      "deleteSelectedSingleDoctorCost",
    ]),
    ...mapActions("commercial", ["loadDoctorData"]),
    initialize() {
      let lines = this.selected.lines.map((item) => item.id);
      let divisions = this.selected.divisions;
      axios
        .post(`/api/single-doctors/`, { lines, divisions })
        .then((response) => {
          this.doctors = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    updateSelectedTypes() {
      this.selected.doctorsCost[this.doctor.id] = {
        account_id: this.doctor.account_id,
        types: this.selectedTypes,
      };
      this.loadDoctorData();
    },
    selectedDoctor() {
      if (this.selected.doctorsCost[this.doctor.id] === undefined) {
        this.setSelectedSingleDoctorCost({
          id: this.doctor.id,
          account_id: this.doctor.account_id,
          types: [],
        });
        this.selected.doctors = [this.doctor.id];
      } else {
        console.log('Hello');
        this.deleteSelectedSingleDoctorCost(this.doctor);
        this.selected.doctors = [];
        return;
      }
      let index = this.selected.doctors.findIndex(
        (id) => this.doctor.id === id
      );
      if (index === -1) {
        this.setSelectedSingleDoctorCost({
          id: this.doctor.id,
          account_id: this.doctor.account_id,
        });
        this.selected.doctors = [this.doctor.id];
      }
      this.loadDoctorData();
    },
  },
  created() {
    this.initialize();
  },
  watch: {
    selectedDoctorsCount: {
      handler: "loadDoctorData",
    },
  },
};
</script>