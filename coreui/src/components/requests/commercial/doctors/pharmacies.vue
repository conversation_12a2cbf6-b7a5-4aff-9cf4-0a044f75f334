<template>
  <v-dialog v-model="dialog" :max-width="options.width" :style="{ zIndex: options.zIndex }" @keydown.esc="cancel">
    <v-card>
      <v-toolbar dark :color="options.color" dense flat>
        <v-toolbar-title class="white--text">{{ title }}</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon @click="cancel">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <c-card class="card-table">
        <c-card-body class="card-scroll">
          <button class="btn btn-sm btn-primary" @click="update" color="primary">
            <c-icon name="cib-addthis" />
          </button>

          <p class="d-inline">Add Pharmacy</p>
          <hr />
          <div class="row" v-for="(x, index) in item.pharmacies" :key="index">
            <div class="col-lg-2 col-md-2 col-sm-8">
              <c-form-group>

                <template #label>
                  <strong>Distributor</strong>
                </template>

                <template #input>
                  <v-select title="Search Distributor" v-model="item.pharmacies[index].distributor_id"
                    :options="distributors" label="name" :value="0" class="mt-2"
                    :reduce="(distributor) => distributor.id"></v-select>
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-8">
              <c-input label="Code" type="number" placeholder="Code" v-model="item.pharmacies[index].code"></c-input>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-8">
              <c-input label="name" type="text" placeholder="name" v-model="item.pharmacies[index].name"></c-input>
            </div>
            <div class="col-lg-1 col-md-1 col-sm-8">
              <c-button style="float: right; margin-top: 28px" color="danger"
                @click="item.pharmacies.splice(index, 1)"><c-icon name="cib-experts-exchange"></c-icon></c-button>
            </div>
          </div>
        </c-card-body>
        <c-card-footer>
          <c-button title="Approve" color="primary" style="float: right" square @click="save()">Save Pharmacies
          </c-button>
        </c-card-footer>
      </c-card>
    </v-card>
  </v-dialog>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["getPharmacies"],
  data() {
    return {
      dialog: false,
      resolve: null,
      reject: null,
      fields: [],
      item: null,
      title: null,
      options: {
        color: "primary",
        width: 1200,
        zIndex: 1000000000,
      },
    };
  },
  computed: {
    ...mapState("commercial", ["distributors"]),
  },
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open(title, data, fields = [], options = null) {
      this.loadVApp();
      this.dialog = true;
      this.title = title;
      this.item = data;
      this.options = Object.assign(this.options, options || this.options);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    agree() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp();
    },
    cancel() {
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp();
    },
    update() {
      let item = {
        distributor_id: null,
        code: null,
        name: null,
      }
      this.item.pharmacies.push(item);
    },
    save() {
      this.$emit("getPharmacies", {
        item: this.item
      });
      this.cancel();
    },
  },
};
</script>

<style scoped>
.card-table {
  position: relative;
}

.card-scroll {
  max-height: 450px;
  overflow: auto;
  margin-top: 20px;
}
</style>