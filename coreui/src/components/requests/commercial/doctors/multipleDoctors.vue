<template>
  <div class="doctors col-12" style="height: auto">
    <table class="table table-striped">
      <thead>
        <th style="opacity: 1; padding-bottom: 27px; background: #2eb85c">
          <c-form-group>
            <template #input>
              <input
                v-if="doctors.length !== 0"
                type="checkbox"
                v-model="hasAllChecked"
                title="Check All Doctors"
                @change="checkAllDoctor"
              />
            </template>
          </c-form-group>
        </th>
        <th style="opacity: 1; position: sticky; top: 0; background: #2eb85c">
          <label
            v-if="doctors.length !== 0"
            style="font-weight: bold; cursor: pointer"
            >Doctors</label
          >
          <c-input
            style="width: 200px"
            class="mt-1"
            type="text"
            placeholder="Search"
            v-model="filter"
          />
        </th>
        <th
          style="opacity: 1; padding-bottom: 27px; background: #2eb85c"
          class="text-center"
          v-for="({ type }, costIndex) in doctorSelectedTypes"
          :key="costIndex"
        >
          <span v-if="type">{{ type.name }}</span>
        </th>
      </thead>
      <tbody>
        <tr v-for="(doctor, index) in filteredRows" :key="index">
          <td style="width: 10%">
            <input
              type="checkbox"
              :value="doctor.id"
              v-model="selected.doctors"
              title="Check Doctor"
              @change="toggleSelectionDoctor(doctor)"
            />
          </td>
          <td>
            <strong>{{ doctor.name }}</strong>
          </td>
          <td
            class="text-center"
            v-for="({ type }, costIndex) in doctorSelectedTypes"
            :key="costIndex"
          >
            <input
              v-if="doctorIsSelected(doctor)"
              type="checkbox"
              placeholder="Cost"
              v-model="selected.doctorsCost[doctor.id]"
              :value="type.id"
              :disabled="!doctorIsSelected(doctor)"
            />
          </td>
        </tr>
      </tbody>
    </table>
    <nav class="flex">
      <ul class="pagination">
        <li class="page-item">
          <button
            type="button"
            class="page-link"
            v-if="page != 1"
            @click="page--"
          >
            Previous
          </button>
        </li>
        <li class="page-item">
          <button
            class="page-link"
            v-for="pageNumber in pages.slice(page - 1, page + 5)"
            @click="page = pageNumber"
            :key="pageNumber"
          >
            {{ pageNumber }}
          </button>
        </li>
        <li class="page-item">
          <button
            type="button"
            class="page-link"
            @click="page++"
            v-if="page < pages.length"
          >
            Next
          </button>
        </li>
      </ul>
    </nav>
  </div>
</template>
<script>
import { mapState, mapMutations } from "vuex";
import paginate from "./../../../pagination/paginate.vue";
export default {
  components: {
    paginate,
  },
  data() {
    return {
      doctors: [],
      filter: "",
      hasAllChecked: false,
      pages: [],
      page: 1,
      perPage: 100,
    };
  },
  computed: {
    ...mapState("commercial", ["selected"]),
    doctorSelectedTypes() {
      return this.selected.types.filter(({ type }) => {
        let exists = false;
        try {
          exists = type.cost.name === "per doctor";
        } catch (e) {}
        return exists;
      });
    },
    filteredRows() {
      return this.displayedDoctors.filter((doctor) => {
        const doctors = doctor.name.toString().toLowerCase();
        const searchTerm = this.filter.toLowerCase();
        return doctors.includes(searchTerm);
      });
    },
    displayedDoctors() {
      return this.paginate(this.doctors);
    },
  },
  methods: {
    ...mapMutations("commercial", [
      "setSelectedDoctors",
      "setDoctorsCost",
      "setSelectedSingleDoctorCost",
      "setSelectedDoctorsCost",
      "deleteSelectedSingleDoctorCost",
      "selectedDivisionsCount",
    ]),
    initialize() {
      console.log('data');
      let lines = this.selected.lines.map((item) => item.id);
      let divisions = this.selected.divisions.map((item) => item.id);
      axios
        .post(`/api/commercial-doctors/`, { lines, divisions })
        .then((response) => {
          this.doctors = response.data.data;
          // this.flag = true;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    doctorIsSelected(doctor) {
      return this.selected.doctors.find((id) => id === doctor.id) === doctor.id;
    },
    checkAllDoctor() {
      this.setSelectedDoctors([]);
      // set to empty object
      this.setSelectedDoctorsCost({});
      if (this.hasAllChecked) {
        this.setSelectedDoctors(this.doctors.map((doctor) => doctor.id));
        // init every single doctorsCost for empty array
        this.doctors.forEach((doctor) => {
          if (this.selected.doctorsCost[doctor.id] === undefined)
            this.setSelectedSingleDoctorCost({ id: doctor.id, data: [] });
        });
      }
    },
    toggleSelectionDoctor(doctor) {
      if (this.selected.doctorsCost[doctor.id] === undefined)
        this.setSelectedSingleDoctorCost({ id: doctor.id, data: [] });

      let index = this.selected.doctors.findIndex((id) => doctor.id === id);
      if (index === -1) {
        this.setSelectedSingleDoctorCost({ id: doctor.id });
      }
    },
    setPages() {
      let numberOfPages = Math.ceil(this.doctors.length / this.perPage);
      for (let index = 1; index <= numberOfPages; index++) {
        this.pages.push(index);
      }
    },
    paginate(doctors) {
      let page = this.page;
      let perPage = this.perPage;
      let from = page * perPage - perPage;
      let to = page * perPage;
      return doctors.slice(from, to);
    },
  },
  watch: {
    doctors() {
      this.setPages();
    },
    selectedDivisionsCount: {
      handler: "initialize",
    },
  },
  filters: {
    trimWords(value) {
      return value.split(" ").splice(0, 20).join(" ") + "...";
    },
  },
  created() {
    this.initialize();
  },
};
</script>
<style scoped>
.doctors {
  overflow-y: auto;
  height: 600px;
}
table {
  display: table;
  white-space: nowrap;
  width: 100%;
}
thead {
  opacity: 1;
  position: sticky;
  top: 0;
  color: white;
  background: #2eb85c;
}
.form-group {
  margin-bottom: 0;
}
label {
  font-weight: bolder;
  margin-bottom: 0;
}
.pagination {
  display: flex;
  justify-items: center;
  justify-content: center;
}
button.page-link {
  display: inline-block;
}
button.page-link {
  font-size: 20px;
  color: #4189de;
  font-weight: 500;
}
</style>
