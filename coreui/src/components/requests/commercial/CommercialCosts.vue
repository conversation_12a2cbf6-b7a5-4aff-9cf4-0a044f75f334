<template>
  <c-card>
    <c-tabs class="custom-tabs">
      <c-tab active v-if="columns.includes('Cost Types')">
        <template slot="title">
          <c-icon class="custom_icon" name="cil-description" /> Cost Types
        </template>
        <div class="row">
          <div class="col-12">
            <table class="table table-striped custom-table">
              <thead class="custom-thead">
                <th>
                  <!-- <c-form-group class="mb-0">
            <template #input>
              <input
                label="All"
                v-if="types.length != 0"
                type="checkbox"
                v-model="hasAllChecked"
                title="Check All Types"
                @change="checkAllTypes"
              />
            </template>
          </c-form-group> -->
                </th>
                <th>Cost Types</th>
                <th>Amount</th>
              </thead>
              <tbody v-if="selectedFlag">
                <tr v-for="(type, index) in types" :key="index">
                  <td style="width: 10%">
                    <input
                      type="checkbox"
                      :true-value="type"
                      :false-value="null"
                      v-model="selected.types[index].type"
                      title="Check Type"
                    />
                  </td>
                  <td>
                    <strong>{{ type.name }}</strong>
                  </td>
                  <td style="width: 20%; padding: 0.5rem">
                    <c-input
                      style="width: 100px"
                      type="number"
                      placeholder="amount"
                      v-model="selected.types[index].amount"
                    />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </c-tab>
      <c-tab v-if="columns.includes('Payments')">
        <template slot="title">
          <c-icon class="custom_icon" name="cil-description" /> Payments
        </template>
        <div class="row">
          <div class="col">
            <!-- <c-button
              class="text-white"
              color="primary"
              @click="showPayments = !showPayments"
            >
              Payments</c-button
            > -->
            <c-card>
              <c-card-body>
                <payments />
              </c-card-body>
            </c-card>
          </div>
        </div>
      </c-tab>
      <c-tab v-if="columns.includes('Cost Elements')">
        <template slot="title">
          <c-icon class="custom_icon" name="cil-description" /> Cost Elements
        </template>
        <div class="row">
          <div class="col">
            <!-- <c-button
              class="text-white"
              color="primary"
              @click="showCostElements = !showCostElements"
            >
              Cost Elements</c-button
            > -->
            <c-card>
              <c-card-body>
                <commercial-categories-cost />
              </c-card-body>
            </c-card>
          </div>
        </div>
      </c-tab>
    </c-tabs>
  </c-card>
</template>
<script>
import { mapActions, mapMutations, mapState } from "vuex";
import CommercialCategoriesCost from "./CommercialCategoriesCost.vue";
import Payments from "./CommercialPayments.vue";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
    CommercialCategoriesCost,
    Payments,
  },
  data() {
    return {
      selectedTypes: [],
      columns: [],
      hasAllChecked: false,
      selectedFlag: false,
      showCostElements: false,
      showPayments: false,
    };
  },
  computed: {
    ...mapState("commercial", ["types", "selected"]),
  },
  methods: {
    ...mapActions("commercial", ["loadTypes"]),
    ...mapMutations("commercial", [
      "setSelectedTypes",
      "setSelectedSingleType",
    ]),
    async initialize() {
      return await this.loadTypes();
    },
    initSelectedTypes() {
      this.setSelectedTypes(this.types.map((type) => ({ type, amount: 0 })));
    },
  },
  created() {
    axios
      .get("/api/chosen-commercial-tabs")
      .then((response) => {
        this.columns = response.data.data;
        console.log(this.columns);
      })
      .catch((error) => {
        this.showErrorMessage(error);
      });
    this.initialize().then(() => {
      this.initSelectedTypes();
      this.selectedFlag = true;
    });
  },
};
</script>
