<template>
  <div ref="video">
    <video
      v-if="stream != null"
      :src-object.prop.camel="stream"
      autoplay
      loop
      playsinline
      @click="toggleFullScreen"
    />
  </div>
</template>

<script>
export default {
  props: ["stream"],
  methods: {
    toggleFullScreen() {
      this.$refs.video.requestFullscreen();
    },
    videoElement() {
      return this.$refs.video.firstChild;
    },
  },
  computed: {
    listeners() {
      return { ...this.$listeners };
    },
    attrs() {
      return { ...this.$attrs };
    },
  },
};
</script>

<style scoped>
video {
  max-width: 15rem;
}
div {
  display: flex;
  justify-content: center;
  align-items: center;
}

@media (min-width: 500px) {
  video {
    max-width: 20rem;
  }
}
@media (min-width: 800px) {
  video {
    max-width: 25rem;
  }
}
@media (min-width: 1024px) {
  video {
    max-width: 35rem;
  }
}

div:fullscreen > video {
  flex-basis: 100%;
  max-width: none;
}
div:-moz-full-screen > video {
  flex-basis: 100%;
  max-width: none;
}
div:-webkit-full-screen > video {
  flex-basis: 100%;
  max-width: none;
}
div:-moz-full-screen > video {
  flex-basis: 100%;
  max-width: none;
}
div:-ms-fullscreen > video {
  flex-basis: 100%;
  max-width: none;
}
</style>
