
<template></template>
<script>
import { mapMutations, mapState } from "vuex";
export default {
  // data() {
  //   return {
  //     onlineChannel: null,
  //   };
  // },
  computed: {
    ...mapState("authentication", ["authUser"]),
    ...mapState("contacts", ["contacts"]),
    ...mapState("app", ["onlineChannel"]),
  },
  methods: {
    ...mapMutations("contacts", [
      "setContacts",
      "setAdmins",
      "markContactsAsOnline",
      "markContactAsOnline",
      "markContactAsOffline",
      "markAdminsAsOnline",
      "markAdminAsOnline",
      "markAdminAsOffline",
    ]),
    ...mapMutations("communication", ["setCaller"]),
    initializeCallListeners() {
      // user channel
      this.onlineChannel.on("calling", (data) => {
        this.handleIncomingCall(data);
      });

      this.onlineChannel.on("call accepted", (data) => {
        this.$root.$call.cancel();
        this.handleCallAccepted(data);
      });

      this.onlineChannel.on("call declined", () => {
        // this.handleCallDeclined(callee);
        // remove the callee from the list of call
        // if there is no callee in list end calling as declined
        // else continue with call

        this.$root.$call.cancel();
      });
    },
    handleCallAccepted(data) {
      if (this.$route.name === "Group") return;
      this.$router.push({ name: "Group", params: { url: data.url } });
    },
    async handleIncomingCall(data) {
      try {
        const answer = await this.$root.$call.init(
          "Incoming Call",
          data.caller.name,
          true
        );
        if (!answer) {
          this.onlineChannel.emit("decline call", data.caller);

          return;
        }
        this.onlineChannel.emit("accept call", {
          caller: data.caller,
          url: data.url,
        });
        this.setCaller(data.caller);
        if (this.$route.name !== "Group") this.handleCallAccepted(data);
      } catch (error) {
        this.showErrorMessage(error);
      }
    },
    establishConnectionWithOnlineChannel() {
      // console.log(this.authUser.id)
      this.onlineChannel.emit("join online", this.authUser.id);
      this.onlineChannel.on("online users", (users) => {
        this.markContactsAsOnline(users);
        this.markAdminsAsOnline(users);
      });
      this.onlineChannel.on("joining", (user) => {
        this.markContactAsOnline(user);
        this.markAdminAsOnline(user);
      });
      this.onlineChannel.on("leaving", (user) => {
        this.markContactAsOffline(user);
        this.markAdminAsOffline(user);
      });
    },
  },
  beforeDestroy() {
    this.onlineChannel.disconnect();
  },
  async created() {
    try {
      let contactsRes = await axios.get(`/api/contacts`);
      let adminContactsRes = await axios.get("/api/contacts/admins");
      this.setAdmins(adminContactsRes.data.data);
      this.setContacts(contactsRes.data.data);
      // this.onlineChannel = this.$root.$Online;
      this.establishConnectionWithOnlineChannel();
      this.initializeCallListeners();
    } catch (error) {
      this.showErrorMessage(error);
    }
  },
};
</script>
