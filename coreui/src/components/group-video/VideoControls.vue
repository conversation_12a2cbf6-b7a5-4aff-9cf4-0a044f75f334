<template>
  <div class="controls">
    <button class="endCall" @click="endGroupCall">End Call</button>
    <button class="resize" @click="toggleFullScreen">
      <c-icon name="cil-resize-both" class="icon" />
    </button>
  </div>
</template>

<script>
import { mapActions } from "vuex";
export default {
  emits: ["fullScreen"],
  methods: {
    ...mapActions("communication", ["endCall"]),
    toggleFullScreen() {
      if (document.fullscreenElement !== null) {
        document.exitFullscreen();
      } else {
        this.$emit("fullScreen");
        this.fullScreen = true;
      }
    },
    async endGroupCall() {
      try {
        await this.endCall();
      } catch (e) {
        console.warn(e);
      }
      console.log("end call");
      this.$router.go(-1);
    },
  },
};
</script>

<style scoped>
.controls {
  margin: 1rem;
  position: relative;
}

.controls .endCall {
  position: absolute;
  top: 0;
  left: 0;
}
.controls .resize {
  position: absolute;
  top: 0;
  right: 0;
}

.controls .endCall {
  color: white;
  background-color: #ff5f57;
  padding: 0.3rem 0.7rem;
  border-radius: 0.5rem;
}
.controls .resize,
.controls .endCall {
  border: none;
  outline: none;
}
.controls .resize:hover,
.controls .endCall:hover {
  opacity: 0.7;
}

.controls .resize:focus,
.controls .endCall:focus {
  transform: scale(1.1);
}
.filled {
  stroke: #91929f;
  stroke-width: 3px;
}
.resize .icon {
  color: #91929f;
  transform: rotateX(180deg);
  width: 22px;
  height: 22px;
}
</style>
