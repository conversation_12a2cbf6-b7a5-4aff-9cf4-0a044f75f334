<template>
  <div class="main-container" ref="mainContainer">
    <div class="dialog">
      <slot name="dialog"></slot>
    </div>
    <div class="chat-dialog">
      <slot name="chatDialog"></slot>
    </div>
    <div class="my-container">
      <slot></slot>
    </div>
    <div class="sidebar">
      <slot name="main"></slot>
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    enableFullscreen() {
      this.$refs.mainContainer.requestFullscreen();
    },
  },
};
</script>

<style scoped>
.main-container {
  display: flex;
  justify-content: space-around;
  background-color: #202022;
  --max-height: 73vh;
}

.sidebar {
  position: relative;
  background-color: white;
  transition: 250ms;
}
.sidebar >>> .group-chat {
  height: var(--max-height);
  width: 18rem;
  position: absolute;
  right: 0;
  top: 0;
}
.dialog,
.chat-dialog {
  position: fixed;
  z-index: 999;
  margin-top: 1rem;
}
.my-container {
  display: flex;
  flex-grow: 1;
  justify-content: space-between;
  flex-direction: column;
  height: var(--max-height);
  background-color: #202022;
  position: relative;
}

.main-container:fullscreen {
  --max-height: 100vh;
}
.main-container:-moz-full-screen {
  --max-height: 100vh;
}
.main-container:-webkit-full-screen {
  --max-height: 100vh;
}
.main-container:-ms-fullscreen {
  --max-height: 100vh;
}
</style>
