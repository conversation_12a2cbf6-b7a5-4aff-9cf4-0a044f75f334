<template>
  <v-dialog
    v-model="dialog"
    :max-width="options.width"
    :style="{ zIndex: options.zIndex }"
    @keydown.esc="cancel"
  >
    <v-card>
      <v-toolbar dark :color="options.color" dense flat>
        <v-toolbar-title class="white--text">{{ title }}</v-toolbar-title>
      </v-toolbar>
      <v-card-text v-if="!Array.isArray(message)" class="pa-4">{{
        message
      }}</v-card-text>
      <div
        v-else-if="Array.isArray(message)"
        style="height: 300px"
        class="overflow-y-auto"
      >
        <v-card-text v-for="{ name } in message" :key="name">{{
          name
        }}</v-card-text>
      </div>
      <v-card-actions class="pt-0">
        <v-spacer></v-spacer>
        <v-btn
          v-if="answerFlag"
          color="primary darken-1"
          text
          @click.native="answer"
          >Answer</v-btn
        >
        <v-btn color="grey" text @click.native="cancel">Reject</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import { mapActions } from "vuex";
export default {
  data: () => ({
    dialog: false,
    resolve: null,
    reject: null,
    message: null,
    title: null,
    answerFlag: false,
    audio: null,
    options: {
      color: "primary",
      width: 290,
      zIndex: 200,
    },
  }),
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    initialize() {
      this.audio = document.createElement("audio");
      this.audio.loop = true;
      // this.audio.src = "/ringtone.mp3";
    },
    init(title, message, answerFlag = false, options = null) {
      this.loadVApp();
      this.dialog = true;
      this.title = title;
      this.message = message;
      this.answerFlag = answerFlag;
      this.audio.src = this.answerFlag ? "ringtone.mp3" : "caller-ringtone.mp3";
      this.options = Object.assign(this.options, options || this.options);

      this.audio.play();

      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    answer() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp();
      this.audio.pause();
    },
    cancel() {
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp();
      this.audio.pause();
    },
  },
  created() {
    this.initialize();
  },
};
</script>
