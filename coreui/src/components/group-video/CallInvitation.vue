<template>
  <div class="main">
    <div class="contents">
      <div>
        <h6>Share Link</h6>
        <div class="input__submit">
          <input type="text" :value="SharedLink" disabled />
          <button class="c-white bg-blue" @click="copyLink">
            {{ copied ? "Copied" : "Copy" }}
          </button>
        </div>
      </div>
      <div>
        <h6>Email Invite</h6>
        <div class="input__submit">
          <input type="text" placeholder="Enter Email" />
          <button class="c-white bg-green" @click="sendMail">Email</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      copied: false,
    };
  },
  computed: {
    SharedLink() {
      return location.href;
    },
  },
  methods: {
    copyLink() {
      navigator.clipboard.writeText(this.SharedLink);
      this.copied = true;
      setTimeout(() => {
        this.copied = false;
      }, 2000);
    },
    //TODO:send mail implementation
    sendMail() {},
  },
};
</script>
<style scoped>
.c-white {
  color: white;
}
.bg-blue {
  background-color: rgb(15, 113, 238);
}
.bg-green {
  background-color: #00bd87;
}

.contents {
  padding: 0.5rem;
}
.contents > * + * {
  margin-top: 1rem;
}

.input__submit input {
  width: 7rem;
  border-left: 1px solid gray;
  border-top: 1px solid gray;
  border-bottom: 1px solid gray;
  padding: 0.2rem 0;
  border-radius: 3px 0 0 3px;
}
.input__submit button {
  width: 4rem;
  padding: 0.25rem 0.25rem;
  border-radius: 0 3px 3px 0;
  margin: 0;
}
.main {
  background-color: #fff;
  margin: 1rem auto;
  width: 12.5rem;
  border-radius: 3px;
}
</style>
