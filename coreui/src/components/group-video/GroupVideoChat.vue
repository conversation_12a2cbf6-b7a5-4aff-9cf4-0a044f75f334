<template>
  <div class="group-chat">
    <div class="messages" ref="messages">
      <message-in-chat
        v-for="(message, index) in groupCall.messages"
        :key="index"
        :message="message"
      />
    </div>
    <div class="input-group">
      <input
        type="text"
        placeholder="Message..."
        v-model="textMessage"
        @keyup.enter="send"
      />
      <button @click="selectFile">
        <c-icon name="cil-paperclip" class="c-white" />
      </button>
    </div>
    <div class="close" @click="toggleShowChat">
      <c-icon name="cil-x-circle" class="c-white" />
    </div>
  </div>
</template>

<script>
import MessageInChat from "./MessageInChat.vue";
import { mapActions, mapState, mapGetters } from "vuex";
import streamSaver from "streamsaver";
import * as ponyfill from "web-streams-polyfill/ponyfill";

streamSaver.WritableStream = ponyfill.WritableStream;
if (window.Worker) {
  var worker = new Worker("/workers/StreamFileWorker.js");
  worker.onerror = (e) => {
    console.log(e);
  };
}

export default {
  emits: ["newMessage"],
  props: {
    peers: {
      type: Array,
      default: () => [],
    },
    rawData: {
      type: Uint8Array,
      default: "",
    },
  },
  components: {
    MessageInChat,
  },
  data() {
    return {
      textMessage: "",
    };
  },
  computed: {
    ...mapState("authentication", ["authUser"]),
    ...mapState("communication", ["groupCall"]),
    ...mapGetters("communication", ["time"]),
  },
  methods: {
    ...mapActions("communication", [
      "sendUserMessage",
      "pushMessage",
      "toggleShowChat",
      "sendFile",
      "sendMessage",
      "readFileThenSend",
      "writeFile",
    ]),
    toggle() {
      if (document.fullscreenElement !== null) this.show = !this.show;
    },
    async send() {
      if (this.textMessage === "") return;
      let message = {
        type: "message",
        user: {
          name: this.authUser.name,
        },
        created_at: this.time,
        body: this.textMessage,
      };
      await this.sendUserMessage(message);

      this.textMessage = "";
      this.$refs.messages.scrollTop = this.$refs.messages.scrollHeight;
    },
    selectFile() {
      let inputElement = document.createElement("input");
      inputElement.setAttribute("type", "file");
      document.body.appendChild(inputElement);
      inputElement.addEventListener("change", async (e) => {
        document.body.removeChild(inputElement);
        await this.readFileThenSend({
          file: e.target.files[0],
          user: this.authUser,
          time: this.time,
        });
      });
      inputElement.click();
    },
    async handleReceivingMessage(decodedMessage) {
      let message = JSON.parse(decodedMessage.toString());
      this.$emit("newMessage", message);
      await this.pushMessage(message);
      this.$refs.messages.scrollTop = this.$refs.messages.scrollHeight;
    },

    async handleReceivingData() {
      let decodedMessage = new TextDecoder("utf-8").decode(this.rawData);
      if (decodedMessage.includes("message")) {
        return await this.handleReceivingMessage(decodedMessage);
      }

      if (decodedMessage.includes("done")) {
        return await this.writeFile({ decodedMessage, worker, streamSaver });
      }
      worker.postMessage(this.rawData);
    },
  },
  watch: {
    rawData: {
      handler: "handleReceivingData",
    },
  },
  beforeDestroy() {
    worker = null;
  },
};
</script>

<style scoped>
.c-white {
  color: white;
}
.group-chat {
  display: flex;
  flex-direction: column;
  background-color: #27282c;
  border: 2px solid #3f3f47;
  padding: 0.75rem;
  gap: 0.5rem;
  position: relative;
}
.close {
  position: absolute;
  left: -1rem;
  top: 50%;
  border-radius: 50%;
  cursor: pointer;
}
.close :first-child {
  width: 2rem;
  height: 2rem;
}
.input-group {
  display: flex;
  background-color: #27282c;
  border: 2px solid #fff;
  border-radius: 5px;
}
.messages {
  display: flex;
  flex-direction: column;
  margin-top: auto;
  max-height: 65vh;
  overflow: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: #e3e3e3 transparent;
}

button {
  margin-left: auto;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}
input {
  margin-left: 0.5rem;
}
input,
input:focus {
  border: none;
  outline: none;
  color: white;
}
button,
button:focus {
  border: none;
  outline: none;
  color: white;
}

.messages::-webkit-scrollbar {
  width: 10px;
}
.messages::-webkit-scrollbar-thumb {
  background: #e3e3e3;
  border-radius: 4px;
}
.messages::-webkit-scrollbar-thumb:hover {
  background: #6d6d6d;
}
</style>
