<template>
  <div class="main">
    <div class="contents">
      <div tabindex="1" @click="record">Record</div>
      <div tabindex="2" @click="stop">Stop</div>
    </div>
  </div>
</template>
<script>
import { Recorder, VideoStreamMerger } from "../../services/BrowserMixer.js";
import { mapState } from "vuex";
export default {
  data() {
    return {
      /**
       * @property { Recorder } recorder
       * */
      recorder: null,
      blockSize: { width: 0, height: 0 },
      coordinates: [],
      rows: 0,
      columns: 0,
    };
  },
  computed: {
    ...mapState("communication", ["groupCall"]),
  },
  methods: {
    /**
     * @return {Number}
     **/
    setRows() {
      this.rows = (this.groupCall.streams.length ** 0.5 + 0.99) | 0;
    },
    /**
     * @return {Number}
     **/
    setColumns() {
      this.columns = (this.groupCall.streams.length ** 0.5 + 0.5) | 0;
    },
    /**
     * @param {Number}totalWidth
     * @param {Number}totalHeight
     **/
    getBlockSize(totalWidth, totalHeight) {
      this.setColumns();
      this.setRows();
      this.blockSize = {
        width: totalWidth / this.columns,
        height: totalHeight / this.rows,
      };
    },
    setMargin(value) {
      this.blockSize = {
        width: this.blockSize.width - value,
        height: this.blockSize.height - value,
      };
    },
    getCoordinates() {
      for (
        let r = 0;
        r < this.rows * this.blockSize.height;
        r += this.blockSize.height
      ) {
        for (
          let c = 0;
          c < this.columns * this.blockSize.width;
          c += this.blockSize.width
        ) {
          this.coordinates.push({ x: c, y: r });
        }
      }
    },
    record() {
      let merger = new VideoStreamMerger({ width: 1920, height: 1080 });
      this.getBlockSize(merger.width, merger.height);
      this.getCoordinates();
      this.setMargin(2);
      merger.start();
      this.groupCall.streams.forEach(({ stream }, i) => {
        merger?.addStream(stream, {
          ...this.coordinates[i],
          ...this.blockSize,
          mute: false,
        });
      });

      this.recorder = new Recorder();
      this.recorder.start(merger.result);
    },
    stop() {
      this.recorder?.stop().then(() => {
        let blobs = this.recorder.getBlob();
        const url = URL.createObjectURL(blobs);
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.download = "record.webm";
        document.body.appendChild(a);
        a.click();
        setTimeout(() => {
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }, 100);
        this.reset();
      });
    },
    reset() {
      this.blockSize = { width: 0, height: 0 };
      this.coordinates = [];
      this.rows = 0;
      this.columns = 0;
    },
  },
};
</script>
<style scoped>
.contents {
  padding: 0.5rem;
}
.contents > * + * {
  margin-top: 0.5rem;
}
.contents > div {
  padding: 0.5em 0;
  text-align: center;
  cursor: pointer;
}
.contents > div:hover,
.contents > div:focus {
  background-color: gray;
  color: white;
}
.main {
  background-color: #fff;
  margin: 1rem auto;
  width: 12.5rem;
  border-radius: 3px;
}
</style>
