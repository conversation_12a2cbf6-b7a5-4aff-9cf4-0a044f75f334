<template>
  <stream-video
    :stream="stream"
    :id="peerObj.id"
    class="peerVideo"
    ref="videoStream"
  />
</template>

<script>
import { mapActions, mapState } from "vuex";
import StreamVideo from "../group-video/StreamVideo.vue";
export default {
  components: {
    StreamVideo,
  },
  emits: ["rowDataReceived"],
  props: ["peerObj"],
  data() {
    return {
      stream: null,
    };
  },
  computed: {
    ...mapState("communication", [
      "groupCall",
      "merger",
      "coordinates",
      "blockSize",
    ]),
  },
  methods: {
    ...mapActions("communication", [
      "pushStream",
      "mapCoordinates",
      "reRenderMerger",
      "handleReceivingData",
    ]),
  },
  mounted() {
    this.peerObj.peer.on("stream", async (stream) => {
      this.stream = stream;
      try {
        await this.pushStream({ stream: this.stream, peerID: this.peerObj.id });
        await this.mapCoordinates();
        await this.reRenderMerger();
      } catch (e) {
        console.warn(e);
      }

      console.log("stream is beginning");
    });
    this.peerObj.peer.on("data", (data) => {
      this.$emit("rowDataReceived", data);
    });
    this.peerObj.peer.on("error", (error) => {
      console.warn(error);
    });
  },
};
</script>

<style scoped>
.peerVideo {
  margin-bottom: 1rem;
}
</style>
