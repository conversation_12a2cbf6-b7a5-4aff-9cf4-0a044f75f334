<template>
  <div>
    <call-invitation v-if="groupCall.invite" />
    <call-other-features v-if="groupCall.otherFeatures" />
    <div class="doc">
      <section @click="toggleMic">
        <div class="icon-title">
          <div
            class="background-icon"
            :class="
              groupCall.toggleUserAudio ? 'bg-white c-black' : 'bg-red c-white'
            "
          >
            <c-icon name="cil-microphone" class="icon" />
          </div>
          <p class="title">Mic</p>
        </div>
      </section>
      <section @click="toggleCamera">
        <div class="icon-title">
          <div
            class="background-icon"
            :class="
              groupCall.toggleUserVideo ? 'bg-white c-black' : 'bg-red c-white'
            "
          >
            <c-icon name="cil-video" class="icon" />
          </div>
          <p class="title">Camera</p>
        </div>
      </section>
      <section @click="sendInvitation">
        <div class="icon-title">
          <div class="background-icon bg-orange">
            <div class="rounded-box bg-white">
              <c-icon name="cil-plus" class="icon c-orange" />
            </div>
          </div>
          <p class="title">Invite</p>
        </div>
      </section>
      <section @click="shareScreen">
        <div class="icon-title">
          <div class="background-icon bg-blue">
            <div class="rounded-box bg-white">
              <c-icon name="cil-arrow-top" class="icon c-blue" />
            </div>
          </div>
          <p class="title">Share</p>
        </div>
      </section>
      <section @click="chatToggle">
        <div class="icon-title">
          <div class="background-icon bg-blue">
            <div class="rounded-box bg-white">
              <c-icon name="cil-chat-bubble" class="icon c-blue" />
            </div>
          </div>
          <p class="title">Chat</p>
        </div>
      </section>
      <section @click="toggleOthers">
        <div class="icon-title">
          <div class="background-icon bg-blue">
            <div class="rounded-box bg-white">
              <c-icon name="cil-options" class="icon c-blue" />
            </div>
          </div>
          <p class="title">Others</p>
        </div>
      </section>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from "vuex";
import CallInvitation from "../../components/group-video/CallInvitation.vue";
import CallOtherFeatures from "../../components/group-video/CallOtherFeatures.vue";
import { getPermissions } from "../../services/BrowserPermissions";
export default {
  components: {
    CallInvitation,
    CallOtherFeatures,
  },
  computed: {
    ...mapState("communication", ["groupCall"]),
  },
  methods: {
    ...mapActions("communication", [
      "toggleShowChat",
      "toggleMuteAudio",
      "toggleMuteVideo",
      "toggleSendInvitation",
      "toggleOtherFeatures",
      "replaceVideoTracks",
    ]),
    chatToggle() {
      this.toggleShowChat();
    },
    async shareScreen() {
      let screenStream = await getPermissions.displayMedia({ cursor: true });
      let screenTracks = screenStream.getTracks();
      this.replaceVideoTracks(screenTracks);
    },
    sendInvitation() {
      this.toggleSendInvitation();
    },
    toggleCamera() {
      this.toggleMuteVideo();
    },
    toggleMic() {
      this.toggleMuteAudio();
    },
    toggleOthers() {
      this.toggleOtherFeatures();
    },
  },
};
</script>


<style scoped>
.bg-orange {
  background-color: #fd7633 !important;
}
.bg-red {
  background-color: #ff5f57 !important;
}
.c-orange {
  color: #fd7633 !important;
}
.c-white {
  color: #fff !important;
}
.c-black {
  color: #000 !important;
}
.bg-blue {
  background-color: #0f71ee !important;
}
.c-blue {
  color: #0f71ee !important;
}
.bg-white {
  background-color: white !important;
}

.rounded-box {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 1rem;
  height: 1rem;
  border-radius: 3px;
}

.doc {
  display: flex;
  margin: auto;
  justify-content: space-evenly;
  background-color: #2e2e36;
  border: 2px #45444e solid;
  border-radius: 5px 5px 0 0;
  gap: 0.128rem;
  height: 5rem;
  width: 18rem;
}
.doc > section {
  width: 3rem;
  cursor: pointer;
}
.doc:hover section {
  filter: blur(3px);
  opacity: 0.5;
  transform: scale(0.98);
  box-shadow: none;
}
.doc:hover section:hover {
  transform: scale(1);
  filter: blur(0px);
  opacity: 1;
  box-shadow: 0 8px 20px 0px rgba(0, 0, 0, 0.125);
}
.doc .background-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  margin-top: 1rem;
}
.doc .title {
  color: white;
}
.doc section .status {
  color: rgb(85, 85, 85);
  font-weight: bold;
}
.icon-title {
  display: flex;
  flex-direction: column;
  align-items: center;
}

@media (min-width: 801px) {
  /* tablet, landscape iPad, lo-res laptops ands desktops */
  .doc {
    width: 22rem;
  }
  .doc > section {
    width: 4rem;
  }
}
@media (min-width: 1025px) {
  /* big landscape tablets, laptops, and desktops */
  .doc {
    width: 37rem;
  }
  .doc > section {
    width: 6rem;
  }
}
@media (min-width: 1281px) {
  /* hi-res laptops and desktops */
  .doc {
    width: 42rem;
  }
  .doc > section {
    width: 8rem;
  }
}
</style>
