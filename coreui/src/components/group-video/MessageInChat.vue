<template>
  <div class="message-row">
    <div class="tag-container">
      <span class="user-tag"
        ><div>{{ message.user.name | capitalize | abbreviate }}</div></span
      >
    </div>
    <div class="message-user">
      <div class="user-time">
        <div class="user-name c-white">
          {{ message.user.name | shortName | capitalize }}
        </div>
        <div class="time c-white">{{ message.created_at }}</div>
      </div>
      <div class="message-body c-white">{{ message.body }}</div>
    </div>
    <!-- <div v-if="isFileText">

    </div> -->
  </div>
</template>
<script>
export default {
  props: {
    message: {
      type: Object,
      required: true,
    },
  },
};
</script>
<style scoped>
.message-row {
  display: flex;
  cursor: context-menu;
  gap: 0.5rem;
}
.message-row:hover {
  opacity: 0.7;
}
.tag-container {
  display: flex;
  align-items: center;
  justify-content: center;
}
.user-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  background-color: #1098ad;
  width: 2pc;
  height: 2pc;
  border-radius: 50%;
  font-size: small;
}
.message-user {
  display: flex;
  flex-direction: column;
}
.user-time {
  display: flex;
  gap: 0.5rem;
}
.c-white {
  color: white;
}
</style>
