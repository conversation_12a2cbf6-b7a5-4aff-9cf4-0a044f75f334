<template>
  <div v-if="show" class="dialog-container" :title="message.user.name">
    <div>{{ limitedBody }}</div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      timer: 3,
      show: false,
      message: null,
    };
  },
  computed: {
    limitedBody() {
      if (this.message === null && this.show === false) return "";
      return this.message.body.slice(0, 35).concat("...");
    },
  },
  methods: {
    close() {
      setTimeout(() => {
        this.show = !this.show;
      }, this.timer * 1000);
    },
    open(message) {
      this.message = message;
      this.show = true;
      this.close();
    },
  },
};
</script>
<style scoped>
.dialog-container {
  display: flex;
  flex-direction: column;
  color: white;
  background-color: #2e2e36;
  border: 1px solid #44444b;
  padding: 0.5rem;
  border-radius: 0.5rem;
  box-shadow: rgba(255, 255, 255, 0.1) 0px 8px 24px;
  cursor: pointer;
}
</style>
