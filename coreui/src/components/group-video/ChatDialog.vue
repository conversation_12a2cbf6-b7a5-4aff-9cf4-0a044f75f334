<template>
    <div class="input-group">
      <input
        type="text"
        placeholder="Message..."
        v-model="message"
        @keyup.enter="sendMessage"
      />
      <button><c-icon name="cil-paperclip" class="c-white" /></button>
    </div>
</template>
<script>
export default {
  data() {
    return {
      timer: 3,
      show: false,
      message:null
    };
  },
  computed: {
    limitedBody() {
      if(this.message===null&&this.show===false)return ''
      return this.message.body.slice(0, 35).concat("...");
    },
  },
  methods: {
    close() {
      setTimeout(() => {
        this.show = !this.show;
      }, this.timer * 1000);
    },
    open(message) {
      this.message=message
      this.show = true;
      this.close();
    },
  },
};
</script>

<style scoped>
.c-white {
  color: white;
}
.input-group {
  display: flex;
  background-color: #27282c;
  border: 2px solid #fff;
  border-radius: 5px;
}

button {
  margin-left: auto;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}
input {
  margin-left: 0.5rem;
}
input,
input:focus {
  border: none;
  outline: none;
  color: white;
}
button,
button:focus {
  border: none;
  outline: none;
  color: white;
}
</style>
