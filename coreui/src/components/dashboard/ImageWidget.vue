<template>
  <div class="w-100 border border-blue-900">
    <a class="btn col-12 col-lg-3 m-3 d-flex justify-content-around flex-wrap"
       v-for="(card, index) in alertData" :key="index" @mouseover="setHovered(index, true)"
       :activePage.sync='currentPage'
       @mouseout="setHovered(index, false)" :class="{ 'hovered': hoveredIndex === index }"
       v-c-tooltip="{
              content: `<b>Emp_code: </b> ${card.emp_code ? card.emp_code : ''} <br> <b>Line: </b> ${ card.lines } <br> <b>Division: </b> <br> ${ card.divisions }`,
              html: true,
              placement: 'down'
        }"
    >
      <img class="card__thumb" :src="card.image" alt=""/>

      <div class="d-flex justify-content-center flex-column justify-end">
        <p class="badge rounded-pill names">{{ card.fullname }}</p>
        <p class="badge rounded-pill roles">{{ card.role }}</p>
      </div>
    </a>

  </div>
</template>

<script>
export default {
  props: {
    alertData: {
      type: Array,
      required: true,
    }
  },
  data() {
    return {
      arrays: [],
      fields: [],
      url: null,
      hoveredIndex: null,
      currentPage: 1
    };
  },
  methods: {
    setHovered(index, isHovered) {
      this.hoveredIndex = isHovered ? index : null;
    },
  },
};
</script>

<style scoped>
* {
  box-sizing: border-box;
}

body {
  font-family: 'Noto Sans JP', sans-serif;
}

a {
  width: 15% !important;
  height: 20% !important;
}

p {
  font-size: 0.65rem;
  color: #eaa206;
  position: absolute;
  margin-bottom: 3px;
  margin-left: 45px;
}

.names {
  font-size: 0.75rem;
}

.card {
  position: relative;
  display: inline-block;
  height: 22%;
  width: 20%;
  margin: 2%;
  margin-top: 0.2%;
  border-radius: 50px !important;
  overflow: hidden;
  text-decoration: none;
}

.card__thumb {
  position: absolute;
  left: 5%;
  bottom: 10%;
  flex-shrink: 0;
  width: 44px;
  height: 44px;
  border-radius: 5%;
}
</style>
