<template>
  <c-row>
    <c-col col="12" xl="12">
      <transition name="slide">
        <c-card>
          <c-card-header> Plan Visits </c-card-header>
          <c-card-body>
            <c-tabs>
              <c-tab>
                <template slot="title">
                  <c-icon name="cil-chart-pie" /> AM Visits
                </template>

                <c-data-table
                  hover
                  striped
                  sorter
                  tableFilter
                  footer
                  :itemsPerPageSelect="perPageSelect"
                  :items="am_items"
                  :fields="plan_fields"
                  :items-per-page="perPage"
                  :active-page="1"
                  :responsive="true"
                  :table-filter="{ external: true, lazy: true }"
                  pagination
                  thead-top
                >
                  <template slot="thead-top">
                    <td style="border-top: none"><strong>Total</strong></td>
                    <td style="border-top: none" class="text-xs-right">
                      {{ am_total }}
                    </td>
                  </template>

                  <template #actions="{ item }">
                    <td>
                      <div
                        class="flex justify-content"
                        v-if="item.plan_id == null"
                      >
                        <c-button
                          title="Create Actual Visit"
                          color="primary"
                          class="text-white"
                          variant="outline"
                          square
                          size="sm"
                          v-if="item.approval == 1"
                          @click="editActualVisit(item.id)"
                        >
                          Actual
                        </c-button>
                        <h6
                          style="color: #321fdb"
                          v-else-if="
                            item.approval == 0 || item.aprroval == null
                          "
                        >
                          pending..
                        </h6>
                      </div>
                    </td>
                  </template>
                </c-data-table>
              </c-tab>
              <c-tab>
                <template slot="title">
                  <c-icon name="cil-chart-pie" /> PM Visits
                </template>

                <c-data-table
                  hover
                  striped
                  sorter
                  tableFilter
                  footer
                  :itemsPerPageSelect="perPageSelect"
                  :items="pm_items"
                  :fields="plan_fields"
                  :items-per-page="perPage"
                  :active-page="1"
                  :responsive="true"
                  :table-filter="{ external: true, lazy: true }"
                  pagination
                  thead-top
                >
                  <template slot="thead-top">
                    <td style="border-top: none"><strong>Total</strong></td>
                    <td style="border-top: none" class="text-xs-right">
                      {{ pm_total }}
                    </td>
                  </template>

                  <template #actions="{ item }">
                    <td>
                      <div
                        class="flex justify-content"
                        v-if="item.plan_id == null"
                      >
                        <c-button
                          title="Create Actual Visit"
                          color="primary"
                          class="text-white"
                          variant="outline"
                          square
                          size="sm"
                          v-if="item.approval == 1"
                          @click="editActualVisit(item.id)"
                        >
                          Actual
                        </c-button>
                        <h6
                          style="color: #321fdb"
                          v-else-if="
                            item.approval == 0 || item.aprroval == null
                          "
                        >
                          pending..
                        </h6>
                      </div>
                    </td>
                  </template>
                </c-data-table>
              </c-tab>
              <c-tab>
                <template slot="title">
                  <c-icon name="cil-chart-pie" /> Office Work Visits
                </template>

                <c-data-table
                  hover
                  striped
                  sorter
                  tableFilter
                  footer
                  :itemsPerPageSelect="perPageSelect"
                  :items="items_ow"
                  :fields="ow_fields"
                  :items-per-page="100"
                  :active-page="1"
                  :responsive="true"
                  :table-filter="{ external: true, lazy: true }"
                  pagination
                  thead-top
                >
                  <template slot="thead-top">
                    <td style="border-top: none"><strong>Total</strong></td>
                    <td style="border-top: none" class="text-xs-right">
                      {{ total_ow }}
                    </td>
                  </template>

                  <template #actions="{ item }">
                    <td>
                      <div
                        class="flex justify-content"
                        v-if="item.plan_id == null"
                      >
                        <c-button
                          title="Create Actual Visit"
                          color="primary"
                          class="text-white"
                          variant="outline"
                          square
                          size="sm"
                          v-if="item.approval == 1"
                          @click="editActualVisit(item.id)"
                        >
                          Actual
                        </c-button>
                        <h6
                          style="color: #321fdb"
                          v-else-if="
                            item.approval == 0 || item.aprroval == null
                          "
                        >
                          pending..
                        </h6>
                      </div>
                    </td>
                  </template>
                </c-data-table>
              </c-tab>
              <c-tab active>
                <template slot="title">
                  <c-icon name="cil-chart-pie" /> All Visits
                </template>

                <c-data-table
                  hover
                  striped
                  sorter
                  tableFilter
                  footer
                  :itemsPerPageSelect="perPageSelect"
                  :items="items"
                  :fields="plan_fields"
                  :items-per-page="perPage"
                  :active-page="1"
                  :responsive="true"
                  :table-filter="{ external: true, lazy: true }"
                  pagination
                  thead-top
                >
                  <template slot="thead-top">
                    <td style="border-top: none"><strong>Total</strong></td>
                    <td style="border-top: none" class="text-xs-right">
                      {{ total }}
                    </td>
                  </template>
                  <template #actions="{ item }">
                    <!-- if plan is actual -->
                    <td>
                      <div
                        class="flex justify-content"
                        v-if="item.plan_id == null"
                      >
                        <c-button
                          title="Create Actual Visit"
                          color="primary"
                          class="text-white"
                          variant="outline"
                          square
                          size="sm"
                          v-if="item.approval == 1"
                          @click="editActualVisit(item.id)"
                        >
                          Actual
                        </c-button>
                        <h6
                          style="color: #321fdb"
                          v-else-if="
                            item.approval == 0 || item.aprroval == null
                          "
                        >
                          pending..
                        </h6>
                      </div>
                    </td>
                  </template>
                </c-data-table>
              </c-tab>
            </c-tabs>
          </c-card-body>
        </c-card>
      </transition>
    </c-col>
  </c-row>
</template>
<script>
export default {
  data() {
    return {
      perPageSelect: {
        values: ["5", "10", "20", "30", "50"],
        external: true,
      },
      items: [],
      am_items: [],
      pm_items: [],
      items_ow: [],
      lines: [],
      accountTypes: [],
      visitTypes: [],
      max_products: "",
      inputs: [],
      accountTypes: [],
      plan_fields: [
        "line",
        "division",
        "account",
        "doctor",
        "shift",
        "actions",
      ],
      ow_fields: ["id", "office_work", "shift", "day", "actions"],
      currentPage: 1,
      perPage: 5,
      total: 0,
      am_total: 0,
      pm_total: 0,
      total_ow: 0,
    };
  },
  methods: {
    editActualVisit(id) {
      const editLink = this.editLink(id);
      this.$router.push({ path: editLink });
    },
    editLink(id) {
      return `actual_visits/create_plan_actual_visit/${id.toString()}`;
    },
    getPlanVisits(visit_date) {
      axios
        .get("/api/getPlanVisits/" + visit_date)
        .then((response) => {
          this.items = response.data.plan_visits;
          this.total = response.data.total;
          this.am_items = response.data.am_plan_visits;
          this.am_total = response.data.am_total;
          this.pm_items = response.data.pm_plan_visits;
          this.pm_total = response.data.pm_total;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },

    createActualVisit() {
      axios
        .get("/api/createActualVisit/" + this.plan.id)
        .then((response) => {
          this.lines = response.data.lines;
          this.accountTypes = response.data.accountTypes;
          this.visitTypes = response.data.visitTypes;
          this.visitFeedbacks = response.data.visitFeedbacks;
          this.max_products = response.data.max_products;
          this.inputs = response.data.inputs;
          this.plan = response.data.plan;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getOWPlanVisits(day) {
      axios
        .get("/api/getOWPlanVisits/" + day)
        .then((response) => {
          this.items_ow = response.data.ow_plan_visits;
          this.total_ow = response.data.total_ow;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    formatDate(date) {
      var d = new Date(date),
        month = "" + (d.getMonth() + 1),
        day = "" + d.getDate(),
        year = d.getFullYear();

      if (month.length < 2) month = "0" + month;
      if (day.length < 2) day = "0" + day;

      return [year, month, day].join("-");
    },
  },

  created() {
    this.getPlanVisits("today");
    this.getOWPlanVisits("today");
  },
};
</script>
