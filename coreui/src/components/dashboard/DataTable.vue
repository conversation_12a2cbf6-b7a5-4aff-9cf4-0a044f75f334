<template>
  <c-card>
    <c-card-body>
      <c-data-table hover striped sorter :items="items" :fields="fields[0]" itemsPerPageSelect :items-per-page="5"
        :active-page="1" :responsive="true" table-filter pagination>
        <template slot="thead-top">
          <td style="border-top: none"><strong>Total</strong></td>
          <td style="border-top: none" class="text-xs-right">
            {{ items.length }}
          </td>
        </template>
        <template #periority="{ item }">
          <td>
            <c-badge class="text-white" style="font-weight: bold; font-size: 15px"
              v-for="(action, key) in item.periority" :key="key" :disabled="action.transaction === ''"
              v-show="action.visible === true" :color="action.color">
              <c-button>{{ action.title }}</c-button>
            </c-badge>
          </td>
        </template>
        <template #file="{ item }">
          <td v-if="item.file.length > 0">
            <div class="row" v-for="(x, index) in item.file" :key="index">
              <CButton style="padding-top: 10px" small class="btn-link" target="_blank" :href="x">{{ x |
                fileNameFromPath }}
              </CButton>
            </div>
          </td>
          <td v-else></td>
        </template>
        <template #follow_up="{ item }">
          <td>
            <c-badge class="text-white" style="font-weight: bold; font-size: 15px"
              v-for="(action, key) in item.follow_up" :key="key" :disabled="action.transaction === ''"
              v-show="action.visible === true" :color="action.color" @click="followUp(item.id)">
              {{ action.title }}
            </c-badge>
          </td>
        </template>
        <template #market_feedback="{ item }">
          <td>
            <c-badge class="text-white" style="font-weight: bold; font-size: 15px"
              v-for="(action, key) in item.market_feedback" :key="key" :disabled="action.transaction === ''"
              v-show="action.visible === true" :color="action.color" @click="marketFeedback(item.id)">
              {{ action.title }}
            </c-badge>
          </td>
        </template>
        <template #quizActions="{ item }">
          <td>
            <c-badge class="text-white" style="font-weight: bold; font-size: 15px; cursor:pointer;"
              v-for="(action, key) in item.quizActions" :key="key" :disabled="action.transaction === ''"
              v-show="action.visible === true" :color="action.color" @click="quiz(item.id, action.title)">
              {{ action.title }}
            </c-badge>
          </td>
        </template>
        <template #double_actions="{ item }">
          <td>
            <c-button class="px-1 m-0 m-1" color="primary" @click="saveDoubleLocation(item)">
              Approved
            </c-button>
          </td>
        </template>
        <template #coachingAction="{ item }">
          <td>
            <c-badge class="text-white" style="font-weight: bold; font-size: 15px; cursor:pointer;"
              v-for="(action, key) in item.coachingAction" :key="key" :disabled="action.transaction === ''"
              v-show="action.visible === true" :color="action.color" @click="coachingAction(item.id)">
              {{ action.title }}
            </c-badge>
          </td>
        </template>
        <template #samplesStatus="{ item }">
          <td>
            <c-button class="text-white" style="font-weight: bold; margin-left:10px;font-size: 15px; cursor:pointer;"
              v-for="(action, key) in item.samplesStatus" :key="key" :color="action.color"
              @click="samplesStatus(item, action)">
              <c-icon :name=action.title />
            </c-button>
          </td>
        </template>
      </c-data-table>
    </c-card-body>
  </c-card>
</template>


<script>
import { mapState } from "vuex";
export default {
  props: {
    widget: {
      type: Object,
      required: true,
    },
  },
  inject: ['reload'],
  data() {
    return {
      items: [],
      fields: [],
      titles: [],
      icons: ["cil-braille", "cil-task", "cil-description"],
      marker: { position: { lat: 0, lng: 0 } },
    };
  },
  computed: {
    ...mapState("authentication", ["authUser"]),
  },
  methods: {
    setData(object) {
      this.items = object;
      const fields = Object.keys(object[0]);

      this.fields.push(fields);
    },
    followUp(id) {
      this.$router.push(`/actual_visits/${id}`);
    },
    marketFeedback(id) {
      this.$router.push(`/actual_visits/${id}`);
    },
    quiz(id, title) {
      if (title == 'Start Quiz') {
        this.$router.push(`/quiz/${id}`);
      }
      if (title == 'Show Answers') {
        this.$router.push({
          name: 'ShowQuizAnswers',
          params: {
            id: id,
            user_id: this.authUser.id,
          }
        })
      }
    },
    coachingAction(id) {
      axios
        .post(`/api/change-coaching-approval`, {
          id: id
        })
        .then((response) => {
          this.flash("ُEvaluation Approved Successfully");
          this.reload(this.widget.id)
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    geolocate() {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition((position) => {
          this.marker.position = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
        });
      } else {
        this.showErrorMessage("Location is not supported");
      }
    },
    saveDoubleLocation(item) {
      this.geolocate();
      axios
        .post(`/api/save-double-location`, {
          id: item.id,
          ll: this.marker.position.lat,
          lg: this.marker.position.lng,

        })
        .then((response) => {
          this.flash("Double Approved Successfully");
          this.reload(this.widget.id)
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    samplesStatus(item, action) {
      if (action.transaction == 'approve') {
        axios
          .post(`/api/change-samples-approval`, {
            id: item.id
          })
          .then((response) => {
            this.flash("Samples Approved Successfully");
            this.reload(this.widget.id)
          })
          .catch((error) => {
            this.showErrorMessage(error);
          });
      } else {
        this.$root.$samples("Edit Sample Quantity", item);
      }
    },
    saveSamples() {

    },

  },
  watch: {
    widget: {
      deep: true,
      immediate: true,
      handler(newVal, oldVal) {
        if (newVal.data.length > 0)
          this.setData(newVal.data);

      }
    }
  }
};
</script>
