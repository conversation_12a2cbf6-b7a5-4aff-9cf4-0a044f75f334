<template>
  <!-- <div> -->
  <CCarousel arrows indicators animate :interval="2000" height="450px">
    <div v-for="u in url" :key="index">
      <CCarouselItem id="leadImg" height="400px" :image="u" />
    </div>
  </CCarousel>
  <!-- <c-card> -->
  <!-- <c-card-body id="card-body"> -->
  <!-- <v-img :src="url" mx-auto contain id="leadImg" alt="Leaderboards Image"></v-img> -->
  <!-- </c-card-body> -->
  <!-- </c-card> -->

  <!-- </div> -->


</template>

<script>

export default {
  props: {
    widget: {
      type: Object,
      required: true,
    },
  },
  computed: {
    url() {
      return this.widget.data ?? [];
    }
  }
};
</script>

<style scoped>
#leadImg {
  width: 100%;
  height: 450px;
}
</style>
