<script>
import _ from "../../mixins/debounce";

export default {
  props: {
    widget: {
      type: Object,
      required: true,
    },
  },
  computed: {
    totalData() {
      return this.widgetData.total
    },
    pages() {
      return this.widgetData.links.length
    }
  },
  data() {
    return {
      search: '',
      hoveredIndex: null,
      page: 1,
      widgetData: {}
    };
  },
  methods: {
    // find: _.debounce((vm) => {
    //   vm.getData().then(() => loading(false))
    // }, 500),
    onSearch(){
      this.find(this)
    },
    find: _.debounce((vm) => {
      vm.getData()
    }, 500),
    setHovered(index, isHovered) {
      this.hoveredIndex = isHovered ? index : null;
    },
    getData() {
      let link = `${this.widget.data.path}?page=${this.page}`;
      if (this.search) {
        link += `&query=${this.search}`
      }
      axios.get(link)
        .then(res => {
          this.widgetData = res.data.data.data
        })
        .catch(this.showErrorMessage)
    }
  },
  created() {
    this.widgetData = this.widget.data
  }
}
;
</script>

<template>
  <div>
    <template>
      <label class="mfe-2">Filter: </label>
      <c-input
        v-model="search"
        placeholder="type string..."
        type="text"
        @input="onSearch"
      />
      <td style="border-top: none"><strong>Total:</strong></td>
      <td style="border-top: none" class="text-xs-right">
        {{ totalData }}
      </td>
    </template>
    <div
      class="alertWidget d-flex justify-content-around flex-row flex-fill flex-wrap overflow-auto w-100 border border-blue-900">
      <a class="btn col-12 col-lg-3 m-3 d-flex justify-content-around flex-wrap"
         v-for="(card, index) in widgetData.data" :key="index"
         @mouseover="setHovered(index, true)"
         @mouseout="setHovered(index, false)" :class="{ 'hovered': hoveredIndex === index }"
      >
        <!--         v-c-tooltip="{-->
        <!--              content: `<b>Emp_code: </b> ${ card?.emp_code ? card.emp_code : ''} <br> <b>Line: </b> ${ card.lines } <br> <b>Division: </b> <br> ${ card.divisions }`,-->
        <!--              html: true,-->
        <!--              placement: 'down'-->
        <!--        }"-->
        <img class="card__thumb" :src="card.url" alt=""/>

        <div class="d-flex justify-content-center flex-column justify-end">
          <p class="badge rounded-pill names">{{ card.line }} - {{ card.division.substring(0, 10) + ".." }}</p>
          <p class="badge rounded-pill names">{{ card.fullname }}</p>
          <!-- <p class="badge rounded-pill roles">{{ card.role }}</p> -->
        </div>
      </a>
    </div>
    <c-pagination
      :pages="pages"
      :activePage.sync="page"
      @update:activePage="getData"
    />
  </div>
</template>

<style scoped>
.alertWidget {
  height: 300px;
  width: 100%;
}

* {
  box-sizing: border-box;
}

body {
  font-family: 'Noto Sans JP', sans-serif;
}

a {
  width: 15% !important;
  height: 20% !important;
}

p {
  font-size: 0.65rem;
  color: #eaa206;
  position: absolute;
  margin-bottom: 3px;
  margin-left: 45px;
}

.names {
  font-size: 0.75rem;
}

.card {
  position: relative;
  display: inline-block;
  height: 22%;
  width: 20%;
  margin: 2%;
  margin-top: 0.2%;
  border-radius: 50px !important;
  overflow: hidden;
  text-decoration: none;
}

.card__thumb {
  position: absolute;
  left: 5%;
  bottom: 10%;
  flex-shrink: 0;
  width: 44px;
  height: 44px;
  border-radius: 5%;
}
</style>
