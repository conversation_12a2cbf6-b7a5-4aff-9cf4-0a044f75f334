<template>
  <c-card>
    <c-card-header>Employees for Approvals</c-card-header>
    <c-card-body>
      <div>
        <c-data-table
          hover
          striped
          sorter
          tableFilter
          footer
          :itemsPerPageSelect="perPageSelect"
          :items="items"
          :fields="fields"
          :items-per-page="perPage"
          :active-page="1"
          :responsive="true"
          :table-filter="{ external: true, lazy: true }"
          pagination
          thead-top
        >
        </c-data-table>
      </div>
    </c-card-body>
  </c-card>
</template>
<script>
export default {
  data() {
    return {
      perPageSelect: {
        values: ["5", "10"],
        external: true,
      },
      items: [],
      lines:[],
      line_id: '',
      fields: ["fullname"],
      currentPage: 1,
      perPage: 5,
    };
  },
  methods: {
    // initialize() {
    //   axios
    //     .get("/api/planapprovals")
    //     .then((response) => {
    //       this.lines = response.data.lines;
    //     })
    //     .catch((error) => {
    //       this.showErrorMessage(error);
    //       this.flash("Oooops something wrong", "error");
    //     });
    // },
    getLineEmployees() {
      axios
        .get(
          `/api/filterofemployees/ ${this.line_id}`
        )
        .then((response) => {
          this.users = response.data.filtered_users;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    //this.initialize();
    this.getLineEmployees();
  },
};
</script>
