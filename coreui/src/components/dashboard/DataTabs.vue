<template>
  <c-card>
    <c-card-body>
      <c-tabs>
        <c-tab v-for="(items, index) in arrays" :key="index">
          <template slot="title">
            <c-icon :name="icons[index]" class="custom_icon" />
            {{ titles[index] }}
            <span class="badge rounded-pill bg-danger text-white">{{
              total[index]
            }}</span>
          </template>
          <c-data-table hover striped sorter footer :items="items" :fields="fields[index]" itemsPerPageSelect
            :items-per-page="5" :active-page="1" :responsive="true" table-filter pagination>
            <template slot="thead-top">
              <td style="border-top: none"><strong>Total</strong></td>
              <td style="border-top: none" class="text-xs-right">
                {{ total[index] }}
              </td>
            </template>
            <template #account="{ item }">
              <td>
                <strong :style="{
                  color: item.color,
                }">{{ item.account }}</strong>
              </td>
            </template>
            <template #doctor="{ item }">
              <td>
                <strong :style="{
                  color: item.color,
                }">{{ item.doctor }}</strong>
              </td>
            </template>
            <template #number_of_vacations="{ item }">
              <td style="text-align: center">
                <c-badge class="text-white" style="cursor: pointer; font-weight: bold; font-size: 15px" color="primary"
                  @click="getVacations(item)">
                  {{ item.number_of_vacations }}
                </c-badge>
              </td>
            </template>

            <template #number_of_visits="{ item }">
              <td style="text-align: center">
                <c-badge class="text-white" style="cursor: pointer; font-weight: bold; font-size: 15px" color="primary"
                  @click="getVisits(item)">
                  {{ item.number_of_visits }}
                </c-badge>
              </td>
            </template>
            <template #number_of_ow="{ item }">
              <td style="text-align: center">
                <c-badge class="text-white" style="cursor: pointer; font-weight: bold; font-size: 15px" color="primary"
                  @click="getOw(item)">
                  {{ item.number_of_ow }}
                </c-badge>
              </td>
            </template>
            <template #number_of_actuals="{ item }">
              <td style="text-align: center">
                <c-badge class="text-white" style="cursor: pointer; font-weight: bold; font-size: 15px" color="primary"
                  @click="getActuals(item)">
                  {{ item.number_of_actuals }}
                </c-badge>
              </td>
            </template>
            <template #number_of_commercials="{ item }">
              <td style="text-align: center">
                <c-badge class="text-white" style="cursor: pointer; font-weight: bold; font-size: 15px" color="primary"
                  @click="getCommercials(item)">
                  {{ item.number_of_commercials }}
                </c-badge>
              </td>
            </template>
            <template #number_of_expenses="{ item }">
              <td style="text-align: center">
                <c-badge class="text-white" style="cursor: pointer; font-weight: bold; font-size: 15px" color="primary"
                  @click="getExpenses(item)">
                  {{ item.number_of_expenses }}
                </c-badge>
              </td>
            </template>


            <template #actions="{ item }">
              <td>
                <div class="row justify-content-center">
                  <c-button class="px-1 m-0 m-1 btn-sm" v-for="(action, key) in item.actions" :key="key"
                    v-show="action.visible == true" :color="action.color" :to="goToPerformAction(action)" @click="show">
                    <c-icon :name="actionIcons[key]" class="text-white" />
                  </c-button>
                </div>
              </td>
            </template>

            <template #action="{ item }">
              <td>
                <!-- <div class="row justify-content-center"> -->
                <c-button class="px-1 m-0 m-1 btn-sm" v-if="item.action == 1" color="success" :to="{
                  name: 'CreatePlanActualVisit',
                  params: { id: item.id },
                }">
                  <c-icon name="cibAngular" class="text-white" />
                </c-button>
                <c-button class="px-1 m-0 m-1 btn-sm" v-if="item.action == 0" color="danger">
                  <c-icon name="cib-experts-exchange" class="text-white" />
                </c-button>
                <c-button class="px-1 m-0 m-1 btn-sm" v-if="item.action == null" color="primary">
                  <c-icon name="cibCodacy" class="text-white" />
                </c-button>
                <c-button class="px-1 m-0 m-1 btn-sm" v-if="item.action == 1" color="primary"
                  @click="getAccountLocation(item)">
                  <c-icon class="text-white" name="marker" />
                </c-button>
                <c-button class="px-1 m-0 m-1 btn-sm" v-if="item.action == 1" color="warning" @click="
                  $root.$clarification(`Plan id: ${item.id}`, [
                    item,
                  ], widget, reload)
                  ">
                  <c-icon class="text-white" name="cil-description" />
                </c-button>
                <!-- </div> -->
              </td>
            </template>
          </c-data-table>
        </c-tab>
      </c-tabs>
    </c-card-body>
  </c-card>
</template>

<!-- :disabled="action.transaction === ''" -->

<script>
export default {
  props: {
    widget: {
      type: Object,
      required: true,
    },
  },
  inject: ['reload'],
  data() {
    return {
      arrays: [],
      fields: [],
      visits: [],
      location: null,
      total: [],
      planFieldsData: [],
      actualFields: [
        "s",
        "user",
        "line",
        "division",
        "account",
        "doctor",
        "type",
        "date",
        "a",
        "f",
      ],
      owFields: ["s", "user", "office_work", "shift", "day", "actions"],
      vacationFields: [
        "s",
        "user",
        "type",
        "from",
        "to",
        "shift",
        "approvals",
        "actions",
      ],
      titles: [],
      flag: 1,
      actionIcons: [
        "cibAngular",
        "cibAngular",
        "cibCodacy",
        "cib-experts-exchange",
      ],
      icons: [
        "cil-braille",
        "cil-task",
        "cil-description",
        "cil-description",
        "cil-description",
        "cil-description",
      ],
    };
  },
  methods: {
    goToPerformAction(action) {
      if (action.route) {
        if (action.transaction == "actualPlan")
          return {
            name: action.route.name,
            params: { id: action.route.params.id },
          };
        if (action.transaction == "actualOw")
          return {
            name: action.route.name,
            params: { id: action.route.params.id },
          };
      }
      return null;
    },
    getAccountLocation(item) {
      axios
        .post("/api/get-account-location", {
          id: item.account_id,
          line_id: item.line_id,
          div_id: item.div_id,
        })
        .then((response) => {
          this.location = response.data.data;
          if (this.location.lat != 0 && this.location.lng != 0) {
            this.$root.$map(`Map of Account id: ${this.location.account_id}`, [
              this.location,
            ]);
          } else {
            this.flash("There is no loaction for this account");
          }
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getVisits(item) {
      axios
        .post(`/api/getPlanVisitsForApproval`, {
          user: item.id,
          line: item.line,
          flag: this.flag,
        })
        .then((response) => {
          const visits = response.data.plans;
          const planFields = response.data.planFields;
          this.$root.$plan("Visit Approvals", visits, planFields, item, this.widget, this.reload);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getActuals(item) {
      axios
        .post(`/api/getActualVisitsForApproval`, {
          user: item.id,
          line: item.line,
          flag: this.flag,
        })
        .then((response) => {
          const visits = response.data.data;
          this.$root.$actual(
            "Actual Approvals",
            visits,
            this.actualFields,
            item,
            this.widget, this.reload
          );
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getOw(item) {
      axios
        .post(`/api/getPlanVisitsForApproval`, {
          user: item.id,
          line: item.line,
          flag: this.flag,
        })
        .then((response) => {
          const ow = response.data.ow;
          this.$root.$plan("Ow Approvals", ow, this.owFields, item);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getVacations(item) {
      axios
        .post(`/api/get-vacations`, {
          user: item.id,
          line: item.line,
          flag: this.flag,
        })
        .then((response) => {
          const vacations = response.data.data;
          this.$root.$vacation(
            "Vacation Approvals",
            vacations,
            this.vacationFields,
            item
          );
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getCommercials(item) {
      axios
        .post(`/api/get-commercials`, {
          user: item.id,
          line: item.line,
          flag: this.flag,
        })
        .then((response) => {
          const commercials = response.data.data.commercials;
          const commercialFields = response.data.data.commercialFields;
          this.$root.$commercial(
            "Commercial Approvals",
            commercials,
            commercialFields,
            item
          );
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getExpenses(item) {
      axios
        .post(`/api/get-expenses`, {
          user: item.id,
          line: item.line,
          flag: this.flag,
        })
        .then((response) => {
          const expenses = response.data.data.expenses;
          const expenseFields = response.data.data.fields;
          this.$root.$expense(
            "Expense Approvals",
            expenses,
            expenseFields,
            item
          );
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    show() {
      console.log("item is clicked");
    },
    reset() {
      this.arrays = [];
      this.fields = [];
      this.total = [];
      this.titles = [];
    },
    getArrays(object) {
      let keys = Object.keys(object);
      if (typeof object != "object") return;
      for (const key of keys) {
        if (Array.isArray(object[key])) {
          const item = object[key];
          if (item.length != 0) {
            this.arrays.push(item);
            let fields = [];

            fields = Object.keys(item[0]).filter((f) => f !== "color" && f !== "account_id" && f !== "div_id" && f !== "line_id");


            this.fields.push(fields);
          } else {
            this.arrays.push([]);
            this.fields.push([]);
          }
          this.total.push(item.length);
          this.titles.push(key);
        } else if (typeof object[key] === "object") {
          this.getArrays(object[key]);
        }

      }
    },
  },
  watch: {
    widget: {
      deep: true,
      immediate: true,
      handler(newVal, oldVal) {
        if (newVal.data.length > 0) {
          this.reset();
          this.getArrays(newVal.data);
        }

      }
    }
  }
};
</script>
