<script>
import ImageWidget from "./ImageWidget.vue";

export default {
  props: {
    widget: {
      type: Object,
      required: true,
    },
  },
  components: {
    ImageWidget,
  },
  data() {
    return {
      arrays: [],
      fields: [],
      url: null,
      alert: [],
      alertWidgetId: '',
      alertId: '',
      page: 1,
      total: 0,
      totalData: 0,
      search: null,
    };
  },
  methods: {
    getArrays(object) {
      if (object.url) {
        let keys = Object.keys(object);
        if (typeof object != "object") return;
        for (const key of keys) {
          if (Array.isArray(object[key])) {
            const item = object[key];
            if (item.length !== 0) {
              this.arrays.push(item);
              this.fields.push(Object.keys(item[0]));
              this.url = this.arrays[0][0];
            } else {
              this.arrays.push([]);
              this.fields.push([]);
            }
          } else if (typeof object[key] === "object") {
            this.getArrays(object[key]);
          }
        }

      }

    },
    getData() {
      this.alertWidgetId = this.widget.id;
      axios
        .post("/api/widgets/" + this.alertWidgetId + "?page=" + this.page, {
          query: this.search,
        })
        .then((res) => {
          this.alert = res.data.data.data.data
          this.alertId = res.data.data.data.data[0].alert_id

        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
    find(search) {
      axios.post('/api/searchalert/index?page=' + this.page, {
        search: this.search,
        widget_id: this.alertWidgetId,
        alert_id: this.alertId
      }).then((response) => {
        this.alert = response.data.data.data;
      })
    }
  },
  created() {
    if (typeof this.widget.data.data === 'object' && Array.isArray(this.widget.data.data)) {
      this.alertWidgetId = this.widget.id
      this.alertId = this.widget.data.data[0].alert_id
      this.alert = this.widget.data.data
      this.total = this.widget.data.last_page;
      this.totalData = this.widget.data.total;

      this.getArrays(this.widget.data);
    }

  }
};
</script>

<template>
  <div>
    <template>
      <label class="mfe-2">Filter: </label>
      <c-input
        v-model="search"
        placeholder="type string..."
        type="text"
        @input="find(search)"
      />
      <td style="border-top: none"><strong>Total:</strong></td>
      <td style="border-top: none" class="text-xs-right">
        {{ totalData }}
      </td>
    </template>
    <image-widget
      class="alertWidget d-flex justify-content-around flex-row flex-fill flex-wrap overflow-auto"
      :active-page="1"
      :alertData="alert"
    >
    </image-widget>
    <c-pagination
      v-if="alert.length !==0"
      :pages="total"
      :activePage.sync="page"
      @update:activePage="getData()"
    />
  </div>
</template>

<style scoped>
.alertWidget {
  height: 300px;
  width: 50%;
}
</style>
