<template>
  <c-card no-header>
    <c-card-body>
      <div class="row">
        <div class="col-3">
          <c-input
            label="Name"
            type="text"
            placeholder="Name"
            v-model="line.name"
          ></c-input>
        </div>
        <div class="col-3">
          <c-input
            label="Sort"
            type="number"
            placeholder="Sort"
            v-model="line.sort"
          ></c-input>
        </div>
        <div class="col-3">
          <c-form-group>
            <template #label> Country </template>
            <template #input>
              <v-select
                v-model="line.country_id"
                :options="countries"
                label="name"
                :value="0"
                :reduce="(country) => country.id"
                placeholder="Select option"
                class="mt-2"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-3">
          <c-form-group>
            <template #label> Timezone </template>
            <template #input>
              <v-select
                v-model="line.timezone"
                :options="timezones"
                :value="0"
                placeholder="Select Timezone"
                class="mt-2"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-4">
          <c-form-group>
            <template #label> Currency </template>
            <template #input>
              <v-select
                v-model="line.currency_id"
                :options="currencies"
                label="name"
                :value="0"
                :reduce="(currency) => currency.id"
                placeholder="Select Currency"
                class="mt-2"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-4">
          <c-form-group>
            <template #label>
              <strong>From Date</strong>
            </template>
            <template #input>
              <c-input
                type="date"
                class="mt-2"
                placeholder="From Date"
                v-model="line.from_date"
              ></c-input>
            </template>
          </c-form-group>
        </div>
        <div class="col-4">
          <c-form-group>
            <template #label>
              <strong>To Date</strong>
            </template>
            <template #input>
              <c-input
                type="date"
                class="mt-2"
                placeholder="To Date"
                v-model="line.to_date"
              ></c-input>
            </template>
          </c-form-group>
        </div>
      </div>

      <div class="row">
        <div class="col">
          <c-textarea
            label="Notes"
            type="text"
            placeholder="Notes"
            v-model="line.notes"
          ></c-textarea>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" @click="update">Update</c-button>
      <c-button color="default" :to="{ name: 'lines' }">Cancel</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      line_id: null,
      fromDate: new Date().toISOString().slice(0, 10),
      line: {
        name: "",
        timezone: "",
        notes: "",
        sort: "",
        from_date: "",
        to_date: "",
        country_id: 0,
        currency_id: 0,
      },
      timezones: [],
      currencies: [],
      countries: [],
    };
  },
  emits: ["getLine"],
  methods: {
    edit_date_format: function (value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    initialize() {
      axios
        .get(`/api/lines/${this.$route.params.id}/edit`)
        .then((response) => {
          this.line = response.data.line;
          this.line.from_date = this.edit_date_format(
            response.data.line.from_date
          );
          this.line.to_date = this.edit_date_format(
            response.data.line.to_date
          );
          this.line_id = response.data.line.id;
          this.timezone = response.data.line.timezone;
          this.currencies = response.data.currencies;
          this.timezones = response.data.timezones;
          this.countries = response.data.countries;
          this.$emit("getLine", { line: this.line_id });
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    update() {
      axios
        .put(`/api/lines/${this.$route.params.id}`, {
          name: this.line.name,
          timezone: this.line.timezone,
          notes: this.line.notes,
          sort: this.line.sort,
          currency_id: this.line.currency_id,
          country_id: this.line.country_id,
          from_date: this.crmDateFormat(this.line.from_date),
          to_date: this.line.to_date? this.crmDateFormat(this.line.to_date):"",
        })
        .then((response) => {
          this.flash("Line is updated successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
  },
};
</script>

<style>
</style>