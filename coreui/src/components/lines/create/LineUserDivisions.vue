<template>
  <div>
    <CCard no-header>
      <CCardBody>
        <div class="row">
          <div class="col">
            <CFormGroup>
              <template #label> Line User </template>
              <template #input>
                <v-select
                  v-model="line_division_user.user_id"
                  :options="lineusers"
                  label="name"
                  :value="0"
                  :reduce="(user) => user.id"
                  placeholder="Select User"
                  class="mt-2"
                />
              </template>
            </CFormGroup>
          </div>
          <div class="col" v-if="!lineUserDivisionIsEditing">
            <CFormGroup>
              <template #label> Line Division </template>
              <template #input>
                <v-select
                  v-model="line_division_user.line_division_id"
                  :options="divisionparents"
                  label="name"
                  :value="0"
                  :reduce="(divisionparent) => divisionparent.id"
                  placeholder="Select Division"
                  multiple
                  class="mt-2"
                />
              </template>
            </CFormGroup>
          </div>
          <div class="col" v-else>
            <CFormGroup>
              <template #label> Line Division </template>
              <template #input>
                <v-select
                  v-model="line_division_user.line_division_id"
                  :options="divisionparents"
                  label="name"
                  :value="0"
                  :reduce="(divisionparent) => divisionparent.id"
                  placeholder="Select Division"
                  class="mt-2"
                />
              </template>
            </CFormGroup>
          </div>
        </div>

        <div class="row">
          <div class="col">
            <CInput
              label="From"
              type="date"
              placeholder="From"
              v-model="line_division_user.from_date"
            ></CInput>
          </div>
          <div class="col">
            <CInput
              label="To"
              type="date"
              placeholder="To"
              v-model="line_division_user.to_date"
            ></CInput>
          </div>
        </div>
      </CCardBody>
      <CCardFooter>
        <CButton
          color="primary"
          v-if="!lineUserDivisionIsEditing"
          @click="store"
          >Create</CButton
        >
        <CButton color="primary" v-else @click="update">Update</CButton>
        <CButton color="default" :to="{ name: 'lines' }">Cancel</CButton>
      </CCardFooter>
    </CCard>

    <CDataTable
      hover
      striped
      sorter
      tableFilter
      footer
      itemsPerPageSelect
      :items="line_division_users"
      :fields="line_division_users_fields"
      :items-per-page="1000"
      :active-page="1"
      :responsive="true"
      pagination
      thead-top
    >
      <template #from_date="{ item }">
        <td>{{ format_date(item.from_date) }}</td>
      </template>
      <template #to_date="{ item }">
        <td>{{ format_date(item.to_date) }}</td>
      </template>
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ line_division_users.length }}
        </td>
      </template>
      <template #actions="{ item }">
        <td>
          <div class="row justify-content-center">
            <CButton
              color="success"
              v-if="checkPermission('edit_line_user_divisions')"
              class="text-white btn-sm mt-2 mr-1"
              @click="edit(item.id)"
              ><i class="cil-pencil"></i><CIcon name="cil-pencil"
            /></CButton>
            <c-button
              color="danger"
              v-if="checkPermission('delete_line_user_divisions')"
              class="btn-sm mt-2 mr-1"
              @click="
                $root
                  .$confirm('Delete', 'Do you want to delete this record?', {
                    color: 'red',
                    width: 290,
                    zIndex: 200,
                  })
                  .then((confirmed) => {
                    if (confirmed) {
                      deleteLineUserDivision(item);
                    }
                  })
              "
              ><c-icon name="cil-trash"
            /></c-button>
          </div>
        </td>
      </template>
    </CDataTable>
  </div>
</template>
<script>
import moment from "moment";

import vSelect from "vue-select";
export default {
  components: {
    vSelect,
  },
  props: {
    line_id: {
      type: Number,
      required: true,
    },
    divisionparents: {
      type: Array,
      required: true,
    },
    lineusers: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      line_division_users: [],
      lineUserDivisionIsEditing: false,
      line_division_users_fields: [
        "id",
        "user_name",
        "line_division_name",
        "from_date",
        "to_date",
        "actions",
      ],

      line_division_user: {
        id: 0,
        line_id: 0,
        user_id: "",
        line_division_id: "",
        from_date: "",
        to_date: "",
      },
      fromDate: new Date().toISOString().slice(0, 10),
    };
  },
  methods: {
    format_date: function (value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    edit_date_format: function (value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    getLineDivisionUsers() {
      axios
        .get(`/api/get_line_division_users/${this.line_id}`)
        .then((response) => {
          this.line_division_users = response.data.line_division_users;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    reset() {
      this.line_division_user = {
        user_id: "",
        line_id: this.line_id,
        line_division_id: "",
        from_date: "",
        to_date: "",
      };
    },
    store() {
      axios
        .post(`/api/lines/${this.line_id}/user/divisions`, {
          user_id: this.line_division_user.user_id,
          line_id: this.line_id,
          line_division_id: this.line_division_user.line_division_id,
          from_date: this.crmDateFormat(this.line_division_user.from_date),
          to_date: this.line_division_user.to_date
            ? this.crmDateFormat(this.line_division_user.to_date)
            : "",
        })
        .then((response) => {
          this.reset();
          this.flash("Line User Division Created Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      this.getLineDivisionUsers();
    },
    edit(id) {
      this.lineUserDivisionIsEditing = true;
      this.scrollToTop();
      axios
        .get(`/api/line-user-divisions/${id}`)
        .then((response) => {
          setTimeout(() => {
            this.line_division_user.id = response.data.line_division_user.id;
            this.line_division_user.line_id =
              response.data.line_division_user.line_id;
            this.line_division_user.line_division_id =
              response.data.line_division_user.line_division_id;
            this.line_division_user.user_id =
              response.data.line_division_user.user_id;
            this.line_division_user.from_date = this.edit_date_format(
              response.data.line_division_user.from_date
            );
            this.line_division_user.to_date = this.edit_date_format(
              response.data.line_division_user.to_date
            );
          }, 1000);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    update() {
      axios
        .put(
          `/api/lines/${this.line_id}/user/division/${this.line_division_user.id}`,
          {
            id: this.line_division_user.id,
            line_id: this.line_division_user.line_id,
            line_division_id: this.line_division_user.line_division_id,
            user_id: this.line_division_user.user_id,
            from_date: this.crmDateFormat(this.line_division_user.from_date),
            to_date: this.line_division_user.to_date
              ? this.crmDateFormat(this.line_division_user.to_date)
              : "",
          }
        )
        .then((response) => {
          this.lineUserDivisionIsEditing = false;
          this.flash("Line User Division Updated Successfully");
          this.getLineDivisionUsers();
          this.reset();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removeLineUserDivision(item) {
      const index = this.line_division_users.findIndex(
        (type) => item.id == type.id
      );
      this.line_division_users.splice(index, 1);
    },
    deleteLineUserDivision(item) {
      axios
        .delete(`/api/line-user-divisions/${item.id}`)
        .then((res) => {
          this.removeLineUserDivision(item);
          this.flash("Line User Division Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
    this.getLineDivisionUsers();
  },
};
</script>
