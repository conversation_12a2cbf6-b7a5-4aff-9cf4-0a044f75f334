<template>
  <div>
    <CCard no-header>
      <CCardBody>
        <div class="row">
          <div class="col">
            <CFormGroup v-if="!parentIsEditing">
              <template #label> Line Division </template>
              <template #input>
                <v-select
                  v-model="line_div_parent.line_div_id"
                  :options="divisionparents"
                  label="name"
                  :value="0"
                  :reduce="(divisionparent) => divisionparent.id"
                  placeholder="Select Division"
                  class="mt-2"
                  multiple
                  @input="changeParents"
                />
              </template>
            </CFormGroup>
            <CFormGroup v-if="parentIsEditing">
              <template #label> Line Division </template>
              <template #input>
                <v-select
                  v-model="line_div_parent.line_div_id"
                  :options="divisionparents"
                  label="name"
                  :value="0"
                  :reduce="(divisionparent) => divisionparent.id"
                  placeholder="Select Parent"
                  class="mt-2"
                />
              </template>
            </CFormGroup>
          </div>
          <div class="col">
            <CFormGroup>
              <template #label> Parent </template>
              <template #input>
                <v-select
                  v-model="line_div_parent.parent_id"
                  :options="parents"
                  label="name"
                  :value="0"
                  :reduce="(parent) => parent.id"
                  placeholder="Select Parent"
                  class="mt-2"
                />
              </template>
            </CFormGroup>
          </div>
        </div>

        <div class="row">
          <div class="col">
            <CInput
              label="From"
              type="date"
              placeholder="From"
              v-model="line_div_parent.from_date"
            ></CInput>
          </div>
          <div class="col">
            <CInput
              label="To"
              type="date"
              placeholder="To"
              v-model="line_div_parent.to_date"
            ></CInput>
          </div>
        </div>
      </CCardBody>
      <CCardFooter>
        <CButton color="primary" v-if="!parentIsEditing" @click="store()"
          >Create</CButton
        >
        <CButton color="primary" v-if="parentIsEditing" @click="update()"
          >Update</CButton
        >
        <CButton color="default" :to="{ name: 'lines' }">Cancel</CButton>
      </CCardFooter>
    </CCard>

    <CDataTable
      hover
      striped
      sorter
      tableFilter
      footer
      itemsPerPageSelect
      :items="line_div_parents"
      :fields="line_div_parents_fields"
      :items-per-page="100"
      :active-page="1"
      :responsive="true"
      pagination
      thead-top
    >
      <template #from_date="{ item }">
        <td>{{ format_date(item.from_date) }}</td>
      </template>
      <template #to_date="{ item }">
        <td>{{ format_date(item.to_date) }}</td>
      </template>
      <template #parent_name="{ item }">
        <td v-if="item.parent_name">{{ item.parent_name }}</td>
        <td v-else-if="!item.parent_name">-No Parent-</td>
      </template>
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ line_div_parents.length }}
        </td>
      </template>
      <template #actions="{ item }">
        <td>
          <div class="row justify-content-center">
            <CButton
              color="success"
              v-if="checkPermission('edit_line_div_parents')"
              class="text-white btn-sm mt-2 mr-1"
              @click="edit(item.id)"
              ><i class="cil-pencil"></i><CIcon name="cil-pencil"
            /></CButton>
            <c-button
              color="danger"
              v-if="checkPermission('delete_line_div_parents')"
              class="btn-sm mt-2 mr-1"
              @click="
                $root
                  .$confirm('Delete', 'Do you want to delete this record?', {
                    color: 'red',
                    width: 290,
                    zIndex: 200,
                  })
                  .then((confirmed) => {
                    if (confirmed) {
                      deleteLineDivision(item);
                    }
                  })
              "
              ><c-icon name="cil-trash"
            /></c-button>
          </div>
        </td>
      </template>
    </CDataTable>
  </div>
</template>
<script>
import moment from "moment";

import vSelect from "vue-select";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      fromDate: new Date().toISOString().slice(0, 10),
      parentIsEditing: false,
      line_div_parents: [],
      line_div_parent: {
        id: null,
        line_div_id: null,
        parent_id: "",
        from_date: "",
        to_date: "",
      },
      line_div_parents_fields: [
        "id",
        "line_division_name",
        "parent_name",
        "from_date",
        "to_date",
        "actions",
      ],
      line_divisions: [],
      parents: [],
    };
  },
  props: {
    divisionparents: {
      type: Array,
      required: true,
    },
    line_id: {
      type: Number,
      required: true,
    },
  },
  methods: {
    format_date: function (value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    edit_date_format: function (value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    getLineDivisions() {
      axios
        .get(`/api/lines/${this.line_id}/divisions`)
        .then((response) => {
          this.line_divisions = response.data.line_divisions;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getParents() {
      axios
        .get(`/api/divisions-parent/${this.line_id}`)
        .then((response) => {
          this.line_div_parents = response.data.line_div_parents;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    store() {
      axios
        .post(`/api/line/${this.line_id}/divisions/parent`, {
          line_id: this.line_id,
          line_div_id: this.line_div_parent.line_div_id,
          parent_id: this.line_div_parent.parent_id,
          from_date: this.crmDateFormat(this.line_div_parent.from_date),
          to_date: this.line_div_parent.to_date
            ? this.crmDateFormat(this.line_div_parent.to_date)
            : "",
        })
        .then((response) => {
          this.line_div_parent = {
            line_div_id: "",
            parent_id: "",
            from_date: "",
            to_date: "",
          };
          this.flash("Line Division Parent Created Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      //Refresh table
      this.getParents();
      this.getLineDivisions();
    },
    edit(id) {
      this.parentIsEditing = true;
      this.scrollToTop();
      axios
        .get(`/api/division-parent/${id}`)
        .then((response) => {
          this.line_div_parent.id = response.data.line_div_parent.id;
          this.line_div_parent.line_div_id =
            response.data.line_div_parent.line_div_id;
          this.changeParents();
          this.line_div_parent.parent_id =
            response.data.line_div_parent.parent_id;
          this.line_div_parent.from_date = this.edit_date_format(
            response.data.line_div_parent.from_date
          );
          this.line_div_parent.to_date = this.edit_date_format(
            response.data.line_div_parent.to_date
          );
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    update() {
      axios
        .put(
          `/api/line/${this.line_id}/divisions/parent/${this.line_div_parent.id}`,
          {
            id: this.line_div_parent.id,
            line_id: this.line_id,
            line_div_id: this.line_div_parent.line_div_id,
            parent_id: this.line_div_parent.parent_id,
            from_date: this.crmDateFormat(this.line_div_parent.from_date),
            to_date: this.line_div_parent.to_date
              ? this.crmDateFormat(this.line_div_parent.to_date)
              : "",
          }
        )
        .then((response) => {
          this.parentIsEditing = false;
          this.flash("Line Division Parent Updated Successfully");
          this.getParents();
          this.line_div_parent = {
            line_div_id: "",
            parent_id: "",
            from_date: "",
            to_date: "",
          };
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    changeParents() {
      axios
        .get(`/api/line/divisions/${this.line_div_parent.line_div_id}/parent`)
        .then((response) => {
          this.parents = response.data.parents;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removeLineDivisionParent(item) {
      const index = this.line_div_parents.findIndex(
        (type) => item.id == type.id
      );
      this.line_div_parents.splice(index, 1);
    },
    deleteLineDivision(item) {
      axios
        .delete(`/api/division-parent/${item.id}`)
        .then((res) => {
          this.removeLineDivisionParent(item);
          this.flash("Line Division Parent Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
    this.getParents();
    this.getLineDivisions();
  },
};
</script>
