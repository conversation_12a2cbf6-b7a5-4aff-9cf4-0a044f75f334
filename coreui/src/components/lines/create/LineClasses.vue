<template>
  <div>
    <CCard no-header>
      <CCardBody>
        <div class="row">
          <div class="col">
            <CFormGroup v-if="!lineClassIsEditing">
              <template #label> Class </template>
              <template #input>
                <v-select
                  v-model="line_classe.classe_id"
                  :options="classes"
                  label="name"
                  :value="0"
                  :reduce="(classe) => classe.id"
                  placeholder="Select option"
                  multiple
                  class="mt-2"
                />
              </template>
            </CFormGroup>
            <CFormGroup v-if="lineClassIsEditing">
              <template #label> Class </template>
              <template #input>
                <v-select
                  v-model="line_classe.classe_id"
                  :options="classes"
                  label="name"
                  :value="0"
                  :reduce="(classe) => classe.id"
                  placeholder="Select option"
                  class="mt-2"
                />
              </template>
            </CFormGroup>
          </div>
          <div class="col">
            <CInput
              label="From"
              type="date"
              placeholder="From"
              v-model="line_classe.from_date"
            ></CInput>
          </div>
          <div class="col">
            <CInput
              label="To"
              type="date"
              placeholder="To"
              v-model="line_classe.to_date"
            ></CInput>
          </div>
        </div>
      </CCardBody>
      <CCardFooter>
        <CButton color="primary" v-if="!lineClassIsEditing" @click="store"
          >Create</CButton
        >
        <CButton color="primary" v-if="lineClassIsEditing" @click="update"
          >Update</CButton
        >
        <CButton color="default" :to="{ name: 'lines' }">Cancel</CButton>
      </CCardFooter>
    </CCard>
    <CDataTable
      hover
      striped
      sorter
      tableFilter
      footer
      itemsPerPageSelect
      :items="line_classes"
      :fields="line_classes_fields"
      :items-per-page="100"
      :active-page="1"
      :responsive="true"
      pagination
      thead-top
    >
      <template #from_date="{ item }">
        <td>{{ format_date(item.from_date) }}</td>
      </template>
      <template #to_date="{ item }">
        <td>{{ format_date(item.to_date) }}</td>
      </template>
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ line_classes.length }}
        </td>
      </template>
      <template #actions="{ item }">
        <td>
          <div class="row justify-content-center">
            <CButton
              color="success"
              v-if="checkPermission('edit_line_classes')"
              class="text-white btn-sm mt-2 mr-1"
              @click="edit(item.id)"
              ><i class="cil-pencil"></i><CIcon name="cil-pencil"
            /></CButton>
            <c-button
              color="danger"
              v-if="checkPermission('delete_line_classes')"
              class="btn-sm mt-2 mr-1"
              @click="
                $root
                  .$confirm('Delete', 'Do you want to delete this record?', {
                    color: 'red',
                    width: 290,
                    zIndex: 200,
                  })
                  .then((confirmed) => {
                    if (confirmed) {
                      deleteLineClass(item);
                    }
                  })
              "
              ><c-icon name="cil-trash"
            /></c-button>
          </div>
        </td>
      </template>
    </CDataTable>
  </div>
</template>

<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      fromDate: new Date().toISOString().slice(0, 10),
      lineClassIsEditing: false,
      line_classe: {
        id: 0,
        line_id: 0,
        classe_id: null,
        from_date: "",
        to_date: "",
      },
      classes: [],
      line_classes_fields: [
        "line_name",
        "class_name",
        "from_date",
        "to_date",
        "actions",
      ],
      line_classes: [],
    };
  },
  props: {
    line_id: {
      type: Number,
      required: true,
    },
  },
  methods: {
    format_date: function (value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    edit_date_format: function (value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    initialize() {
      axios
        .get("/api/line-classes")
        .then((response) => {
          this.classes = response.data.classes;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineClasses() {
      axios
        .get(`/api/get_line_classes/${this.line_id}`)
        .then((response) => {
          this.line_classes = response.data.line_classes;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    store() {
      axios
        .post(`/api/lines/${this.line_id}/classes`, {
          line_id: this.line_id,
          class_id: this.line_classe.classe_id,
          from_date: this.crmDateFormat(this.line_classe.from_date),
          to_date: this.line_classe.to_date? this.crmDateFormat(this.line_classe.to_date):"",
        })
        .then((response) => {
          this.line_classe = {
            line_id: this.line_classe.line_id,
            classe_id: "",
            from_date: "",
            to_date: "",
          };
          this.flash("Line Class Created Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      this.getLineClasses();
    },
    edit(id) {
      this.lineClassIsEditing = true;
      this.scrollToTop();
      axios
        .get(`/api/line-classes/${id}`)
        .then((response) => {
          this.line_classe.id = response.data.line_classe.id;
          this.line_classe.line_id = response.data.line_classe.line_id;
          this.line_classe.classe_id = response.data.line_classe.class_id;
          this.line_classe.from_date = this.edit_date_format(
            response.data.line_classe.from_date
          );
          this.line_classe.to_date = this.edit_date_format(
            response.data.line_classe.to_date
          );
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    update() {
      axios
        .put(`/api/lines/${this.line_id}/class/${this.line_classe.id}`, {
          id: this.line_classe.id,
          line_id: this.line_classe.line_id,
          class_id: this.line_classe.classe_id,
          from_date: this.crmDateFormat(this.line_classe.from_date),
          to_date: this.line_classe.to_date? this.crmDateFormat(this.line_classe.to_date):"",
        })
        .then((response) => {
          this.lineClassIsEditing = false;
          this.flash("Line Class Updated Successfully");
          this.getLineClasses();
          this.line_classe = {
            id: 0,
            line_id: this.line_classe.line_id,
            classe_id: "",
            from_date: "",
            to_date: "",
          };
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removeLineClass(item) {
      const index = this.line_classes.findIndex((type) => item.id == type.id);
      this.line_classes.splice(index, 1);
    },
    deleteLineClass(item) {
      axios
        .delete(`/api/line-classes/${item.id}`)
        .then((res) => {
          this.removeLineClass(item);
          this.flash("Line Class Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
    this.initialize();
    this.getLineClasses();
  },
};
</script>

