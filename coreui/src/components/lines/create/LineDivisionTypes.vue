<template>
  <div>
    <CCard no-header>
      <CCardBody>
        <div class="row">
          <div class="col">
            <CFormGroup v-if="!lineDivisionTypeIsEditing">
              <template #label> Division Type </template>
              <template #input>
                <v-select
                  v-model="line_division_type.divisiontype_id"
                  :options="divisiontypes"
                  label="name"
                  :value="0"
                  :reduce="(divisiontype) => divisiontype.id"
                  placeholder="Select Division Type"
                  multiple
                  class="mt-2"
                />
              </template>
            </CFormGroup>
            <CFormGroup v-if="lineDivisionTypeIsEditing">
              <template #label> Division Type </template>
              <template #input>
                <v-select
                  v-model="line_division_type.divisiontype_id"
                  :options="divisiontypes"
                  label="name"
                  :value="0"
                  :reduce="(divisiontype) => divisiontype.id"
                  placeholder="Select Division Type"
                  class="mt-2"
                />
              </template>
            </CFormGroup>
          </div>
          <div class="col">
            <CInput
              label="From"
              type="date"
              placeholder="From"
              v-model="line_division_type.from_date"
            ></CInput>
          </div>
          <div class="col">
            <CInput
              label="To"
              type="date"
              placeholder="To"
              v-model="line_division_type.to_date"
            ></CInput>
          </div>
        </div>
      </CCardBody>
      <CCardFooter>
        <CButton
          color="primary"
          v-if="!lineDivisionTypeIsEditing"
          @click="store()"
          >Create</CButton
        >
        <CButton
          color="primary"
          v-if="lineDivisionTypeIsEditing"
          @click="update"
          >Update</CButton
        >
        <CButton color="default" :to="{ name: 'lines' }">Cancel</CButton>
      </CCardFooter>
    </CCard>
    <CDataTable
      hover
      striped
      sorter
      tableFilter
      footer
      itemsPerPageSelect
      :items="line_division_types"
      :fields="line_division_types_fields"
      :items-per-page="100"
      :active-page="1"
      :responsive="true"
      pagination
      thead-top
    >
      <template #from_date="{ item }">
        <td>{{ format_date(item.from_date) }}</td>
      </template>
      <template #to_date="{ item }">
        <td>{{ format_date(item.to_date) }}</td>
      </template>
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ line_division_types.length }}
        </td>
      </template>
      <template #actions="{ item }">
        <td>
          <div class="row justify-content-center">
            <CButton
              color="success"
              v-if="checkPermission('edit_line_division_types')"
              class="text-white btn-sm mt-2 mr-1"
              @click="edit(item.id)"
              ><i class="cil-pencil"></i><CIcon name="cil-pencil"
            /></CButton>
            <c-button
              color="danger"
              v-if="checkPermission('delete_line_division_types')"
              class="btn-sm mt-2 mr-1"
              @click="
                $root
                  .$confirm('Delete', 'Do you want to delete this record?', {
                    color: 'red',
                    width: 290,
                    zIndex: 200,
                  })
                  .then((confirmed) => {
                    if (confirmed) {
                      deleteLineDivisionType(item);
                    }
                  })
              "
              ><c-icon name="cil-trash"
            /></c-button>
          </div>
        </td>
      </template>
    </CDataTable>
  </div>
</template>
<script>
import moment from "moment";

import vSelect from "vue-select";
export default {
  components: {
    vSelect,
  },
  props: {
    line_id: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      fromDate: new Date().toISOString().slice(0, 10),
      lineDivisionTypeIsEditing: false,
      line_division_type: {
        id: 0,
        line_id: 0,
        divisiontype_id: "",
        from_date: "",
        to_date: "",
      },
      divisiontypes: [],
      line_division_types: [],
      line_division_types_fields: [
        "id",
        "line name",
        "division type name",
        "from_date",
        "to_date",
        "actions",
      ],
      lineDivisionTypes: [],
    };
  },
  emits: ["getLineDivisionTypes"],
  methods: {
    format_date: function (value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    edit_date_format: function (value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    initialize() {
      axios
        .get(`/api/lines/division-types`)
        .then((response) => {
          this.divisiontypes = response.data.divisiontypes;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineDivisionTypes() {
      axios
        .get(`/api/lines/${this.line_id}/division-types`)
        .then((response) => {
          this.line_division_types = response.data.line_division_types;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    sendLineDivisionTypes() {
      axios
        .get(`/api/line-divisions/${this.line_id}`)
        .then((response) => {
          this.lineDivisionTypes = response.data.lineDivisionTypes;
          this.$emit("getLineDivisionTypes", {
            lineDivisionTypes: this.lineDivisionTypes,
          });
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    store() {
      axios
        .post(`/api/lines/${this.line_id}/division-types`, {
          line_id: this.line_id,
          divisiontype_id: this.line_division_type.divisiontype_id,
          from_date: this.crmDateFormat(this.line_division_type.from_date),
          to_date: this.line_division_type.to_date
            ? this.crmDateFormat(this.line_division_type.to_date)
            : "",
        })
        .then((response) => {
          this.line_division_type = {
            line_id: this.line_division_type.line_id,
            divisiontype_id: "",
            from_date: "",
            to_date: "",
          };
          this.sendLineDivisionTypes();
          this.getLineDivisionTypes();
          this.flash("Line Division Type Created Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    edit(id) {
      this.lineDivisionTypeIsEditing = true;
      this.scrollToTop();
      axios
        .get(`/api/lines/division-types/${id}`)
        .then((response) => {
          this.line_division_type.id = response.data.line_division_type.id;
          this.line_division_type.line_id =
            response.data.line_division_type.line_id;
          this.line_division_type.divisiontype_id =
            response.data.line_division_type.divisiontype_id;
          this.line_division_type.from_date = this.edit_date_format(
            response.data.line_division_type.from_date
          );
          this.line_division_type.to_date = this.edit_date_format(
            response.data.line_division_type.to_date
          );
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    update() {
      axios
        .put(
          `/api/lines/${this.line_id}/division-types/${this.line_division_type.id}`,
          {
            id: this.line_division_type.id,
            line_id: this.line_division_type.line_id,
            divisiontype_id: this.line_division_type.divisiontype_id,
            from_date: this.crmDateFormat(this.line_division_type.from_date),
            to_date: this.line_division_type.to_date
              ? this.crmDateFormat(this.line_division_type.to_date)
              : "",
          }
        )
        .then((response) => {
          this.lineDivisionTypeIsEditing = false;
          this.sendLineDivisionTypes();
          this.flash("Line Division Type Updated Successfully");
          this.getLineDivisionTypes();
          this.line_division_type = {
            line_id: this.line_division_type.line_id,
            divisiontype_id: "",
            from_date: "",
            to_date: "",
          };
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removeLineDivisionType(item) {
      const index = this.line_division_types.findIndex(
        (type) => item.id == type.id
      );
      this.line_division_types.splice(index, 1);
    },
    deleteLineDivisionType(item) {
      axios
        .delete(`/api/line-division-types/${item.id}`)
        .then((res) => {
          this.removeLineDivisionType(item);
          this.flash("Line Division Type Deleted Successfully");
          this.sendLineDivisionTypes();
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
    this.initialize();
    this.getLineDivisionTypes();
    this.sendLineDivisionTypes();
  },
};
</script>
