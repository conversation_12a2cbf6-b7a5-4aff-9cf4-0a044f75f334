<template>
  <div>
    <CCard no-header>
      <CCardBody>
        <c-row>
          <c-col>
            <CDropdown placement="bottom-end" toggler-text="Tools" color="primary" class="float-right d-inline-block">
              <CDropdownItem v-if="checkPermission('download_all_templates')"
                @click="templateDownload('lineDivisionProducts.xlsx')">Template</CDropdownItem>

              <c-dropdown-item v-if="checkPermission('import_line_division_products')" @click="successModal = true">Upload
                New</c-dropdown-item>

              <c-dropdown-item v-if="checkPermission('import_bulk_edit')" @click="updateModal = true">Bulk
                Edit</c-dropdown-item>

              <c-dropdown-divider></c-dropdown-divider>

              <CDropdownItem v-if="checkPermission('export_xlsx_line_division_products')" @click="exportLevel()">Export to Excel
              </CDropdownItem>
              <CDropdownItem v-if="checkPermission('export_csv_line_division_products')" @click="exportCSV()">Export to CSV
              </CDropdownItem>
              <CDropdownItem v-if="checkPermission('export_pdf_line_division_products')" @click="exportLevelPDF()">Export to PDF
              </CDropdownItem>
              <CDropdownItem v-if="checkPermission('export_email_line_division_products')" @click="sendModal = true">Send to Mail
              </CDropdownItem>
            </CDropdown>

            <CModal title="Upload Line Division Products" color="success" :show.sync="successModal">
              <CInputFile type="file" ref="file" id="file" name="file_name" v-on:change="handleFileUpload"
                placeholder="New file" />
              <CProgress :value="uploadPercentage" color="success" animated showPercentage show-value
                style="height: 15px" class="mt-1" :max="100" v-show="progressBar" />
              <template #footer>
                <CButton @click="successModal = false" color="danger">Discard</CButton>
                <CButton @click="importLineDivisionProducts()" color="success">Upload</CButton>
              </template>
            </CModal>
          </c-col>
        </c-row>
        <div class="row">
          <div class="col">
            <CFormGroup v-if="!lineProductIsEditing">
              <template #label> Product </template>
              <template #input>
                <v-select v-model="line_product.product_id" :options="products" label="name" :value="0"
                  :reduce="(product) => product.id" placeholder="Select Product" multiple class="mt-2" />
              </template>
            </CFormGroup>
            <CFormGroup v-if="lineProductIsEditing">
              <template #label> Product </template>
              <template #input>
                <v-select v-model="line_product.product_id" :options="products" label="name" :value="0"
                  :reduce="(product) => product.id" placeholder="Select Product" class="mt-2" />
              </template>
            </CFormGroup>
          </div>
          <div class="col">
            <CFormGroup v-if="!lineProductIsEditing">
              <template #label> Division </template>
              <template #input>
                <v-select v-model="line_product.line_division_id" :options="divisions" label="name" :value="0"
                  :reduce="(division) => division.id" placeholder="Select Division" class="mt-2" />
              </template>
            </CFormGroup>
            <CFormGroup v-if="lineProductIsEditing">
              <template #label> Division </template>
              <template #input>
                <v-select v-model="line_product.line_division_id" :options="divisions" label="name" :value="0"
                  :reduce="(division) => division.id" placeholder="Select Division" class="mt-2" />
              </template>
            </CFormGroup>
          </div>
          <div class="col">
            <CInput label="From" type="date" placeholder="From" v-model="line_product.from_date"></CInput>
          </div>
          <div class="col">
            <CInput label="To" type="date" placeholder="To" v-model="line_product.to_date"></CInput>
          </div>
        </div>
      </CCardBody>
      <CCardFooter>
        <CButton color="primary" v-if="!lineProductIsEditing" @click="store">Create</CButton>
        <CButton color="primary" v-if="lineProductIsEditing" @click="update">Update</CButton>
        <CButton color="default" :to="{ name: 'lines' }">Cancel</CButton>
      </CCardFooter>
    </CCard>

    <CDataTable hover striped sorter tableFilter footer itemsPerPageSelect :items="line_products"
      :fields="line_products_fields" :items-per-page="100" :active-page="1" :responsive="true" pagination thead-top>
      <template #from_date="{ item }">
        <td>{{ format_date(item.from_date) }}</td>
      </template>
      <template #to_date="{ item }">
        <td>{{ format_date(item.to_date) }}</td>
      </template>
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ line_products.length }}
        </td>
      </template>
      <template #actions="{ item }">
        <td>
          <div class="row justify-content-center">
            <CButton color="success" v-if="checkPermission('edit_line_products')" class="text-white btn-sm mt-2 mr-1"
              @click="edit(item.id)"><i class="cil-pencil"></i>
              <CIcon name="cil-pencil" />
            </CButton>
            <c-button color="danger" v-if="checkPermission('delete_line_products')" class="btn-sm mt-2 mr-1" @click="
              $root
                .$confirm('Delete', 'Do you want to delete this record?', {
                  color: 'red',
                  width: 290,
                  zIndex: 200,
                })
                .then((confirmed) => {
                  if (confirmed) {
                    deleteLineProduct(item);
                  }
                })
              "><c-icon name="cil-trash" /></c-button>
          </div>
        </td>
      </template>
    </CDataTable>
  </div>
</template>
<script>
import moment from "moment";

import vSelect from "vue-select";
export default {
  components: {
    vSelect,
  },
  props: {
    line_id: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      fromDate: new Date().toISOString().slice(0, 10),
      line_products: [],
      divisions: [],
      lineProductIsEditing: false,
      line_product: {
        id: 0,
        line_id: 0,
        line_division_id: '',
        product_id: "",
        from_date: "",
        to_date: "",
      },
      line_products_fields: [
        "id",
        "line",
        "Division",
        "product",
        "from_date",
        "to_date",
        "actions",
      ],
      products: [],
      uploadPercentage: 0,
      progressBar: false,
      addOnKey: [13, 32, ":", ";"],
      successModal: false,
      updateModal: false,
      sendModal: false,
      file: "",
      file_name: "",
      myResult: "",
      text: "",
      validation: [
        {
          classes: "email",
          rule: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,24}))$/,
        },
      ],
      email: "",
      emails: [],
    };
  },
  methods: {
    format_date: function (value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    edit_date_format: function (value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    importLineDivisionProducts() {
      this.progressBar = true;
      const formData = new FormData();
      formData.append("file", this.file);
      this.importFile("/api/import-line-division-products", formData);
      document.getElementById("file").value = "";
      this.successModal = false;
    },

    importUpdateLevel(){
      this.progressBar = true;
      let formData = new FormData();
      formData.append("file", this.file);
      this.importFile("/api/import-update-line-division-products", formData);
      document.getElementById("bulkEditFile").value = "";
      this.updateModal = false;
    },
    
    handleFileUpload(files, event) {
      this.file = event.target.files[0];
      this.file_name = event.target.files[0].name;
    },
    exportLevel() {
      this.exportFile("/api/export-line-division-products", "LineDivisionProduct.xlsx");
    },
    exportCSV() {
      this.exportFile("/api/export-line-division-products-csv", "LineDivisionProduct.csv");
    },
    exportLevelPDF() {
      this.exportFile("/api/exort-line-division-products-pdf", "LineDivisionProduct.pdf");
    },
    sendLevelMail() {
      const formData = {
        emails: JSON.stringify(this.emails),
        text: this.text,
      };
      this.sendMail("/api/send-mail-line-division-products", formData);
      this.successModal = false;
    },
    initialize() {
      axios
        .get("/api/line-division-products")
        .then((response) => {
          this.products = response.data.products;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineProducts() {
      axios
        .get(`/api/lines/${this.line_id}/division/products`)
        .then((response) => {
          this.line_products = response.data.line_products;
          this.divisions = response.data.divisions;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    reset() {
      this.line_product = {
        line_id: this.line_id,
        line_division_id: "",
        product_id: "",
        from_date: "",
        to_date: "",
      };
    },
    store() {
      axios
        .post(`/api/lines/${this.line_id}/division/products`, {
          line_id: this.line_id,
          line_division_id: this.line_product.line_division_id,
          product_id: this.line_product.product_id,
          from_date: this.crmDateFormat(this.line_product.from_date),
          to_date: this.line_product.to_date
            ? this.crmDateFormat(this.line_product.to_date)
            : "",
        })
        .then((response) => {
          this.reset();
          this.flash("Line Products Created Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      this.getLineProducts();
    },
    edit(id) {
      this.lineProductIsEditing = true;
      axios
        .get(`/api/line-division-products/${id}`)
        .then((response) => {
          this.line_product.id = response.data.line_product.id;
          this.line_product.line_id = response.data.line_product.line_id;
          this.line_product.line_division_id = response.data.line_product.line_division_id;
          this.line_product.product_id = response.data.line_product.product_id;
          this.line_product.from_date = this.edit_date_format(
            response.data.line_product.from_date
          );
          this.line_product.to_date = this.edit_date_format(
            response.data.line_product.to_date
          );
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    update() {
      axios
        .put(`/api/lines/${this.line_id}/division/products/${this.line_product.id}`, {
          id: this.line_product.id,
          line_id: this.line_product.line_id,
          line_division_id: this.line_product.line_division_id,
          product_id: this.line_product.product_id,
          from_date: this.crmDateFormat(this.line_product.from_date),
          to_date: this.line_product.to_date
            ? this.crmDateFormat(this.line_product.to_date)
            : "",
        })
        .then((response) => {
          this.lineProductIsEditing = false;
          this.flash("Line Product Updated Successfully");
          this.getLineProducts();
          this.reset();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removeLineProduct(item) {
      const index = this.line_products.findIndex((type) => item.id == type.id);
      this.line_products.splice(index, 1);
    },
    deleteLineProduct(item) {
      axios
        .delete(`/api/line-division-products/${item.id}`)
        .then((res) => {
          this.removeLineProduct(item);
          this.flash("Line Product Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
    this.initialize();
    this.getLineProducts();
  },
};
</script>
