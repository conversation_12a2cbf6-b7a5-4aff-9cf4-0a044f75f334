<template>
  <CCard no-header>
    <CCardBody>
      <div class="row">
        <div class="col-4">
          <CInput
            label="Name"
            type="text"
            placeholder="Name"
            v-model="line.name"
          ></CInput>
        </div>
        <div class="col-4">
          <CFormGroup>
            <template #label> Country </template>
            <template #input>
              <v-select
                v-model="line.country_id"
                :options="countries"
                label="name"
                :value="0"
                :reduce="(country) => country.id"
                placeholder="Select option"
                class="mt-2"
              />
            </template>
          </CFormGroup>
        </div>
        <div class="col-4">
          <CFormGroup>
            <template #label> Currency </template>
            <template #input>
              <v-select
                v-model="line.currency_id"
                :options="currencies"
                label="name"
                :value="0"
                :reduce="(currency) => currency.id"
                placeholder="Select option"
                class="mt-2"
              />
            </template>
          </CFormGroup>
        </div>
        <div class="col-4">
          <c-form-group>
            <template #label>
              <strong>From Date</strong>
            </template>
            <template #input>
              <c-input
                type="date"
                class="mt-2"
                placeholder="From Date"
                v-model="line.fromDate"
              ></c-input>
            </template>
          </c-form-group>
        </div>
        <div class="col-4">
          <c-form-group>
            <template #label>
              <strong>To Date</strong>
            </template>
            <template #input>
              <c-input
                type="date"
                class="mt-2"
                placeholder="To Date"
                v-model="line.toDate"
              ></c-input>
            </template>
          </c-form-group>
        </div>
      </div>

      <div class="row">
        <div class="col">
          <CTextarea
            label="Notes"
            type="text"
            placeholder="Notes"
            v-model="line.notes"
          ></CTextarea>
        </div>
      </div>
    </CCardBody>
    <CCardFooter>
      <CButton color="primary" @click="store">Create</CButton>
      <CButton color="default" :to="{ name: 'lines' }">Cancel</CButton>
    </CCardFooter>
  </CCard>
</template>
<script>
import vSelect from "vue-select";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      line_id: null,
      line: {
        name: "",
        notes: "",
        fromDate: "",
        toDate: "",
        country_id: "",
        currency_id: "",
      },
      currencies: [],
      countries: [],
    };
  },
  emits: ["getLine"],
  methods: {
    initialize() {
      axios
        .get("/api/lines/create")
        .then((response) => {
          this.currencies = response.data.currencies;
          this.countries = response.data.countries;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    store() {
      axios
        .post("/api/lines", {
          name: this.line.name,
          notes: this.line.notes,
          currency_id: this.line.currency_id,
          country_id: this.line.country_id,
          from_date: this.crmDateFormat(this.line.fromDate),
          to_date: this.line.toDate ? this.crmDateFormat(this.line.toDate) : "",
        })
        .then((response) => {
          this.line = {
            line_id: response.data.line.id,
            name: "",
            notes: "",
            currency_id: "",
            country_id: "",
          };
          this.flash("Line is created successfully");
          this.$emit("getLine", { line: this.line.line_id });
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
