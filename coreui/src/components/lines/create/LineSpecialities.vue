<template>
  <div>
    <CCard no-header>
      <CCardBody>
        <div class="row">
          <div class="col">
            <CFormGroup v-if="!lineSpecialityIsEditing">
              <template #label> Specialities </template>
              <template #input>
                <v-select
                  v-model="line_speciality.speciality_id"
                  :options="specialities"
                  label="name"
                  :value="0"
                  :reduce="(speciality) => speciality.id"
                  placeholder="Select Speciality"
                  multiple
                  class="mt-2"
                />
              </template>
            </CFormGroup>
            <CFormGroup v-if="lineSpecialityIsEditing">
              <template #label> Speciality </template>
              <template #input>
                <v-select
                  v-model="line_speciality.speciality_id"
                  :options="specialities"
                  label="name"
                  :value="0"
                  :reduce="(speciality) => speciality.id"
                  placeholder="Select Speciality"
                  class="mt-2"
                />
              </template>
            </CFormGroup>
          </div>
          <div class="col">
            <CInput
              label="From"
              type="date"
              placeholder="From"
              v-model="line_speciality.from_date"
            ></CInput>
          </div>
          <div class="col">
            <CInput
              label="To"
              type="date"
              placeholder="To"
              v-model="line_speciality.to_date"
            ></CInput>
          </div>
        </div>
      </CCardBody>
      <CCardFooter>
        <CButton color="primary" v-if="!lineSpecialityIsEditing" @click="store"
          >Create</CButton
        >
        <CButton color="primary" v-if="lineSpecialityIsEditing" @click="update"
          >Update</CButton
        >
        <CButton color="default" :to="{ name: 'lines' }">Cancel</CButton>
      </CCardFooter>
    </CCard>

    <CDataTable
      hover
      striped
      sorter
      tableFilter
      footer
      itemsPerPageSelect
      :items="line_specialities"
      :fields="line_specialities_fields"
      :items-per-page="100"
      :active-page="1"
      :responsive="true"
      pagination
      thead-top
    >
      <template #from_date="{ item }">
        <td>{{ format_date(item.from_date) }}</td>
      </template>
      <template #to_date="{ item }">
        <td>{{ format_date(item.to_date) }}</td>
      </template>
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ line_specialities.length }}
        </td>
      </template>
      <template #parent_name="{ item }">
        <td v-if="item.parent_name">{{ item.parent_name }}</td>
        <td v-else-if="!item.parent_name">-No Parent-</td>
      </template>
      <template #actions="{ item }">
        <td>
          <div class="row justify-content-center">
            <CButton
              color="success"
              v-if="checkPermission('edit_line_specialities')"
              class="text-white btn-sm mt-2 mr-1"
              @click="edit(item.id)"
              ><i class="cil-pencil"></i><CIcon name="cil-pencil"
            /></CButton>
            <c-button
              color="danger"
              v-if="checkPermission('delete_line_specialities')"
              class="btn-sm mt-2 mr-1"
              @click="
                $root
                  .$confirm('Delete', 'Do you want to delete this record?', {
                    color: 'red',
                    width: 290,
                    zIndex: 200,
                  })
                  .then((confirmed) => {
                    if (confirmed) {
                      deleteLineSpeciality(item);
                    }
                  })
              "
              ><c-icon name="cil-trash"
            /></c-button>
          </div>
        </td>
      </template>
    </CDataTable>
  </div>
</template>

<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      fromDate: new Date().toISOString().slice(0, 10),
      lineSpecialityIsEditing: false,
      line_speciality: {
        id: 0,
        line_id: 0,
        speciality_id: null,
        from_date: "",
        to_date: "",
      },
      specialities: [],
      line_specialities_fields: [
        "line_name",
        "speciality_name",
        "parent_name",
        "from_date",
        "to_date",
        "actions",
      ],
      line_specialities: [],
    };
  },
  props: {
    line_id: {
      type: Number,
      required: true,
    },
  },
  methods: {
    format_date: function (value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    edit_date_format: function (value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    initialize() {
      axios
        .get("/api/line-specialities")
        .then((response) => {
          this.specialities = response.data.specialities;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineSpecialities() {
      let self = this;
      axios
        .get(`/api/get_line_specialities/${this.line_id}`)
        .then((response) => {
          this.line_specialities = response.data.line_specialities;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    store() {
      axios
        .post(`/api/lines/${this.line_id}/specialities`, {
          line_id: this.line_id,
          speciality_id: this.line_speciality.speciality_id,
          from_date: this.crmDateFormat(this.line_speciality.from_date),
          to_date: this.line_speciality.to_date? this.crmDateFormat(this.line_speciality.to_date):"",
        })
        .then((response) => {
          this.line_speciality = {
            id: 0,
            line_id: this.line_speciality.line_id,
            speciality_id: "",
            from_date: "",
            to_date: "",
          };
          this.flash("Line Speciality Created Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      this.getLineSpecialities();
    },
    edit(id) {
      this.lineSpecialityIsEditing = true;
      this.scrollToTop();
      axios
        .get(`/api/line-specialities/${id}`)
        .then((response) => {
          this.line_speciality.id = response.data.line_speciality.id;
          this.line_speciality.line_id = response.data.line_speciality.line_id;
          this.line_speciality.speciality_id =
            response.data.line_speciality.speciality_id;
          this.line_speciality.from_date = this.edit_date_format(
            response.data.line_speciality.from_date
          );
          this.line_speciality.to_date = this.edit_date_format(
            response.data.line_speciality.to_date
          );
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    update() {
      axios
        .put(`/api/lines/${this.line_id}/speciality/${this.line_speciality.id}`, {
          id: this.line_speciality.id,
          line_id: this.line_speciality.line_id,
          speciality_id: this.line_speciality.speciality_id,
          from_date: this.crmDateFormat(this.line_speciality.from_date),
          to_date: this.line_speciality.to_date? this.crmDateFormat(this.line_speciality.to_date):"",
        })
        .then( (response) =>{
          this.lineSpecialityIsEditing = false;
          this.flash('Line Speciality Updated Successfully');
          this.getLineSpecialities();
          this.line_speciality = {
            id: 0,
            line_id: this.line_speciality.line_id,
            speciality_id: "",
            from_date: "",
            to_date: "",
          };
        })
        .catch((error)=> {
          this.showErrorMessage(error);
        });
    },
    removeLineSpeciality(item) {
      const index = this.line_specialities.findIndex(
        (type) => item.id == type.id
      );
      this.line_specialities.splice(index, 1);
    },
    deleteLineSpeciality(item) {
      axios
        .delete(`/api/line-specialities/${item.id}`)
        .then((res) => {
          this.removeLineSpeciality(item);
          this.flash("Line Speciality Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
    this.initialize();
    this.getLineSpecialities();
  },
};
</script>
