<template>
  <div>
    <CCard no-header>
      <CCardBody>
        <div class="row">
          <div class="col" v-if="!lineBrickIsEditing">
            <CFormGroup>
              <template #label> Brick</template>
              <template #input>
                <v-select v-model="line_brick.brick_id" :options="bricks" label="name" :value="0"
                  :reduce="(brick) => brick.id" placeholder="Select Brick" multiple class="mt-2" />
              </template>
            </CFormGroup>
          </div>
          <div class="col" v-else>
            <CFormGroup>
              <template #label> Brick</template>
              <template #input>
                <v-select v-model="line_brick.brick_id" :options="bricks" label="name" :value="0"
                  :reduce="(brick) => brick.id" placeholder="Select Brick" class="mt-2" />
              </template>
            </CFormGroup>
          </div>
          <div class="col">
            <CFormGroup>
              <template #label> Line Division</template>
              <template #input>
                <v-select v-model="line_brick.line_division_id" :options="divisions" label="name" :value="0"
                  :reduce="(division) => division.id" placeholder="Select Division" class="mt-2" />
              </template>
            </CFormGroup>
          </div>
          <div class="col">
            <CInput label="Ratio" type="number" placeholder="Ratio" v-model="line_brick.ratio"></CInput>
          </div>
        </div>
        <div class="row">
          <div class="col">
            <CInput label="From" type="date" placeholder="From" v-model="line_brick.from_date"></CInput>
          </div>
          <div class="col">
            <CInput label="To" type="date" placeholder="To" v-model="line_brick.to_date"></CInput>
          </div>
        </div>
        <div class="row" v-if="!lineBrickIsEditing">
          <strong style="margin-top: 10px; padding-left: 5px"><label> Other Options:</label></strong>
          <div class="col">
            <label>Copy List</label>
            <input type="checkbox" :id="`copy_list_brick`" v-model="copy_list_brick" :checked="checkCopyListBrick()"
              @click="setCopyListBrick()" />
          </div>
        </div>
        <div class="row" v-if="lineBrickIsEditing">
          <strong style="margin-top: 10px; padding-left: 5px"><label> Other Options:</label></strong>
          <div class="col">
            <label>Move List</label>
            <input type="checkbox" :id="`update_list_division`" v-model="update_list_division"
              :checked="checkUpdateList()" @click="setUpdateList()" />
          </div>
        </div>
      </CCardBody>
      <CCardFooter>
        <CButton color="primary" v-if="!lineBrickIsEditing" @click="store">Create
        </CButton>
        <CButton color="primary" v-else @click="update">Update</CButton>
        <CButton color="default" :to="{ name: 'lines' }">Cancel</CButton>
      </CCardFooter>
    </CCard>

    <c-modal :title="`Move Brick ( ${item.brick} )`" color="success" :show.sync="moveModal" :closeOnBackdrop="false">
      <c-form-group>
        <template #label>Old Line Division</template>
        <template #input>
          <v-select v-model="line_brick_movable.old_line_division_id" :options="divisions" label="name" :value="0"
            :reduce="(division) => division.id" placeholder="Select Division" class="mt-2" disabled />
        </template>
      </c-form-group>
      <c-form-group>
        <template #label>New Line Division</template>
        <template #input>
          <v-select v-model="line_brick_movable.new_line_division_id" :options="divisions" label="name" :value="0"
            :reduce="(division) => division.id" placeholder="Select Division" class="mt-2" aria-required="true"
            required />
          <p id="error-message-line-division" :style="{ ...errorNewLineDivisionMessage }">Please select the new line
            division.</p>
        </template>
      </c-form-group>
      <c-input label="Ratio" type="number" placeholder="Ratio" v-model="line_brick_movable.ratio" disabled></c-input>
      <c-input label="Effect Date" type="date" placeholder="Effect Date" v-model="line_brick_movable.effect_date"
        aria-required="true" required></c-input>
      <p id="error-message" :style="{ ...errorDateMessage }">Please select the first day
        of the month.</p>
      <div class="row">
        <strong style="margin-top: 10px; padding-left: 5px">
          <label> Other Options:</label>
        </strong>
        <div class="col">
          <label>Copy List
            <input type="checkbox" v-model="line_brick_movable.copy_list" />
          </label>

        </div>
        <div class="col">
          <label>Move Sales
            <input type="checkbox" v-model="line_brick_movable.move_sales" /></label>

        </div>
      </div>
      <template #footer>
        <CButton @click="moveModal = false;" class="text-white" color="danger">Cancel</CButton>
        <CButton type="submit" @click="moveBrick()" class="text-white" color="success">Submit</CButton>
      </template>
    </c-modal>

    <c-modal :title="`Restructure Brick ( ${item.brick} )`" color="success" :show.sync="reShapeModal"
      :closeOnBackdrop="false">
      <c-button @click="line_brick_reshape.divisions.push({ id: null, ratio: 0 })" color="primary">
        <c-icon name="cib-addthis" />
      </c-button>
      <c-button color="danger" @click="line_brick_reshape.divisions.pop()">
        <c-icon name="cil-minus"></c-icon>
      </c-button>
      <c-form-group v-for="(div, i) in line_brick_reshape.divisions" :key="i">
        <template #label>Division</template>
        <template #input>
          <v-select v-model="div.id" :options="divisions" label="name" :value="0" :reduce="(division) => division.id"
            placeholder="Select Division" class="mt-2" />
          <c-input label="Ratio" type="number" placeholder="Ratio" v-model="div.ratio"></c-input>
        </template>
      </c-form-group>
      <c-input label="Effect Date" type="date" placeholder="Effect Date" v-model="line_brick_reshape.effect_date"
        aria-required="true" required></c-input>
      <div class="row">
        <div class="col">
          <label>Copy List
            <input type="checkbox" v-model="line_brick_reshape.copy_list" />
          </label>
        </div>
        <div class="col">
          <label>Move Sales
            <input type="checkbox" v-model="line_brick_reshape.move_sales" />
          </label>
        </div>
      </div>
      <template #footer>
        <CButton @click="reShapeModal = false;" class="text-white" color="danger">Cancel</CButton>
        <CButton type="submit" @click="reshapeBrick()" class="text-white" color="success">Submit</CButton>
      </template>
    </c-modal>

    <CDataTable hover striped sorter tableFilter footer itemsPerPageSelect :items="line_bricks"
      :fields="line_bricks_fields" :items-per-page="1000" :active-page="1" :responsive="true" pagination thead-top>
      <template #from="{ item }">
        <td>{{ crmDateFormat(item.from, "YYYY-MM-DD") }}</td>
      </template>
      <template #to="{ item }">
        <td>{{ crmDateFormat(item.to, "YYYY-MM-DD") }}</td>
      </template>
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ line_bricks.length }}
        </td>
      </template>
      <template #actions="{ item }">
        <td>
          <CDropdown placement="bottom-end" toggler-text="Actions" color="primary" class="m-2">
            <CDropdownItem v-if="checkPermission('edit_users')" @click="edit(item.id)">
              <div class="dd-item">
                <CIcon name="cil-pencil" />
                <span class="dd-item-text">Edit</span>
              </div>
            </CDropdownItem>
            <CDropdownItem v-if="checkPermission('delete_line_bricks')" @click="
              $root.$confirm('Delete', 'Do you want to delete this record?', {
                color: 'red',
                width: 290,
                zIndex: 200,
              })
                .then((confirmed) => {
                  if (confirmed) {
                    deleteLineBrick(item);
                  }
                })
              ">
              <div class="dd-item">
                <CIcon name="cil-trash" />
                <span class="dd-item-text">Delete</span>
              </div>
            </CDropdownItem>
            <CDropdownItem @click="move(item)">
              <div class="dd-item">
                <CIcon name="cil-cursor-move" />
                <span class="dd-item-text">Move</span>
              </div>
            </CDropdownItem>

            <CDropdownItem @click="reshape(item)">
              <div class="dd-item">
                <CIcon name="cil-sitemap" />
                <span class="dd-item-text">Restructure</span>
              </div>
            </CDropdownItem>
            <CDropdownItem @click="edit(item.id)">
              <div class="dd-item">
                <CIcon name="cil-cursor-move" />
                <span class="dd-item-text">Update</span>
              </div>
            </CDropdownItem>
          </CDropdown>
        </td>
      </template>
    </CDataTable>
  </div>
</template>

<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";

export default {
  components: {
    vSelect,
  },
  props: {
    divisions: {
      type: Array,
      required: true,
    },
    line_id: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      fromDate: new Date().toISOString().slice(0, 10),
      lineBrickIsEditing: false,
      line_brick: {
        id: 0,
        line_id: null,
        brick_id: null,
        line_division_id: null,
        ratio: 100,
        from_date: "",
        to_date: "",
      },
      line_brick_movable: {
        id: 0,
        line_id: null,
        brick_id: null,
        old_line_division_id: null,
        new_line_division_id: null,
        ratio: 100,
        effect_date: "",
        copy_list: false,
        move_sales: false,
      },
      item: {},
      moveModal: false,
      bricks: [],
      line_bricks_fields: [
        "id",
        "line",
        "division",
        "brick",
        "ratio",
        "from",
        "to",
        "actions",
      ],
      line_bricks: [],
      update_list_division: null,
      copy_list_brick: 0,
      dateError: false,
      newLineDivisionError: false,
      reShapeModal: false,
      line_brick_reshape: {
        line_id: null,
        brick_id: null,
        divisions: [],
        effect_date: "",
        copy_list: false,
        move_sales: false,
      }
    };
  },
  computed: {
    errorDateMessage() {
      if (!this.dateError) return { color: "red", display: "none" }
      return { color: "red", display: "block" }
    },
    errorNewLineDivisionMessage() {
      if (!this.newLineDivisionError) return { color: "red", display: "none" }
      return { color: "red", display: "block" }
    }
  },
  methods: {
    edit_date_format: function (value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    setUpdateList() {
      if (document.getElementById("update_list_division").checked) {
        this.update_list_division = 1;
      } else {
        this.update_list_division = 0;
      }
    },
    checkUpdateList() {
      if (this.update_list_division === 1) {
        return true;
      }
      return false;
    },
    setCopyListBrick() {
      if (document.getElementById("copy_list_brick").checked) {
        this.copy_list_brick = 1;
      } else {
        this.copy_list_brick = 0;
      }
    },
    checkCopyListBrick() {
      if (this.copy_list_brick === 1) {
        return true;
      }
      return false;
    },
    initialize() {
      axios
        .get(`/api/line-bricks/${this.line_id}`)
        .then((response) => {
          this.bricks = response.data.bricks;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineBricks() {
      axios
        .get(`/api/get_line_bricks/${this.line_id}`)
        .then((response) => {
          this.line_bricks = response.data.line_bricks;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    reset() {
      this.line_brick = {
        id: 0,
        line_id: this.line_id,
        brick_id: "",
        ratio: 100,
        line_division_id: "",
        from_date: "",
        to_date: "",
      };
      this.update_list_division = 0;
      this.copy_list_brick = 0;
    },
    async move(item) {
      this.item = item
      this.moveModal = true;
      const [res, error] = await this.getBrickData(item.id);
      if (error) {
        this.showErrorMessage(error);
        return
      }
      this.line_brick_movable.id = res.data.line_brick.id;
      this.line_brick_movable.line_id = res.data.line_brick.line_id;
      this.line_brick_movable.brick_id = res.data.line_brick.brick_id;
      this.line_brick_movable.old_line_division_id = res.data.line_brick.line_division_id;
      this.line_brick_movable.ratio = res.data.line_brick.ratio;
      this.line_brick_movable.effect_date = this.edit_date_format(new Date());
    },
    async moveBrick() {
      let effectDate = new Date(this.line_brick_movable.effect_date);
      if (effectDate.getDate() !== 1) {
        this.dateError = true
        return;
      } else {
        this.dateError = false
      }
      if (!this.line_brick_movable.new_line_division_id) {
        this.newLineDivisionError = true
        return;
      } else {
        this.newLineDivisionError = false
      }
      await axios.post('/api/line-brick/move', this.line_brick_movable)
      this.getLineBricks();
      this.moveModal = false

    },
    async getBrickData(id) {
      try {
        const res = await axios.get(`/api/line-brick/${id}`)
        return [res, null]
      } catch (e) {
        return [null, e]
      }
    },
    async getLineBricksData(id) {
      try {
        const res = await axios.get(`/api/lines/${this.line_id}/brick/${id}`)
        return [res, null]
      } catch (e) {
        return [null, e]
      }
    },
    store() {
      axios
        .post(`/api/lines/${this.line_id}/bricks`, {
          line_id: this.line_id,
          brick_id: this.line_brick.brick_id,
          line_division_id: this.line_brick.line_division_id,
          copy_list_brick: this.copy_list_brick,
          ratio: this.line_brick.ratio,
          from_date: this.crmDateFormat(this.line_brick.from_date),
          to_date: this.line_brick.to_date
            ? this.crmDateFormat(this.line_brick.to_date)
            : "",
        })
        .then(() => {
          this.reset();
          this.flash("Line Brick Created Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      this.getLineBricks();
    },
    async edit(id) {
      this.scrollToTop();
      this.lineBrickIsEditing = true;
      const [res, error] = await this.getBrickData(id);
      if (error) {
        this.showErrorMessage(error);
        return
      }
      this.line_brick.id = res.data.line_brick.id;
      this.line_brick.line_id = res.data.line_brick.line_id;
      this.line_brick.brick_id = res.data.line_brick.brick_id;
      this.line_brick.line_division_id =
        res.data.line_brick.line_division_id;
      this.line_brick.ratio = res.data.line_brick.ratio;
      this.line_brick.from_date = this.edit_date_format(
        res.data.line_brick.from_date
      );
      this.line_brick.to_date = this.edit_date_format(
        res.data.line_brick.to_date
      );

    },
    update() {
      axios
        .put(`/api/lines/${this.line_id}/brick/${this.line_brick.id}`, {
          id: this.line_brick.id,
          line_id: this.line_brick.line_id,
          brick_id: this.line_brick.brick_id,
          line_division_id: this.line_brick.line_division_id,
          ratio: this.line_brick.ratio,
          update_list_division: this.update_list_division,
          from_date: this.crmDateFormat(this.line_brick.from_date),
          to_date: this.line_brick.to_date
            ? this.crmDateFormat(this.line_brick.to_date)
            : "",
        })
        .then((response) => {
          this.lineBrickIsEditing = false;
          this.flash("Line Brick Updated Successfully");
          this.getLineBricks();
          this.reset();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    update() {
      axios
        .put(`/api/lines/${this.line_id}/brick/${this.line_brick.id}`, {
          id: this.line_brick.id,
          line_id: this.line_brick.line_id,
          brick_id: this.line_brick.brick_id,
          line_division_id: this.line_brick.line_division_id,
          ratio: this.line_brick.ratio,
          update_list_division: this.update_list_division,
          from_date: this.crmDateFormat(this.line_brick.from_date),
          to_date: this.line_brick.to_date
            ? this.crmDateFormat(this.line_brick.to_date)
            : "",
        })
        .then((response) => {
          this.lineBrickIsEditing = false;
          this.flash("Line Brick Updated Successfully");
          this.getLineBricks();
          this.reset();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removeLineBrick(item) {
      const index = this.line_bricks.findIndex((type) => item.id === type.id);
      this.line_bricks.splice(index, 1);
    },
    deleteLineBrick(item) {
      axios
        .delete(`/api/line-bricks/${item.id}`)
        .then((res) => {
          this.removeLineBrick(item);
          this.flash("Line Brick Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
    async reshape(item) {
      this.item = item
      const [res, error] = await this.getLineBricksData(item.brick_id)

      if (error) {
        this.showErrorMessage(error);
        return
      }
      this.line_brick_reshape.brick_id = item.brick_id;
      this.line_brick_reshape.line_id = this.line_id;
      this.line_brick_reshape.divisions = res.data.line_bricks.map(div => ({
        id: div.line_division_id,
        ratio: div.ratio
      }))
      this.reShapeModal = true;
    },
    async reshapeBrick() {
      await axios.post('/api/line-brick/reshape', this.line_brick_reshape)
      this.getLineBricks();
      this.reShapeModal = false
    }
  },
  watch: {
    moveModal(value) {
      this.dateError = false;
      this.newLineDivisionError = false;
      if (!value)
        this.line_brick_movable = {
          id: 0,
          line_id: null,
          brick_id: null,
          old_line_division_id: null,
          new_line_division_id: null,
          ratio: 100,
          effect_date: "",
          copy_list: false,
          move_sales: false,
        }
    },
    reShapeModal(value) {
      if (!value)
        this.line_brick_reshape = {
          line_id: null,
          brick_id: null,
          divisions: [],
          effect_date: "",
          copy_list: false,
          move_sales: false,
        }
    }
  },
  created() {
    this.initialize();
    this.getLineBricks();
  },
};
</script>
