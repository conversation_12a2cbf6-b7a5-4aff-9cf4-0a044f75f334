<template>
  <div>
    <CCard no-header>
      <CCardBody>
        <div class="row">
          <div class="col">
            <CFormGroup v-if="!lineUserIsEditing">
              <template #label> User </template>
              <template #input>
                <v-select
                  v-model="line_user.user_id"
                  :options="users"
                  label="fullname"
                  :value="0"
                  :reduce="(user) => user.id"
                  placeholder="Select User"
                  multiple
                  class="mt-2"
                />
              </template>
            </CFormGroup>
            <CFormGroup v-if="lineUserIsEditing">
              <template #label> User </template>
              <template #input>
                <v-select
                  v-model="line_user.user_id"
                  :options="users"
                  label="fullname"
                  :value="0"
                  :reduce="(user) => user.id"
                  placeholder="Select User"
                  class="mt-2"
                />
              </template>
            </CFormGroup>
          </div>
          <div class="col">
            <CInput
              label="From"
              type="date"
              placeholder="From"
              v-model="line_user.from_date"
            ></CInput>
          </div>
          <div class="col">
            <CInput
              label="To"
              type="date"
              placeholder="To"
              v-model="line_user.to_date"
            ></CInput>
          </div>
        </div>
      </CCardBody>
      <CCardFooter>
        <CButton color="primary" v-if="!lineUserIsEditing" @click="store()"
          >Create</CButton
        >
        <CButton color="primary" v-if="lineUserIsEditing" @click="update()"
          >Update</CButton
        >
        <CButton color="default" :to="{ name: 'lines' }">Cancel</CButton>
      </CCardFooter>
    </CCard>

    <CDataTable
      hover
      striped
      sorter
      tableFilter
      footer
      itemsPerPageSelect
      :items="line_users"
      :fields="line_users_fields"
      :items-per-page="1000"
      :active-page="1"
      :responsive="true"
      pagination
      thead-top
    >
      <template #from_date="{ item }">
        <td>{{ format_date(item.from_date) }}</td>
      </template>
      <template #to_date="{ item }">
        <td>{{ format_date(item.to_date) }}</td>
      </template>
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ line_users.length }}
        </td>
      </template>
      <template #actions="{ item }">
        <td>
          <div class="row justify-content-center">
            <CButton
              color="success"
              v-if="checkPermission('edit_line_users')"
              class="text-white btn-sm mt-2 mr-1"
              @click="edit(item.id)"
              ><i class="cil-pencil"></i><CIcon name="cil-pencil"
            /></CButton>
            <c-button
              color="danger"
              v-if="checkPermission('delete_line_users')"
              class="btn-sm mt-2 mr-1"
              @click="
                $root
                  .$confirm('Delete', 'Do you want to delete this record?', {
                    color: 'red',
                    width: 290,
                    zIndex: 200,
                  })
                  .then((confirmed) => {
                    if (confirmed) {
                      deleteLineUser(item);
                    }
                  })
              "
              ><c-icon name="cil-trash"
            /></c-button>
          </div>
        </td>
      </template>
    </CDataTable>
  </div>
</template>
<script>
import moment from "moment";

import vSelect from "vue-select";
export default {
  components: {
    vSelect,
  },
  props: {
    line_id: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      users: [],
      line_users: [],
      line_users_fields: [
        "id",
        "line name",
        "user_name",
        "from_date",
        "to_date",
        "actions",
      ],
      line_user: {
        id: 0,
        line_id: 0,
        user_id: "",
        from_date: "",
        to_date: "",
      },
      lineUserIsEditing: false,
      fromDate: new Date().toISOString().slice(0, 10),
      lineusers: [],
    };
  },
  emits: ["getLineUsers"],
  methods: {
    format_date: function (value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    edit_date_format: function (value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    initialize() {
      axios
        .get("/api/line-users")
        .then((response) => {
          this.users = response.data.users;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineUsers() {
      axios
        .get(`/api/get_line_users/${this.line_id}`)
        .then((response) => {
          this.line_users = response.data.line_users;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    sendLineUsers() {
      axios
        .get(`/api/lines/${this.line_id}/users`)
        .then((response) => {
          this.lineusers = Object.entries(response.data.lineusers).map(
            ([key, value]) => {
              return { id: value.id, name: value.fullname };
            }
          );
          this.$emit("getLineUsers", { lineusers: this.lineusers });
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    reset() {
      this.line_user = {
        line_id: this.line_id,
        user_id: "",
        from_date: "",
        to_date: "",
      };
    },
    store() {
      axios
        .post(`/api/lines/${this.line_id}/users`, {
          line_id: this.line_id,
          user_id: this.line_user.user_id,
          from_date: this.crmDateFormat(this.line_user.from_date),
          to_date: this.line_user.to_date
            ? this.crmDateFormat(this.line_user.to_date)
            : "",
        })
        .then((response) => {
          this.reset();
          this.flash("Line User Created Successfully");
          this.sendLineUsers();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      //Refresh table
      this.getLineUsers();
    },
    edit(id) {
      this.lineUserIsEditing = true;
      this.scrollToTop();
      axios
        .get(`/api/line-users/${id}`)
        .then((response) => {
          this.line_user.id = response.data.line_user.id;
          this.line_user.line_id = response.data.line_user.line_id;
          this.line_user.user_id = response.data.line_user.user_id;
          this.line_user.from_date = this.edit_date_format(
            response.data.line_user.from_date
          );
          this.line_user.to_date = this.edit_date_format(
            response.data.line_user.to_date
          );
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    update() {
      axios
        .put(`/api/lines/${this.line_id}/user/${this.line_user.id}`, {
          id: this.line_user.id,
          line_id: this.line_user.line_id,
          user_id: this.line_user.user_id,
          from_date: this.crmDateFormat(this.line_user.from_date),
          to_date: this.line_user.to_date
            ? this.crmDateFormat(this.line_user.to_date)
            : "",
        })
        .then((response) => {
          this.lineUserIsEditing = false;
          this.flash("Line User Updated Successfully");
          this.getLineUsers();
          this.sendLineUsers();
          this.reset();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removeLineUser(item) {
      const index = this.line_users.findIndex((type) => item.id == type.id);
      this.line_users.splice(index, 1);
    },
    deleteLineUser(item) {
      axios
        .delete(`/api/line-users/${item.id}`)
        .then((res) => {
          this.removeLineUser(item);
          this.flash("Line User Deleted Successfully");
          this.sendLineUsers();
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
    this.initialize();
    this.getLineUsers();
    this.sendLineUsers();
  },
};
</script>
