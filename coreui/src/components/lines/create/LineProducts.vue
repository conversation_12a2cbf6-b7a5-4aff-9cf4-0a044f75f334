<template>
  <div>
    <CCard no-header>
      <CCardBody>
        <div class="row">
          <div class="col-3">
            <CFormGroup v-if="!lineProductIsEditing">
              <template #label> Product </template>
              <template #input>
                <v-select
                  v-model="line_product.product_id"
                  :options="products"
                  label="name"
                  :value="0"
                  :reduce="(product) => product.id"
                  placeholder="Select Product"
                  multiple
                  class="mt-2"
                />
              </template>
            </CFormGroup>
            <CFormGroup v-if="lineProductIsEditing">
              <template #label> Product </template>
              <template #input>
                <v-select
                  v-model="line_product.product_id"
                  :options="products"
                  label="name"
                  :value="0"
                  :reduce="(product) => product.id"
                  placeholder="Select Product"
                  class="mt-2"
                />
              </template>
            </CFormGroup>
          </div>
          <div class="col-3">
            <CInput
              label="Ratio"
              type="number"
              placeholder="Ratio"
              v-model="line_product.ratio"
            ></CInput>
          </div>
          <div class="col-3">
            <CInput
              label="From"
              type="date"
              placeholder="From"
              v-model="line_product.from_date"
            ></CInput>
          </div>
          <div class="col-3">
            <CInput
              label="To"
              type="date"
              placeholder="To"
              v-model="line_product.to_date"
            ></CInput>
          </div>
        </div>
      </CCardBody>
      <CCardFooter>
        <CButton color="primary" v-if="!lineProductIsEditing" @click="store"
          >Create</CButton
        >
        <CButton color="primary" v-if="lineProductIsEditing" @click="update"
          >Update</CButton
        >
        <CButton color="default" :to="{ name: 'lines' }">Cancel</CButton>
      </CCardFooter>
    </CCard>

    <CDataTable
      hover
      striped
      sorter
      tableFilter
      footer
      itemsPerPageSelect
      :items="line_products"
      :fields="line_products_fields"
      :items-per-page="100"
      :active-page="1"
      :responsive="true"
      pagination
      thead-top
    >
      <template #from_date="{ item }">
        <td>{{ format_date(item.from_date) }}</td>
      </template>
      <template #to_date="{ item }">
        <td>{{ format_date(item.to_date) }}</td>
      </template>
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ line_products.length }}
        </td>
      </template>
      <template #actions="{ item }">
        <td>
          <div class="row justify-content-center">
            <CButton
              color="success"
              v-if="checkPermission('edit_line_products')"
              class="text-white btn-sm mt-2 mr-1"
              @click="edit(item.id)"
              ><i class="cil-pencil"></i><CIcon name="cil-pencil"
            /></CButton>
            <c-button
              color="danger"
              v-if="checkPermission('delete_line_products')"
              class="btn-sm mt-2 mr-1"
              @click="
                $root
                  .$confirm('Delete', 'Do you want to delete this record?', {
                    color: 'red',
                    width: 290,
                    zIndex: 200,
                  })
                  .then((confirmed) => {
                    if (confirmed) {
                      deleteLineProduct(item);
                    }
                  })
              "
              ><c-icon name="cil-trash"
            /></c-button>
          </div>
        </td>
      </template>
    </CDataTable>
  </div>
</template>
<script>
import moment from "moment";

import vSelect from "vue-select";
export default {
  components: {
    vSelect,
  },
  props: {
    line_id: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      fromDate: new Date().toISOString().slice(0, 10),
      line_products: [],
      lineProductIsEditing: false,
      line_product: {
        id: 0,
        line_id: 0,
        product_id: "",
        ratio: "",
        from_date: "",
        to_date: "",
      },
      line_products_fields: [
        "id",
        "line name",
        "product name",
        "ratio",
        "from_date",
        "to_date",
        "actions",
      ],
      products: [],
    };
  },
  methods: {
    format_date: function (value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    edit_date_format: function (value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    initialize() {
      axios
        .get("/api/line-products")
        .then((response) => {
          this.products = response.data.products;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineProducts() {
      axios
        .get(`/api/lines/${this.line_id}/products`)
        .then((response) => {
          this.line_products = response.data.line_products;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    reset() {
      this.line_product = {
        line_id: this.line_id,
        product_id: "",
        ratio: "",
        from_date: "",
        to_date: "",
      };
    },
    store() {
      axios
        .post(`/api/lines/${this.line_id}/products`, {
          line_id: this.line_id,
          product_id: this.line_product.product_id,
          ratio: this.line_product.ratio,
          from_date: this.crmDateFormat(this.line_product.from_date),
          to_date: this.line_product.to_date
            ? this.crmDateFormat(this.line_product.to_date)
            : "",
        })
        .then((response) => {
          this.reset();
          this.flash("Line Products Created Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      this.getLineProducts();
    },
    edit(id) {
      this.lineProductIsEditing = true;
      axios
        .get(`/api/line-products/${id}`)
        .then((response) => {
          this.line_product.id = response.data.line_product.id;
          this.line_product.line_id = response.data.line_product.line_id;
          this.line_product.product_id = response.data.line_product.product_id;
          this.line_product.ratio = response.data.line_product.ratio;
          this.line_product.from_date = this.edit_date_format(
            response.data.line_product.from_date
          );
          this.line_product.to_date = this.edit_date_format(
            response.data.line_product.to_date
          );
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    update() {
      axios
        .put(`/api/lines/${this.line_id}/products/${this.line_product.id}`, {
          id: this.line_product.id,
          line_id: this.line_product.line_id,
          product_id: this.line_product.product_id,
          ratio: this.line_product.ratio,
          from_date: this.crmDateFormat(this.line_product.from_date),
          to_date: this.line_product.to_date
            ? this.crmDateFormat(this.line_product.to_date)
            : "",
        })
        .then((response) => {
          this.lineProductIsEditing = false;
          this.flash("Line Product Updated Successfully");
          this.getLineProducts();
          this.reset();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removeLineProduct(item) {
      const index = this.line_products.findIndex((type) => item.id == type.id);
      this.line_products.splice(index, 1);
    },
    deleteLineProduct(item) {
      axios
        .delete(`/api/line-products/${item.id}`)
        .then((res) => {
          this.removeLineProduct(item);
          this.flash("Line Product Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
    this.initialize();
    this.getLineProducts();
  },
};
</script>
