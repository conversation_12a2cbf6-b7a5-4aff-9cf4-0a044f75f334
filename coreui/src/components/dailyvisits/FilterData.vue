<template>
  <c-card>
    <c-card-header>
      <span class="float-left"> Create Plan Visits </span>
      <button class="btn btn-sm btn-info policy-btn rounded-circle pr-2 pl-2 float-right d-flex "
        @click="showPolicy = !showPolicy"> ??</button>
    </c-card-header>
    <c-card-body>
      <transition name="bounce">
        <div class=" mb-3 policy" v-if="showPolicy">
          <div class="alert alert-info mt-2 ">
            <ul class="list-group">
              <li class="list-group-item" v-for="(item, index) in policies" :key="index">{{ item.policy }}</li>
            </ul>
          </div>
        </div>
      </transition>
      <c-tabs>
        <c-tab active>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-description" /> Main
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <CFormGroup>
                    <template #label>
                      <strong>Line</strong> <strong><span style="color:red">*</span></strong>
                    </template>
                    <template #input>
                      <v-select v-model="line_id" :options="lines" label="name" :value="0" required
                        :reduce="(line) => line.id" placeholder="Select Line" class="mt-2"
                        @input="getLineDivisions()" />
                    </template>
                  </CFormGroup>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8" v-if="line_id">
                  <c-form-group>
                    <template #label> Division <strong><span style="color:red">*</span></strong></template>
                    <template #input>
                      <input label="All" v-if="divisions.length != 0" class="m-1" type="checkbox" id="divisions"
                        v-model="checkAllDivisions" title="Check All Divisions" @change="checkAllDivs" />
                      <label for="divisions" v-if="divisions.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="division_ids" :options="divisions" label="name" :value="0"
                        :reduce="(division) => division.id" placeholder="Select Division" class="mt-2" multiple
                        @input="getLineDivisionBricks" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8" v-if="line_id">
                  <c-form-group>
                    <template #label>
                      <strong>Brick</strong> <strong><span style="color:red">*</span></strong>
                    </template>
                    <template #input>
                      <input label="All" v-if="bricks.length != 0" class="m-1" type="checkbox" id="bricks"
                        v-model="checkAllBricks" title="Check All Bricks" @change="checkAllBrick" />
                      <label for="bricks" v-if="bricks.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="brick_ids" :options="bricks" label="name" :value="0" required
                        :reduce="(brick) => brick.id" placeholder="Select Brick" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
              </div>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8" v-if="from_date">
                  <CFormGroup>
                    <template #label>
                      <strong>From Date</strong> <strong><span style="color:red">*</span></strong>
                    </template>
                    <template #input>
                      <input type="date" v-model="from_date" :max="max_to_date" :min="min_from_date"
                        class="form-control mt-2" />
                    </template>
                  </CFormGroup>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8" v-if="to_date">
                  <CFormGroup>
                    <template #label>
                      <strong>To Date</strong> <strong><span style="color:red">*</span></strong>
                    </template>
                    <template #input>
                      <input type="date" v-model="to_date" :max="max_to_date" :min="min_from_date"
                        class="form-control mt-2" />
                    </template>
                  </CFormGroup>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-description" /> Account Types
            & Specialities & Classes
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Account Type </template>
                    <template #input>
                      <input label="All" v-if="types.length != 0" class="m-1" type="checkbox" id="types"
                        v-model="allAccountTypes" title="Check All Types" @change="checkAllAccounts" />
                      <label for="types" v-if="types.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="type_ids" :options="types" label="name" :value="0" :reduce="(type) => type.id"
                        placeholder="Select Account Type" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Speciality </template>
                    <template #input>
                      <input label="All" v-if="specialities.length != 0" class="m-1" id="specialities" type="checkbox"
                        v-model="checkAllSpecialities" title="Check All Specialities" @change="checkAllSpech" />
                      <label for="specialities" v-if="specialities.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="speciality_ids" :options="specialities" label="name" :value="0"
                        :reduce="(speciality) => speciality.id" placeholder="Select Speciality" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Classes </template>
                    <template #input>
                      <input label="All" v-if="classes.length != 0" class="m-1" id="classes" type="checkbox"
                        v-model="checkAllClasses" title="Check All Classes" @change="checkAllClass" />
                      <label for="classes" v-if="classes.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="class_id" :options="classes" label="name" :value="0"
                        :reduce="(classe) => classe.id" placeholder="Select Class" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-description" /> Coverage & Frequency
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Coverage </template>
                    <template #input>
                      <v-select v-model="coverage" :options="['All', 'Covered', 'Uncovered']"
                        placeholder="Select Coverage" class="mt-2" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Frequency </template>
                    <template #input>
                      <v-select v-model="frequency" :options="['Below', 'Meet', 'Above']" placeholder="Select Frequency"
                        class="mt-2" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Per Groups </template>
                    <template #input>
                      <v-select v-model="perGroup" :options="['No', 'Yes']" placeholder="Select Groups" class="mt-2" />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
      </c-tabs>
    </c-card-body>
    <c-card-footer>
      <CButton :disabled="line_id == null &&
        division_ids.length == 0 &&
        brick_ids.length == 0 &&
        speciality_ids.length == 0
        " color="primary" @click="show()" style="float: right">Show</CButton>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      showPolicy: false,
      policies: [],
      line_id: null,
      coverage: 'All',
      frequency: null,
      division_ids: [],
      brick_ids: [],
      type_ids: [],
      speciality_ids: [],
      lines: [],
      perGroup: 'No',
      divisions: [],
      bricks: [],
      types: [],
      specialities: [],
      classes: [],
      class_id: [],
      checkAllClasses: true,
      checkAllSpecialities: true,
      checkAllDivisions: false,
      allAccountTypes: true,
      checkAllBricks: false,
      from_date: null,
      to_date: null,
      min_from_date: null,
      max_to_date: null,
    };
  },
  emits: ["Schedule"],
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: null,
          to: null,
          data: ['lines', 'accountTypes']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.line_id = this.lines[0].id;
          this.getLineDivisions();
          this.types = response.data.data.accountTypes;
          this.type_ids = this.types.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineDivisions() {
      axios
        .get(`/api/get-line-division/${this.line_id}`)
        .then((response) => {
          this.divisions = response.data.divisions;
          this.division_ids = this.divisions.map((item) => item.id);
          this.specialities = response.data.specialities;
          this.classes = response.data.classes;
          this.class_id = this.classes.map((item) => item.id);
          this.speciality_ids = this.specialities.map((item) => item.id);
          this.from_date = response.data.user_plan_start_day;
          this.min_from_date = response.data.user_plan_start_day;
          this.to_date = response.data.to_date;
          this.max_to_date = response.data.final_user_plan_end_day;
          this.getLineDivisionBricks();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineDivisionBricks() {
      axios
        .post(`/api/get-line-division-bricks/`, {
          divisions: this.division_ids,
        })
        .then((response) => {
          this.bricks = response.data.data;
          // this.brick_ids = this.bricks.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllAccounts() {
      if (this.allAccountTypes)
        this.type_ids = this.types.map((item) => item.id);
      if (this.allAccountTypes == false) this.type_ids = [];
    },
    checkAllDivs() {
      if (this.checkAllDivisions) {
        this.division_ids = this.divisions.map((item) => item.id);
        this.getLineDivisionBricks();
      }
      if (this.checkAllDivisions == false) {
        this.division_ids = [];
        this.brick_ids = [];
        this.checkAllBricks = false;
      }
    },
    checkAllSpech() {
      if (this.checkAllSpecialities)
        this.speciality_ids = this.specialities.map((item) => item.id);
      if (this.checkAllSpecialities == false) this.speciality_ids = [];
    },
    checkAllClass() {
      if (this.checkAllClasses)
        this.class_id = this.classes.map((item) => item.id);
      if (this.checkAllClasses == false) this.class_id = [];
    },
    checkAllBrick() {
      if (this.checkAllBricks)
        this.brick_ids = this.bricks.map((item) => item.id);
      if (this.checkAllBricks == false) this.brick_ids = [];
    },
    getPolicies() {
      axios
        .get("/api/plan-visit-policies/")
        .then((response) => {
          this.policies = response.data.data.policies;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    show() {
      let planFilter = {
        line: this.line_id,
        coverage: this.coverage,
        frequency: this.frequency,
        perGroup: this.perGroup,
        divisions: this.division_ids,
        bricks: this.brick_ids,
        types: this.type_ids,
        specialities: this.speciality_ids,
        classes: this.class_id,
        view: this.class_id,
        fromDate: this.from_date,
        toDate: this.to_date,
      }
      this.$emit("Schedule", planFilter);
    },
  },
  created() {
    this.initialize();
    this.getPolicies();
  },
};
</script>
<style scoped>
.bounce-enter-active {
  animation: bounce-in 0.5s;
}

.bounce-leave-active {
  animation: bounce-in 0.5s reverse;
}

@keyframes bounce-in {
  0% {
    transform: scale(0);
  }

  50% {
    transform: scale(1.25);
  }

  100% {
    transform: scale(1);
  }
}
</style>
