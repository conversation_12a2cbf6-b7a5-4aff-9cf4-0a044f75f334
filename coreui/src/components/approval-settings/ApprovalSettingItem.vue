<template>
  <div class="form-row form-group">
    <div class="col-3">
      <label><strong>{{ approvable.name }}</strong></label>
    </div>
    <div class="col-1">
      <input type="checkbox" v-model="checked" class="mr-1" :true-value="approvable.id" :false-value="null"
        @change="setValue" />Approve
    </div>
    <div class="col-1" v-if="checked">
      <input type="checkbox" v-model="showData" class="mr-1" @change="setValue" />show
    </div>
    <div class="col-1" v-if="checked">
      <input type="checkbox" v-model="required" class="mr-1" @change="setValue" />Required
    </div>
    <div class="col-1" v-if="checked">
      <c-form-group>
        <template #input>
          <v-select v-model="flow" :options="countApprovables" placeholder="Flow" @input="setValue" />
        </template>
      </c-form-group>
    </div>
    <div class="col-1" v-if="checked">
      <c-form-group>
        <template #input>
          <v-select v-model="num_days" :options="days" placeholder="Days" @input="setValue" />
        </template>
      </c-form-group>
    </div>
  </div>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["setNewApprovable"],
  props: {
    approvable: {
      type: Object,
      required: true,
    },
    countApprovables: {
      type: Array,
      default: () => [],
    },
    checkedItems: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      checked: null,
      required: null,
      showData: false,
      flow: null,
      num_days: null,
      days: [1, 2, 3, 4, 5, 6, 7]
    };
  },
  methods: {
    initialize() {
      this.required = false;
      this.showData = false;
      let checkedItem = this.checkedItems.find(
        (item) => item.approvable_id === this.approvable.id
      );
      console.log(checkedItem);
      if (checkedItem) {
        this.checked = checkedItem.approvable_id;
        this.showData = checkedItem.show_data;
        this.required = checkedItem.required;
        this.flow = checkedItem.flow;
        this.num_days = checkedItem.num_days;
      }
    },
    setValue() {
      let obj = {
        approvable_id: this.checked,
        replace: this.approvable.id,
        required: this.required,
        showData: this.showData,
        flow: this.flow,
        num_days: this.num_days,
      };
      this.$emit("setNewApprovable", obj);
    },
  },
  created() {
    this.initialize();
  },
};
</script>
