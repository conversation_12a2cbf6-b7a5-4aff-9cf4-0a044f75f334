<template>
    <c-row v-if="kpi">
        <c-col>
            <c-card>
                <c-card-header>
                    Edit KPI : {{ kpi.name }}
                </c-card-header>
            </c-card>
            <div>
                <c-card no-header>
                    <CCardBody>
                        <div class="row">
                            <div class="col">
                                <CFormGroup>
                                    <template #label> Line </template>
                                    <template #input>
                                        <v-select v-model="fieldDays.line" :options="lines" label="name" :value="0"
                                            :reduce="(line) => line.id" placeholder="Select Line" class="mt-2" />
                                    </template>
                                </CFormGroup>
                            </div>
                            <div class="col">
                                <CFormGroup>
                                    <template #label> Role </template>
                                    <template #input>
                                        <v-select v-model="fieldDays.role" :options="roles" label="name"
                                            :reduce="(role) => role.id" placeholder="Select Role" class="mt-2" />
                                    </template>
                                </CFormGroup>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <CInput label="Minimum" type="number" placeholder="minimum" v-model="fieldDays.minimum">
                                </CInput>
                            </div>
                        </div>
                    
                        <div class="row" v-if="fieldDays.line">
                            <div class="col">
                                <CFormGroup>
                                    <template #label> Copy To </template>
                                    <template #input>
                                        <v-select v-model="copyTolines" :options="linesToSelectFrom" label="name" :value="0"
                                            multiple :reduce="(line) => line.id" placeholder="Select Line" class="mt-2" />
                                    </template>
                                </CFormGroup>
                            </div>
                        </div>
                    </CCardBody>
                    <CCardFooter>
                        <CButton color="primary" @click="store">Create</CButton>
                        <CButton color="default" :to="{ name: 'kpis-field-days-kpi' }">Cancel</CButton>
                    </CCardFooter>
                </c-card>
            </div>

        </c-col>
    </c-row>
</template>
<script>
import vSelect from "vue-select";
export default {
    components: {
        vSelect,
    },
    data() {
        return {
            fields: [
                'line',
                'role',
                'minimum',
                'ratio',
                'actions'
            ],
            lines: [],
            copyTolines: [],
            roles: [],
            kpi: null,
            fieldDays: {
                id: null,
                role: null,
                line: null,
                minimum: 0,
                items:[]
            },
        }
    },
    computed: {
        selectedLine() {
            return this.kpiRatio.line
        },
        linesToSelectFrom() {
            return this.lines.filter((line) => line.id !== this.kpiRatio.line)
        }
    },
    watch: {
        selectedLine(newValue) {
            this.roles = this.lines.find(line => line.id == this.kpiRatio.line).roles ?? [];
        }

    },
    methods: {
        initialize() {
            axios.get(`/api/kpis/${this.$route.params.id}`)
                .then(res => {
                    this.lines = res.data.data.lines
                })
        },
        getLine(id) {
            return id ? this.lines.find(line => line.id == id)?.name : ""
        },
        getRole(lineId, roleId) {
            return lineId ? this.lines.find(line => line.id == lineId)?.roles.find(role => role.id == roleId)?.name : ""
        },
        store() {
            axios.post(`/api/kpis/${this.$route.params.id}`, {
                id: this.$route.params.id,
                line_id: this.fieldDays.line,
                roleable_id: this.fieldDays.role.split('_')[0],//this.fieldDays.role,
                roleable_type: this.fieldDays.role.split('_')[1],
                ratio: this.fieldDays.ratio,
                minimum: this.fieldDays.minimum,
                lines_to_copy: this.copyTolines
            })
                .then(res => {
                    this.kpi = res.data.data.kpi
                    this.lines = res.data.data.lines
                    this.initialize();
                    this.clearData();
                })
                .catch(this.showErrorMessage)
        },
        clearData() {
            this.fieldDays.line = null;
            this.fieldDays.role = null;
            this.fieldDays.minimum = null;
            this.fieldDays.ratio = null;
        }
    },
    created() {
        this.initialize();
    }
}
</script>