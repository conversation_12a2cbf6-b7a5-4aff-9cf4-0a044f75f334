<template>
  <div>
    <validation-observer ref="form" v-slot="{ invalid }">
      <CCard no-header>
        <CCardBody>
          <h4 v-if="product" class="pb-3">
            Add Presentation To:
            <span class="font-weight-bold">{{ product.name }}</span>
          </h4>
          <div class="row">
            <div class="col">
              <CFormGroup>
                <template #label>
                  Presentation
                </template>
                <template #input>
                  <validation-provider
                    v-slot="{ errors, dirty, valid }"
                    name="Presentation"
                    rules="required"
                  >
                    <v-select
                      v-model="productPresentation.presentation_id"
                      :options="presentations"
                      label="name"
                      :value="0"
                      :reduce="presentation => presentation.id"
                      :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                      placeholder="Select option"
                      multiple
                      class="mt-2"
                    />
                    <div class="invalid-feedback">{{ errors[0] }}</div>
                  </validation-provider>
                </template>
              </CFormGroup>
            </div>
          </div>
        </CCardBody>
        <CCardFooter>
          <button
            class="btn btn-primary rounded"
            :style="`cursor: ${invalid ? 'not-allowed' : ''}`"
            :disabled="invalid"
            @click="create"
          >
            Create
          </button>
          <CButton :to="{name:'products'}" color="default">Cancel</CButton>
        </CCardFooter>
      </CCard>
      <product-presentations v-if="product" />
    </validation-observer>
  </div>
</template>

<script>
import { mapState, mapActions } from "vuex";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import ProductPresentations from "./ProductPresentations";
import { ValidationProvider, ValidationObserver, extend } from "vee-validate";
import { required } from "vee-validate/dist/rules";
extend("required", { ...required, message: "The {_field_} field is required" });
extend("after", {
  validate(toDate, { fromDate }) {
    return toDate >= fromDate;
  },
  params: ["fromDate"],
  message: "The {_field_} field must be after or equal {fromDate}."
});

export default {
  components: {
    vSelect,
    ProductPresentations,
    ValidationObserver,
    ValidationProvider
  },
  data() {
    return {
      productPresentation: {}
    };
  },
  computed: {
    ...mapState("product", ["product", "presentations"])
  },
  methods: {
    ...mapActions("product", ["getProductPresentations"]),
    create() {
      const { id } = this.product;

      axios
        .post(`/api/products/${id}/presentations`, {
          ...this.productPresentation,
          product_id: id
        })
        .then(() => {
          this.getProductPresentations().then(this.reset);
        })
        .catch(() => {
          this.flash("oOOops, something went wrong", "error");
        });
    },
    reset() {
      this.productPresentation = {};
      this.$refs.form.reset();
      this.scrollToTop();
      this.flash("Product Presentation Created successfully.");
    }
  },
};
</script>
