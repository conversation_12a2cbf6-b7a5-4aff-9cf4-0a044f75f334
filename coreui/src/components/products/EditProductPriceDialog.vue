<template>
  <validation-observer ref="form" v-slot="{ invalid }">
    <CModal
      v-if="updatedPrice"
      :title="`Edit ${updatedPrice.name}`"
      color="primary"
      :show.sync="dialog"
    >
      <div class="row">
        <div class="col-sm-12">
          <CFormGroup>
            <template #label>
              Distributor
            </template>
            <template #input>
              <validation-provider
                v-slot="{ errors, dirty, valid }"
                name="Distributor"
                rules="required"
              >
                <v-select
                  v-model="updatedPrice.distributor_id"
                  :options="distributors"
                  label="name"
                  :value="0"
                  :reduce="distributor => distributor.id"
                  :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                  placeholder="Select option"
                  class="mt-2"
                />
                <div class="invalid-feedback">{{ errors[0] }}</div>
              </validation-provider>
            </template>
          </CFormGroup>
        </div>
        <div class="col-sm-12">
          <label for="from_date">From</label>
          <validation-provider
            v-slot="{ errors, dirty, valid }"
            name="From Date"
            rules="required"
          >
            <input
              type="date"
              id="from_date"
              :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
              class="form-control"
              v-model="updatedPrice.from_date"
              placeholder="From"
            />
            <div class="invalid-feedback">{{ errors[0] }}</div>
          </validation-provider>
        </div>
        <div class="col-sm-12">
          <validation-provider
            v-slot="{ errors, dirty, valid }"
            name="To Date"
            :rules="`after:${updatedPrice.from_date}`"
          >
            <label for="to_date">To</label>
            <input
              type="date"
              id="to_date"
              :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
              class="form-control"
              v-model="updatedPrice.to_date"
              placeholder="To"
            />
            <div class="invalid-feedback">{{ errors[0] }}</div>
          </validation-provider>
        </div>
        <div class="col-sm-12">
          <CInput
            label="Selling Price"
            type="text"
            placeholder="Selling Price"
            name="product_price.selling_price"
            v-model="updatedPrice.selling_price"
          ></CInput>
        </div>
        <div class="col-sm-12">
          <CInput
            label="Avarge Price"
            type="text"
            placeholder="Avg Price"
            name="product_price.avg_price"
            v-model="updatedPrice.avg_price"
          ></CInput>
        </div>
        <div class="col-sm-12">
          <CInput
            label="Avarge Tender Price"
            type="text"
            placeholder="Avg Tender Price"
            name="product_price.avg_tender_price"
            v-model="updatedPrice.avg_tender_price"
          ></CInput>
        </div>
        <div class="col-sm-12">
          <CInput
            label="Avarge Target Price"
            type="text"
            placeholder="Avg Target Price"
            name="product_price.avg_target_price"
            v-model="updatedPrice.avg_target_price"
          ></CInput>
        </div>
      </div>
      <template #footer>
        <CButton @click="close" color="default">Cancel</CButton>
        <button
          class="btn btn-primary rounded"
          :style="`cursor: ${invalid ? 'not-allowed' : ''}`"
          :disabled="invalid"
          @click="update"
        >
          Edit
        </button>
      </template>
    </CModal>
  </validation-observer>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { mapState, mapActions } from "vuex";
import { ValidationProvider, ValidationObserver, extend } from "vee-validate";
import { required } from "vee-validate/dist/rules";
extend("required", { ...required, message: "The {_field_} field is required" });
extend("after", {
  validate(toDate, { fromDate }) {
    return toDate >= fromDate;
  },
  params: ["fromDate"],
  message: "The {_field_} field must be after or equal {fromDate}."
});

export default {
  components: {
    vSelect,
    ValidationObserver,
    ValidationProvider
  },
  data() {
    return {
      updatedPrice: null
    };
  },
  computed: {
    ...mapState("product", ["distributors", "product"])
  },
  methods: {
    ...mapActions("product", ["getProductPrices"]),
    open(distributor) {
      this.dialog = true;
      this.updatedPrice = { ...distributor };
    },
    close() {
      this.dialog = false;
      this.updatedPrice = null;
    },
    update() {
      const { id } = this.updatedPrice;

      axios
        .put(`/api/product-distributors/${id}`, {
          ...this.updatedPrice,
          product_id: this.product.id
        })
        .then(() => {
          this.getProductPrices().then(() => {
            this.close();
            this.scrollToTop();
            this.flash("Updated successfully.");
          });
        });
    }
  }
};
</script>
