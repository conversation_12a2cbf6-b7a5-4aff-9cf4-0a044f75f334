<template>
  <div>
    <validation-observer ref="form" v-slot="{ invalid }">
      <CCard no-header>
        <CCardBody>
          <h4 v-if="product" class="pb-3">
            Add Ceiling To:
            <span class="font-weight-bold">{{ product.name }}</span>
          </h4>
          <div class="row">
            <div class="col-3">
              <CFormGroup>
                <template #label> units </template>
                <template #input>
                  <validation-provider
                    v-slot="{ errors, dirty, valid }"
                    name="units"
                    rules="required"
                  >
                    <c-input
                      v-model="productCeiling.units"
                      class="mt-2"
                      type="number"
                      :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                    />
                    <div class="invalid-feedback">{{ errors[0] }}</div>
                  </validation-provider>
                </template>
              </CFormGroup>
            </div>
            <div class="col-3">
              <CFormGroup>
                <template #label> sales Types </template>
                <template #input>
                  <validation-provider
                    v-slot="{ errors, dirty, valid }"
                    name="message"
                    rules="required"
                  >
                    <v-select
                      v-model="productCeiling.type"
                      :options="sales"
                      label="message"
                      :value="0"
                      :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                      placeholder="Select Type"
                      multiple
                      class="mt-2"
                    />
                    <div class="invalid-feedback">{{ errors[0] }}</div>
                  </validation-provider>
                </template>
              </CFormGroup>
            </div>
            <div class="col-3">
              <label for="from_date">From</label>
              <validation-provider
                v-slot="{ errors, dirty, valid }"
                name="From Date"
                rules="required"
              >
                <input
                  type="date"
                  id="from_date"
                  :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                  class="form-control"
                  v-model="productCeiling.from_date"
                  placeholder="From"
                />
                <div class="invalid-feedback">{{ errors[0] }}</div>
              </validation-provider>
            </div>
            <div class="col-3">
              <validation-provider
                v-slot="{ errors, dirty, valid }"
                name="To Date"
                :rules="`after:${productCeiling.from_date}`"
              >
                <label for="to_date">To</label>
                <input
                  type="date"
                  id="to_date"
                  :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                  class="form-control"
                  v-model="productCeiling.to_date"
                  placeholder="To"
                />
                <div class="invalid-feedback">{{ errors[0] }}</div>
              </validation-provider>
            </div>
          </div>
        </CCardBody>
        <CCardFooter>
          <button
            class="btn btn-primary rounded"
            :style="`cursor: ${invalid ? 'not-allowed' : ''}`"
            :disabled="invalid"
            @click="create"
          >
            Create
          </button>
          <CButton :to="{ name: 'products' }" color="default">Cancel</CButton>
        </CCardFooter>
      </CCard>
      <product-ceilings v-if="product" />
    </validation-observer>
  </div>
</template>

<script>
import { mapState , mapActions } from "vuex";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { ValidationProvider, ValidationObserver, extend } from "vee-validate";
import { required } from "vee-validate/dist/rules";
import ProductCeilings from "./ProductCeilings.vue";
extend("required", { ...required, message: "The {_field_} field is required" });

export default {
  components: {
    vSelect,
    ProductCeilings,
    ValidationObserver,
    ValidationProvider,
  },
  data() {
    return {
      productCeiling: {},
      sales: ["CR_TARGET", "CR_SALES"],
    };
  },
  computed: {
    ...mapState("product", ["product"]),
  },
  methods: {
    ...mapActions("product", ["getProductCeilings"]),
    create() {
      const { id } = this.product;

      axios
        .post(`/api/products/${id}/ceilings`, {
          ...this.productCeiling,
          product_id: id,
        })
        .then(() => {
          this.getProductCeilings().then(this.reset);
        })
        .catch(() => {
          this.flash("oOOops, something went wrong", "error");
        });
    },
    reset() {
      this.productCeiling = {};
      this.$refs.form.reset();
      this.scrollToTop();
      this.flash("Product Ceiling Created successfully.");
    },
  },
};
</script>
