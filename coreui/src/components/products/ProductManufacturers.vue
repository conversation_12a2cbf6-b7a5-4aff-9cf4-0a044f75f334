<template>
  <div>
    <CDataTable
      hover
      striped
      sorter
      tableFilter
      footer
      :itemsPerPageSelect="perPageSelect"
      :items="productManufacturers"
      :fields="fields"
      :items-per-page="100"
      :active-page="1"
      :responsive="true"
      :table-filter="{ external: true, lazy: true }"
      pagination
      thead-top
    >
      <template #from_date="{item}">
        <td>{{ format_date(item.from_date) }}</td>
      </template>
      <template #to_date="{item}">
        <td>
          <span :class="item.to_date ? '' : 'badge badge-danger p-1'">{{
            item.to_date ? format_date(item.to_date) : "Not Registered"
          }}</span>
        </td>
      </template>
      <template slot="thead-top">
        <td style="border-top:none;"><strong>Total</strong></td>
        <td style="border-top:none;" class="text-xs-right">
          {{ manufacturersCount }}
        </td>
      </template>
    </CDataTable>
    <delete-confirmation-dialog
      ref="deleteManufacturerConfirmation"
      @DeleteConfirmed="deleteManufacturer"
    />
    <edit-product-manufacturers-dialog ref="editManufacturerDialog" />
  </div>
</template>

<script>
import { mapActions, mapGetters, mapState } from "vuex";
import DeleteConfirmationDialog from "../common/DeleteConfirmationDialog.vue";
import moment from "moment";
import EditProductManufacturersDialog from "./EditProductManufacturersDialog.vue";

export default {
  components: { DeleteConfirmationDialog, EditProductManufacturersDialog },
  data() {
    return {
      fields: ["id","product", "manufacturer", "from_date", "to_date"],
      perPageSelect: {
        values: ["100", "200", "300", "400", "500"],
        external: true
      },
      manufacturerToDelete: null
    };
  },
  computed: {
    ...mapState("product", ["productManufacturers"]),
    ...mapGetters("product", ["manufacturersCount"])
  },
  created() {
    this.initialize();
  },
  methods: {
    ...mapActions("product", ["getProductManufacturers"]),
    initialize() {
      this.getProductManufacturers();
    },

    format_date: function(value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },

    confirmDelete(manufacturer) {
      this.$refs.deleteManufacturerConfirmation.open();
      this.manufacturerToDelete = manufacturer;
    },

    deleteManufacturer() {
      const { id } = this.manufacturerToDelete;

      axios
        .delete(`/api/product-manufacturers/${id}`)
        .then(() => {
          this.getProductManufacturers().then(() => {
            this.flash("Deleted Successfully.");
          });
        })
        .catch(() => {
          this.flash("oOOops, something went wrong.", "error");
        })
        .then(() => {
          this.$refs.deleteManufacturerConfirmation.close();
          this.scrollToTop();
        });
    }
  }
};
</script>
