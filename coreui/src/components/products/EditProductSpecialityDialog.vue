<template>
  <validation-observer ref="form" v-slot="{ invalid }">
    <CModal
      v-if="updatedSpeciality"
      :title="`Edit ${updatedSpeciality.name}`"
      color="primary"
      :show.sync="dialog"
    >
      <div class="row">
        <div class="col-sm-12">
          <CFormGroup>
            <template #label>
              Speciality
            </template>
            <template #input>
              <validation-provider
                v-slot="{ errors, dirty, valid }"
                name="Speciality"
                rules="required"
              >
                <v-select
                  v-model="updatedSpeciality.speciality_id"
                  :options="specialities"
                  label="name"
                  :value="0"
                  :reduce="speciality => speciality.id"
                  :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                  placeholder="Select option"
                  class="mt-2"
                />
                <div class="invalid-feedback">{{ errors[0] }}</div>
              </validation-provider>
            </template>
          </CFormGroup>
        </div>
        <div class="col-sm-12">
          <label for="from_date">From</label>
          <validation-provider
            v-slot="{ errors, dirty, valid }"
            name="From Date"
            rules="required"
          >
            <input
              type="date"
              id="from_date"
              :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
              class="form-control"
              v-model="updatedSpeciality.from_date"
              placeholder="From"
            />
            <div class="invalid-feedback">{{ errors[0] }}</div>
          </validation-provider>
        </div>
        <div class="col-sm-12">
          <validation-provider
            v-slot="{ errors, dirty, valid }"
            name="To Date"
            :rules="`after:${updatedSpeciality.from_date}`"
          >
            <label for="to_date">To</label>
            <input
              type="date"
              id="to_date"
              :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
              class="form-control"
              v-model="updatedSpeciality.to_date"
              placeholder="To"
            />
            <div class="invalid-feedback">{{ errors[0] }}</div>
          </validation-provider>
        </div>
      </div>
      <template #footer>
        <CButton @click="close" color="default">Cancel</CButton>
        <button
          class="btn btn-primary rounded"
          :style="`cursor: ${invalid ? 'not-allowed' : ''}`"
          :disabled="invalid"
          @click="update"
        >
          Edit
        </button>
      </template>
    </CModal>
  </validation-observer>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { mapState, mapActions } from "vuex";
import { ValidationProvider, ValidationObserver, extend } from "vee-validate";
import { required } from "vee-validate/dist/rules";
extend("required", { ...required, message: "The {_field_} field is required" });
extend("after", {
  validate(toDate, { fromDate }) {
    return toDate >= fromDate;
  },
  params: ["fromDate"],
  message: "The {_field_} field must be after or equal {fromDate}."
});

export default {
  components: {
    vSelect,
    ValidationObserver,
    ValidationProvider
  },
  data() {
    return {
      updatedSpeciality: null
    };
  },
  computed: {
    ...mapState("product", ["specialities", "product"])
  },
  methods: {
    ...mapActions("product", ["getProductSpecialities"]),
    open(speciality) {
      this.dialog = true;
      this.updatedSpeciality = { ...speciality };
    },
    close() {
      this.dialog = false;
      this.updatedSpeciality = null;
    },
    update() {
      const { id } = this.updatedSpeciality;

      axios
        .put(`/api/product-specialities/${id}`, {
          ...this.updatedSpeciality,
          product_id: this.product.id
        })
        .then(() => {
          this.getProductSpecialities().then(() => {
            this.close();
            this.scrollToTop();
            this.flash("Updated successfully.");
          });
        });
    }
  }
};
</script>
