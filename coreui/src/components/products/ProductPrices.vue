<template>
  <div>
    <CDataTable
      hover
      striped
      sorter
      tableFilter
      footer
      :itemsPerPageSelect="perPageSelect"
      :items="productPrices"
      :fields="fields"
      :items-per-page="100"
      :active-page="1"
      :responsive="true"
      :table-filter="{ external: true, lazy: true }"
      pagination
      thead-top
    >
      <template #from_date="{item}">
        <td>{{ format_date(item.from_date) }}</td>
      </template>
      <template #to_date="{item}">
        <td>
          <span :class="item.to_date ? '' : 'badge badge-danger p-1'">{{
            item.to_date ? format_date(item.to_date) : "Not Registered"
          }}</span>
        </td>
      </template>
      <template slot="thead-top">
        <td style="border-top:none;"><strong>Total</strong></td>
        <td style="border-top:none;" class="text-xs-right">
          {{ distributorsCount }}
        </td>
      </template>
      <!-- <template #actions="{item}">
        <td>
          <div class="row justify-content-center">
            <CButton
              color="success"
              class="btn-sm"
              @click="$refs.editPriceDialog.open(item)"
              ><i class="cil-pencil"></i><CIcon name="cil-pencil"
            /></CButton>
            <CButton color="danger" class="btn-sm" @click="confirmDelete(item)"
              ><CIcon name="cil-trash"
            /></CButton>
          </div>
        </td>
      </template> -->
    </CDataTable>
    <delete-confirmation-dialog
      ref="deletePriceConfirmation"
      @DeleteConfirmed="deletePrice"
    />
    <edit-product-price-dialog ref="editPriceDialog" />
  </div>
</template>

<script>
import { mapActions, mapGetters, mapState } from "vuex";
import moment from "moment";
import DeleteConfirmationDialog from "../common/DeleteConfirmationDialog.vue";
import EditProductPriceDialog from "./EditProductPriceDialog.vue";

export default {
  components: { DeleteConfirmationDialog, EditProductPriceDialog },
  data() {
    return {
      fields: [
        "id",
        "product",
        "distributor",
        "selling_price",
        "avg_price",
        "avg_tender_price",
        "avg_target_price",
        "from_date",
        "to_date",
      ],
      perPageSelect: {
        values: ["100", "200", "300", "400", "500"],
        external: true
      },
      priceToDelete: null
    };
  },
  computed: {
    ...mapState("product", ["productPrices"]),
    ...mapGetters("product", ["distributorsCount"])
  },
  created() {
    this.initialize();
  },
  methods: {
    ...mapActions("product", ["getProductPrices"]),
    initialize() {
      this.getProductPrices();
    },

    format_date: function(value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    confirmDelete(distributor) {
      this.$refs.deletePriceConfirmation.open();
      this.priceToDelete = distributor;
    },
    deletePrice() {
      const { id } = this.priceToDelete;

      axios
        .delete(`/api/product-distributors/${id}`)
        .then(() => {
          this.getProductPrices().then(() => {
            this.flash("Deleted Successfully.");
          });
        })
        .catch(() => {
          this.flash("oOOops, something went wrong.", "error");
        })
        .then(() => {
          this.$refs.deletePriceConfirmation.close();
          this.scrollToTop();
        });
    }
  }
};
</script>
