<template>
  <validation-observer ref="form" v-slot="{ invalid }">
    <CCard no-header>
      <CCardBody>
        <div class="row mb-3">
          <div class="col">
            <validation-provider v-slot="{ errors, dirty, valid }" name="Ucode" rules="required|min:3">
              <label for="ucode">Ucode</label>
              <input type="text" id="ucode" :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                class="form-control" v-model="product.ucode" placeholder="Ucode" />
              <div class="invalid-feedback">{{ errors[0] }}</div>
            </validation-provider>
          </div>
          <div class="col">
            <validation-provider v-slot="{ errors, dirty, valid }" name="Name" rules="required|min:3">
              <label for="name">Name</label>
              <input type="text" id="name" :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'" class="form-control"
                v-model="product.name" placeholder="Name" />
              <div class="invalid-feedback">{{ errors[0] }}</div>
            </validation-provider>
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-lg-4 col-md-4 col-sm-8">
            <validation-provider v-slot="{ errors, dirty, valid }" name="Short Name"
              :rules="product.short_name ? 'min:3' : ''">
              <label for="short_name">Short Name</label>
              <input type="text" id="short_name" :class="!dirty
                ? ''
                : valid
                  ? !product.short_name
                    ? ''
                    : 'is-valid'
                  : 'is-invalid'
                " class="form-control" v-model="product.short_name" placeholder="Short Name" />
              <div class="invalid-feedback">{{ errors[0] }}</div>
            </validation-provider>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-8">
            <validation-provider v-slot="{ errors, dirty, valid }" name="Quantity" rules="required">
              <label for="quantity">Quantity</label>
              <input type="number" id="quantity" :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                class="form-control" v-model="product.quantity" placeholder="Quantity" />
              <div class="invalid-feedback">{{ errors[0] }}</div>
            </validation-provider>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-8">
            <CInput label="Launch Date" type="date" placeholder="Launch Date" v-model="product.launch_date"></CInput>
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-lg-3 col-md-3 col-sm-8">
            <CFormGroup>
              <template #label>
                Type
              </template>
              <template #input>
                <v-select v-model="product.type_id" :options="types" label="name" :value="0" :reduce="type => type.id"
                  placeholder="Select option" class="mt-2" />
              </template>
            </CFormGroup>
          </div>
          <div class="col-lg-3 col-md-3 col-sm-8">
            <CFormGroup>
              <template #label>
                Family
              </template>
              <template #input>
                <v-select v-model="product.family_id" :options="families" label="name" :value="0"
                  :reduce="family => family.id" placeholder="Select option" class="mt-2" />
              </template>
            </CFormGroup>
          </div>

          <div class="col-lg-3 col-md-3 col-sm-8">
            <CFormGroup>
              <template #label>
                Classification
              </template>
              <template #input>
                <v-select v-model="product.classification_id" :options="classifications" label="name" :value="0"
                  :reduce="classification => classification.id" placeholder="Select option" class="mt-2" />
              </template>
            </CFormGroup>
          </div>
          <div class="col-lg-3 col-md-3 col-sm-8">
            <label>Max Sample</label><br />
            <c-input type="number" placeholder="Max Sample" v-model="max_sample" />
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-lg-6 col-md-6 col-sm-8">
            <CTextarea label="Notes" type="text" placeholder="Notes" v-model="product.notes"></CTextarea>
          </div>
          <div class="col-lg-6 col-md-6 col-sm-8">
            <label>Hide</label><br />
            <input type="checkbox" :id="`hide`" v-model="hide" @click="hideProduct()" />
          </div>
        </div>
      </CCardBody>
      <CCardFooter>
        <button class="btn btn-primary rounded" :style="`cursor: ${invalid ? 'not-allowed' : ''}`" :disabled="invalid"
          @click="create">
          Create
        </button>
        <CButton color="default" :to="{ name: 'products' }">Cancel</CButton>
      </CCardFooter>
    </CCard>
  </validation-observer>
</template>

<script>
import { mapState, mapMutations } from "vuex";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { ValidationProvider, ValidationObserver, extend } from "vee-validate";
import { required, min } from "vee-validate/dist/rules";

extend("required", { ...required, message: "The {_field_} field is required" });
extend("min", {
  validate(value, { length }) {
    return value.length >= length;
  },
  params: ["length"],
  message: "The {_field_} field must have at least {length} characters"
});

export default {
  components: {
    vSelect,
    ValidationObserver,
    ValidationProvider
  },
  data() {
    return {
      product: {},
      hide: null,
      max_sample: 10,
    };
  },
  computed: {
    ...mapState("product", ["types", "families", "classifications", "maxSort"])
  },
  methods: {
    ...mapMutations("product", ["setProduct", "setMaxSort"]),
    hideProduct() {
      if (document.getElementById("hide").checked)
        this.hide = 1;
      else this.hide = 0;
    },
    create() {
      const product = {
        ...this.product,
        sort: this.maxSort + 100,
        is_hidden: this.hide ? 1 : 0,
        max_sample: this.max_sample,
      };

      axios
        .post(`/api/products`, {
          product
        })
        .then(response => {
          this.setProduct(response.data);
          this.setMaxSort(response.data.maxSort);
          this.product = {};
          this.scrollToTop();
          this.$refs.form.reset();
          this.flash("Product Created Successfully.");
        })
        .catch(() => {
          this.scrollToTop();
          this.flash("oOOops, something went wrong", "error");
        });
    }
  }
};
</script>

<style lang="scss" scoped></style>
