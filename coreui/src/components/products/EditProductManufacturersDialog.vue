<template>
  <div>
    <validation-observer ref="form" v-slot="{ invalid }">
      <CModal
        v-if="updatedManufacturer"
        :title="`Edit ${updatedManufacturer.name}`"
        color="primary"
        :show.sync="dialog"
      >
        <div class="row">
          <div class="col-sm-12">
            <CFormGroup>
              <template #label>
                Manufacturer
              </template>
              <template #input>
                <validation-provider
                  v-slot="{ errors, dirty, valid }"
                  name="Manufacturer"
                  rules="required"
                >
                  <v-select
                    v-model="updatedManufacturer.manufacturer_id"
                    :options="manufacturers"
                    label="name"
                    :value="0"
                    :reduce="manufacturer => manufacturer.id"
                    :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                    placeholder="Select option"
                    class="mt-2"
                  />
                  <div class="invalid-feedback">{{ errors[0] }}</div>
                </validation-provider>
              </template>
            </CFormGroup>
          </div>
          <div class="col-sm-12">
            <label for="from_date">From</label>
            <validation-provider
              v-slot="{ errors, dirty, valid }"
              name="From Date"
              rules="required"
            >
              <input
                type="date"
                id="from_date"
                :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                class="form-control"
                v-model="updatedManufacturer.from_date"
                placeholder="From"
              />
              <div class="invalid-feedback">{{ errors[0] }}</div>
            </validation-provider>
          </div>
          <div class="col-sm-12">
            <validation-provider
              v-slot="{ errors, dirty, valid }"
              name="To Date"
              :rules="`after:${updatedManufacturer.from_date}`"
            >
              <label for="to_date">To</label>
              <input
                type="date"
                id="to_date"
                :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                class="form-control"
                v-model="updatedManufacturer.to_date"
                placeholder="To"
              />
              <div class="invalid-feedback">{{ errors[0] }}</div>
            </validation-provider>
          </div>
        </div>
        <template #footer>
          <CButton @click="close" color="default">Cancel</CButton>
          <button
            class="btn btn-primary rounded"
            :style="`cursor: ${invalid ? 'not-allowed' : ''}`"
            :disabled="invalid"
            @click="update"
          >
            Edit
          </button>
        </template>
      </CModal>
    </validation-observer>
  </div>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { mapState, mapActions } from "vuex";
import { ValidationProvider, ValidationObserver, extend } from "vee-validate";
import { required } from "vee-validate/dist/rules";
extend("required", { ...required, message: "The {_field_} field is required" });
extend("after", {
  validate(toDate, { fromDate }) {
    return toDate >= fromDate;
  },
  params: ["fromDate"],
  message: "The {_field_} field must be after or equal {fromDate}."
});
export default {
  components: {
    vSelect,
    ValidationObserver,
    ValidationProvider
  },
  data() {
    return {
      updatedManufacturer: null
    };
  },
  computed: {
    ...mapState("product", ["manufacturers", "product"])
  },
  methods: {
    ...mapActions("product", ["getProductManufacturers"]),
    open(manufacturer) {
      this.dialog = true;
      this.updatedManufacturer = { ...manufacturer };
    },
    close() {
      this.dialog = false;
      this.updatedManufacturer = null;
    },
    update() {
      const { id } = this.updatedManufacturer;

      axios
        .put(`/api/product-manufacturers/${id}`, {
          ...this.updatedManufacturer,
          product_id: this.product.id
        })
        .then(() => {
          this.getProductManufacturers().then(() => {
            this.close();
            this.scrollToTop();
            this.flash("Updated successfully.");
          });
        });
    }
  }
};
</script>
