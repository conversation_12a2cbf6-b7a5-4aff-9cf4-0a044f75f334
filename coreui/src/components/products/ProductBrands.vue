<template>
  <div>
    <CDataTable
      hover
      striped
      sorter
      tableFilter
      footer
      :itemsPerPageSelect="perPageSelect"
      :items="productBrands"
      :fields="fields"
      :items-per-page="100"
      :active-page="1"
      :responsive="true"
      :table-filter="{ external: true, lazy: true }"
      pagination
      thead-top
    >
      <template #from_date="{item}">
        <td>{{ format_date(item.from_date) }}</td>
      </template>
      <template #to_date="{item}">
        <td>
          <span :class="item.to_date ? '' : 'badge badge-danger p-1'">{{
            item.to_date ? format_date(item.to_date) : "Not Registered"
          }}</span>
        </td>
      </template>
      <template slot="thead-top">
        <td style="border-top:none;"><strong>Total</strong></td>
        <td style="border-top:none;" class="text-xs-right">
          {{ brandsCount }}
        </td>
      </template>
      <!-- <template #actions="{item}">
        <td>
          <div class="row justify-content-center">
            <CButton
              color="success"
              class="btn-sm"
              @click="$refs.editBrandDialog.open(item)"
              ><i class="cil-pencil"></i><CIcon name="cil-pencil"
            /></CButton>
            <CButton color="danger" class="btn-sm" @click="confirmDelete(item)"
              ><CIcon name="cil-trash"
            /></CButton>
          </div>
        </td>
      </template> -->
    </CDataTable>
    <delete-confirmation-dialog
      ref="deleteBrandConfirmation"
      @DeleteConfirmed="deleteBrand"
    />
    <edit-product-brand-dialog ref="editBrandDialog" />
  </div>
</template>

<script>
import { mapActions, mapGetters, mapState } from "vuex";
import moment from "moment";
import DeleteConfirmationDialog from "../common/DeleteConfirmationDialog.vue";
import EditProductBrandDialog from "./EditProductBrandDialog.vue";

export default {
  components: { DeleteConfirmationDialog, EditProductBrandDialog },
  data() {
    return {
      fields: ["id", "product" , "brand", "from_date", "to_date"],
      perPageSelect: {
        values: ["100", "200", "300", "400", "500"],
        external: true
      },
      brandToDelete: null
    };
  },
  computed: {
    ...mapState("product", ["productBrands"]),
    ...mapGetters("product", ["brandsCount"])
  },
  created() {
    this.initialize();
  },
  methods: {
    ...mapActions("product", ["getProductBrands"]),
    initialize() {
      this.getProductBrands();
    },

    format_date(value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    confirmDelete(brand) {
      this.$refs.deleteBrandConfirmation.open();
      this.brandToDelete = brand;
    },
    deleteBrand() {
      const { id } = this.brandToDelete;

      axios
        .delete(`/api/product-brands/${id}`)
        .then(() => {
          this.getProductBrands().then(() => {
            this.flash("Deleted Successfully.");
          });
        })
        .catch(() => {
          this.flash("oOOops, something went wrong.", "error");
        })
        .then(() => {
          this.$refs.deleteBrandConfirmation.close();
          this.scrollToTop();
        });
    }
  }
};
</script>
