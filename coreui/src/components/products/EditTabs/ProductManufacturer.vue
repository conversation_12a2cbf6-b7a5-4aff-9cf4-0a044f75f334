<template>
  <div>
    <c-card no-header>
      <c-card-body>
        <div class="row">
          <div class="col">
            <c-form-group>
              <template #label> Manufacturer </template>
              <template #input>
                <v-select
                  v-model="product_manufacturer.manufacturer_id"
                  :options="manufacturers"
                  label="name"
                  :value="0"
                  :reduce="(manufacturer) => manufacturer.id"
                  placeholder="Select option"
                  class="mt-2"
                />
              </template>
            </c-form-group>
          </div>
          <div class="col">
            <c-input
              label="From"
              type="date"
              placeholder="From"
              v-model="product_manufacturer.from_date"
            ></c-input>
          </div>
          <div class="col">
            <c-input
              label="To"
              type="date"
              placeholder="To"
              v-model="product_manufacturer.to_date"
            ></c-input>
          </div>
        </div>
      </c-card-body>
      <c-card-footer>
        <c-button
          color="primary"
          v-if="!manufacturerIsEditing"
          @click="storeProductManufacturer"
          >Create</c-button
        >
        <c-button
          color="primary"
          v-if="manufacturerIsEditing"
          @click="updateManufacturer"
          >Update</c-button
        >
        <c-button color="default" :to="{name:'products'}">Cancel</c-button>
      </c-card-footer>
    </c-card>

    <c-data-table
      hover
      striped
      sorter
      tableFilter
      footer
      itemsPerPageSelect
      :items="product_manufacturers"
      :fields="manufacturers_fields"
      :items-per-page="1000"
      :active-page="1"
      :responsive="true"
      pagination
      thead-top
    >
      <template #from_date="{ item }">
        <td>{{ format_date(item.from_date) }}</td>
      </template>
      <template #to_date="{ item }">
        <td>{{ format_date(item.to_date) }}</td>
      </template>
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ product_manufacturers.length }}
        </td>
      </template>
      <template #actions="{ item }">
        <td>
          <div class="row justify-content-center">
            <c-button
              color="success"
              class="btn-sm mt-2 mr-1 text-white"
              @click="editManufacturer(item.id)"
              ><i class="cil-pencil"></i><CIcon name="cil-pencil"
            /></c-button>
            <c-button
                  color="danger"
                  class="btn-sm mt-2 mr-1"
                  @click="
                    $root
                      .$confirm(
                        'Delete',
                        'Do you want to delete this record?',
                        {
                          color: 'red',
                          width: 290,
                          zIndex: 200,
                        }
                      )
                      .then((confirmed) => {
                        if (confirmed) {
                          deleteProductManufacturer(item);
                        }
                      })
                  "
                  ><c-icon name="cil-trash"
                /></c-button>
          </div>
        </td>
      </template>
    </c-data-table>
  </div>
</template>
<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  props: {
    product_id: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      manufacturers: [],
      fromDate: new Date().toISOString().slice(0, 10),
      manufacturerIsEditing: false,
      product_manufacturer: {
        id: 0,
        product_id: "",
        manufacturer_id: "",
        from_date: "",
        to_date: "",
      },
      product_manufacturers: [],
      manufacturers_fields: [
        "id",
        "product",
        "manufacturer",
        "from_date",
        "to_date",
        "actions",
      ],
    };
  },
  methods: {
    initialize() {
      axios
        .get(`/api/get_manufacturers`)
        .then((response) => {
          this.manufacturers = response.data.manufacturers;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    format_date (value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    edit_date_format (value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    getProductManufacturers() {
      axios
        .get(`/api/products/${this.product_id}/manufacturers`)
        .then((response) => {
          this.product_manufacturers = response.data.product_manufacturers;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    reset(){
        this.product_manufacturer = {
            id:0,
            product_id:"",
            manufacturer_id: "",
            from_date: "",
            to_date: "",
          };
    },
    storeProductManufacturer() {
      axios
        .post(`/api/products/${this.product_id}/manufacturers`, {
          product_id: this.product_id,
          manufacturer_id: this.product_manufacturer.manufacturer_id,
          from_date: this.crmDateFormat(this.product_manufacturer.from_date),
          to_date: this.product_manufacturer.to_date ? this.crmDateFormat(this.product_manufacturer.to_date):null,
        })
        .then((response) => {
          this.flash("Manufacturer of this product created successfully");
          this.reset();
          
        })
        .catch((error) =>{
          this.showErrorMessage(error);
        });
      //Refresh table
      this.getProductManufacturers();
    },
    editManufacturer ( id ) {
      axios.get(`/api/product-manufacturers/${id}`)
      .then( (response)=> {
        this.manufacturerIsEditing = true;
        this.product_manufacturer.id = response.data.product_manufacturer.id;
        this.product_manufacturer.product_id = response.data.product_manufacturer.product_id;
        this.product_manufacturer.manufacturer_id = response.data.product_manufacturer.manufacturer_id;
        this.product_manufacturer.from_date = this.edit_date_format(response.data.product_manufacturer.from_date);
        this.product_manufacturer.to_date = this.edit_date_format(response.data.product_manufacturer.to_date);
        }).catch( (error) =>{
            this.showErrorMessage(error);
      });
    },
    updateManufacturer() {
      axios.put(`/api/product-manufacturers/${this.product_manufacturer.id}`,
      {
          id: this.product_manufacturer.id,
          product_id: this.product_manufacturer.product_id,
          manufacturer_id: this.product_manufacturer.manufacturer_id,
          from_date: this.crmDateFormat(this.product_manufacturer.from_date),
          to_date: this.product_manufacturer.to_date ? this.crmDateFormat(this.product_manufacturer.to_date):null,
      })
      .then( (response)=> {
          this.manufacturerIsEditing= false,
          this.flash("Manufacturer of this product updated successfully");
          this.getProductManufacturers();
          this.reset();
      }).catch( (error)=> {
          this.showErrorMessage(error);
      });
    },
    removeProductManufacturer(item) {
      const index = this.product_manufacturers.findIndex((type) => item.id == type.id);
      this.product_manufacturers.splice(index, 1);
    },
    deleteProductManufacturer(item) {
      axios
        .delete(`/api/product-manufacturers/${item.id}`)
        .then((response) => {
          this.flash("Product Manufacturer Deleted Successfully");
          this.removeProductManufacturer(item);
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
    this.initialize();
    this.getProductManufacturers();
  },
};
</script>