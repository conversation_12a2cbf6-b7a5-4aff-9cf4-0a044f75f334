<template>
  <div>
    <c-card no-header>
      <c-card-body>
        <div class="row">
          <div class="col">
            <c-form-group>
              <template #label> Brand </template>
              <template #input>
                <v-select v-model="product_brand.brand_id" :options="brands" label="name" :value="0"
                  :reduce="(brand) => brand.id" placeholder="Select option" class="mt-2" />
              </template>
            </c-form-group>
          </div>
          <div class="col">
            <c-input label="From" type="date" placeholder="From" v-model="product_brand.from_date"></c-input>
          </div>
          <div class="col">
            <c-input label="To" type="date" placeholder="To" v-model="product_brand.to_date"></c-input>
          </div>
        </div>
      </c-card-body>
      <c-card-footer>
        <c-button color="primary" v-if="!brandIsEditing" @click="storeProductBrand()">Create</c-button>
        <c-button color="primary" v-if="brandIsEditing" @click="updateBrand()">Update</c-button>
        <CButton color="default" :to="{ name: 'products' }">Cancel</CButton>
      </c-card-footer>
    </c-card>
    <c-card>
      <c-card-body>
        <CDataTable hover striped sorter tableFilter footer itemsPerPageSelect :items="product_brands"
          :fields="brands_fields" :items-per-page="1000" :active-page="1" :responsive="true" pagination thead-top>
          <template #from_date="{ item }">
            <td>{{ format_date(item.from_date) }}</td>
          </template>
          <template #to_date="{ item }">
            <td>{{ format_date(item.to_date) }}</td>
          </template>
          <template slot="thead-top">
            <td style="border-top: none"><strong>Total</strong></td>
            <td style="border-top: none" class="text-xs-right">
              {{ product_brands.length }}
            </td>
          </template>
          <template #actions="{ item }">
            <td>
              <div class="row justify-content-center">
                <c-button color="success" class="btn-sm mt-2 mr-1 text-white" @click="editBrand(item.id)"><i
                    class="cil-pencil"></i><c-icon name="cil-pencil" /></c-button>
                <c-button color="danger" class="btn-sm mt-2 mr-1" @click="
                  $root
                    .$confirm(
                      'Delete',
                      'Do you want to delete this record?',
                      {
                        color: 'red',
                        width: 290,
                        zIndex: 200,
                      }
                    )
                    .then((confirmed) => {
                      if (confirmed) {
                        deleteProductBrand(item);
                      }
                    })
                  "><c-icon name="cil-trash" /></c-button>
              </div>
            </td>
          </template>
        </CDataTable>
      </c-card-body>
    </c-card>
  </div>
</template>
<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  props: {
    product_id: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      brandIsEditing: false,
      brands: [],
      brands_fields: [
        "id",
        "product",
        "brand",
        "from_date",
        "to_date",
        "actions",
      ],
      product_brands: [],
      fromDate: new Date().toISOString().slice(0, 10),
      product_brand: {
        id: 0,
        product_id: "",
        brand_id: "",
        from_date: "",
        to_date: "",
      },
    };
  },
  methods: {
    initialize() {
      axios
        .get(`/api/get-brands`)
        .then((response) => {
          this.brands = response.data.brands;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    format_date(value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    edit_date_format(value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    getProductBrands() {
      axios
        .get(`/api/products/${this.product_id}/brands/`)
        .then((response) => {
          this.product_brands = response.data.product_brands;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    reset() {
      this.product_brand = {
        id: 0,
        product_id: "",
        brand_id: "",
        from_date: "",
        to_date: "",
      };
    },
    storeProductBrand() {
      axios
        .post(`/api/products/${this.product_id}/brands`, {
          product_id: this.product_id,
          brand_id: this.product_brand.brand_id,
          from_date: this.crmDateFormat(this.product_brand.from_date),
          to_date: this.product_brand.to_date ? this.crmDateFormat(this.product_brand.to_date) : null,
        })
        .then((response) => {
          this.flash("Brand of this product created successfully");
          this.reset();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      //Refresh table
      this.getProductBrands();
    },
    editBrand(id) {
      axios
        .get(`/api/product-brands/${id}`)
        .then((response) => {
          this.brandIsEditing = true;
          this.product_brand.id = response.data.product_brand.id;
          this.product_brand.product_id =
            response.data.product_brand.product_id;
          this.product_brand.brand_id = response.data.product_brand.brand_id;
          this.product_brand.from_date = this.edit_date_format(
            response.data.product_brand.from_date
          );
          this.product_brand.to_date = this.edit_date_format(
            response.data.product_brand.to_date
          );
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    updateBrand() {
      axios
        .put(`/api/product-brands/${this.product_brand.id}`, {
          id: this.product_brand.id,
          product_id: this.product_brand.product_id,
          brand_id: this.product_brand.brand_id,
          from_date: this.crmDateFormat(this.product_brand.from_date),
          to_date: this.product_brand.to_date ? this.crmDateFormat(this.product_brand.to_date) : null,
        })
        .then((response) => {
          this.brandIsEditing = false;
          this.flash("Brand of this product updated successfully");
          this.getProductBrands();
          this.reset();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removeProductBrand(item) {
      const index = this.product_brands.findIndex((type) => item.id == type.id);
      this.product_brands.splice(index, 1);
    },
    deleteProductBrand(item) {
      axios
        .delete(`/api/product-brands/${item.id}`)
        .then((response) => {
          this.flash("Product Brand Deleted Successfully");
          this.removeProductBrand(item);
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
    this.initialize();
    this.getProductBrands();
  },
};
</script>