<template>
  <div>
    <c-card no-header>
      <c-card-body>
        <div class="row">
          <div class="col-lg-4 col-md-4 col-sm-8">
            <c-form-group v-if="!presentationIsEditing">
              <template #label> Presentation </template>
              <template #input>
                <v-select
                  v-model="product_presentation.presentation_id"
                  :options="presentations"
                  label="name"
                  :value="0"
                  :reduce="(presentation) => presentation.id"
                  multiple
                  placeholder="Select Presentation"
                  class="mt-2"
                />
              </template>
            </c-form-group>
            <c-form-group v-if="presentationIsEditing">
              <template #label> Presentation </template>
              <template #input>
                <v-select
                  v-model="product_presentation.presentation_id"
                  :options="presentations"
                  label="name"
                  :value="0"
                  :reduce="(presentation) => presentation.id"
                  placeholder="Select Presentation"
                  class="mt-2"
                />
              </template>
            </c-form-group>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-8">
            <c-form-group>
              <template #label> Line </template>
              <template #input>
                <v-select
                  v-model="product_presentation.line_id"
                  :options="lines"
                  label="name"
                  :value="0"
                  :reduce="(line) => line.id"
                  placeholder="Select Line"
                  class="mt-2"
                />
              </template>
            </c-form-group>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-8">
            <c-form-group>
              <template #label> Message </template>
              <template #input>
                <v-select
                  v-model="product_presentation.message_id"
                  :options="messages"
                  label="message"
                  :value="0"
                  :reduce="(message) => message.id"
                  placeholder="Select Message"
                  class="mt-2"
                />
              </template>
            </c-form-group>
          </div>
        </div>
      </c-card-body>
      <c-card-footer>
        <c-button
          color="primary"
          v-if="!presentationIsEditing"
          @click="store"
          >Create</c-button
        >
        <c-button color="primary" v-if="presentationIsEditing" @click="update"
          >Update</c-button
        >
        <CButton color="default" :to="{ name: 'products' }">Cancel</CButton>
      </c-card-footer>
    </c-card>
    <c-card>
      <c-card-body>
        <CDataTable
          hover
          striped
          sorter
          tableFilter
          footer
          itemsPerPageSelect
          :items="product_presentations"
          :fields="presentations_fields"
          :items-per-page="1000"
          :active-page="1"
          :responsive="true"
          pagination
          thead-top
        >
          <template slot="thead-top">
            <td style="border-top: none"><strong>Total</strong></td>
            <td style="border-top: none" class="text-xs-right">
              {{ product_presentations.length }}
            </td>
          </template>
          <template #actions="{ item }">
            <td>
              <div class="row justify-content-center">
                <c-button
                  color="success"
                  class="btn-sm mt-2 mr-1 text-white"
                  @click="edit(item.id)"
                  ><i class="cil-pencil"></i><c-icon name="cil-pencil"
                /></c-button>
                <c-button
                  color="danger"
                  class="btn-sm mt-2 mr-1"
                  @click="
                    $root
                      .$confirm(
                        'Delete',
                        'Do you want to delete this record?',
                        {
                          color: 'red',
                          width: 290,
                          zIndex: 200,
                        }
                      )
                      .then((confirmed) => {
                        if (confirmed) {
                          deleteProductPresentation(item);
                        }
                      })
                  "
                  ><c-icon name="cil-trash"
                /></c-button>
              </div>
            </td>
          </template>
        </CDataTable>
      </c-card-body>
    </c-card>
  </div>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  props: {
    product_id: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      presentationIsEditing: false,
      presentations: [],
      lines: [],
      messages: [],
      presentations_fields: ["id", "product","line", "presentation", "message", "actions"],
      product_presentations: [],
      product_presentation: {
        id: 0,
        product_id: "",
        presentation_id: "",
        line_id: "",
        message_id: "",
      },
    };
  },
  methods: {
    initialize() {
      axios
        .get(`/api/get-presentations/${this.product_id}`)
        .then((response) => {
          this.presentations = response.data.presentations;
          this.lines = response.data.lines;
          this.messages = response.data.messages;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getProductPresentations() {
      axios
        .get(`/api/products/${this.product_id}/presentations/`)
        .then((response) => {
          this.product_presentations = response.data.product_presentations;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    reset() {
      this.product_presentation = {
        id: 0,
        product_id: "",
        presentation_id: "",
      };
    },
    store() {
      axios
        .post(`/api/products/${this.product_id}/presentations`, {
          product_id: this.product_id,
          presentations: this.product_presentation.presentation_id,
          message: this.product_presentation.message_id,
          line: this.product_presentation.line_id,
        })
        .then((response) => {
          this.flash("Presentation of this product created successfully");
          this.reset();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      //Refresh table
      this.getProductPresentations();
    },
    edit(id) {
      axios
        .get(`/api/product-presentations/${id}`)
        .then((response) => {
          this.presentationIsEditing = true;
          this.product_presentation.id = response.data.product_presentation.id;
          this.product_presentation.product_id =
            response.data.product_presentation.product_id;
          this.product_presentation.presentation_id =
            response.data.product_presentation.presentation_id;
            this.product_presentation.message_id =
            response.data.product_presentation.message_id;
            this.product_presentation.line_id =
            response.data.product_presentation.line_id;
            console.log(this.product_presentation);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    update() {
      axios
        .put(`/api/product-presentations/${this.product_presentation.id}`, {
          id: this.product_presentation.id,
          product_id: this.product_presentation.product_id,
          presentation_id: this.product_presentation.presentation_id,
          line_id: this.product_presentation.line_id,
          message_id: this.product_presentation.message_id,
        })
        .then((response) => {
          this.presentationIsEditing = false;
          this.flash("Presentation of this product updated successfully");
          this.getProductPresentations();
          this.reset();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removeProductPresentation(item) {
      const index = this.product_presentations.findIndex(
        (type) => item.id == type.id
      );
      this.product_presentations.splice(index, 1);
    },
    deleteProductPresentation(item) {
      axios
        .delete(`/api/product-presentations/${item.id}`)
        .then((response) => {
          this.flash("Product Presentation Deleted Successfully");
          this.removeProductPresentation(item);
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
    this.initialize();
    this.getProductPresentations();
  },
};
</script>