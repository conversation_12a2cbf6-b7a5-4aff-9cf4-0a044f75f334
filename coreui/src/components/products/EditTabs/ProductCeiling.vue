<template>
  <div>
    <c-card no-header>
      <c-card-body>
        <div class="row">
          <div class="col-3">
            <CFormGroup>
              <template #label> units </template>
              <template #input>
                <c-input v-model="product_ceiling.units" type="number" class="mt-2"/>
              </template>
            </CFormGroup>
          </div>
          <div class="col">
            <c-form-group>
              <template #label> sales Type </template>
              <template #input>
                <v-select
                  v-model="product_ceiling.type"
                  :options="sales"
                  label="type"
                  placeholder="Select type"
                  class="mt-2"
                />
              </template>
            </c-form-group>
          </div>
          <div class="col-3">
            <label for="from_date">From</label>
            <input
              type="date"
              id="from_date"
              class="form-control"
              v-model="product_ceiling.from_date"
              placeholder="From"
            />
          </div>
          <div class="col-3">
            <label for="to_date">To</label>
            <input
              type="date"
              id="to_date"
              class="form-control"
              v-model="product_ceiling.to_date"
              placeholder="To"
            />
          </div>
        </div>
      </c-card-body>
      <c-card-footer>
        <c-button color="primary" v-if="!ceilIsEditing" @click="store"
          >Create</c-button
        >
        <c-button color="primary" v-if="ceilIsEditing" @click="update"
          >Update</c-button
        >
        <CButton color="default" :to="{ name: 'products' }">Cancel</CButton>
      </c-card-footer>
    </c-card>
    <c-card>
      <c-card-body>
        <CDataTable
          hover
          striped
          sorter
          tableFilter
          footer
          itemsPerPageSelect
          :items="product_ceilings"
          :fields="fields"
          :items-per-page="1000"
          :active-page="1"
          :responsive="true"
          pagination
          thead-top
        >
          <template slot="thead-top">
            <td style="border-top: none"><strong>Total</strong></td>
            <td style="border-top: none" class="text-xs-right">
              {{ product_ceilings.length }}
            </td>
          </template>
          <template #actions="{ item }">
            <td>
              <div class="row justify-content-center">
                <c-button
                  color="success"
                  class="btn-sm mt-2 mr-1 text-white"
                  @click="edit(item.id)"
                  ><i class="cil-pencil"></i><c-icon name="cil-pencil"
                /></c-button>
                <c-button
                  color="danger"
                  class="btn-sm mt-2 mr-1"
                  @click="
                    $root
                      .$confirm(
                        'Delete',
                        'Do you want to delete this record?',
                        {
                          color: 'red',
                          width: 290,
                          zIndex: 200,
                        }
                      )
                      .then((confirmed) => {
                        if (confirmed) {
                          deleteProductCeil(item);
                        }
                      })
                  "
                  ><c-icon name="cil-trash"
                /></c-button>
              </div>
            </td>
          </template>
        </CDataTable>
      </c-card-body>
    </c-card>
  </div>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  props: {
    product_id: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      ceilIsEditing: false,
      sales: ["CR_TARGET", "CR_SALES"],
      fields: [
        "id",
        "product",
        "units",
        "type",
        "from_date",
        "to_date",
        "actions",
      ],
      product_ceilings: [],
      product_ceiling: {
        id: 0,
        product_id: null,
        units: null,
        from_date: null,
        to_date: null,
        type: [],
      },
    };
  },
  methods: {
    getProductCeilings() {
      axios
        .get(`/api/products/${this.product_id}/ceilings/`)
        .then((response) => {
          this.product_ceilings = response.data.product_ceilings;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    reset() {
      this.product_ceiling = {
        id: 0,
        product_id: null,
        units: null,
        type: [],
        from_date: null,
        to_date: null,
      };
    },
    store() {
      axios
        .post(`/api/products/${this.product_id}/ceilings`, {
          product_id: this.product_id,
          units: this.product_ceiling.units,
          from_date: this.product_ceiling.from_date,
          to_date: this.product_ceiling.to_date,
          type: this.product_ceiling.type,
        })
        .then((response) => {
          this.flash("Ceiling of this product created successfully");
          this.reset();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      //Refresh table
      this.getProductCeilings();
    },
    edit(id) {
      axios
        .get(`/api/product-ceilings/${id}`)
        .then((response) => {
          this.ceilIsEditing = true;
          this.product_ceiling.id = response.data.product_ceiling.id;
          this.product_ceiling.product_id =
            response.data.product_ceiling.product_id;
          this.product_ceiling.units = response.data.product_ceiling.units;
          this.product_ceiling.type = response.data.product_ceiling.type;
          this.product_ceiling.from_date =
            response.data.product_ceiling.from_date;
          this.product_ceiling.to_date = response.data.product_ceiling.to_date;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    update() {
      axios
        .put(`/api/product-ceilings/${this.product_ceiling.id}`, {
          id: this.product_ceiling.id,
          product_id: this.product_ceiling.product_id,
          units: this.product_ceiling.units,
          from_date: this.product_ceiling.from_date,
          to_date: this.product_ceiling.to_date,
          type: this.product_ceiling.type,
        })
        .then((response) => {
          this.ceilIsEditing = false;
          this.flash("Ceiling of this product updated successfully");
          this.getProductCeilings();
          this.reset();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      this.scrollToTop();
    },
    removeProductCeil(item) {
      const index = this.product_ceilings.findIndex(
        (type) => item.id == type.id
      );
      this.product_ceilings.splice(index, 1);
    },
    deleteProductCeil(item) {
      axios
        .delete(`/api/product-ceilings/${item.id}`)
        .then((response) => {
          this.flash("Product Ceiling Deleted Successfully");
          this.removeProductCeil(item);
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
    this.getProductCeilings();
  },
};
</script>