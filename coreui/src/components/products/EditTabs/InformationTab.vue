<template>
  <c-card no-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-6 col-md-6 col-sm-8">
          <c-input label="Ucode" type="text" placeholder="Ucode" v-model="product.ucode"></c-input>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-8">
          <c-input label="Name" type="text" placeholder="Name" v-model="product.name"></c-input>
        </div>
      </div>

      <div class="row">
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-input label="Short Name" type="text" placeholder="Short Name" v-model="product.short_name"></c-input>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-input label="Quantity" type="number" placeholder="Quantity" v-model="product.quantity"></c-input>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-input label="Launch Date" type="date" placeholder="Launch Date" v-model="product.launch_date"></c-input>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-input label="Sort" type="number" placeholder="Sort" v-model="product.sort" disabled></c-input>
        </div>
      </div>

      <div class="row">
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Type </template>
            <template #input>
              <v-select v-model="product.type_id" :options="types" label="name" :value="0" :reduce="(type) => type.id"
                placeholder="Select option" class="mt-2" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Family </template>
            <template #input>
              <v-select v-model="product.family_id" :options="families" label="name" :value="0"
                :reduce="(family) => family.id" placeholder="Select option" class="mt-2" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Classification </template>
            <template #input>
              <v-select v-model="product.classification_id" :options="classifications" label="name" :value="0"
                :reduce="(classification) => classification.id" placeholder="Select option" class="mt-2" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <label>Max Sample</label><br />
          <c-input type="number" placeholder="Max Sample" v-model="product.max_sample" />
        </div>
      </div>

      <div class="row">
        <div class="col-lg-6 col-md-6 col-sm-8">
          <c-textarea label="Notes" type="text" placeholder="Notes" v-model="product.notes"></c-textarea>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-8">
          <label>Hide</label><br />
          <input type="checkbox" :id="`is_hidden`" v-model="product.is_hidden" @click="hideProduct()" />
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <CButton color="primary" @click="update">Update</CButton>
      <CButton color="default" :to="{ name: 'products' }">Cancel</CButton>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      product_id: 0,
      product: {
        ucode: "",
        name: "",
        short_name: "",
        type_id: "",
        classification_id: "",
        family_id: "",
        quantity: "",
        notes: "",
        sort: "",
        is_hidden: 0,
        launch_date: "",
        max_sample: 0,
      },
      types: [],
      families: [],
      classifications: [],
    };
  },
  emits: ["getProduct"],
  methods: {
    initialize() {
      axios
        .get(`/api/products/${this.$route.params.id}/edit`)
        .then((response) => {
          this.product_id = response.data.product.id;
          this.product = response.data.product;
          this.types = response.data.types;
          this.classifications = response.data.classifications;
          this.families = response.data.families;
          this.$emit("getProduct", { product: this.product_id })
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    hideProduct() {
      if (document.getElementById("is_hidden").checked)
        this.product.is_hidden = 1;
      else this.product.is_hidden = 0;
    },
    update() {
      axios
        .put(`/api/products/${this.$route.params.id}`, {
          ucode: this.product.ucode,
          name: this.product.name,
          short_name: this.product.short_name,
          quantity: this.product.quantity,
          notes: this.product.notes,
          sort: this.product.sort,
          launch_date: this.product.launch_date,
          is_hidden: this.product.is_hidden,
          max_sample: this.product.max_sample,
          type_id: this.product.type_id,
          classification_id: this.product.classification_id,
          family_id: this.product.family_id,
        })
        .then((response) => {
          this.flash("Product Updated Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
  },
};
</script>