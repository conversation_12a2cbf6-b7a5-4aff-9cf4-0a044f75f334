<template>
  <div>
    <c-card no-header>
      <c-card-body>
        <div class="row">
          <div class="col">
            <c-form-group>
              <template #label> Distributor </template>
              <template #input>
                <v-select
                  v-model="product_price.distributor_id"
                  :options="distributors"
                  label="name"
                  :value="0"
                  :reduce="(distributor) => distributor.id"
                  placeholder="Select option"
                  class="mt-2"
                />
              </template>
            </c-form-group>
          </div>
          <div class="col">
            <c-input
              label="From"
              type="date"
              placeholder="From"
              name="product_price.from_date"
              v-model="product_price.from_date"
            ></c-input>
          </div>
          <div class="col">
            <c-input
              label="To"
              type="date"
              placeholder="To"
              name="product_price.to_date"
              v-model="product_price.to_date"
            ></c-input>
          </div>
        </div>

        <div class="row">
          <div class="col">
            <c-input
              label="Selling price"
              type="text"
              placeholder="selling price"
              name="product_price.selling_price"
              v-model="product_price.selling_price"
            ></c-input>
          </div>
          <div class="col">
            <c-input
              label="Average price"
              type="text"
              placeholder="Avg price"
              name="product_price.avg_price"
              v-model="product_price.avg_price"
            ></c-input>
          </div>
        </div>

        <div class="row">
          <div class="col">
            <c-input
              label="Average tender price"
              type="text"
              placeholder="Avg tender price"
              name="product_price.avg_tender_price"
              v-model="product_price.avg_tender_price"
            ></c-input>
          </div>
          <div class="col">
            <c-input
              label="Average target price"
              type="text"
              placeholder="Avg target price"
              name="product_price.avg_target_price"
              v-model="product_price.avg_target_price"
            ></c-input>
          </div>
        </div>
      </c-card-body>
      <c-card-footer>
        <c-button
          color="primary"
          v-if="!priceIsEditing"
          @click="storeProductPrice"
          >Create</c-button
        >
        <c-button color="primary" v-if="priceIsEditing" @click="updatePrice"
          >Update</c-button
        >
        <c-button color="default" :to="{name:'products'}">Cancel</c-button>
      </c-card-footer>
    </c-card>
    <c-data-table
      hover
      striped
      sorter
      tableFilter
      footer
      itemsPerPageSelect
      :items="product_prices"
      :fields="prices_fields"
      :items-per-page="1000"
      :active-page="1"
      :responsive="true"
      pagination
      thead-top
    >
      <template #from_date="{ item }">
        <td>{{ format_date(item.from_date) }}</td>
      </template>
      <template #to_date="{ item }">
        <td>{{ format_date(item.to_date) }}</td>
      </template>
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ product_prices.length }}
        </td>
      </template>
      <template #actions="{ item }">
        <td>
          <div class="row justify-content-center">
            <c-button
              color="success"
              class="btn-sm mt-2 mr-1 text-white"
              @click="editPrice(item.id)"
              ><i class="cil-pencil"></i><c-icon name="cil-pencil"
            /></c-button>
            <c-button
              color="danger"
              class="btn-sm mt-2 mr-1"
              @click="
                $root
                  .$confirm('Delete', 'Do you want to delete this record?', {
                    color: 'red',
                    width: 290,
                    zIndex: 200,
                  })
                  .then((confirmed) => {
                    if (confirmed) {
                      deleteProductPrice(item);
                    }
                  })
              "
              ><c-icon name="cil-trash"
            /></c-button>
          </div>
        </td>
      </template>
    </c-data-table>
  </div>
</template>
<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  props: {
    product_id: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      priceIsEditing: false,
      product_price: {
        id: 0,
        product_id: "",
        distributor_id: "",
        selling_price: "",
        avg_price: "",
        avg_tender_price: "",
        avg_target_price: "",
        from_date: "",
        to_date: "",
      },
      fromDate: new Date().toISOString().slice(0, 10),
      distributors: [],
      product_prices: [],
      prices_fields: [
        "id",
        "product",
        "distributor",
        "selling_price",
        "avg_price",
        "avg_tender_price",
        "avg_target_price",
        "from_date",
        "to_date",
        "actions",
      ],
    };
  },
  methods: {
    initialize() {
      axios
        .get(`/api/get-distributors`)
        .then((response) => {
          this.distributors = response.data.distributors;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    format_date (value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    edit_date_format (value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    getProductPrices() {
      axios
        .get(`/api/products/${this.product_id}/prices`)
        .then((response) => {
          this.product_prices = response.data.product_prices;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    reset() {
      this.product_price = {
        id: 0,
        product_id: "",
        distributor_id: "",
        selling_price: "",
        avg_price: "",
        avg_tender_price: "",
        avg_target_price: "",
        from_date: "",
        to_date: "",
      };
    },
    storeProductPrice() {
      axios
        .post(`/api/products/${this.product_id}/prices`, {
          product_id: this.product_id,
          distributor_id: this.product_price.distributor_id,
          selling_price: this.product_price.selling_price,
          avg_price: this.product_price.avg_price,
          avg_tender_price: this.product_price.avg_tender_price,
          avg_target_price: this.product_price.avg_target_price,
          from_date: this.crmDateFormat(this.product_price.from_date),
          to_date: this.product_price.to_date ? this.crmDateFormat(this.product_price.to_date):null,
        })
        .then((response) => {
          this.flash("Price of this product created successfully");
          this.reset();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      //Refresh table
      this.getProductPrices();
    },
    editPrice(id) {
      axios
        .get(`/api/product-prices/${id}`)
        .then((response) => {
          this.priceIsEditing = true;
          this.product_price.id = response.data.product_price.id;
          this.product_price.product_id =
            response.data.product_price.product_id;
          this.product_price.distributor_id =
            response.data.product_price.distributor_id;
          this.product_price.selling_price =
            response.data.product_price.selling_price;
          this.product_price.avg_price = response.data.product_price.avg_price;
          this.product_price.avg_tender_price =
            response.data.product_price.avg_tender_price;
          this.product_price.avg_target_price =
            response.data.product_price.avg_target_price;
          this.product_price.from_date = this.edit_date_format(
            response.data.product_price.from_date
          );
          this.product_price.to_date = this.edit_date_format(
            response.data.product_price.to_date
          );
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    updatePrice() {
      axios
        .put(`/api/product-prices/${this.product_price.id}`, {
          id: this.product_price.id,
          product_id: this.product_price.product_id,
          distributor_id: this.product_price.distributor_id,
          selling_price: this.product_price.selling_price,
          avg_price: this.product_price.avg_price,
          avg_tender_price: this.product_price.avg_tender_price,
          avg_target_price: this.product_price.avg_target_price,
          from_date: this.crmDateFormat(this.product_price.from_date),
          to_date: this.product_price.to_date ? this.crmDateFormat(this.product_price.to_date):null,
        })
        .then((response) => {
          this.priceIsEditing = false;
          this.flash("Price of this brand updated successfully");
          this.getProductPrices();
          this.reset();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removeProductPrice(item) {
      const index = this.product_prices.findIndex((type) => item.id == type.id);
      this.product_prices.splice(index, 1);
    },
    deleteProductPrice(item) {
      axios
        .delete(`/api/product-prices/${item.id}`)
        .then((response) => {
          this.flash("Product Price Deleted Successfully");
          this.removeProductPrice(item);
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
    this.initialize();
    this.getProductPrices();
  },
};
</script>