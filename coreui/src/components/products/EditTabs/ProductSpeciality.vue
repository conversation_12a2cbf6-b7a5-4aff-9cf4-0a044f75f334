<template>
  <div>
    <c-card no-header>
      <c-card-body>
        <div class="row">
          <div class="col">
            <c-form-group>
              <template #label> Speciality </template>
              <template #input>
                <v-select
                  v-model="product_speciality.speciality_id"
                  :options="specialities"
                  label="name"
                  :value="0"
                  :reduce="(speciality) => speciality.id"
                  placeholder="Select option"
                  class="mt-2"
                />
              </template>
            </c-form-group>
          </div>
          <div class="col">
            <c-input
              label="From"
              type="date"
              placeholder="From"
              v-model="product_speciality.from_date"
            ></c-input>
          </div>
          <div class="col">
            <c-input
              label="To"
              type="date"
              placeholder="To"
              v-model="product_speciality.to_date"
            ></c-input>
          </div>
        </div>
      </c-card-body>
      <c-card-footer>
        <c-button
          color="primary"
          v-if="!productSpecialityIsEditing"
          @click="storeProductSpeciality"
          >Create</c-button
        >
        <c-button
          color="primary"
          v-if="productSpecialityIsEditing"
          @click="updateProductSpeciality"
          >Update</c-button
        >
        <c-button color="default" :to="{name:'products'}">Cancel</c-button>
      </c-card-footer>
    </c-card>

    <c-data-table
      hover
      striped
      sorter
      tableFilter
      footer
      itemsPerPageSelect
      :items="product_specialities"
      :fields="product_specialities_fields"
      :items-per-page="100"
      :active-page="1"
      :responsive="true"
      pagination
      thead-top
    >
      <template #from_date="{ item }">
        <td>{{ format_date(item.from_date) }}</td>
      </template>
      <template #to_date="{ item }">
        <td>{{ format_date(item.to_date) }}</td>
      </template>
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ product_specialities.length }}
        </td>
      </template>
      <template #actions="{ item }">
        <td>
          <div class="row justify-content-center">
            <c-button
              color="success"
              class="btn-sm mt-2 mr-1 text-white"
              @click="editProductSpeciality(item.id)"
              ><i class="cil-pencil"></i><c-icon name="cil-pencil"
            /></c-button>
            <c-button
              color="danger"
              class="btn-sm mt-2 mr-1"
              @click="
                $root
                  .$confirm('Delete', 'Do you want to delete this record?', {
                    color: 'red',
                    width: 290,
                    zIndex: 200,
                  })
                  .then((confirmed) => {
                    if (confirmed) {
                      deleteProductSpeciality(item);
                    }
                  })
              "
              ><c-icon name="cil-trash"
            /></c-button>
          </div>
        </td>
      </template>
    </c-data-table>
  </div>
</template>
<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  props: {
    product_id: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      productSpecialityIsEditing: false,
      product_speciality: {
        id: 0,
        product_id: 0,
        speciality_id: 0,
        from_date: "",
        to_date: "",
      },
      fromDate: new Date().toISOString().slice(0, 10),
      specialities: [],
      product_specialities: [],
      product_specialities_fields: [
        "id",
        "product",
        "speciality",
        "from_date",
        "to_date",
        "actions",
      ],
    };
  },
  methods: {
    initialize() {
      axios
        .get(`/api/get-specialities`)
        .then((response) => {
          this.specialities = response.data.specialities;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    format_date (value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    edit_date_format (value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    getProductSpecialities() {
      axios
        .get(`/api/products/${this.product_id}/specialities`)
        .then((response) => {
          this.product_specialities = response.data.product_specialities;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    reset() {
      this.product_speciality = {
        id: 0,
        product_id: 0,
        speciality_id: 0,
        from_date: "",
        to_date: "",
      };
    },
    storeProductSpeciality() {
      axios
        .post(`/api/products/${this.product_id}/specialities`, {
          product_id: this.product_id,
          speciality_id: this.product_speciality.speciality_id,
          from_date: this.crmDateFormat(this.product_speciality.from_date),
          to_date: this.product_speciality.to_date ? this.crmDateFormat(this.product_speciality.to_date):null,
        })
        .then((response) => {
          this.reset();
          this.flash("Speciality of this product created successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      //Refresh table
      this.getProductSpecialities();
    },
    editProductSpeciality(id) {
      axios
        .get(`/api/product-specialities/${id}`)
        .then((response) => {
          this.productSpecialityIsEditing = true;
          this.product_speciality.id = response.data.product_speciality.id;
          this.product_speciality.product_id =
            response.data.product_speciality.product_id;
          this.product_speciality.speciality_id =
            response.data.product_speciality.speciality_id;
          this.product_speciality.from_date = this.edit_date_format(
            response.data.product_speciality.from_date
          );
          this.product_speciality.to_date = this.edit_date_format(
            response.data.product_speciality.to_date
          );
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    updateProductSpeciality() {
      axios
        .put(`/api/product-specialities/${this.product_speciality.id}`, {
          id: this.product_speciality.id,
          product_id: this.product_speciality.product_id,
          speciality_id: this.product_speciality.speciality_id,
          from_date: this.crmDateFormat(this.product_speciality.from_date),
          to_date: this.product_speciality.to_date ? this.crmDateFormat(this.product_speciality.to_date):null,
        })
        .then((response) => {
          this.productpecialityIsEditing = false;
          this.flash("Speciality of this product Updated Successfully");
          this.getProductSpecialities();
          this.reset();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removeProductSpeciality(item) {
      const index = this.product_specialities.findIndex((type) => item.id == type.id);
      this.product_specialities.splice(index, 1);
    },
    deleteProductSpeciality(item) {
      axios
        .delete(`/api/product-specialities/${item.id}`)
        .then((response) => {
          this.flash("Product Speciality Deleted Successfully");
          this.removeProductSpeciality(item);
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
    this.initialize();
    this.getProductSpecialities();
  },
};
</script>