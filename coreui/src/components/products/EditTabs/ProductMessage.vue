<template>
  <div>
    <c-card no-header>
      <c-card-body>
        <div class="row">
          <div class="col">
            <c-form-group v-if="!messageIsEditing">
              <template #label> Message </template>
              <template #input>
                <v-select
                  v-model="product_message.message_id"
                  :options="messages"
                  label="message"
                  :value="0"
                  :reduce="(message) => message.id"
                  multiple
                  placeholder="Select option"
                  class="mt-2"
                />
              </template>
            </c-form-group>
            <c-form-group v-if="messageIsEditing">
              <template #label> Message </template>
              <template #input>
                <v-select
                  v-model="product_message.message_id"
                  :options="messages"
                  label="message"
                  :value="0"
                  :reduce="(message) => message.id"
                  placeholder="Select option"
                  class="mt-2"
                />
              </template>
            </c-form-group>
          </div>
        </div>
      </c-card-body>
      <c-card-footer>
        <c-button
          color="primary"
          v-if="!messageIsEditing"
          @click="store"
          >Create</c-button
        >
        <c-button color="primary" v-if="messageIsEditing" @click="update"
          >Update</c-button
        >
        <CButton color="default" :to="{ name: 'products' }">Cancel</CButton>
      </c-card-footer>
    </c-card>
    <c-card>
      <c-card-body>
        <CDataTable
          hover
          striped
          sorter
          tableFilter
          footer
          itemsPerPageSelect
          :items="product_messages"
          :fields="messages_fields"
          :items-per-page="1000"
          :active-page="1"
          :responsive="true"
          pagination
          thead-top
        >
          <template slot="thead-top">
            <td style="border-top: none"><strong>Total</strong></td>
            <td style="border-top: none" class="text-xs-right">
              {{ product_messages.length }}
            </td>
          </template>
          <template #actions="{ item }">
            <td>
              <div class="row justify-content-center">
                <c-button
                  color="success"
                  class="btn-sm mt-2 mr-1 text-white"
                  @click="edit(item.id)"
                  ><i class="cil-pencil"></i><c-icon name="cil-pencil"
                /></c-button>
                <c-button
                  color="danger"
                  class="btn-sm mt-2 mr-1"
                  @click="
                    $root
                      .$confirm(
                        'Delete',
                        'Do you want to delete this record?',
                        {
                          color: 'red',
                          width: 290,
                          zIndex: 200,
                        }
                      )
                      .then((confirmed) => {
                        if (confirmed) {
                          deleteProductMessage(item);
                        }
                      })
                  "
                  ><c-icon name="cil-trash"
                /></c-button>
              </div>
            </td>
          </template>
        </CDataTable>
      </c-card-body>
    </c-card>
  </div>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  props: {
    product_id: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      messageIsEditing: false,
      messages: [],
      messages_fields: ["id", "product", "message", "actions"],
      product_messages: [],
      product_message: {
        id: 0,
        product_id: "",
        message_id: [],
      },
    };
  },
  methods: {
    initialize() {
      axios
        .get(`/api/get-messages`)
        .then((response) => {
          this.messages = response.data.messages;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getProductMessages() {
      axios
        .get(`/api/products/${this.product_id}/messages/`)
        .then((response) => {
          this.product_messages = response.data.product_messages;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    reset() {
      this.product_message = {
        id: 0,
        product_id: "",
        message_id: "",
      };
    },
    store() {
      axios
        .post(`/api/products/${this.product_id}/messages`, {
          product_id: this.product_id,
          message_id: this.product_message.message_id,
        })
        .then((response) => {
          this.flash("Message of this product created successfully");
          this.reset();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      //Refresh table
      this.getProductMessages();
    },
    edit(id) {
      axios
        .get(`/api/product-messages/${id}`)
        .then((response) => {
          this.messageIsEditing = true;
          this.product_message.id = response.data.product_message.id;
          this.product_message.product_id =
            response.data.product_message.product_id;
          this.product_message.message_id =
            response.data.product_message.message_id;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    update() {
      axios
        .put(`/api/product-messages/${this.product_message.id}`, {
          id: this.product_message.id,
          product_id: this.product_message.product_id,
          message_id: this.product_message.message_id,
        })
        .then((response) => {
          this.messageIsEditing = false;
          this.flash("Message of this product updated successfully");
          this.getProductMessages();
          this.reset();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
        this.scrollToTop();
    },
    removeProductMessage(item) {
      const index = this.product_messages.findIndex(
        (type) => item.id == type.id
      );
      this.product_messages.splice(index, 1);
    },
    deleteProductMessage(item) {
      axios
        .delete(`/api/product-messages/${item.id}`)
        .then((response) => {
          this.flash("Product Message Deleted Successfully");
          this.removeProductMessage(item);
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
    this.initialize();
    this.getProductMessages();
  },
};
</script>