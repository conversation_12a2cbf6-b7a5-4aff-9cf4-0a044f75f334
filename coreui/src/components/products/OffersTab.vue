<template>
  <div>
    <validation-observer ref="form">
      <c-card no-header>
        <c-card-body>
          <h4 v-if="product" class="pb-3">
            Make Offer To:
            <span class="font-weight-bold">{{
              product.name | capitalize
            }}</span>
          </h4>
          <c-row>
            <c-col>
              <label for="Related To">Related To</label>
              <validation-provider
                v-slot="{ errors, dirty, valid }"
                name="Products"
                rules="required"
              >
                <v-select
                  required
                  v-model="offer.products"
                  :options="relatedToProducts"
                  multiple
                  :reduce="(product) => product.id"
                  label="name"
                  placeholder="Select option"
                  class="mt-2"
                  :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                  :disabled="!product"
                />
                <div class="invalid-feedback">{{ errors[0] }}</div>
              </validation-provider>
            </c-col>
          </c-row>
          <c-row class="mb-3">
            <c-col v-if="offer.ratios.length == 0">
              <label for="Units">Units</label>
              <vue-tags-input
                v-model="unit"
                :tags="offer.units"
                :disabled="!product"
                placeholder="Units"
                :avoid-adding-duplicates="false"
                :is-duplicate="() => false"
                :separators="[',', ';']"
                :save-on-key="[',', ';']"
                :validation="validation"
                @tags-changed="(newUnits) => (offer.units = newUnits)"
                class="mt-2"
              />
            </c-col>
            <c-col v-if="offer.units.length == 0">
              <label for="Ratios">Ratios</label>
              <vue-tags-input
                :disabled="!product"
                v-model="ratio"
                :tags="offer.ratios"
                placeholder="Ratios"
                :avoid-adding-duplicates="false"
                :is-duplicate="() => false"
                :separators="[',', ';']"
                :save-on-key="[',', ';']"
                :validation="validation"
                @tags-changed="(newRatios) => (offer.ratios = newRatios)"
                class="mt-2"
              />
            </c-col>
          </c-row>

          <div class="d-block">
            <label for="hide">Hide</label>
            <input
              class="p-1 m-2"
              :disabled="!product"
              type="checkbox"
              id="hide"
              v-model="offer.hide"
              placeholder="Hide"
            />
          </div>
        </c-card-body>
        <c-card-footer>
          <button
            class="rounded btn btn-primary"
            :disabled="!customValidToStore"
            @click="store"
          >
            Create
          </button>
          <c-button :to="{name:'products'}" color="default">Cancel</c-button>
        </c-card-footer>
      </c-card>
      <product-offers v-if="product" />
    </validation-observer>
  </div>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import VueTagsInput from "@johmun/vue-tags-input";
import ProductOffers from "../../components/products/ProductOffers.vue";
import { mapState, mapActions, mapMutations } from "vuex";
import { ValidationProvider, ValidationObserver, extend } from "vee-validate";
import { required } from "vee-validate/dist/rules";
extend("required", { ...required, message: "The {_field_} field is required" });

export default {
  components: {
    vSelect,
    VueTagsInput,
    ValidationProvider,
    ValidationObserver,
    ProductOffers,
  },
  data() {
    return {
      unit: "",
      ratio: "",
      offer: {
        products: [],
        units: [],
        ratios: [],
        hide: false,
      },
      validation: [
        {
          classes: "class" /* css class */,
          rule: /(^[0-9]*)$/ /* RegExp */,
          disableAdd: true,
        },
      ],
    };
  },
  computed: {
    ...mapState("product", ["product", "products"]),
    relatedToProducts() {
      let relatedTo = this.products;
      return relatedTo.filter((product) => this.product != product);
    },
    customValidToStore() {
      if (this.product == null) return false;
      if (this.offer.products.length == 0) return false;
      return (
        this.offer.products.length == this.offer.units.length ||
        this.offer.products.length == this.offer.ratios.length
      );
    },
  },
  methods: {
    ...mapActions("product", ["getProducts",'getProductOffers']),
    store() {
      let request = {
        ...this.offer,
        units: this.offer.units.map((unit) => Number(unit.text)),
        ratio: this.offer.ratios.map((ratio) => Number(ratio.text)),
      };
      axios
        .post(`/api/products/${this.product.id}/offers`, request)
        .then(() => {
          this.getProductOffers().then(() => {
            this.rest();
          });
        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
    rest() {
      this.offer = {
        hide: false,
        products: [],
        units: [],
        ratios: [],
      };
      this.$refs.form.reset();
      this.scrollToTop();
      this.flash("Offer was added successfully.");
    },
  },
  created(){
    this.getProducts()
  }
};
</script>

<style>
.vue-tags-input .ti-input {
  border-radius: 3px;
  padding: 1px;
}
</style>
