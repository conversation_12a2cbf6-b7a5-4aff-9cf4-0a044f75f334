<template>
  <div>
    <CDataTable
      hover
      striped
      sorter
      tableFilter
      footer
      itemsPerPageSelect
      :items="productCeilings"
      :fields="fields"
      :items-per-page="1000"
      :active-page="1"
      :responsive="true"
      pagination
      thead-top
    >
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ productCeilings.length }}
        </td>
      </template>
      <template #actions="{ item }">
        <td>
          <div class="row justify-content-center">
            <CButton color="danger" class="btn-sm" @click="confirmDelete(item)"
              ><CIcon name="cil-trash"
            /></CButton>
          </div>
        </td>
      </template>
    </CDataTable>
  </div>
</template>

<script>
import { mapActions, mapState } from "vuex";
export default {
  data() {
    return {
      fields: [
        "id",
        "product",
        "units",
        "type",
        "from_date",
        "to_date",
        "actions",
      ],
      perPageSelect: {
        values: ["100", "200", "300", "400", "500"],
        external: true,
      },
      brandToDelete: null,
    };
  },
  computed: {
    ...mapState("product", ["productCeilings"]),
  },
  created() {
    this.initialize();
  },
  methods: {
    ...mapActions("product", ["getProductCeilings"]),
    initialize() {
      this.getProductCeilings();
    },
  },
};
</script>
