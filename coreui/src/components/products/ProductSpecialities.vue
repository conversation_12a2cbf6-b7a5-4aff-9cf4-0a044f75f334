<template>
  <div>
    <CDataTable
      hover
      striped
      sorter
      tableFilter
      footer
      :itemsPerPageSelect="perPageSelect"
      :items="productSpecialities"
      :fields="fields"
      :items-per-page="100"
      :active-page="1"
      :responsive="true"
      :table-filter="{ external: true, lazy: true }"
      pagination
      thead-top
    >
      <template #from_date="{item}">
        <td>{{ format_date(item.from_date) }}</td>
      </template>
      <template #to_date="{item}">
        <td>
          <span :class="item.to_date ? '' : 'badge badge-danger p-1'">{{
            item.to_date ? format_date(item.to_date) : "Not Registered"
          }}</span>
        </td>
      </template>
      <template slot="thead-top">
        <td style="border-top:none;"><strong>Total</strong></td>
        <td style="border-top:none;" class="text-xs-right">
          {{ specialitiesCount }}
        </td>
      </template>
      <!-- <template #actions="{item}">
        <td>
          <div class="row justify-content-center">
            <CButton
              color="success"
              class="btn-sm"
              @click="$refs.editSpecialityDialog.open(item)"
              ><i class="cil-pencil"></i><CIcon name="cil-pencil"
            /></CButton>
            <CButton color="danger" class="btn-sm" @click="confirmDelete(item)"
              ><CIcon name="cil-trash"
            /></CButton>
          </div>
        </td>
      </template> -->
    </CDataTable>
    <delete-confirmation-dialog
      ref="deleteSpecialityConfirmation"
      @DeleteConfirmed="deleteSpeciality"
    />
    <edit-product-speciality-dialog ref="editSpecialityDialog" />
  </div>
</template>

<script>
import { mapActions, mapGetters, mapState } from "vuex";
import moment from "moment";
import DeleteConfirmationDialog from "../common/DeleteConfirmationDialog.vue";
import EditProductSpecialityDialog from "./EditProductSpecialityDialog.vue";

export default {
  components: { DeleteConfirmationDialog, EditProductSpecialityDialog },
  data() {
    return {
      fields: ["id", "product", "speciality" , "from_date", "to_date"],
      perPageSelect: {
        values: ["100", "200", "300", "400", "500"],
        external: true
      },
      specialityToDelete: null
    };
  },
  computed: {
    ...mapState("product", ["productSpecialities"]),
    ...mapGetters("product", ["specialitiesCount"])
  },
  created() {
    this.initialize();
  },
  methods: {
    ...mapActions("product", ["getProductSpecialities"]),
    initialize() {
      this.getProductSpecialities();
    },

    format_date: function(value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    confirmDelete(speciality) {
      this.$refs.deleteSpecialityConfirmation.open();
      this.specialityToDelete = speciality;
    },
    deleteSpeciality() {
      const { id } = this.specialityToDelete;

      axios
        .delete(`/api/product-specialities/${id}`)
        .then(() => {
          this.getProductSpecialities().then(() => {
            this.flash("Deleted Successfully.");
          });
        })
        .catch(() => {
          this.flash("oOOops, something went wrong.", "error");
        })
        .then(() => {
          this.$refs.deleteSpecialityConfirmation.close();
          this.scrollToTop();
        });
    }
  }
};
</script>
