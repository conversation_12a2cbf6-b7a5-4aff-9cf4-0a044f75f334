<template>
  <div>
    <validation-observer ref="form" v-slot="{ invalid }">
      <CCard no-header>
        <CCardBody>
          <h4 v-if="product" class="pb-3">
            Add Brand To:
            <span class="font-weight-bold">{{ product.name }}</span>
          </h4>
          <div class="row">
            <div class="col">
              <CFormGroup>
                <template #label>
                  Brand
                </template>
                <template #input>
                  <validation-provider
                    v-slot="{ errors, dirty, valid }"
                    name="Brand"
                    rules="required"
                  >
                    <v-select
                      v-model="productBrand.brand_id"
                      :options="brands"
                      label="name"
                      :value="0"
                      :reduce="brand => brand.id"
                      :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                      placeholder="Select option"
                      class="mt-2"
                    />
                    <div class="invalid-feedback">{{ errors[0] }}</div>
                  </validation-provider>
                </template>
              </CFormGroup>
            </div>
            <div class="col">
              <label for="from_date">From</label>
              <validation-provider
                v-slot="{ errors, dirty, valid }"
                name="From Date"
                rules="required"
              >
                <input
                  type="date"
                  id="from_date"
                  :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                  class="form-control"
                  v-model="productBrand.from_date"
                  placeholder="From"
                />
                <div class="invalid-feedback">{{ errors[0] }}</div>
              </validation-provider>
            </div>
            <div class="col">
              <validation-provider
                v-slot="{ errors, dirty, valid }"
                name="To Date"
                :rules="`after:${productBrand.from_date}`"
              >
                <label for="to_date">To</label>
                <input
                  type="date"
                  id="to_date"
                  :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                  class="form-control"
                  v-model="productBrand.to_date"
                  placeholder="To"
                />
                <div class="invalid-feedback">{{ errors[0] }}</div>
              </validation-provider>
            </div>
          </div>
        </CCardBody>
        <CCardFooter>
          <button
            class="btn btn-primary rounded"
            :style="`cursor: ${invalid ? 'not-allowed' : ''}`"
            :disabled="invalid"
            @click="create"
          >
            Create
          </button>
          <CButton :to="{name:'products'}" color="default">Cancel</CButton>
        </CCardFooter>
      </CCard>
      <product-brands v-if="product" />
    </validation-observer>
  </div>
</template>

<script>
import { mapState, mapActions } from "vuex";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import ProductBrands from "./ProductBrands";
import { ValidationProvider, ValidationObserver, extend } from "vee-validate";
import { required } from "vee-validate/dist/rules";
extend("required", { ...required, message: "The {_field_} field is required" });
extend("after", {
  validate(toDate, { fromDate }) {
    return toDate >= fromDate;
  },
  params: ["fromDate"],
  message: "The {_field_} field must be after or equal {fromDate}."
});

export default {
  components: {
    vSelect,
    ProductBrands,
    ValidationObserver,
    ValidationProvider
  },
  data() {
    return {
      productBrand: {}
    };
  },
  computed: {
    ...mapState("product", ["product", "brands"])
  },
  methods: {
    ...mapActions("product", ["getProductBrands"]),
    create() {
      const { id } = this.product;

      axios
        .post(`/api/products/${id}/brands`, {
          ...this.productBrand,
          product_id: id
        })
        .then(() => {
          this.getProductBrands().then(this.reset);
        })
        .catch(() => {
          this.flash("oOOops, something went wrong", "error");
        });
    },
    reset() {
      this.productBrand = {};
      this.$refs.form.reset();
      this.scrollToTop();
      this.flash("Created successfully.");
    }
  }
};
</script>
