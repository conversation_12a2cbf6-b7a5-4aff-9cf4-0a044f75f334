<template>
  <div>
    <validation-observer ref="form" v-slot="{ invalid }">
      <CCard no-header>
        <CCardBody>
          <h4 v-if="product" class="pb-3">
            Add Price To:
            <span class="font-weight-bold">{{ product.name }}</span>
          </h4>
          <div class="row mb-3">
            <div class="col">
              <CFormGroup>
                <template #label>
                  Distributor
                </template>
                <template #input>
                  <validation-provider
                    v-slot="{ errors, dirty, valid }"
                    name="Brand"
                    rules="required"
                  >
                    <v-select
                      v-model="productPrice.distributor_id"
                      :options="distributors"
                      label="name"
                      :value="0"
                      :reduce="distributor => distributor.id"
                      :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                      placeholder="Select option"
                      class="mt-2"
                    />
                    <div class="invalid-feedback">{{ errors[0] }}</div>
                  </validation-provider>
                </template>
              </CFormGroup>
            </div>
            <div class="col">
              <label for="from_date">From</label>
              <validation-provider
                v-slot="{ errors, dirty, valid }"
                name="From Date"
                rules="required"
              >
                <input
                  type="date"
                  id="from_date"
                  :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                  class="form-control"
                  v-model="productPrice.from_date"
                  placeholder="From"
                />
                <div class="invalid-feedback">{{ errors[0] }}</div>
              </validation-provider>
            </div>
            <div class="col">
              <validation-provider
                v-slot="{ errors, dirty, valid }"
                name="To Date"
                :rules="`after:${productPrice.from_date}`"
              >
                <label for="to_date">To</label>
                <input
                  type="date"
                  id="to_date"
                  :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                  class="form-control"
                  v-model="productPrice.to_date"
                  placeholder="To"
                />
                <div class="invalid-feedback">{{ errors[0] }}</div>
              </validation-provider>
            </div>
          </div>
          <div class="row">
            <div class="col">
              <CInput
                label="Selling Price"
                type="text"
                placeholder="Selling Price"
                name="product_price.selling_price"
                v-model="productPrice.selling_price"
              ></CInput>
            </div>
            <div class="col">
              <CInput
                label="Avarge Price"
                type="text"
                placeholder="Avg Price"
                name="product_price.avg_price"
                v-model="productPrice.avg_price"
              ></CInput>
            </div>
          </div>

          <div class="row">
            <div class="col">
              <CInput
                label="Avarge Tender Price"
                type="text"
                placeholder="Avg Tender Price"
                name="product_price.avg_tender_price"
                v-model="productPrice.avg_tender_price"
              ></CInput>
            </div>
            <div class="col">
              <CInput
                label="Avarge Target Price"
                type="text"
                placeholder="Avg Target Price"
                name="product_price.avg_target_price"
                v-model="productPrice.avg_target_price"
              ></CInput>
            </div>
          </div>
        </CCardBody>
        <CCardFooter>
          <button
            class="btn btn-primary rounded"
            :style="`cursor: ${invalid ? 'not-allowed' : ''}`"
            :disabled="invalid"
            @click="create"
          >
            Create
          </button>
          <CButton :to="{name:'products'}" color="default">Cancel</CButton>
        </CCardFooter>
      </CCard>
      <product-prices v-if="product" />
    </validation-observer>
  </div>
</template>

<script>
import { mapState, mapActions } from "vuex";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { ValidationProvider, ValidationObserver, extend } from "vee-validate";
import { required } from "vee-validate/dist/rules";
import ProductPrices from "./ProductPrices.vue";
extend("required", { ...required, message: "The {_field_} field is required" });
extend("after", {
  validate(toDate, { fromDate }) {
    return toDate >= fromDate;
  },
  params: ["fromDate"],
  message: "The {_field_} field must be after or equal {fromDate}."
});

export default {
  components: {
    vSelect,
    ValidationObserver,
    ValidationProvider,
    ProductPrices
  },
  data() {
    return {
      productPrice: {}
    };
  },
  computed: {
    ...mapState("product", ["product", "distributors"])
  },
  methods: {
    ...mapActions("product", ["getProductPrices"]),
    create() {
      const { id } = this.product;

      axios
        .post(`/api/products/${id}/prices`, {
          ...this.productPrice,
          product_id: id
        })
        .then(() => {
          this.getProductPrices().then(this.reset);
        })
        .catch(() => {
          this.flash("oOOops, something went wrong", "error");
        });
    },
    reset() {
      this.productPrice = {};
      this.$refs.form.reset();
      this.scrollToTop();
      this.flash("Created successfully.");
    }
  }
};
</script>
