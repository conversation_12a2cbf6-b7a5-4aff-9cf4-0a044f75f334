<template>
  <div>
    <CDataTable
      hover
      striped
      sorter
      tableFilter
      footer
      itemsPerPageSelect
      :items="productOffers"
      :fields="fields"
      :items-per-page="100"
      :active-page="1"
      :responsive="true"
      table-filter
      pagination
      thead-top
    >
      <template #units="{ item }">
        <td>
          <span :class="item.units || 'badge badge-danger p-1'">{{
            item.units || "Not Registered"
          }}</span>
        </td>
      </template>
      <template #ratios="{ item }">
        <td>
          <span :class="item.ratios || 'badge badge-danger p-1'">{{
            item.ratios || "Not Registered"
          }}</span>
        </td>
      </template>
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ offersCount }}
        </td>
      </template>
      <template #actions="{ item }">
        <td>
          <div class="row justify-content-center">
            <CButton
              color="success"
              class="btn-sm"
              @click="$refs.editPriceDialog.open(item)"
              ><i class="cil-pencil"></i><CIcon name="cil-pencil"
            /></CButton>
            <CButton color="danger" class="btn-sm" @click="showModal(item)"
              ><CIcon name="cil-trash"
            /></CButton>
          </div>
        </td>
      </template>
    </CDataTable>
    <edit-product-price-dialog ref="editPriceDialog" />
  </div>
</template>

<script>
import { mapActions, mapGetters, mapState } from "vuex";
import EditProductPriceDialog from "./EditProductPriceDialog.vue";

export default {
  components: { EditProductPriceDialog },
  data() {
    return {
      fields: ["id", "name", "units", "ratios", "actions"],
    };
  },
  computed: {
    ...mapState("product", ["product", "productOffers"]),
    ...mapGetters("product", ["offersCount"]),
  },
  created() {
    this.initialize();
  },
  mounted() {
    this.$root.$on("DeleteConfirmed", (offer) => {
      this.delete(offer);
    });
  },
  methods: {
    ...mapActions("product", ["getProductOffers"]),
    initialize() {
      this.getProductOffers();
    },
    showModal(item) {
      this.$root
        .$confirm(
          "Delete",
          `Are you sure you want to delete ${item.name}`,
          { color: "red" }
        )
        .then((confirm) => {
          if (confirm) {
            this.delete(item);
          }
        });
    },
    delete(offer) {
      const { id } = offer;
      axios
        .delete(`/api/products/${this.product.id}/offers/${id}`)
        .then(() => {
          this.getProductOffers().then(this.flash("Deleted Successfully."));
        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
  },
};
</script>
