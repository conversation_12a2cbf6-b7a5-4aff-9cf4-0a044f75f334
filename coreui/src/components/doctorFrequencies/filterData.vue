<template>
  <c-card>
    <c-card-header>Create Doctor Frequencies</c-card-header>
    <c-card-body>
      <c-tabs>
        <c-tab active>
          <template slot="title">
            <c-icon name="cil-chart-pie" class="custom_icon" /> Line & Divisions
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Line </template>
                    <template #input>
                      <v-select
                        v-model="line_id"
                        :options="lines"
                        label="name"
                        :value="0"
                        :reduce="(line) => line.id"
                        placeholder="Select Line"
                        class="mt-2"
                        @input="getLineData"
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Division </template>
                    <template #input>
                      <input
                        id="divisions"
                        label="All"
                        v-if="divisions.length != 0"
                        class="m-1"
                        type="checkbox"
                        v-model="checkAllDivisions"
                        title="Check All Divisions"
                        @change="checkAllDivs"
                      />
                      <label
                        for="divisions"
                        v-if="divisions.length != 0"
                        style="font-weight: bold"
                        >All</label
                      >
                      <v-select
                        v-model="division_ids"
                        :options="divisions"
                        label="name"
                        :value="0"
                        :reduce="(division) => division.id"
                        placeholder="Select Division"
                        class="mt-2"
                        multiple
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-input
                    label="Date"
                    type="date"
                    placeholder="Date"
                    :min="minDate"
                    v-model="date"
                  ></c-input>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon name="cil-description" class="custom_icon" /> Account Types & Specialities
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Account Type </template>
                    <template #input>
                      <input
                        id="types"
                        label="All"
                        v-if="types.length != 0"
                        class="m-1"
                        type="checkbox"
                        v-model="allAccountTypes"
                        title="Check All Types"
                        @change="checkAllAccounts"
                      />
                      <label
                        for="types"
                        v-if="types.length != 0"
                        style="font-weight: bold"
                        >All</label
                      >
                      <v-select
                        v-model="type_ids"
                        :options="types"
                        label="name"
                        :value="0"
                        :reduce="(type) => type.id"
                        placeholder="Select Account Type"
                        class="mt-2"
                        multiple
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Speciality </template>
                    <template #input>
                      <input
                        id="specialities"
                        label="All"
                        v-if="specialities.length != 0"
                        class="m-1"
                        type="checkbox"
                        v-model="checkAllSpecialities"
                        title="Check All Specialities"
                        @change="checkAllSpech"
                      />
                      <label
                        for="specialities"
                        v-if="specialities.length != 0"
                        style="font-weight: bold"
                        >All</label
                      >
                      <v-select
                        v-model="speciality_ids"
                        :options="specialities"
                        label="name"
                        :value="0"
                        :reduce="(speciality) => speciality.id"
                        placeholder="Select Speciality"
                        class="mt-2"
                        multiple
                      />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
      </c-tabs>
    </c-card-body>
    <c-card-footer>
      <c-button
        v-if="
          line_id != null &&
          division_ids.length != 0 &&
          type_ids.length != 0 &&
          speciality_ids.length != 0
        "
        color="primary"
        class="text-white"
        @click="show"
        style="float: right"
        >Show</c-button
      >
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      type_ids: [],
      speciality_ids: [],
      division_ids: [],
      line_id: null,
      types: [],
      specialities: [],
      divisions: [],
      lines: [],
      allAccountTypes: true,
      checkAllSpecialities: true,
      checkAllDivisions: false,
      date: null,
      minDate:null
    };
  },
  methods: {
    async initialize() {
      await axios
        .get("/api/accountsLines/")
        .then((response) => {
          this.lines = response.data.data.lines;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
        await axios
        .get("/api/date-control/")
        .then((response) => {
          this.date = response.data.date;
          this.minDate = response.data.min_date;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .get(`/api/getDataLineReport/${this.line_id}`)
        .then((response) => {
          this.divisions = response.data.data.divisions;
          this.types = response.data.data.account_types;
          this.specialities = response.data.data.specialities;
          this.type_ids = this.types.map((item) => item.id);
          this.speciality_ids = this.specialities.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllAccounts() {
      if (this.allAccountTypes)
        this.type_ids = this.types.map((item) => item.id);
      if (this.allAccountTypes == false) this.type_ids = null;
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_ids = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_ids = null;
    },
    checkAllSpech() {
      if (this.checkAllSpecialities)
        this.speciality_ids = this.specialities.map((item) => item.id);
      if (this.checkAllSpecialities == false) this.speciality_ids = null;
    },
    show() {
      let listFilter = {
        line: this.line_id,
        divisions: this.division_ids,
        types: this.type_ids,
        specialities: this.speciality_ids,
        date: this.date,
      };
      this.$emit("getSchedule", { listFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>