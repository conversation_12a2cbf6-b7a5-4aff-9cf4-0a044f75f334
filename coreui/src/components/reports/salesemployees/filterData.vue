<template>
  <c-card>
    <c-card-header>Sales Employees Report</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Line </template>
            <template #input>
              <v-select
                v-model="line_id"
                :options="lines"
                label="name"
                :value="0"
                :reduce="(line) => line.id"
                placeholder="Select option"
                class="mt-2"
                @input="getLineData"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Division </template>
            <template #input>
              <input
                label="All"
                id="divisons"
                v-if="divisions.length != 0"
                class="m-1"
                type="checkbox"
                v-model="checkAllDivisions"
                title="Check All Divisions"
                @change="checkAllDivision"
              />
              <label
                for="divisions"
                v-if="divisions.length != 0"
                style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="division_id"
                :options="divisions"
                label="name"
                :value="0"
                :reduce="(division) => division.id"
                placeholder="Select option"
                class="mt-2"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Product </template>
            <template #input>
              <input
                label="All"
                id="products"
                v-if="products.length != 0"
                class="m-1"
                type="checkbox"
                v-model="checkAllProducts"
                title="Check All Products"
                @change="checkAllProduct"
              />
              <label
                for="products"
                v-if="products.length != 0"
                style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="product_id"
                :options="products"
                label="name"
                :value="0"
                :reduce="(product) => product.id"
                placeholder="Select option"
                class="mt-2"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Users </template>
            <template #input>
              <input
                label="All"
                id="users"
                v-if="users.length != 0"
                class="m-1"
                type="checkbox"
                v-model="checkAllUsers"
                title="Check All Users"
                @change="checkAllUser"
              />
              <label
                for="users"
                v-if="users.length != 0"
                style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="user_id"
                :options="users"
                label="name"
                :value="0"
                :reduce="(user) => user.id"
                placeholder="Select option"
                class="mt-2"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> types </template>
            <template #input>
              <v-select
                v-model="type_id"
                :options="types"
                label="name"
                :value="0"
                :reduce="(type) => type.id"
                placeholder="Select option"
                class="mt-2"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-input
            label="From Date"
            type="date"
            placeholder="Date"
            v-model="from_date"
          ></c-input>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-input
            label="To Date"
            type="date"
            placeholder="Date"
            v-model="to_date"
          ></c-input>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button
        v-if="line_id != null"
        color="primary"
        class="text-white"
        @click="show"
        style="float: right"
        >Show</c-button
      >
    </c-card-footer>
  </c-card>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      lines: [],
      users: [],
      types: [
        { id: 1, name: "Product" },
        { id: 2, name: "Brand" },
      ],
      type_id: 1,
      divisions: [],
      products: [],
      line_id: null,
      user_id: [],
      division_id: [],
      product_id: [],
      checkAllUsers: false,
      checkAllDivisions: false,
      checkAllProducts: false,
      from_date: new Date().toISOString().slice(0, 10),
      to_date: new Date().toISOString().slice(0, 10),
    };
  },
  methods: {
    initialize() {
      axios
        .get("/api/lines-sales-employees/")
        .then((response) => {
          this.lines = response.data.data.lines;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post("/api/line-data-sales-employees/", { line: this.line_id })
        .then((response) => {
          this.divisions = response.data.data.divisions;
          this.users = response.data.data.users;
          this.distributors = response.data.data.distributors;
          this.products = response.data.data.products;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllUser() {
      if (this.checkAllUsers)
        this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    checkAllDivision() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = null;
    },
    checkAllProduct() {
      if (this.checkAllProducts)
        this.product_id = this.products.map((item) => item.id);
      if (this.checkAllProducts == false) this.product_id = null;
    },
    show() {
      let saleFilter = {
        line: this.line_id,
        users: this.user_id,
        divisions: this.division_id,
        products: this.product_id,
        type: this.type_id,
        fromDate: this.from_date,
        toDate: this.to_date,
      };
      this.$emit("getSchedule", { saleFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
