<template>
  <c-card>
    <c-card-header>Vacations Report</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Line </template>
            <template #input>
              <input
                label="All"
                v-if="lines.length != 0"
                id="line"
                class="m-1"
                type="checkbox"
                v-model="checkAllLines"
                title="Check All lines"
                @change="checkAllLine"
              />
              <label
                for="line"
                v-if="lines.length != 0"
                style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="line_id"
                :options="lines"
                label="name"
                :value="0"
                :reduce="(line) => line.id"
                placeholder="Select Line"
                class="mt-2"
                multiple
                @input="getLineData"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Employee </template>
            <template #input>
              <input
                label="All"
                v-if="users.length != 0"
                id="user"
                class="m-1"
                type="checkbox"
                v-model="checkAllUsers"
                title="Check All Users"
                @change="checkAllEmployees"
              />
              <label
                for="user"
                v-if="users.length != 0"
                style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="user_id"
                :options="users"
                label="fullname"
                :value="0"
                :reduce="(user) => user.id"
                placeholder="Select Employee"
                class="mt-2"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Vacation Types</strong>
            </template>
            <template #input>
              <input
                v-if="types.length != 0"
                class="m-1"
                type="checkbox"
                id="type"
                v-model="checkAllTypes"
                title="Check All Types"
                @change="checkAllType"
              />
              <label
                for="type"
                v-if="types.length != 0"
                style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="type_id"
                :options="types"
                label="name"
                :value="0"
                required
                multiple
                :reduce="(type) => type.id"
                placeholder="Select Vacation Type"
                class="mt-2"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Approval Types</strong>
            </template>
            <template #input>
              <v-select
                v-model="approval_id"
                :options="approvals"
                label="name"
                :value="0"
                required
                :reduce="(approval) => approval.id"
                placeholder="Select Approvals"
                class="mt-2"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-input
            label="From"
            type="date"
            placeholder="From"
            v-model="from_date"
          ></c-input>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-input
            label="To"
            type="date"
            placeholder="To"
            v-model="to_date"
          ></c-input>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button
        v-if="line_id.length > 0"
        color="primary"
        class="text-white"
        @click="show"
        style="float: right"
        >Show</c-button
      >
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      type_id: [],
      approval_id: null,
      user_id: [],
      line_id: [],
      divisions: [],
      lines: [],
      types: [],
      approvals: [
        { id: 1, name: "Approved" },
        { id: 2, name: "Disapproved" },
        { id: 3, name: "Total" },
      ],
      users: [],
      checkAllTypes: false,
      checkAllApprovals: false,
      checkAllUsers: false,
      checkAllLines: false,
      from_date: moment().startOf('month').format("YYYY-MM-DD"),
      to_date: moment().endOf('month').format("YYYY-MM-DD"),
    };
  },
  methods: {
    initialize() {
      axios
        .get("/api/vacation-report-lines/")
        .then((response) => {
          this.lines = response.data.lines;
          this.types = response.data.types;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post(`/api/get-vacation-line-data/`, {
          lines: this.line_id,
        })
        .then((response) => {
          this.users = response.data.users;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllType() {
      if (this.checkAllTypes) this.type_id = this.types.map((item) => item.id);
      if (this.checkAllTypes == false) this.type_id = null;
    },
    checkAllLine() {
      if (this.checkAllLines) {
        this.line_id = this.lines.map((item) => item.id);
        this.getLineData();
      }
      if (this.checkAllLines == false) this.line_id = null;
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    show() {
      let vacationFilter = {
        lines: this.line_id,
        types: this.type_id,
        approval: this.approval_id,
        users: this.user_id,
        fromDate: this.from_date,
        toDate: this.to_date,
      };
      this.$emit("getSchedule", { vacationFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>