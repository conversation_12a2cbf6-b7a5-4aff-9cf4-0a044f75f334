<template>
  <c-card>
    <c-card-header>List Per Speciality Report</c-card-header>
    <c-card-body>
      <!-- <c-tabs> -->
        <!-- <c-tab active> -->
          <template slot="title">
            <c-icon class="custom_icon" name="cil-description" /> Line &
            Division
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <!-- <c-form-group>
                    <template #label> Line </template>
                    <template #input>
                      <v-select
                        v-model="line_id"
                        :options="lines"
                        label="name"
                        :value="0"
                        :reduce="(line) => line.id"
                        placeholder="Select Line"
                        class="mt-2"
                        @input="getDataWithLine"
                      />
                    </template>
                  </c-form-group> -->
                  <c-form-group>
                    <template #label> Line </template>
                    <template #input>
                      <input
                        label="All"
                        v-if="lines.length != 0"
                        class="m-1"
                        id="lines"
                        type="checkbox"
                        v-model="checkAllLines"
                        title="Check All Lines"
                        @change="checkAllLine"
                      />
                      <label
                        for="lines"
                        v-if="lines.length != 0"
                        style="font-weight: bold"
                        >All</label
                      >
                      <v-select
                        v-model="line_id"
                        :options="lines"
                        label="name"
                        :value="0"
                        :reduce="(line) => line.id"
                        placeholder="Select Line"
                        class="mt-2"
                        @input="getDataWithLine"
                        multiple
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label>
                      Filter By <span style="color: red">*</span></template
                    >
                    <template #input>
                      <v-select
                        v-model="filter"
                        :options="filters"
                        label="name"
                        :value="0"
                        :reduce="(line) => line.id"
                        placeholder="Select Filter"
                        class="mt-2"
                        @input="
                          filter == 1 ? (user_id = []) : (division_id = [])
                        "
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8" v-if="filter == 1">
                  <c-form-group>
                    <template #label> Division </template>
                    <template #input>
                      <input
                        label="All"
                        id="division"
                        v-if="divisions.length != 0"
                        class="m-1"
                        type="checkbox"
                        v-model="checkAllDivisions"
                        title="Check All Divisions"
                        @change="checkAllDivs"
                      />
                      <label
                        for="division"
                        v-if="divisions.length != 0"
                        style="font-weight: bold"
                        >All</label
                      >
                      <v-select
                        v-model="division_ids"
                        :options="divisions"
                        label="name"
                        :value="0"
                        :reduce="(division) => division.id"
                        placeholder="Select Division"
                        class="mt-2"
                        multiple
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8" v-if="filter == 2">
                  <c-form-group>
                    <template #label> Employee </template>
                    <template #input>
                      <input
                        label="All"
                        v-if="users.length != 0"
                        id="user"
                        class="m-1"
                        type="checkbox"
                        v-model="checkAllUsers"
                        title="Check All Users"
                        @change="checkAllEmployees"
                      />
                      <label
                        for="user"
                        v-if="users.length != 0"
                        style="font-weight: bold"
                        >All</label
                      >
                      <v-select
                        v-model="user_id"
                        :options="users"
                        label="fullname"
                        :value="0"
                        :reduce="(user) => user.id"
                        placeholder="Select Employee"
                        class="mt-2"
                        multiple
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Speciality </template>
                    <template #input>
                      <input
                        label="All"
                        v-if="specialities.length != 0"
                        class="m-1"
                        type="checkbox"
                        v-model="checkAllSpecialities"
                        title="Check All Specialities"
                        @change="checkAllSpech"
                      />
                      <label
                        v-if="specialities.length != 0"
                        style="font-weight: bold"
                        >All</label
                      >
                      <v-select
                        v-model="speciality_ids"
                        :options="specialities"
                        label="name"
                        :value="0"
                        :reduce="(speciality) => speciality.id"
                        placeholder="Select Speciality"
                        class="mt-2"
                        multiple
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Account Type </template>
                    <template #input>
                      <input
                        label="All"
                        v-if="accountTypes.length != 0"
                        class="m-1"
                        type="checkbox"
                        v-model="checkAllAccountTypes"
                        title="Check All Account Types"
                        @change="checkAllAccounts"
                      />
                      <label
                        v-if="accountTypes.length != 0"
                        style="font-weight: bold"
                        >All</label
                      >
                      <v-select
                        v-model="accountType_ids"
                        :options="accountTypes"
                        label="name"
                        :value="0"
                        :reduce="(type) => type.id"
                        placeholder="Select Account Type"
                        class="mt-2"
                        multiple
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-form-group>
                    <template #label> Status </template>
                    <template #input>
                      <v-select
                        v-model="type"
                        :options="['All' ,'Active', 'Inactive']"
                        placeholder="Select Type"
                        class="mt-2"
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-form-group>
                    <template #label>
                      <strong>Active Date</strong>
                    </template>
                    <template #input>
                      <c-input
                        type="date"
                        class="mt-2"
                        placeholder="Active Date"
                        v-model="activeDate"
                      ></c-input>
                    </template>
                  </c-form-group>
                </div>
                <div
                  v-if="filter == 1"
                  style="padding-top: 45px; padding-left: 60px"
                  class="col-lg-2 col-md-2 col-sm-8"
                >
                  <strong>Show Bricks : </strong>
                  <input class="px-4" type="checkbox" v-model="checked" />
                </div>
              </div>
            </c-card-body>
          </c-card>
        <!-- </c-tab> -->

        <!-- <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Account Types &
            Specialities
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Account Type </template>
                    <template #input>
                      <input
                        label="All"
                        v-if="accountTypes.length != 0"
                        class="m-1"
                        type="checkbox"
                        v-model="checkAllAccountTypes"
                        title="Check All Account Types"
                        @change="checkAllAccounts"
                      />
                      <label
                        v-if="accountTypes.length != 0"
                        style="font-weight: bold"
                        >All</label
                      >
                      <v-select
                        v-model="accountType_ids"
                        :options="accountTypes"
                        label="name"
                        :value="0"
                        :reduce="(type) => type.id"
                        placeholder="Select Account Type"
                        class="mt-2"
                        multiple
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Speciality </template>
                    <template #input>
                      <input
                        label="All"
                        v-if="specialities.length != 0"
                        class="m-1"
                        type="checkbox"
                        v-model="checkAllSpecialities"
                        title="Check All Specialities"
                        @change="checkAllSpech"
                      />
                      <label
                        v-if="specialities.length != 0"
                        style="font-weight: bold"
                        >All</label
                      >
                      <v-select
                        v-model="speciality_ids"
                        :options="specialities"
                        label="name"
                        :value="0"
                        :reduce="(speciality) => speciality.id"
                        placeholder="Select Speciality"
                        class="mt-2"
                        multiple
                      />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab> -->
        <!-- <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Classes
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Classes </template>
                    <template #input>
                      <input
                        label="All"
                        v-if="classes.length != 0"
                        class="m-1"
                        type="checkbox"
                        v-model="checkAllClasses"
                        title="Check All Classes"
                        @change="checkAllclass"
                      />
                      <label
                        v-if="classes.length != 0"
                        style="font-weight: bold"
                        >All</label
                      >
                      <v-select
                        v-model="class_ids"
                        :options="classes"
                        label="name"
                        :value="0"
                        :reduce="(clas) => clas.id"
                        placeholder="Select Class"
                        class="mt-2"
                        multiple
                      />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab> -->

      <!-- </c-tabs> -->
    </c-card-body>
    <c-card-footer v-if="line_id != null">
      <c-button
        color="primary"
        class="text-white"
        @click="show"
        style="float: right"
        >Show</c-button
      >
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  props: {
    autoscroll: {
      type: Boolean,
      default: true,
    },
  },
  emits: ["getList"],
  data() {
    return {
      lines: [],
      line_id: [],
      specialities: [],
      accountTypes: [],
      divisions: [],
      classes: [],
      speciality_ids: [],
      class_ids: [],
      accountType_ids: [],
      division_ids: [],
      users: [],
      user_id: [],
      checked: false,
      filters: [
        { id: 1, name: "Division" },
        { id: 2, name: "Employee" },
      ],
      countBy: "Doctor",
      filter: 2,
      type: "Active",
      checkAllLines: false,
      checkAllAccountTypes: true,
      checkAllClasses: true,
      checkAllSpecialities: true,
      checkAllDivisions: false,
      checkAllUsers: false,
      activeDate: new Date().toISOString().slice(0, 10),
    };
  },
  methods: {
    initialize() {
      axios
        .get("/api/listPerSpecialityLines")
        .then((response) => {
          this.lines = response.data.lines;
          this.users = response.data.users;
          this.accountTypes = response.data.account_types;
          this.accountType_ids = this.accountTypes.map((item) => item.id);
        })
        .catch((error) => {
          showErrorMessage(error);
        });
    },
    getDataWithLine() {
      axios
        .post(`/api/data-for-list-per-speciality`,{
          lines: this.line_id
        })
        .then((response) => {
          this.specialities = response.data.specialities;
          this.divisions = response.data.divisions;
          this.users = response.data.users;
          this.speciality_ids = this.specialities.map((item) => item.id);

        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },

    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = null;
      this.getLineData();
    },
    checkAllAccounts() {
      if (this.checkAllAccountTypes)
        this.accountType_ids = this.accountTypes.map((item) => item.id);
      if (this.checkAllAccountTypes == false) this.accountType_ids = null;
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_ids = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_ids = null;
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    checkAllSpech() {
      if (this.checkAllSpecialities)
        this.speciality_ids = this.specialities.map((item) => item.id);
      if (this.checkAllSpecialities == false) this.speciality_ids = null;
    },
    // checkAllclass() {
    //   if (this.checkAllClasses)
    //     this.class_ids = this.classes.map((item) => item.id);
    //   if (this.checkAllClasses == false) this.class_ids = null;
    // },
    show() {
      let listFilter = {
        lines: this.line_id,
        filter: this.filter,
        type: this.type,
        countBy: this.countBy,
        accountTypes: this.accountType_ids,
        specialities: this.speciality_ids,
        // classes: this.class_ids,
        divisions: this.division_ids,
        users: this.user_id,
        fromDate: this.activeDate,
        toDate: this.activeDate,
        checked: this.checked,
      };
      this.$emit("getSchedule", { listFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>