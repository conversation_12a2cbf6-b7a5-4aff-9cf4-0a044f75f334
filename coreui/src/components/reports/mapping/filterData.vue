<template>
  <c-card>
    <c-card-header>Mapping Report</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Line </template>
            <template #input>
              <input
                label="All"
                v-if="lines.length != 0"
                class="m-1"
                id="lines"
                type="checkbox"
                v-model="checkAllLines"
                title="Check All Lines"
                @change="checkAllLine"
              />
              <label
                for="lines"
                v-if="lines.length != 0"
                style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="line_id"
                :options="lines"
                label="name"
                :value="0"
                :reduce="(line) => line.id"
                placeholder="Select option"
                class="mt-2"
                @input="getLineData"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Division </template>
            <template #input>
              <input
                label="All"
                v-if="divisions.length != 0"
                class="m-1"
                type="checkbox"
                v-model="checkAllDivisions"
                title="Check All Divisions"
                @change="checkAllDivs"
              />
              <label v-if="divisions.length != 0" style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="division_id"
                :options="divisions"
                label="name"
                :value="0"
                :reduce="(division) => division.id"
                placeholder="Select option"
                class="mt-2"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Distributor </template>
            <template #input>
              <input
                label="All"
                v-if="distributors.length != 0"
                class="m-1"
                type="checkbox"
                v-model="checkAllDistributors"
                title="Check All Distributors"
                @change="checkAllDistributor"
              />
              <label v-if="distributors.length != 0" style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="distributor_id"
                :options="distributors"
                label="name"
                :value="0"
                :reduce="(distributor) => distributor.id"
                placeholder="Select option"
                class="mt-2"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Type </template>
            <template #input>
              <input
                label="All"
                v-if="types.length != 0"
                class="m-1"
                type="checkbox"
                v-model="checkAllTypes"
                title="Check All Types"
                @change="checkAllType"
              />
              <label v-if="types.length != 0" style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="type_id"
                :options="types"
                label="name"
                :value="0"
                :reduce="(type) => type.id"
                placeholder="Select option"
                class="mt-2"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Ratio </template>
            <template #input>
              <v-select
                v-model="ratio"
                :options="percent"
                label="name"
                :value="0"
                :reduce="(percent) => percent.id"
                placeholder="Select option"
                class="mt-2"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-input
            label="date"
            type="date"
            placeholder="date"
            v-model="date"
          ></c-input>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button
        v-if="line_id.length > 0"
        color="primary"
        class="text-white"
        @click="show"
        style="float: right"
        >Show</c-button
      >
    </c-card-footer>
  </c-card>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      lines: [],
      distributors: [],
      divisions: [],
      types: [],
      line_id: [],
      distributor_id: [],
      type_id: [],
      percent: [
        { id: 1, name: "100%" },
        { id: 2, name: "Other" },
      ],
      ratio: null,
      division_id: [],
      checkAllLines: false,
      checkAllTypes: false,
      checkAllDivisions: false,
      checkAllDistributors: false,
      date: new Date().toISOString().slice(0, 10),
    };
  },
  methods: {
    initialize() {
      axios
        .get("/api/lines-mapping/")
        .then((response) => {
          this.lines = response.data.data.lines;
          this.types = response.data.data.types;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post("/api/getDataLineReport/", { lines: this.line_id })
        .then((response) => {
          this.divisions = response.data.data.divisions;
          this.distributors = response.data.data.distributors;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = null;
      this.getLineData();
    },
    checkAllType() {
      if (this.checkAllTypes) this.type_id = this.types.map((item) => item.id);
      if (this.checkAllTypes == false) this.type_id = null;
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = null;
    },
    checkAllDistributor() {
      if (this.checkAllDistributors)
        this.distributor_id = this.distributors.map((item) => item.id);
      if (this.checkAllDistributors == false) this.distributor_id = null;
    },
    show() {
      let mappingFilter = {
        lines: this.line_id,
        types: this.type_id,
        ratio: this.ratio,
        divisions: this.division_id,
        distributors: this.distributor_id,
        date: this.date,
      };
      this.$emit("getSchedule", { mappingFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
