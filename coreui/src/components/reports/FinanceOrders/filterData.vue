<template>
  <c-card>
    <c-card-header>Finance Orders Report</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Line </template>
            <template #input>
              <input label="All" v-if="lines.length != 0" id="line" class="m-1" type="checkbox" v-model="checkAllLines"
                title="Check All Lines" @change="checkAllLine" />
              <label for="line" v-if="lines.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                placeholder="Select Line" class="mt-2" multiple @input="getLineData" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Employee </template>
            <template #input>
              <input label="All" v-if="users.length != 0" id="user" class="m-1" type="checkbox" v-model="checkAllUsers"
                title="Check All Employees" @change="checkAllUser" />
              <label for="user" v-if="users.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="user_id" :options="users" label="fullname" :value="0" :reduce="(user) => user.id"
                placeholder="Select Employee" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Status </template>
            <template #input>
              <v-select v-model="statusAction" :options="status" label="name" placeholder="Select Status"
                class="mt-2" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-input label="From" type="date" placeholder="From" v-model="from_date"></c-input>
        </div>

        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-input label="To" type="date" placeholder="To" v-model="to_date"></c-input>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" class="text-white" @click="show" style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";

export default {
  components: {
    vSelect,
  },
  emits: ["getOrders"],
  data() {
    return {
      lines: [],
      line_id: [],
      users: [],
      user_id: [],
      status: ["Total", "Pending", "Paid", "Later"],
      statusAction: null,
      checkAllLines: false,
      checkAllUsers: false,
      from_date: moment().startOf('month').format("YYYY-MM-DD"),
      to_date: moment().endOf('month').format("YYYY-MM-DD"),
    };
  },
  methods: {
    initialize() {
      axios
        .get("/api/material-costs-lines")
        .then((response) => {
          this.lines = response.data.data.lines;
          this.users = response.data.data.users;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post(`/api/get-line-material-cost`, {
          lines: this.line_id
        })
        .then((response) => {
          this.users = response.data.data.users;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) {
        this.line_id = [];
        this.user_id = [];
      }
      this.getLineData();
    },
    checkAllUser() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = [];
    },
    show() {
      let financeOrderFilter = {
        lines: this.line_id,
        users: this.user_id,
        status: this.statusAction,
        fromDate: this.from_date,
        toDate: this.to_date,
      };
      this.$emit("getOrders", { financeOrderFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>