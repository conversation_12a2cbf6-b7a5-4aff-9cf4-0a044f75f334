<template>
    <v-dialog v-model="dialog" :max-width="options.width" :style="{ zIndex: options.zIndex }" @keydown.esc="cancel">
        <v-card>
            <v-toolbar dark :color="options.color" dense flat>
                <v-toolbar-title class="white--text">{{ title }}</v-toolbar-title>
                <v-spacer></v-spacer>
                <v-btn icon @click="cancel">
                    <v-icon>mdi-close</v-icon>
                </v-btn>
            </v-toolbar>
            <v-card-text>
                <v-tabs v-model="activeTab">
                    <v-tab>Table View</v-tab>
                    <v-tab>Card View</v-tab>

                    <v-tab-item>
                        <v-data-table :headers="headers" :items="salesData" :items-per-page="5" class="elevation-1 mt-3"
                            :footer-props="{
                                'items-per-page-options': [5, 10, 15, -1],
                                'items-per-page-text': 'Records per page',
                            }">
                            <template v-slot:item.date="{ item }">
                                {{ formatDate(item.date) }}
                            </template>
                            <template v-slot:item.created_at="{ item }">
                                {{ formatDate(item.created_at) }}
                            </template>
                            <template v-slot:item.updated_at="{ item }">
                                {{ formatDate(item.updated_at) }}
                            </template>
                        </v-data-table>
                    </v-tab-item>

                    <v-tab-item>
                        <div class="mt-3">
                            <v-expansion-panels>
                                <v-expansion-panel v-for="(sale, index) in salesData" :key="index">
                                    <v-expansion-panel-header>
                                        <div class="d-flex align-center">
                                            <v-chip class="mr-2" :color="getRandomColor(index)" text-color="white"
                                                small>
                                                ID: {{ sale.id }}
                                            </v-chip>
                                            <span>{{ sale.product_id ? `Product: ${sale.product_id}` : '' }}</span>
                                            <v-spacer></v-spacer>
                                            <span class="text-caption">{{ formatDate(sale.date) }}</span>
                                        </div>
                                    </v-expansion-panel-header>
                                    <v-expansion-panel-content>
                                        <v-card flat>
                                            <v-card-text>
                                                <v-row>
                                                    <v-col cols="12" sm="6" md="4">
                                                        <v-list-item>
                                                            <v-list-item-content>
                                                                <v-list-item-subtitle>ID</v-list-item-subtitle>
                                                                <v-list-item-title>{{ sale.id }}</v-list-item-title>
                                                            </v-list-item-content>
                                                        </v-list-item>
                                                    </v-col>
                                                    <v-col cols="12" sm="6" md="4">
                                                        <v-list-item>
                                                            <v-list-item-content>
                                                                <v-list-item-subtitle>Mapping ID</v-list-item-subtitle>
                                                                <v-list-item-title>{{ sale.mapping_id
                                                                }}</v-list-item-title>
                                                            </v-list-item-content>
                                                        </v-list-item>
                                                    </v-col>
                                                    <v-col cols="12" sm="6" md="4">
                                                        <v-list-item>
                                                            <v-list-item-content>
                                                                <v-list-item-subtitle>Quantity</v-list-item-subtitle>
                                                                <v-list-item-title>{{ sale.quantity
                                                                }}</v-list-item-title>
                                                            </v-list-item-content>
                                                        </v-list-item>
                                                    </v-col>
                                                    <v-col cols="12" sm="6" md="4">
                                                        <v-list-item>
                                                            <v-list-item-content>
                                                                <v-list-item-subtitle>Bonus</v-list-item-subtitle>
                                                                <v-list-item-title>{{ sale.bonus }}</v-list-item-title>
                                                            </v-list-item-content>
                                                        </v-list-item>
                                                    </v-col>
                                                    <v-col cols="12" sm="6" md="4">
                                                        <v-list-item>
                                                            <v-list-item-content>
                                                                <v-list-item-subtitle>Region</v-list-item-subtitle>
                                                                <v-list-item-title>{{ sale.region }}</v-list-item-title>
                                                            </v-list-item-content>
                                                        </v-list-item>
                                                    </v-col>
                                                    <v-col cols="12" sm="6" md="4">
                                                        <v-list-item>
                                                            <v-list-item-content>
                                                                <v-list-item-subtitle>Product ID</v-list-item-subtitle>
                                                                <v-list-item-title>{{ sale.product_id
                                                                }}</v-list-item-title>
                                                            </v-list-item-content>
                                                        </v-list-item>
                                                    </v-col>
                                                    <v-col cols="12" sm="6" md="4">
                                                        <v-list-item>
                                                            <v-list-item-content>
                                                                <v-list-item-subtitle>Distributor
                                                                    ID</v-list-item-subtitle>
                                                                <v-list-item-title>{{ sale.distributor_id
                                                                }}</v-list-item-title>
                                                            </v-list-item-content>
                                                        </v-list-item>
                                                    </v-col>
                                                    <v-col cols="12" sm="6" md="4">
                                                        <v-list-item>
                                                            <v-list-item-content>
                                                                <v-list-item-subtitle>Value</v-list-item-subtitle>
                                                                <v-list-item-title>{{ sale.value }}</v-list-item-title>
                                                            </v-list-item-content>
                                                        </v-list-item>
                                                    </v-col>
                                                    <v-col cols="12" sm="6" md="4">
                                                        <v-list-item>
                                                            <v-list-item-content>
                                                                <v-list-item-subtitle>Date</v-list-item-subtitle>
                                                                <v-list-item-title>{{ formatDate(sale.date)
                                                                }}</v-list-item-title>
                                                            </v-list-item-content>
                                                        </v-list-item>
                                                    </v-col>
                                                    <v-col cols="12" sm="6" md="4">
                                                        <v-list-item>
                                                            <v-list-item-content>
                                                                <v-list-item-subtitle>File ID</v-list-item-subtitle>
                                                                <v-list-item-title>{{ sale.file_id
                                                                }}</v-list-item-title>
                                                            </v-list-item-content>
                                                        </v-list-item>
                                                    </v-col>
                                                    <v-col cols="12" sm="6" md="4">
                                                        <v-list-item>
                                                            <v-list-item-content>
                                                                <v-list-item-subtitle>Created At</v-list-item-subtitle>
                                                                <v-list-item-title>{{ formatDate(sale.created_at)
                                                                }}</v-list-item-title>
                                                            </v-list-item-content>
                                                        </v-list-item>
                                                    </v-col>
                                                    <v-col cols="12" sm="6" md="4">
                                                        <v-list-item>
                                                            <v-list-item-content>
                                                                <v-list-item-subtitle>Updated At</v-list-item-subtitle>
                                                                <v-list-item-title>{{ formatDate(sale.updated_at)
                                                                }}</v-list-item-title>
                                                            </v-list-item-content>
                                                        </v-list-item>
                                                    </v-col>
                                                    <v-col cols="12" sm="6" md="4">
                                                        <v-list-item>
                                                            <v-list-item-content>
                                                                <v-list-item-subtitle>Ceiling</v-list-item-subtitle>
                                                                <v-list-item-title>{{ sale.ceiling
                                                                }}</v-list-item-title>
                                                            </v-list-item-content>
                                                        </v-list-item>
                                                    </v-col>
                                                    <v-col cols="12" sm="6" md="4">
                                                        <v-list-item>
                                                            <v-list-item-content>
                                                                <v-list-item-subtitle>Sale IDs</v-list-item-subtitle>
                                                                <v-list-item-title>{{ sale.sale_ids
                                                                }}</v-list-item-title>
                                                            </v-list-item-content>
                                                        </v-list-item>
                                                    </v-col>
                                                </v-row>
                                            </v-card-text>
                                        </v-card>
                                    </v-expansion-panel-content>
                                </v-expansion-panel>
                            </v-expansion-panels>
                        </div>
                    </v-tab-item>
                </v-tabs>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script>
import moment from "moment";
import { mapActions } from "vuex";

export default {
    data() {
        return {
            dialog: false,
            activeTab: 0,
            resolve: null,
            reject: null,
            options: {
                color: "primary",
                width: 900,
                zIndex: 200,
            },
            title: "Sales Details",
            salesData: [],
            headers: [
                { text: "ID", value: "id" },
                { text: "Mapping ID", value: "mapping_id" },
                { text: "Quantity", value: "quantity" },
                { text: "Bonus", value: "bonus" },
                { text: "Region", value: "region" },
                { text: "Product ID", value: "product_id" },
                { text: "Distributor ID", value: "distributor_id" },
                { text: "Value", value: "value" },
                { text: "Date", value: "date" },
                { text: "File ID", value: "file_id" },
                { text: "Created At", value: "created_at" },
                { text: "Updated At", value: "updated_at" },
                { text: "Ceiling", value: "ceiling" },
                { text: "Sale IDs", value: "sale_ids" },
            ],
        };
    },
    methods: {
        ...mapActions("app", ["loadVApp", "unLoadVApp"]),
        open(salesData) {
            this.unLoadVApp()
            this.dialog = true;
            this.salesData = Array.isArray(salesData) ? salesData : [salesData];
            return new Promise((resolve, reject) => {
                this.resolve = resolve;
                this.reject = reject;
            });
        },
        cancel() {
            this.resolve(false);
            this.dialog = false;
            this.unLoadVApp()
        },
        formatDate(dateString) {
            if (!dateString) return "";
            return moment(dateString).format("YYYY-MM-DD HH:mm:ss");
        },
        getRandomColor(index) {
            const colors = [
                "primary",
                "secondary",
                "success",
                "info",
                "warning",
                "error",
                "purple",
                "indigo",
                "teal",
            ];
            return colors[index % colors.length];
        },
    },
};
</script>

<style scoped>
.v-data-table {
    max-height: 500px;
    overflow-y: auto;
}
</style>