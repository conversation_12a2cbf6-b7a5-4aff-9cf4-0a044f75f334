<template>
  <div class="col-5">
    <c-form-group>
      <template #label> Product </template>
      <template #input>
        <input
          label="All"
          v-if="products.length != 0"
          class="m-1"
          type="checkbox"
          v-model="checkAllProducts"
          title="Check All Products"
          checked
          @change="checkAllProduct"
        />
        <label v-if="products.length != 0" style="font-weight: bold"
          >All</label
        >
        <v-select
          v-model="product_ids"
          :options="products"
          label="name"
          :value="0"
          :reduce="(product) => product.id"
          placeholder="Select option"
          class="mt-2"
          multiple
          @input="send"
        />
      </template>
    </c-form-group>
  </div>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  props:{
    line: {
      type:Number,
      required:true,
    }
  },
  data() {
    return {
      products: [],
      product_ids: [],
      checkAllProducts: true,
    };
  },
  emits: ["products"],
  methods: {
    initialize() {
      axios
        .get(`/api/get-products/${this.line}`)
        .then((response) => {
          this.products = response.data.products;
          this.product_ids = this.products.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllProduct() {
      if (this.checkAllProducts)
        this.product_ids = this.products.map((item) => item.id);
      if (this.checkAllProducts == false) this.product_ids = null;
    },
    send() {
      this.$emit("products", this.product_ids );
    },
  },
  created() {
    this.initialize();
  },
};
</script>