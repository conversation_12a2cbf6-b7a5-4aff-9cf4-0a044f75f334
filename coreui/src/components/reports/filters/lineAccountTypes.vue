<template>
  <div class="col-5">
    <c-form-group>
      <template #label> Account Type </template>
      <template #input>
        <input
          label="All"
          v-if="accountTypes.length != 0"
          class="m-1"
          type="checkbox"
          v-model="checkAllAccountTypes"
          title="Check All Account Types"
          checked
          @change="checkAllAccounts"
        />
        <label v-if="accountTypes.length != 0" style="font-weight: bold"
          >All</label
        >
        <v-select
          v-model="accountType_ids"
          :options="accountTypes"
          label="name"
          :value="0"
          :reduce="(type) => type.id"
          placeholder="Select option"
          class="mt-2"
          multiple
        />
      </template>
    </c-form-group>
  </div>
</template>
<script>
export default {
  props: {
    line: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      accountTypes: [],
      accountType_ids: [],
      checkAllAccountTypes: true,
    };
  },
  methods: {
    initialize() {},
  },
  created() {
    this.initialize();
  },
};
</script>