<template>
  <div class="col-4">
    <c-form-group>
      <template #label> Line </template>
      <template #input>
        <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
          placeholder="Select option" class="mt-2" @input="getData()" />
      </template>
    </c-form-group>
  </div>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      lines: [],
      line_id: null,
    };
  },
  emits: ["lines"],
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: null,
          to: null,
          data: ['lines']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getData() {
      this.$emit("lines", this.line_id);
    }
  },
  created() {
    this.initialize();
  },
};
</script>