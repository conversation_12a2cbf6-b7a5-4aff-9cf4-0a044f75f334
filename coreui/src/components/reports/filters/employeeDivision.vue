<template>
  <div class="col-4">
    <c-form-group>
      <template #label> Employee </template>
      <template #input>
        <input
          label="All"
          v-if="users.length != 0"
          class="m-1"
          type="checkbox"
          v-model="checkAllUsers"
          title="Check All Employees"
          @change="checkAllusers"
        />
        <label v-if="users.length != 0" style="font-weight: bold">All</label>
        <v-select
          v-model="user_ids"
          :options="users"
          label="name"
          :value="0"
          :reduce="(user) => user.id"
          placeholder="Select option"
          class="mt-2"
          multiple
          @input="send"
        />
      </template>
    </c-form-group>
  </div>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  props: {
    divisions: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      users: [],
      user_ids: [],
      checkAllUsers: true,
    };
  },
  emits:['users'],
  methods: {
    initialize() {
      this.user_ids=[];
      axios
        .post(`/api/get-users/`, { divisions:this.divisions })
        .then((response) => {
          this.users = response.data.users;
          this.user_ids = this.users.map((item) => item.id);
        })
        .catch((error) => {
          showErrorMessage(error);
        });
    },
    checkAllusers() {
      if (this.checkAllUsers) this.user_ids = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_ids = null;
    },
    send() {
      this.$emit("users", this.user_ids );
    },
  },
  created() {
    this.initialize();
  },
};
</script>