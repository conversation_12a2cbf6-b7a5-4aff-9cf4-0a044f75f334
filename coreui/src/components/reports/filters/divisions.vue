<template>
  <div class="col-4">
    <c-form-group>
      <template #label> Division </template>
      <template #input>
        <input
          label="All"
          v-if="divisions.length != 0"
          class="m-1"
          type="checkbox"
          v-model="checkAllDivisions"
          title="Check All Divisions"
          @change="checkAllDivs"
        />
        <label v-if="divisions.length != 0" style="font-weight: bold"
          >All</label
        >
        <v-select
          v-model="division_ids"
          :options="divisions"
          label="name"
          :value="0"
          :reduce="(division) => division.id"
          placeholder="Select option"
          class="mt-2"
          multiple
          @input="getEmployees"
        />
      </template>
    </c-form-group>
  </div>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  props: {
    line:{
      type: Number,
      required: true,
    }
  },
  data() {
    return {
      divisions: [],
      division_ids: [],
      checkAllDivisions: false,
    };
  },
  emits:['divisions'],
  methods: {
    initialize() {
      axios
        .get(`/api/get-divisions/${this.line}`)
        .then((response) => {
          this.divisions = response.data.divisions;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_ids = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_ids = null;
    },
    getEmployees(){
        this.$emit('divisions',this.division_ids);
    },
  },
  created(){
    this.initialize();
  },
};
</script>