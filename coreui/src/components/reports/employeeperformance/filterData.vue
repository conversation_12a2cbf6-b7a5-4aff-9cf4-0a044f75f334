<template>
  <c-card>
    <c-card-header>Employee Performance Report</c-card-header>
    <c-card-body>
      <c-tabs>
        <c-tab active>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Main
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-input label="From" type="date" placeholder="From" v-model="from_date"></c-input>
                </div>

                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-input label="To" type="date" placeholder="To" v-model="to_date" @input="getAllData"></c-input>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Line </template>
                    <template #input>
                      <input label="All" v-if="lines.length != 0" id="line" class="m-1" type="checkbox"
                        v-model="checkAllLines" title="Check All lines" @change="checkAllLine" />
                      <label for="line" v-if="lines.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                        placeholder="Select Lines" class="mt-2" multiple @input="getLineData" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-form-group>
                    <template #label> Positions </template>
                    <template #input>
                      <v-select v-model="position_id" :options="positions" label="name" :value="0"
                        :reduce="(position) => position.id" placeholder="Select Position" class="mt-3"
                        @input="getUsers()" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Employee </template>
                    <template #input>
                      <input label="All" v-if="users.length != 0" id="user" class="m-1" type="checkbox"
                        v-model="checkAllUsers" title="Check All Users" @change="checkAllEmployees" />
                      <label for="user" v-if="users.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="user_id" :options="users" label="fullname" :value="0"
                        :reduce="(user) => user.id" placeholder="Select Employee" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Shift </template>
                    <template #input>
                      <input label="All" v-if="shifts.length != 0" id="shift" class="m-1" type="checkbox"
                        v-model="checkAllShifts" title="Check All Shifts" @change="checkAllShift" />
                      <label for="shift" v-if="shifts.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="shift_id" :options="shifts" label="name" placeholder="Select Shift"
                        :reduce="(shift) => shift.id" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Result By </template>
                    <template #input>
                      <v-select v-model="result_by" :options="['filtered', 'All Lines']" label="name"
                        placeholder="Select Type" class="mt-2" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-form-group>
                    <template #label> View </template>
                    <template #input>
                      <v-select v-model="view" :options="['Advanced', 'Classic']" label="name" placeholder="Select View"
                        class="mt-2" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8" v-if="view == 'Classic'">
                  <c-form-group>
                    <template #label> Class </template>
                    <template #input>
                      <input label="All" v-if="classes.length != 0" id="class" class="m-1" type="checkbox"
                        v-model="checkAllClasses" title="Check All Classes" @change="checkAllClass" />
                      <label for="class" v-if="classes.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="class_id" :options="classes" label="name" placeholder="Select Class"
                        :reduce="(classe) => classe.id" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>

        <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Account Types &
            Specialities & classes
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Account Type </template>
                    <template #input>
                      <input label="All" id="type" v-if="types.length != 0" class="m-1" type="checkbox"
                        v-model="allAccountTypes" title="Check All Types" @change="checkAllAccounts" />
                      <label for="type" v-if="types.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="type_id" :options="types" label="name" :value="0" :reduce="(type) => type.id"
                        placeholder="Select Account Types" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Speciality </template>
                    <template #input>
                      <input label="All" id="speciality" v-if="specialities.length != 0" class="m-1" type="checkbox"
                        v-model="checkAllSpecialities" title="Check All Specialities" @change="checkAllSpech" />
                      <label for="speciality" v-if="specialities.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="speciality_id" :options="specialities" label="name" :value="0"
                        :reduce="(speciality) => speciality.id" placeholder="Select Specialities" class="mt-2"
                        multiple />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
      </c-tabs>
    </c-card-body>
    <c-card-footer>
      <c-button v-if="line_id.length > 0" color="warning" class="text-white" @click="show('post')"
        style="float: left">Post
        Call Rate</c-button>
      <c-button v-if="line_id.length > 0" color="primary" class="text-white" @click="show('show')"
        style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";

export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule", "getPostData"],
  data() {
    return {
      user_id: [],
      shift_id: [],
      class_id: [],
      line_id: [],
      view: "Advanced",
      lines: [],
      users: [],
      classes: [],
      shifts: [],
      positions: [],
      position_id: null,
      specialities: [],
      speciality_id: [],
      types: [],
      type_id: [],
      result_by: 'filtered',
      checkAllLines: false,
      checkAllClasses: true,
      checkAllUsers: false,
      checkAllShifts: true,
      allAccountTypes: false,
      checkAllSpecialities: false,
      from_date: moment().startOf("month").format("YYYY-MM-DD"),
      to_date: moment().endOf("month").format("YYYY-MM-DD"),
    };
  },
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: this.from_date,
          to: this.to_date,
          data: ['lines', 'shifts', 'accountTypes', 'users']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.shifts = response.data.data.shifts;
          this.users = response.data.data.users;
          this.types = response.data.data.accountTypes;
          this.type_id = this.types.map((item) => item.id);
          this.shift_id = this.shifts.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getpositions() {
      axios
        .post("/api/get-positions", {
          lines: this.line_id,
          from: this.from_date,
          to: this.to_date,
        })
        .then((res) => {
          this.positions = res.data.data;
        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
    getUsers() {
      axios
        .post("/api/position-users", {
          lines: this.line_id,
          role_id: this.position_id.split("_")[0],
          role_type: this.position_id.split("_")[1],
          from: this.from_date,
          to: this.to_date,
        })
        .then((res) => {
          this.users = res.data.users;
        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
    getLineData() {
      axios
        .post(`/api/get-lines-data-with-dates`, {
          lines: this.line_id,
          from: this.from_date,
          to: this.to_date,
          data: ['users', 'classes', 'specialities']
        })
        .then((response) => {
          this.users = response.data.data.users;
          this.classes = response.data.data.classes;
          this.class_id = this.classes.map((item) => item.id);
          this.specialities = response.data.data.specialities;
          this.speciality_id = this.specialities.map((item) => item.id);
          this.getpositions()
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getAllData() {
      this.line_id = [];
      this.position_id = null;
      this.user_id = [];
      this.class_id = [];
      this.speciality_id = [];
      this.initialize();
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = null;
      this.getLineData();
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    checkAllShift() {
      if (this.checkAllShifts)
        this.shift_id = this.shifts.map((item) => item.id);
      if (this.checkAllShifts == false) this.shift_id = null;
    },
    checkAllClass() {
      if (this.checkAllClasses)
        this.class_id = this.classes.map((item) => item.id);
      if (this.checkAllClasses == false) this.class_id = null;
    },
    checkAllSpech() {
      if (this.checkAllSpecialities)
        this.speciality_id = this.specialities.map((item) => item.id);
      if (this.checkAllSpecialities == false) this.speciality_id = [];
    },
    checkAllAccounts() {
      if (this.allAccountTypes)
        this.type_id = this.types.map((item) => item.id);
      if (this.allAccountTypes == false) this.type_id = [];
    },
    show(show) {
      let kpisFilter = {
        show: show,
        lines: this.line_id,
        users: this.user_id,
        position: this.position_id,
        shifts: this.shift_id,
        classes: this.class_id,
        view: this.view,
        result_by: this.result_by,
        fromDate: this.from_date,
        toDate: this.to_date,
        accountTypes: this.type_id,
        specialities: this.speciality_id,
      };
      this.$emit("getSchedule", { kpisFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>