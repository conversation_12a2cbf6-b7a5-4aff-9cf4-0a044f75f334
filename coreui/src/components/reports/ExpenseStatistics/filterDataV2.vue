<template>
  <c-card>
    <c-card-header>Expense Report</c-card-header>
    <c-card-body>
      <!-- <c-tabs>
        <c-tab active>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Main
          </template> -->
      <c-card>
        <c-card-body>
          <div class="row">
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-input label="From" type="date" placeholder="From" v-model="from_date"></c-input>
            </div>

            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-input label="To" type="date" placeholder="To" v-model="to_date" @input="getAllData"></c-input>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-form-group>
                <template #label>
                  Line <span style="color: red">*</span></template>
                <template #input>
                  <input label="All" v-if="lines.length != 0" id="line" class="m-1" type="checkbox"
                    v-model="checkAllLines" title="Check All lines" @change="checkAllLine" />
                  <label for="line" v-if="lines.length != 0" style="font-weight: bold">All</label>
                  <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                    placeholder="Select Line" class="mt-2" multiple @input="getLineData" />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-form-group>
                <template #label>
                  Filter By <span style="color: red">*</span></template>
                <template #input>
                  <v-select v-model="filter" :options="filters" label="name" :value="0" :reduce="(filter) => filter.id"
                    placeholder="Select Filter" class="mt-2" @input="getUsers" />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-form-group>
                <template #label> Employee </template>
                <template #input>
                  <input label="All" v-if="users.length != 0" id="user" class="m-1" type="checkbox"
                    v-model="checkAllUsers" title="Check All Users" @change="checkAllEmployees" />
                  <label for="user" v-if="users.length != 0" style="font-weight: bold">All</label>
                  <v-select v-model="user_id" :options="users" label="fullname" :value="0" :reduce="(user) => user.id"
                    placeholder="Select Employee" class="mt-2" multiple />
                </template>
              </c-form-group>
            </div>
            <!-- <div class="row"> -->
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-form-group>
                <template #label> Approval Types </template>
                <template #input>
                  <v-select v-model="approval_id" :options="approvals" label="name" :value="0"
                    :reduce="(approval) => approval.value" placeholder="Select Approval Types" class="mt-2" />
                </template>
              </c-form-group>
            </div>
          </div>
        </c-card-body>
      </c-card>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" class="text-white" @click="show" style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";

export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      // division_id: [],
      // product_id: [],
      // type_id: [],
      user_id: [],
      line_id: [],
      // divisions: [],
      // products: [],
      approvals: [
        { name: "Pending", value: 1 },
        { name: "Approved", value: 2 },
        { name: "Disapproved", value: 3 },
        { name: "Pending & Approved", value: 4 },
        { name: "Total", value: 5 },
      ],
      approval_id: 5,
      // payments: [
      //   { name: "No", value: 1 },
      //   { name: "Yes", value: 2 },
      //   { name: "Partial", value: 3 },
      //   { name: "Total", value: 4 },
      // ],
      // payment_id: 4,
      // specialities: [],
      // types: [],
      lines: [],
      users: [],
      filters: [
        { id: 1, name: "position" },
        { id: 2, name: "Employee" },
        { id: 3, name: "All" },
      ],
      filter: 2,
      // checkAllDivisions: false,
      checkAllUsers: false,
      // checkAllExpenseTypes: false,
      // checkAllProducts: false,
      checkAllLines: false,
      from_date: moment().startOf("month").format("YYYY-MM-DD"),
      to_date: moment().endOf("month").format("YYYY-MM-DD"),
    };
  },
  methods: {
    getUsers() {
      axios
        .post("/api/get-users-stats/", {
          filter: this.filter,
          lines: this.line_id,
          from: this.from_date,
          to: this.to_date,
        })
        .then((response) => {
          this.users = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: null,
          to: null,
          data: ['lines', 'users']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.users = response.data.data.users;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post(`/api/get-lines-stats-data`, {
          lines: this.line_id,
          filter: this.filter,
          from: this.from_date,
          to: this.to_date,
        })
        .then((response) => {
          this.users = response.data.users;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = null;
      this.getLineData();
    },
    getAllData() {
      this.line_id = [];
      this.user_id = [];
      this.filter = 2;
      this.initialize();
    },
    // checkAllExpenseType() {
    //   if (this.checkAllExpenseTypes)
    //     this.type_id = this.types.map((item) => item.id);
    //   if (this.checkAllExpenseTypes == false) this.type_id = null;
    // },
    // checkAllProduct() {
    //   if (this.checkAllProducts)
    //     this.product_id = this.products.map((item) => item.id);
    //   if (this.checkAllProducts == false) this.product_id = null;
    // },
    // checkAllDivs() {
    //   if (this.checkAllDivisions)
    //     this.division_id = this.divisions.map((item) => item.id);
    //   if (this.checkAllDivisions == false) this.division_id = null;
    // },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    show() {
      let expenseFilter = {
        lines: this.line_id,
        filter: this.filter,
        approval: this.approval_id,
        // payment: this.payment_id,
        // divisions: this.division_id,
        // types: this.type_id,
        // specialities: this.speciality_id,
        // products: this.product_id,
        users: this.user_id,
        fromDate: this.from_date,
        toDate: this.to_date,
      };
      this.$emit("getSchedule", { expenseFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>