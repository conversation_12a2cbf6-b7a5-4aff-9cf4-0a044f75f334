<template>
  <div>
    <GenericDataTable
      :items="items"
      :columns="tableColumns"
      :search-term="searchTerm"
      :selected-items="selectedItems"
      :select-all="selectAll"
      :selectable="hasSelectionColumn"
      :is-loading="isLoading"
      search-placeholder="Search visits in all fields..."
      @search="$emit('search', $event)"
      @sort="$emit('sort', $event)"
      @selection-change="onSelectionChange"
      @page-change="$emit('page-change', $event)"
      @page-size-change="$emit('page-size-change', $event)"
    >
      <!-- Comment field with show more/less functionality -->
      <template #comment="{ item, value }">
        <div>
          <div v-if="!readMore[item.id]" @click="$emit('showMore', item.id)">
            {{ value && value.length > 30 ? value.substring(0, 30) + ".." : value }}
            <br v-if="value && value.length > 30" />
            <span v-if="value && value.length > 30" style="color: blue; cursor: pointer">show more</span>
          </div>
          <div v-else @click="$emit('showLess', item.id)">
            {{ value }}
            <br />
            <span style="color: blue; cursor: pointer">show less</span>
          </div>
        </div>
      </template>

      <!-- Division field -->
      <template #division="{ value }">
        <strong style="color: green">{{ value }}</strong>
      </template>

      <!-- Date fields -->
      <template #start="{ value }">
        <span>{{ formatDate(value) }}</span>
      </template>

      <template #end="{ value }">
        <span>{{ formatDate(value) }}</span>
      </template>

      <!-- Account field -->
      <template #account="{ item, value }">
        <strong style="color: blue; cursor: pointer">
          <a @click="$root.$account('Account Data', item.account_id)">{{ value }}</a>
        </strong>
      </template>

      <!-- Doctor field -->
      <template #doctor="{ item, value }">
        <strong style="color: blue; cursor: pointer">
          <a @click="$root.$doctor('Doctor Data', item.doctor_id)">{{ value }}</a>
        </strong>
      </template>

      <!-- Attachments field -->
      <template #attachments="{ value }">
        <div>
          <span v-if="value">
            <span v-for="(path, pathIndex) in value.split(', ')" :key="pathIndex">
              <a :href="path" target="_blank">{{ path.split('/').pop() }}</a>
              <span v-if="pathIndex < value.split(', ').length - 1">, </span>
            </span>
          </span>
          <span v-else>-</span>
        </div>
      </template>

      <!-- Type field -->
      <template #type="{ item, value }">
        <div>
          <strong v-if="item.is_automatic == 1" style="color: #f1c40f; cursor: pointer">{{ value }}</strong>
          <strong v-else>{{ value }}</strong>
        </div>
      </template>

      <!-- Account type field -->
      <template #acc_type="{ item, value }">
        <div>
          <strong v-if="item.acc_shift_id == 1" style="color: #efa609">{{ value }}</strong>
          <strong v-if="item.acc_shift_id == 2" style="color: #ef09c2">{{ value }}</strong>
          <strong v-if="item.acc_shift_id == 3" style="color: #09efde">{{ value }}</strong>
        </div>
      </template>

      <!-- Visit shift field -->
      <template #visit_shift_id="{ item }">
        <div>
          <strong v-if="item.visit_shift_id == 1" style="color: #efa609">{{ item.acc_type }}</strong>
          <strong v-if="item.visit_shift_id == 2" style="color: #ef09c2">{{ item.acc_type }}</strong>
          <strong v-if="item.visit_shift_id == 3" style="color: #09efde">{{ item.acc_type }}</strong>
        </div>
      </template>

      <!-- Status field -->
      <template #status="{ value }">
        <div>
          <strong v-if="value == 'Approved'" style="color: green">Approved</strong>
          <strong v-if="value == 'Disapproved'" style="color: red">Disapproved</strong>
          <strong v-if="value == 'Pending'" style="color: blue">Pending</strong>
        </div>
      </template>

      <!-- Details button -->
      <template #v_details="{ item, index }">
        <div class="py-2">
          <CButton color="primary" variant="outline" square size="sm" class="mt-3"
            @click="$emit('toggleDetails', item, index)">
            {{ Boolean(item._toggled) ? "Hide" : "Details" }}
          </CButton>
        </div>
      </template>

      <!-- Map button -->
      <template #map="{ item }">
        <div>
          <c-button v-if="checkPermission('show_single_actual_visits_locations')" color="primary"
            class="btn-sm mt-2 mr-1" @click="$emit('getLocation', item)">
            <CIcon class="text-white" name="cil-location-pin" />
          </c-button>
        </div>
      </template>

      <!-- Plan actions -->
      <template #plan_actions="{ item }">
        <div>
          <c-button color="success" class="btn-sm mt-2 mr-1" v-if="checkPermission('edit_plan_visits')"
            :to="{ name: 'EditPlanVisit', params: { id: item.id } }">
            <CIcon name="cil-pencil" />
          </c-button>
          <c-button color="danger" class="btn-sm mt-2 mr-1" v-if="checkPermission('delete_plan_visits')" @click="
            $root
              .$confirm('Delete', 'Do you want to delete this record?', {
                color: 'red',
                width: 290,
                zIndex: 200,
              })
              .then((confirmed) => {
                if (confirmed) {
                  $emit('deletePlan', item);
                }
              })
            ">
            <CIcon name="cil-trash" />
          </c-button>
        </div>
      </template>

      <!-- Actual actions -->
      <template #actual_actions="{ item }">
        <div>
          <CButton color="primary" class="btn-sm mt-2 mr-1" v-if="checkPermission('show_single_actual_visits')"
            :to="{ name: 'ActualVisit', params: { id: item.id } }" target="_blank">
            <CIcon name="cil-magnifying-glass" />
          </CButton>
          <CButton color="warning" class="btn-sm mt-2 mr-1 text-white"
            v-if="checkPermission('create_pv_request')" :to="{ name: 'PV', params: { id: item.id } }"
            target="_blank">
            PV
          </CButton>
          <CButton color="success" class="btn-sm mt-2 mr-1" v-if="checkPermission('edit_actual_visits')"
            :to="{ name: 'EditActualVisit', params: { id: item.id } }" target="_blank">
            <CIcon name="cil-pencil" />
          </CButton>
          <c-button color="danger" v-if="checkPermission('delete_actual_visits')" class="btn-sm mt-2 mr-1"
            @click="
              $root
                .$confirm('Delete', 'Do you want to delete this record?', {
                  color: 'red',
                  width: 290,
                  zIndex: 200,
                })
                .then((confirmed) => {
                  if (confirmed) {
                    $emit('deleteActualVisit', item);
                  }
                })
              ">
            <CIcon name="cil-trash" />
          </c-button>
        </div>
      </template>

      <!-- OW actions -->
      <template #ow_actions="{ item }">
        <div>
          <CButton color="success" class="btn-sm mt-2 mr-1" v-if="checkPermission('edit_ow_actual_visits')" :to="{
            name: 'EditOwActualVisit',
            params: { id: item.id },
          }">
            <CIcon name="cil-pencil" />
          </CButton>
          <c-button color="danger" class="btn-sm mt-2 mr-1" v-if="checkPermission('delete_ow_actual_visits')"
            @click="
              $root
                .$confirm('Delete', 'Do you want to delete this record?', {
                  color: 'red',
                  width: 290,
                  zIndex: 200,
                })
                .then((confirmed) => {
                  if (confirmed) {
                    $emit('deleteOwActual', item);
                  }
                })
              ">
            <CIcon name="cil-trash" />
          </c-button>
        </div>
      </template>
    </GenericDataTable>
  </div>
</template>

<script>
import moment from "moment";
import GenericDataTable from "../../common/GenericDataTable.vue";

export default {
  name: 'VisitsReportTable',
  components: {
    GenericDataTable
  },
  props: {
    items: {
      type: Array,
      required: true
    },
    fields: {
      type: Array,
      required: true
    },
    readMore: {
      type: Object,
      required: true
    },
    searchTerm: {
      type: String,
      default: ''
    },
    selectAll: {
      type: Boolean,
      default: false
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      selectedItems: []
    };
  },

  computed: {
    // Convert fields array to columns configuration
    tableColumns() {
      return this.fields
        .filter(field => field !== 's') // Remove 's' field as it's handled by selectable prop
        .map(field => ({
          key: field,
          label: this.formatFieldName(field),
          sortable: !this.isActionColumn(field),
          type: this.getColumnType(field)
        }));
    },

    // Check if selection column should be shown
    hasSelectionColumn() {
      return this.fields.includes('s');
    }
  },

  methods: {
    // Format field name (convert snake_case to Title Case)
    formatFieldName(field) {
      if (!field) return '';
      const withSpaces = field.replace(/[_-]/g, ' ');
      return withSpaces.replace(/\b\w/g, c => c.toUpperCase());
    },

    // Get column type for proper formatting
    getColumnType(field) {
      if (['start', 'end', 'created_at', 'updated_at', 'visit_date'].includes(field)) {
        return 'date';
      }
      return 'text';
    },

    // Check if column is an action column
    isActionColumn(field) {
      return ['plan_actions', 'actual_actions', 'ow_actions', 'map', 'v_details'].includes(field);
    },

    // Format date
    formatDate(value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD HH:mm:ss");
      }
      return '';
    },

    // Handle selection changes
    onSelectionChange(selectedItems) {
      this.selectedItems = selectedItems;
      this.$emit('item-selection-changed', selectedItems);
      this.$emit('update:selected', selectedItems);
    },

    // Check permission method
    checkPermission(permission) {
      // Use the same permission check method as the parent component
      return this.$parent.checkPermission ? this.$parent.checkPermission(permission) : true;
    }
  },

  // Forward all the original events
  emits: [
    'showMore',
    'showLess',
    'toggleDetails',
    'getLocation',
    'deletePlan',
    'deleteActualVisit',
    'deleteOwActual',
    'item-selection-changed',
    'update:selected',
    'update:selectAll',
    'sort',
    'search',
    'page-change',
    'page-size-change'
  ]
};
</script>

<style scoped>
/* Any additional custom styles specific to visits table can go here */
</style>
