<template>
  <div>
    <!-- Performance-optimized search container -->
    <div class="search-container-enhanced">
      <div class="search-header">
        <div class="search-label-enhanced">
          <CIcon name="cil-filter" class="search-icon-enhanced" />
          <span>Filter Data</span>
        </div>
        <div class="search-stats" v-if="localSearchTerm">
          <CIcon name="cil-check-circle" class="search-stats-icon" />
          <span class="results-count">{{ totalFilteredItems }}</span>
          <span class="results-text">found of</span>
          <span class="total-count">{{ items.length }}</span>
          <span class="total-text">total</span>
        </div>
      </div>
      <div class="search-input-wrapper-enhanced">
        <CIcon name="cil-magnifying-glass" class="search-icon" />
        <input ref="searchInput" type="text" class="search-input-enhanced" placeholder="Search in all fields..."
          v-model="localSearchTerm" @input="debouncedSearch" @keydown.esc="clearSearch" />
        <button v-if="localSearchTerm" class="clear-search-btn-enhanced" @click="clearSearch">
          <CIcon name="cil-x" />
        </button>
        <div v-if="isSearching" class="search-loading">
          <CIcon name="cil-reload" class="loading-spinner" />
        </div>
      </div>
    </div>

    <!-- Loading overlay -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner-container">
        <CIcon name="cil-reload" class="loading-spinner-large" />
        <div class="loading-text">Loading data...</div>
      </div>
    </div>

    <!-- Performance-optimized table container with pagination -->
    <div class="table-container-enhanced" ref="tableContainer">
      <!-- Total Records Row - Positioned directly above table -->
      <div class="total-records-bar">
        <div class="total-info-enhanced">
          <div class="total-section">
            <CIcon name="cil-chart-pie" class="total-icon" />
            <span class="total-label">Total Records:</span>
            <span class="total-count-badge">{{ totalFilteredItems }}</span>
          </div>
          <div class="filter-section" v-if="localSearchTerm && totalFilteredItems !== items.length">
            <CIcon name="cil-filter" class="filter-icon" />
            <span class="filter-text">Filtered from {{ items.length }} total</span>
          </div>
          <div class="page-section">
            <CIcon name="cil-layers" class="page-icon" />
            <span class="page-text">Page {{ currentPage }} of {{ totalPages }}</span>
          </div>
        </div>
      </div>

      <div class="table-wrapper">
        <!-- Fixed Headers -->
        <div class="table-header-container">
          <table class="data-table-enhanced header-table">
            <thead class="table-header-enhanced">
              <tr class="header-row">
              <th v-for="field in fields" :key="`header-${field}`" class="column-header-enhanced"
                :class="getHeaderClass(field)" @click="handleHeaderClick(field)">

                <!-- Select all checkbox -->
                <template v-if="field === 's'">
                  <div class="select-all-container">
                    <input type="checkbox" v-model="selectAllModel" title="Select All Items"
                      class="select-all-checkbox-enhanced" @click.stop />
                    <span class="select-all-label">All</span>
                  </div>
                </template>

                <!-- Regular column header -->
                <template v-else>
                  <div class="header-content">
                    <span class="header-text">{{ formatFieldName(field) }}</span>
                    <span class="sort-indicator" v-if="!isActionColumn(field)">
                      <CIcon v-if="sortColumn === field"
                        :name="sortDirection === 'asc' ? 'cil-sort-ascending' : 'cil-sort-descending'"
                        class="sort-icon-active" />
                      <CIcon v-else name="cil-swap-vertical" class="sort-icon-inactive" />
                    </span>
                  </div>
                </template>
              </th>
            </tr>
          </thead>
        </table>
        </div>

        <!-- Scrollable Body -->
        <div class="table-body-container" @scroll="handleHorizontalScroll">
          <table class="data-table-enhanced body-table">
            <!-- Table body with paginated items -->
            <tbody class="table-body-enhanced" :key="paginationKey">
            <tr v-for="(item, index) in getPaginatedItems()" :key="`row-${currentPage}-${pageSize}-${item.id || index}`" class="data-row-enhanced"
              :class="getRowClass(item, index)">
              <!-- Render each field -->
              <td v-for="field in fields" :key="`${item.id}-${field}`" :class="getCellClass(item, field)">
                <!-- Comment field with show more/less functionality -->
                <div v-if="field === 'comment'">
                  <div v-if="!readMore[item.id]" @click="$emit('showMore', item.id)">
                    {{ item.comment && item.comment.length > 30 ? item.comment.substring(0, 30) + ".." : item.comment }}
                    <br v-if="item.comment && item.comment.length > 30" />
                    <span v-if="item.comment && item.comment.length > 30" style="color: blue; cursor: pointer">show
                      more</span>
                  </div>
                  <div v-else @click="$emit('showLess', item.id)">
                    {{ item.comment }}
                    <br />
                    <span style="color: blue; cursor: pointer">show less</span>
                  </div>
                </div>

                <!-- Division field -->
                <strong v-else-if="field === 'division'" style="color: green">{{ item.division }}</strong>

                <!-- Date fields -->
                <span v-else-if="field === 'start' || field === 'end'">{{ formatDate(item[field]) }}</span>

                <!-- Account field -->
                <strong v-else-if="field === 'account'" style="color: blue; cursor: pointer">
                  <a @click="$root.$account('Account Data', item.account_id)">{{ item.account }}</a>
                </strong>

                <!-- Doctor field -->
                <strong v-else-if="field === 'doctor'" style="color: blue; cursor: pointer">
                  <a @click="$root.$doctor('Doctor Data', item.doctor_id)">{{ item.doctor }}</a>
                </strong>

                <!-- Attachments field -->
                <div v-else-if="field === 'attachments'">
                  <span v-if="item.attachments">
                    <span v-for="(path, pathIndex) in item.attachments.split(', ')" :key="pathIndex">
                      <a :href="path" target="_blank">{{ path.split('/').pop() }}</a>
                      <span v-if="pathIndex < item.attachments.split(', ').length - 1">, </span>
                    </span>
                  </span>
                  <span v-else>-</span>
                </div>

                <!-- Type field -->
                <div v-else-if="field === 'type'">
                  <strong v-if="item.is_automatic == 1" style="color: #f1c40f; cursor: pointer">{{ item.type }}</strong>
                  <strong v-else>{{ item.type }}</strong>
                </div>

                <!-- Account type field -->
                <div v-else-if="field === 'acc_type'">
                  <strong v-if="item.acc_shift_id == 1" style="color: #efa609">{{ item.acc_type }}</strong>
                  <strong v-if="item.acc_shift_id == 2" style="color: #ef09c2">{{ item.acc_type }}</strong>
                  <strong v-if="item.acc_shift_id == 3" style="color: #09efde">{{ item.acc_type }}</strong>
                </div>

                <!-- Visit shift field -->
                <div v-else-if="field === 'visit_shift_id'">
                  <strong v-if="item.visit_shift_id == 1" style="color: #efa609">{{ item.acc_type }}</strong>
                  <strong v-if="item.visit_shift_id == 2" style="color: #ef09c2">{{ item.acc_type }}</strong>
                  <strong v-if="item.visit_shift_id == 3" style="color: #09efde">{{ item.acc_type }}</strong>
                </div>

                <!-- Status field -->
                <div v-else-if="field === 'status'">
                  <strong v-if="item.status == 'Approved'" style="color: green">Approved</strong>
                  <strong v-if="item.status == 'Disapproved'" style="color: red">Disapproved</strong>
                  <strong v-if="item.status == 'Pending'" style="color: blue">Pending</strong>
                </div>

                <!-- Details button -->
                <div v-else-if="field === 'v_details'" class="py-2">
                  <CButton color="primary" variant="outline" square size="sm" class="mt-3"
                    @click="$emit('toggleDetails', item, index)">
                    {{ Boolean(item._toggled) ? "Hide" : "Details" }}
                  </CButton>
                </div>

                <!-- Map button -->
                <div v-else-if="field === 'map'">
                  <c-button v-if="checkPermission('show_single_actual_visits_locations')" color="primary"
                    class="btn-sm mt-2 mr-1" @click="$emit('getLocation', item)">
                    <CIcon class="text-white" name="cil-location-pin" />
                  </c-button>
                </div>

                <!-- Plan actions -->
                <div v-else-if="field === 'plan_actions'">
                  <c-button color="success" class="btn-sm mt-2 mr-1" v-if="checkPermission('edit_plan_visits')"
                    :to="{ name: 'EditPlanVisit', params: { id: item.id } }">
                    <CIcon name="cil-pencil" />
                  </c-button>
                  <c-button color="danger" class="btn-sm mt-2 mr-1" v-if="checkPermission('delete_plan_visits')" @click="
                    $root
                      .$confirm('Delete', 'Do you want to delete this record?', {
                        color: 'red',
                        width: 290,
                        zIndex: 200,
                      })
                      .then((confirmed) => {
                        if (confirmed) {
                          $emit('deletePlan', item);
                        }
                      })
                    ">
                    <CIcon name="cil-trash" />
                  </c-button>
                </div>

                <!-- Actual actions -->
                <div v-else-if="field === 'actual_actions'">
                  <CButton color="primary" class="btn-sm mt-2 mr-1" v-if="checkPermission('show_single_actual_visits')"
                    :to="{ name: 'ActualVisit', params: { id: item.id } }" target="_blank">
                    <CIcon name="cil-magnifying-glass" />
                  </CButton>
                  <CButton color="warning" class="btn-sm mt-2 mr-1 text-white"
                    v-if="checkPermission('create_pv_request')" :to="{ name: 'PV', params: { id: item.id } }"
                    target="_blank">
                    PV
                  </CButton>
                  <CButton color="success" class="btn-sm mt-2 mr-1" v-if="checkPermission('edit_actual_visits')"
                    :to="{ name: 'EditActualVisit', params: { id: item.id } }" target="_blank">
                    <CIcon name="cil-pencil" />
                  </CButton>
                  <c-button color="danger" v-if="checkPermission('delete_actual_visits')" class="btn-sm mt-2 mr-1"
                    @click="
                      $root
                        .$confirm('Delete', 'Do you want to delete this record?', {
                          color: 'red',
                          width: 290,
                          zIndex: 200,
                        })
                        .then((confirmed) => {
                          if (confirmed) {
                            $emit('deleteActualVisit', item);
                          }
                        })
                      ">
                    <CIcon name="cil-trash" />
                  </c-button>
                </div>

                <!-- Checkbox for selection -->
                <div v-else-if="field === 's'" class="checkbox-container">
                  <input class="row-checkbox" type="checkbox" v-model="selectedItems" :value="item"
                    title="Check One Plan Visit" @change="$emit('item-selection-changed', selectedItems)" />
                </div>

                <!-- OW actions -->
                <div v-else-if="field === 'ow_actions'">
                  <CButton color="success" class="btn-sm mt-2 mr-1" v-if="checkPermission('edit_ow_actual_visits')" :to="{
                    name: 'EditOwActualVisit',
                    params: { id: item.id },
                  }">
                    <CIcon name="cil-pencil" />
                  </CButton>
                  <c-button color="danger" class="btn-sm mt-2 mr-1" v-if="checkPermission('delete_ow_actual_visits')"
                    @click="
                      $root
                        .$confirm('Delete', 'Do you want to delete this record?', {
                          color: 'red',
                          width: 290,
                          zIndex: 200,
                        })
                        .then((confirmed) => {
                          if (confirmed) {
                            $emit('deleteOwActual', item);
                          }
                        })
                      ">
                    <CIcon name="cil-trash" />
                  </c-button>
                </div>

                <!-- Default field rendering -->
                <span v-else>{{ item[field] }}</span>
              </td>
            </tr>
          </tbody>
          </table>
        </div>
      </div>

    </div>

    <!-- Enhanced Pagination Controls -->
    <div class="pagination-container-enhanced">
      <div class="pagination-wrapper">
        <!-- Left section: Page size selector -->
        <div class="page-size-section">
          <div class="page-size-label">
            <CIcon name="cil-list" class="page-size-icon" />
            <span>Show</span>
          </div>
          <select
            class="page-size-select"
            :value="pageSize"
            @change="onPageSizeChange"
          >
            <option v-for="size in pageSizeOptions" :key="size" :value="size">{{ size }}</option>
          </select>
          <span class="page-size-text">per page</span>
        </div>

        <!-- Center section: Navigation controls -->
        <div class="pagination-navigation">
          <button class="nav-btn nav-btn-first" :disabled="currentPage === 1" @click="goToPage(1)" title="First Page">
            <CIcon name="cil-media-skip-backward" />
          </button>

          <button class="nav-btn nav-btn-prev" :disabled="currentPage === 1" @click="goToPage(currentPage - 1)"
            title="Previous Page">
            <CIcon name="cil-chevron-left" />
          </button>

          <!-- Page numbers -->
          <div class="page-numbers">
            <template v-for="(page, index) in getVisiblePages()">
              <button v-if="page !== '...'" :key="`page-${index}`" class="page-btn"
                :class="{ 'page-btn-active': page === currentPage }" @click="goToPage(page)">
                {{ page }}
              </button>
              <span v-else :key="`ellipsis-${index}`" class="page-ellipsis">...</span>
            </template>
          </div>

          <button class="nav-btn nav-btn-next" :disabled="currentPage === totalPages" @click="goToPage(currentPage + 1)"
            title="Next Page">
            <CIcon name="cil-chevron-right" />
          </button>

          <button class="nav-btn nav-btn-last" :disabled="currentPage === totalPages" @click="goToPage(totalPages)"
            title="Last Page">
            <CIcon name="cil-media-skip-forward" />
          </button>
        </div>

        <!-- Right section: Page info -->
        <div class="page-info-section">
          <div class="page-info-text">
            <span class="current-range">
              {{ ((currentPage - 1) * pageSize + 1) }}-{{ Math.min(currentPage * pageSize, totalFilteredItems) }}
            </span>
            <span class="range-separator">of</span>
            <span class="total-items">{{ totalFilteredItems }}</span>
            <span class="items-text">items</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance info (development only) -->
    <div v-if="showPerformanceInfo" class="performance-info">
      <small>
        Last render: {{ lastRenderTime.toFixed(2) }}ms |
        Total: {{ totalFilteredItems }} |
        Page: {{ paginatedItems.length }} |
        Current: {{ currentPage }}/{{ totalPages }}
      </small>
    </div>
  </div>
</template>

<script>
import moment from "moment";

export default {
  name: 'VisitsReportTable',
  props: {
    items: {
      type: Array,
      required: true
    },
    fields: {
      type: Array,
      required: true
    },
    readMore: {
      type: Object,
      required: true
    },
    searchTerm: {
      type: String,
      default: ''
    },
    selectAll: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedItems: [],
      sortColumn: null,
      sortDirection: 'asc',
      localSearchTerm: '',

      // Performance optimization properties
      isSearching: false,
      isLoading: false,
      searchDebounceTimer: null,

      // Pagination properties
      currentPage: 1,
      pageSize: 25,
      pageSizeOptions: [10, 25, 50, 100, 200],

      // Total filtered items count
      totalFilteredItems: 0,

      // Performance monitoring
      renderStartTime: 0,
      lastRenderTime: 0
    };
  },
  mounted() {
    this.initializeComponent();
    this.setupVirtualScrolling();
    this.setupPerformanceMonitoring();
    this.setupScrollIndicator();
    
    // Synchronize column widths after component is mounted
    this.$nextTick(() => {
      this.synchronizeColumnWidths();
    });
  },

  beforeDestroy() {
    this.cleanup();
  },

  computed: {
    // Filtered items - simplified without problematic caching
    filteredItems() {
      const currentSearchTerm = this.localSearchTerm || this.searchTerm;
      console.log('filteredItems computed called with search term:', currentSearchTerm);

      let result;
      if (!currentSearchTerm) {
        result = this.sortedItems;
      } else {
        result = this.performOptimizedSearch(currentSearchTerm);
      }

      // Update total count
      this.totalFilteredItems = result.length;
      console.log('filteredItems result length:', result.length);

      return result;
    },

    // Sorted items - simplified without problematic caching
    sortedItems() {
      return this.performOptimizedSort([...this.items]);
    },

    // Paginated items for current page - FIXED REACTIVITY
    paginatedItems() {
      // Force dependency tracking by accessing reactive properties
      const currentPage = this.currentPage;
      const pageSize = this.pageSize;
      const filteredItems = this.filteredItems;

      console.log('paginatedItems computed called:', {
        currentPage,
        pageSize,
        filteredItemsLength: filteredItems.length,
        totalPages: this.totalPages
      });

      const startIndex = (currentPage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const result = filteredItems.slice(startIndex, endIndex);

      console.log('paginatedItems result:', {
        startIndex,
        endIndex,
        resultLength: result.length,
        firstItem: result[0]?.id || 'none'
      });

      return result;
    },

    // Calculate total pages - FIXED REACTIVITY
    totalPages() {
      const totalItems = this.totalFilteredItems;
      const pageSize = this.pageSize;
      const result = Math.max(1, Math.ceil(totalItems / pageSize));
      console.log('totalPages computed:', { totalItems, pageSize, result });
      return result;
    },

    // Force reactivity key - FIXED REACTIVITY
    paginationKey() {
      const currentPage = this.currentPage;
      const pageSize = this.pageSize;
      const totalItems = this.totalFilteredItems;
      const key = `${currentPage}-${pageSize}-${totalItems}`;
      console.log('paginationKey computed:', key);
      return key;
    },

    // Performance monitoring
    showPerformanceInfo() {
      return process.env.NODE_ENV === 'development' && this.lastRenderTime > 0;
    },

    isAllSelected() {
      // Check if all filtered items are selected
      return this.filteredItems.length > 0 &&
        this.filteredItems.every(item =>
          this.selectedItems.some(selected => selected.id === item.id)
        );
    },
    selectAllModel: {
      get() {
        return this.isAllSelected;
      },
      set(value) {
        if (value) {
          // Select all filtered items
          this.selectedItems = [...this.filteredItems];
        } else {
          // Deselect all items
          this.selectedItems = [];
        }
        // Emit the selection change event
        this.$emit('item-selection-changed', this.selectedItems);
        // Update the selectAll prop in the parent component
        this.$emit('update:selectAll', value);
      }
    }
  },
  watch: {
    selectedItems: {
      handler(newVal) {
        this.$emit('update:selected', newVal);
      },
      deep: true
    },
    searchTerm: {
      handler(newVal) {
        this.localSearchTerm = newVal;
      },
      immediate: true
    },
    selectAll: {
      handler(newVal) {
        // Only apply select all if the 's' field is present (plan filter)
        if (this.fields.includes('s') && newVal !== this.isAllSelected) {
          // Use the setter to handle the change
          this.selectAllModel = newVal;
        }
      },
      immediate: true
    },
    // Watch for items changes to ensure real-time updates
    items: {
      handler(newItems) {
        // Update total count immediately
        this.totalFilteredItems = newItems.length;

        // Adjust current page if needed
        const maxPage = Math.ceil(this.totalFilteredItems / this.pageSize);
        if (this.currentPage > maxPage && maxPage > 0) {
          this.currentPage = maxPage;
        }
        
        // Synchronize column widths when data changes
        this.$nextTick(() => {
          this.synchronizeColumnWidths();
        });
      },
      deep: true,
      immediate: true
    },

    // Watch pagination changes to force reactivity
    currentPage: {
      handler(newPage) {
        console.log('currentPage watcher triggered:', newPage);
        this.$forceUpdate();
      }
    },

    pageSize: {
      handler(newSize) {
        console.log('pageSize watcher triggered:', newSize);
        this.$forceUpdate();
        
        // Synchronize column widths when page size changes
        this.$nextTick(() => {
          this.synchronizeColumnWidths();
        });
      }
    },
    
    // Watch paginatedItems to synchronize column widths when displayed data changes
    paginatedItems: {
      handler() {
        this.$nextTick(() => {
          this.synchronizeColumnWidths();
        });
      }
    },

  },
  methods: {
    // Initialize component
    initializeComponent() {
      // Set default sort column
      if (!this.sortColumn && this.fields.length > 0) {
        const defaultSortColumn = this.fields.find(field => !this.isActionColumn(field));
        if (defaultSortColumn) {
          this.sortColumn = defaultSortColumn;
          this.$emit('sort', { column: this.sortColumn, direction: this.sortDirection });
        }
      }
    },

    // No virtual scrolling setup needed
    setupVirtualScrolling() {
      // Virtual scrolling disabled
    },

    // Setup performance monitoring
    setupPerformanceMonitoring() {
      // Only track performance in development mode
      if (process.env.NODE_ENV === 'development') {
        this.renderStartTime = performance.now();
        console.log('VisitsReportTable: Performance monitoring enabled');
      }
    },

    // Setup scroll indicator behavior
    setupScrollIndicator() {
      this.$nextTick(() => {
        const tableContainer = this.$refs.tableContainer;
        const tableWrapper = tableContainer ? tableContainer.querySelector('.table-wrapper') : null;

        if (tableWrapper) {
          tableWrapper.addEventListener('scroll', () => {
            const scrollIndicator = tableContainer.querySelector('.scroll-indicator');
            if (scrollIndicator && tableWrapper.scrollTop > 20) {
              // Hide the indicator after user has scrolled
              scrollIndicator.style.opacity = '0';
              // Remove it after animation completes
              setTimeout(() => {
                if (scrollIndicator.parentNode) {
                  scrollIndicator.parentNode.removeChild(scrollIndicator);
                }
              }, 300);
            }
          });
        }
      });
    },

    // Cleanup event listeners
    cleanup() {
      if (this.searchDebounceTimer) {
        clearTimeout(this.searchDebounceTimer);
      }
      // No scroll event listeners to remove since virtual scrolling is disabled
    },



    // Optimized search with indexing
    performOptimizedSearch(searchTerm) {
      const term = searchTerm.toLowerCase();
      const searchFields = this.getSearchableFields();

      return this.items.filter(item => {
        // Search only in relevant fields for better performance
        return searchFields.some(field => {
          const value = item[field];
          if (value === null || value === undefined) return false;
          return String(value).toLowerCase().includes(term);
        });
      });
    },

    // Get searchable fields (exclude action columns)
    getSearchableFields() {
      return this.fields.filter(field => !this.isActionColumn(field));
    },

    // Optimized sorting with better performance
    performOptimizedSort(itemsToSort) {
      if (!this.sortColumn || itemsToSort.length === 0) return itemsToSort;

      const column = this.sortColumn;
      const direction = this.sortDirection;
      const modifier = direction === 'asc' ? 1 : -1;

      // Use faster sorting for different data types
      return itemsToSort.sort((a, b) => {
        const aValue = a[column];
        const bValue = b[column];

        // Handle null/undefined values
        if (aValue === null || aValue === undefined) return 1 * modifier;
        if (bValue === null || bValue === undefined) return -1 * modifier;

        // Numeric comparison
        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return (aValue - bValue) * modifier;
        }

        // Date comparison
        if (this.isDateField(column)) {
          const dateA = new Date(aValue);
          const dateB = new Date(bValue);
          return (dateA.getTime() - dateB.getTime()) * modifier;
        }

        // String comparison with locale
        const strA = String(aValue);
        const strB = String(bValue);
        return strA.localeCompare(strB, undefined, { numeric: true }) * modifier;
      });
    },
    isActionColumn(field) {
      // Identify action columns that shouldn't be sortable
      return ['plan_actions', 'actual_actions', 'ow_actions', 'map', 'v_details', 's'].includes(field);
    },

    // Optimized header click handler
    handleHeaderClick(field) {
      if (field === 's') return; // Skip selection column
      this.handleSort(field);
    },

    // Sort handler
    handleSort(field) {
      if (this.isActionColumn(field)) return;

      if (this.sortColumn === field) {
        this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        this.sortColumn = field;
        this.sortDirection = 'asc';
      }

      this.$emit('sort', { column: this.sortColumn, direction: this.sortDirection });
    },

    // Debounced search for better performance
    debouncedSearch() {
      this.isSearching = true;

      if (this.searchDebounceTimer) {
        clearTimeout(this.searchDebounceTimer);
      }

      this.searchDebounceTimer = setTimeout(() => {
        this.$emit('search', this.localSearchTerm);
        this.isSearching = false;
        // Reset to first page when search changes
        this.currentPage = 1;
      }, 300); // 300ms debounce
    },

    // Pagination methods
    goToPage(page) {
      console.log('goToPage called with:', page);
      // Ensure page is a number
      page = parseInt(page);

      if (page < 1) page = 1;
      if (page > this.totalPages) page = this.totalPages;

      console.log('Setting currentPage from', this.currentPage, 'to', page);
      if (this.currentPage !== page) {
        this.currentPage = page;
        console.log('currentPage updated to:', this.currentPage);

        // Force Vue to update
        this.$nextTick(() => {
          this.$forceUpdate();
          console.log('Force update completed');
        });
      }
    },

    onPageSizeChange(event) {
      const newPageSize = parseInt(event.target.value);
      console.log('onPageSizeChange called, new pageSize:', newPageSize);
      this.pageSize = newPageSize;
      this.handlePageSizeChange();

      // Force Vue to update
      this.$nextTick(() => {
        this.$forceUpdate();
        console.log('Page size change force update completed');
      });
    },

    handlePageSizeChange() {
      console.log('handlePageSizeChange called, new pageSize:', this.pageSize);
      // Adjust current page if needed
      const maxPage = Math.ceil(this.totalFilteredItems / this.pageSize);
      console.log('maxPage calculated:', maxPage, 'currentPage:', this.currentPage);

      if (this.currentPage > maxPage && maxPage > 0) {
        console.log('Adjusting currentPage to maxPage:', maxPage);
        this.currentPage = maxPage;
      } else if (this.currentPage < 1) {
        console.log('Resetting currentPage to 1');
        this.currentPage = 1;
      }
      console.log('Final currentPage:', this.currentPage);
    },

    // Method version of paginatedItems (instead of computed)
    getPaginatedItems() {
      console.log('getPaginatedItems method called');
      const startIndex = (this.currentPage - 1) * this.pageSize;
      const endIndex = startIndex + this.pageSize;
      const result = this.filteredItems.slice(startIndex, endIndex);

      console.log('getPaginatedItems result:', {
        currentPage: this.currentPage,
        pageSize: this.pageSize,
        startIndex,
        endIndex,
        resultLength: result.length,
        totalItems: this.filteredItems.length
      });

      return result;
    },

    // Handle horizontal scroll events
    handleHorizontalScroll(event) {
      // Get the body container that triggered the scroll event
      const bodyContainer = event.target;
      const scrollLeft = bodyContainer.scrollLeft;
      const maxScrollLeft = bodyContainer.scrollWidth - bodyContainer.clientWidth;
      
      // Find the header container and synchronize its scroll position
      const headerContainer = this.$el.querySelector('.table-header-container');
      if (headerContainer) {
        headerContainer.scrollLeft = scrollLeft;
      }

      // Add visual indicators if needed
      if (scrollLeft > 0) {
        bodyContainer.classList.add('scrolled-left');
      } else {
        bodyContainer.classList.remove('scrolled-left');
      }

      if (scrollLeft < maxScrollLeft) {
        bodyContainer.classList.add('can-scroll-right');
      } else {
        bodyContainer.classList.remove('can-scroll-right');
      }
    },
    
    // Synchronize column widths between header and body tables
    synchronizeColumnWidths() {
      const headerTable = this.$el.querySelector('.header-table');
      const bodyTable = this.$el.querySelector('.body-table');
      
      if (!headerTable || !bodyTable) return;
      
      const headerCols = headerTable.querySelectorAll('th');
      const firstBodyRow = bodyTable.querySelector('tbody tr');
      
      // If there's no data in the body, we can't synchronize
      if (!firstBodyRow) return;
      
      const bodyCols = firstBodyRow.querySelectorAll('td');
      
      // Ensure we have the same number of columns
      if (headerCols.length !== bodyCols.length) return;
      
      // Calculate and apply widths
      for (let i = 0; i < headerCols.length; i++) {
        // Get the maximum width between header and body column
        const headerWidth = headerCols[i].getBoundingClientRect().width;
        const bodyWidth = bodyCols[i].getBoundingClientRect().width;
        const maxWidth = Math.max(headerWidth, bodyWidth);
        
        // Apply the maximum width to both columns
        headerCols[i].style.width = `${maxWidth}px`;
        bodyCols[i].style.width = `${maxWidth}px`;
      }
    },

    // Manual method to test reactivity
    testReactivity() {
      console.log('Testing reactivity...');
      console.log('Current values:', {
        currentPage: this.currentPage,
        pageSize: this.pageSize,
        totalFilteredItems: this.totalFilteredItems,
        paginatedItemsLength: this.paginatedItems.length
      });

      // Force re-computation
      this.$forceUpdate();
    },

    // Clear search
    clearSearch() {
      this.localSearchTerm = '';
      this.$emit('search', '');

      // Focus back to search input
      this.$nextTick(() => {
        if (this.$refs.searchInput) {
          this.$refs.searchInput.focus();
        }
      });
    },

    // Get visible page numbers for pagination
    getVisiblePages() {
      const pages = [];
      const totalPages = this.totalPages;
      const currentPage = this.currentPage;

      if (totalPages <= 7) {
        // Show all pages if total is 7 or less
        for (let i = 1; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // Always show first page
        pages.push(1);

        if (currentPage > 4) {
          pages.push('...');
        }

        // Show pages around current page
        const start = Math.max(2, currentPage - 1);
        const end = Math.min(totalPages - 1, currentPage + 1);

        for (let i = start; i <= end; i++) {
          if (i !== 1 && i !== totalPages) {
            pages.push(i);
          }
        }

        if (currentPage < totalPages - 3) {
          pages.push('...');
        }

        // Always show last page
        if (totalPages > 1) {
          pages.push(totalPages);
        }
      }

      return pages;
    },

    // Get header class for styling
    getHeaderClass(field) {
      const classes = ['sortable-header'];

      if (field === 's') {
        classes.push('select-all-header');
      } else if (!this.isActionColumn(field)) {
        if (this.sortColumn === field) {
          classes.push(this.sortDirection === 'asc' ? 'sorted-asc' : 'sorted-desc');
        }
      }

      return classes;
    },

    // Get row class for styling
    getRowClass(item, index) {
      const classes = [];

      if (index % 2 === 0) {
        classes.push('even-row');
      } else {
        classes.push('odd-row');
      }

      if (this.selectedItems.some(selected => selected.id === item.id)) {
        classes.push('selected-row');
      }

      return classes;
    },
    formatFieldName(field) {
      if (!field) return '';
      const withSpaces = field.replace(/[_-]/g, ' ');
      return withSpaces.replace(/\b\w/g, c => c.toUpperCase());
    },
    formatDate(value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD HH:mm:ss");
      }
      return '';
    },
    getCellClass(item, field) {
      // Add conditional classes based on field and item values
      const classes = {};

      if (field === 'division') {
        classes['division-cell'] = true;
      } else if (field === 'account' || field === 'doctor') {
        classes['link-cell'] = true;
      } else if (field === 'status') {
        if (item.status === 'Approved') classes['status-approved'] = true;
        if (item.status === 'Disapproved') classes['status-disapproved'] = true;
        if (item.status === 'Pending') classes['status-pending'] = true;
      }

      return classes;
    },
    isDateField(field) {
      // Identify date fields for proper sorting
      return ['start', 'end', 'created_at', 'updated_at', 'visit_date'].includes(field);
    },
    checkPermission(permission) {
      // Use the same permission check method as the parent component
      return this.$parent.checkPermission(permission);
    },
    toggleSelectAll() {
      // Toggle the selectAllModel value
      this.selectAllModel = !this.isAllSelected;
    }
  },
  emits: [
    'showMore',
    'showLess',
    'toggleDetails',
    'getLocation',
    'deletePlan',
    'deleteActualVisit',
    'deleteOwActual',
    'update:selected',
    'update:selectAll',
    'sort',
    'search',
    'item-selection-changed'
  ]
};
</script>

<style scoped>
/* Enhanced Search Container Styles */
.search-container-enhanced {
  margin: 20px 0;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  /* Performance optimizations */
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
  transition: box-shadow 0.3s ease, transform 0.3s ease;
  contain: layout style;
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.search-label-enhanced {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 700;
  color: #2d3748;
  font-size: 16px;
}

.search-icon-enhanced {
  color: #667eea;
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.search-stats {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #718096;
  font-weight: 500;
  background: rgba(102, 126, 234, 0.1);
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.search-stats-icon {
  color: #10b981;
  width: 16px;
  height: 16px;
}

.results-count {
  color: #667eea;
  font-weight: 700;
  font-size: 16px;
}

.results-text {
  color: #718096;
  font-weight: 400;
}

.total-count {
  color: #4a5568;
  font-weight: 600;
  font-size: 15px;
}

.total-text {
  color: #718096;
  font-weight: 400;
}

.search-input-wrapper-enhanced {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
  /* Performance optimizations */
  will-change: transform, opacity;
  transform: translateZ(0);
  transition: transform 0.3s ease;
  contain: layout style;
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #667eea;
  font-size: 16px;
  width: 16px;
  height: 16px;
  z-index: 2;
}

.search-input-enhanced {
  width: 100%;
  padding: 16px 50px 16px 48px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), border-color 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background: white;
  color: #2d3748;
  /* Performance optimizations */
  will-change: transform, border-color, box-shadow;
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-font-smoothing: subpixel-antialiased;
}

.search-input-enhanced:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2), 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px) translateZ(0);
}

.search-input-enhanced::placeholder {
  color: #a0aec0;
  font-weight: 400;
}

.clear-search-btn-enhanced {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  border: none;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.clear-search-btn-enhanced:hover {
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.5);
}

.search-loading {
  position: absolute;
  right: 50px;
  top: 50%;
  transform: translateY(-50%);
  color: #667eea;
}

.loading-spinner {
  animation: spin 1s linear infinite;
  width: 16px;
  height: 16px;
  /* Performance optimizations */
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

@keyframes spin {
  from {
    transform: rotate(0deg) translateZ(0);
  }

  to {
    transform: rotate(360deg) translateZ(0);
  }
}

/* Optimized loading states with reduced repaints */
.search-container-enhanced,
.table-container-enhanced,
.pagination-container {
  contain: layout style paint;
}

/* Optimize rendering for table cells */
.data-row-enhanced td {
  will-change: contents;
  contain: layout style;
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
}

/* Improve row visibility when scrolling */
.data-row-enhanced:nth-child(even) {
  background-color: #f8fafc;
}

.data-row-enhanced:hover {
  background-color: #edf2f7;
}

.search-results {
  font-size: 13px;
  color: #718096;
  margin-bottom: 8px;
  margin-left: 15px;
  font-style: italic;
}

/* Total Records Bar Styles */
.total-records-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 24px;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-bottom: none; /* Remove border to eliminate space */
  margin-bottom: 0; /* Remove any margin */
}

.total-info-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.total-section,
.filter-section,
.page-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.total-icon,
.filter-icon,
.page-icon {
  width: 18px;
  height: 18px;
  color: rgba(255, 255, 255, 0.9);
}

.total-label,
.filter-text,
.page-text {
  font-weight: 600;
  font-size: 14px;
}

.total-count-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 6px 16px;
  border-radius: 20px;
  font-weight: 700;
  font-size: 16px;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Enhanced Table Container Styles with Performance Optimizations */
.table-container-enhanced {
  position: relative;
  overflow: visible;
  max-height: 650px;
  /* Increased height to show more records */
  height: 850px;
  /* Increased fixed height */
  border-radius: 16px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  background: white;
  border: 1px solid #e2e8f0;
  /* Performance optimizations */
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform: translateZ(0);
  -webkit-font-smoothing: subpixel-antialiased;
  contain: content;
  /* Responsive height adjustments */
  min-height: 400px;
  margin-bottom: 0;
  /* Remove bottom margin for seamless connection with pagination */
}

/* Responsive adjustments for different screen sizes */
@media (max-width: 1200px) {
  .table-container-enhanced {
    height: 600px;
  }

  .table-wrapper {
    max-height: 550px;
    height: 550px;
    min-height: 550px;
  }
}

@media (max-width: 992px) {
  .table-container-enhanced {
    height: 550px;
  }

  .table-wrapper {
    max-height: 500px;
    height: 500px;
    min-height: 500px;
  }
}

@media (max-width: 768px) {
  .table-container-enhanced {
    height: 500px;
  }

  .table-wrapper {
    max-height: 450px;
    height: 450px;
    min-height: 450px;
  }

  .total-info-enhanced {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  /* Enhanced horizontal scroll on mobile */
  .table-body-container::-webkit-scrollbar {
    height: 8px;
    width: 8px;
  }
}

@media (max-width: 576px) {
  .table-container-enhanced {
    height: 450px;
  }

  .table-wrapper {
    max-height: 400px;
    height: 400px;
    min-height: 400px;
  }

  /* Smaller scrollbar on very small screens */
  .table-body-container::-webkit-scrollbar {
    height: 6px;
    width: 6px;
  }
}

/* Scroll indicator styling */
.scroll-indicator {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(102, 126, 234, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
  z-index: 100;
  opacity: 0.9;
  transition: opacity 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  pointer-events: none;
}

.scroll-indicator:hover {
  opacity: 1;
}

.scroll-arrow {
  animation: bounce 1.5s infinite;
}

@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-5px);
  }

  60% {
    transform: translateY(-3px);
  }
}

.table-wrapper {
  position: relative;
  min-height: 600px; /* Increased minimum height for table body */
  height: 600px; /* Set fixed height for table body */
  /* Ensure table takes full width */
  width: 100%;
  /* Smooth scrolling with momentum */
  -webkit-overflow-scrolling: touch;
  border-radius: 0 0 16px 16px;
  margin-top: 0; /* Remove any top margin */
  padding-top: 0; /* Remove any top padding */
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Fixed Header Container */
.table-header-container {
  position: relative;
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  flex-shrink: 0;
  z-index: 100;
  /* Hide scrollbar but keep functionality */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

/* Hide scrollbar for Chrome/Safari/Opera */
.table-header-container::-webkit-scrollbar {
  display: none;
}

.header-table {
  width: 100%;
  table-layout: fixed;
  /* Ensure header columns match body columns */
  min-width: max-content;
}

/* Scrollable Body Container */
.table-body-container {
  flex: 1;
  overflow-x: auto;
  overflow-y: auto;
  width: 100%;
  /* Custom scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: #667eea #f1f5f9;
  position: relative;
}

.body-table {
  width: 100%;
  table-layout: fixed;
  /* Ensure body columns match header columns */
  min-width: max-content;
}

/* Webkit scrollbar styling for horizontal scroll */
.table-body-container::-webkit-scrollbar {
  height: 12px;
  width: 12px;
}

.table-body-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 6px;
}

.table-body-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 6px;
  border: 2px solid #f1f5f9;
}

.table-body-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.table-body-container::-webkit-scrollbar-corner {
  background: #f1f5f9;
}

/* Scroll indicators for body container */
.table-body-container.scrolled-left::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 12px; /* Account for scrollbar height */
  width: 20px;
  background: linear-gradient(to right, rgba(0,0,0,0.1), transparent);
  pointer-events: none;
  z-index: 10;
}

.table-body-container.can-scroll-right::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 12px; /* Account for scrollbar height */
  width: 20px;
  background: linear-gradient(to left, rgba(0,0,0,0.1), transparent);
  pointer-events: none;
  z-index: 10;
}

.data-table-enhanced {
  width: 100%;
  min-width: max-content; /* Allow table to expand beyond container width */
  border-collapse: separate;
  border-spacing: 0;
  font-size: 14px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  background: white;
  table-layout: auto;
}

/* Ensure header and body tables have same column widths */
.header-table,
.body-table {
  width: 100%;
  min-width: max-content;
}

/* Force column width consistency between header and body */
.header-table th,
.body-table td {
  width: auto;
  white-space: nowrap;
}

.table-header-enhanced {
  position: relative;
  z-index: 100;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  /* Performance optimizations */
  will-change: transform;
  transform: translateZ(0);
  contain: layout style;
  transition: box-shadow 0.3s ease;
  margin-top: 0; /* Remove any top margin */
  padding-top: 0; /* Remove any top padding */
}

/* Total row stays at the very top */
.total-row th {
  position: sticky;
  top: 0;
  z-index: 101;
  background: white;
}

/* Enhanced Header Styles */
.total-header-enhanced {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  text-align: left !important;
  padding: 16px 20px !important;
  font-weight: 700;
  font-size: 16px;
  border-bottom: 2px solid #5a67d8;
}

.total-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.total-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 20px;
  font-weight: 700;
  font-size: 14px;
}

.filtered-badge {
  background: rgba(255, 255, 255, 0.15);
  padding: 4px 10px;
  border-radius: 16px;
  font-size: 12px;
  font-style: italic;
}

.column-header-enhanced {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  color: white;
  padding: 16px 20px;
  text-align: center;
  font-weight: 700;
  font-size: 14px;
  white-space: nowrap;
  border-bottom: 2px solid #1a202c;
  border-top: none; /* Remove top border to eliminate space */
  transition: background-color 0.3s ease, transform 0.2s ease;
  user-select: none;
  position: relative;
  /* Performance optimizations */
  will-change: transform, background-color;
  transform: translateZ(0);
  contain: layout style;
  backface-visibility: hidden;
  margin-top: 0; /* Remove any top margin */
  padding-top: 16px; /* Keep padding but ensure no extra space */
}

.data-table thead tr:first-child th {
  top: 0;
  z-index: 11;
}

.data-table thead tr:nth-child(2) th {
  top: 48px;
  /* Adjust based on the height of the first header row */
  z-index: 10;
}

.total-header {
  background-color: white !important;
  color: #333 !important;
  text-align: left !important;
  padding-left: 20px !important;
}

.column-header {
  background-color: #333333;
  /* Changed from blue to dark gray to match thead th */
  color: white;
}

/* Sortable header styles */
.sortable-header {
  cursor: pointer;
  transition: background-color 0.2s ease;
  user-select: none;
  position: relative;
}

.sortable-header:hover {
  background-color: #444444;
  /* Darker gray for hover state */
}

.sortable-header:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: rgba(255, 255, 255, 0.2);
}

.sortable-header.sorted-asc,
.sortable-header.sorted-desc {
  background-color: #222222;
  /* Even darker gray for sorted state */
}

.sortable-header.sorted-asc:after,
.sortable-header.sorted-desc:after {
  background-color: rgba(255, 255, 255, 0.5);
}

.sort-indicator {
  margin-left: 8px;
  display: inline-flex;
  align-items: center;
}

.sort-icon-active {
  color: #10b981;
  width: 14px;
  height: 14px;
  opacity: 1;
  transition: all 0.3s ease;
}

.sort-icon-inactive {
  color: rgba(255, 255, 255, 0.6);
  width: 12px;
  height: 12px;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.sortable-header:hover .sort-icon-inactive {
  opacity: 1;
  color: rgba(255, 255, 255, 0.9);
}

/* Select all checkbox styling */
.select-all-header {
  text-align: center;
  width: 50px;
  min-width: 50px;
  max-width: 50px;
}

.select-all-checkbox {
  cursor: pointer;
  width: 20px;
  height: 20px;
  margin: 0;
  padding: 0;
  border: 2px solid white;
  border-radius: 3px;
  background-color: transparent;
  position: relative;
  z-index: 20;
  vertical-align: middle;
}

.select-all-checkbox:checked {
  background-color: #ffcc00;
  /* Changed to yellow for better contrast with blue header */
  position: relative;
}

.select-all-checkbox:checked:after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 6px;
  height: 12px;
  border: solid #333;
  /* Darker checkmark for better visibility */
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.data-table tbody tr:hover {
  background-color: rgba(0, 92, 200, 0.05);
}

/* Row checkbox styling */
.checkbox-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.row-checkbox {
  cursor: pointer;
  width: 18px;
  height: 18px;
  margin: 0;
  padding: 0;
  border: 2px solid #333333;
  /* Changed to match header color */
  border-radius: 3px;
  position: relative;
  vertical-align: middle;
}

.row-checkbox:checked {
  background-color: #ffcc00;
  /* Changed to yellow to match select-all checkbox */
  position: relative;
}

.row-checkbox:checked:after {
  content: '';
  position: absolute;
  left: 5px;
  top: 1px;
  width: 5px;
  height: 10px;
  border: solid #333;
  /* Changed to dark gray to match select-all checkbox */
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.data-table td {
  padding: 10px 16px;
  border-bottom: 1px solid #e2e8f0;
  vertical-align: middle;
}

/* Status cell styles */
.status-approved {
  color: #0cce6b;
  font-weight: 600;
}

.status-disapproved {
  color: #ff4757;
  font-weight: 600;
}

.status-pending {
  color: #0056b3;
  font-weight: 600;
}

/* Division cell styles */
.division-cell {
  color: #198520;
  font-weight: 600;
}

/* Link cell styles */
/* Enhanced Row Styles */
.table-body-enhanced {
  background: white;
  min-height: 500px; /* Increased minimum height for table body */
  height: auto; /* Allow dynamic height based on content */
}

.data-row-enhanced {
  transition: transform 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease;
  border-bottom: 1px solid #f1f5f9;
  /* Performance optimizations */
  will-change: transform, background-color, box-shadow;
  contain: layout style;
}

.data-row-enhanced:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.even-row {
  background: #fafbfc;
}

.odd-row {
  background: white;
}

.selected-row {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%) !important;
  border-left: 4px solid #667eea;
}

/* Loading overlay with optimized animations */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  /* Performance optimizations */
  opacity: 0;
  transition: opacity 0.3s ease-out;
  will-change: opacity;
  pointer-events: none;
}

.loading-overlay[v-if] {
  opacity: 1;
  pointer-events: auto;
  animation: fadeIn 0.3s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  transform: translateY(10px);
  animation: slideUp 0.4s ease-out forwards;
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.loading-spinner-large {
  font-size: 2rem;
  color: #321fdb;
  animation: spin 1s linear infinite;
  /* Hardware acceleration for smoother animation */
  will-change: transform;
  transform: translateZ(0);
}

.loading-text {
  margin-top: 1rem;
  font-weight: bold;
  color: #3c4b64;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* Enhanced Pagination Styles */
.pagination-container-enhanced {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #e2e8f0;
  border-top: none;
  border-radius: 0 0 16px 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 20px 24px;
  margin-top: 0;
  /* Performance optimizations */
  will-change: opacity, transform;
  transform: translateZ(0);
  transition: all 0.3s ease;
  contain: layout style;
}

.pagination-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

/* Page Size Section */
.page-size-section {
  display: flex;
  align-items: center;
  gap: 12px;
  background: white;
  padding: 12px 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.page-size-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  color: #4a5568;
  font-size: 14px;
}

.page-size-icon {
  width: 16px;
  height: 16px;
  color: #667eea;
}

.page-size-select {
  padding: 8px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #2d3748;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 60px;
}

.page-size-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.page-size-text {
  color: #718096;
  font-weight: 500;
  font-size: 14px;
}

/* Navigation Controls */
.pagination-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  padding: 8px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  background: white;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.nav-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.nav-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  background: #f7fafc;
  color: #a0aec0;
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 4px;
  margin: 0 8px;
}

.page-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  background: white;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 14px;
}

.page-btn:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.page-btn-active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  border-color: #667eea !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  transform: translateY(-1px);
}

.page-ellipsis {
  padding: 0 8px;
  color: #a0aec0;
  font-weight: 600;
}

/* Page Info Section */
.page-info-section {
  display: flex;
  align-items: center;
  background: white;
  padding: 12px 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.page-info-text {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 500;
}

.current-range {
  color: #667eea;
  font-weight: 700;
  font-size: 16px;
}

.range-separator {
  color: #718096;
}

.total-items {
  color: #2d3748;
  font-weight: 700;
  font-size: 16px;
}

.items-text {
  color: #718096;
}

.data-row-enhanced td {
  padding: 16px 20px;
  vertical-align: middle;
  font-weight: 500;
  color: #2d3748;
}

/* Enhanced Select All Styles */
.select-all-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.select-all-checkbox-enhanced {
  width: 24px;
  height: 24px;
  border: 2px solid white;
  border-radius: 6px;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.select-all-checkbox-enhanced:checked {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-color: #10b981;
}

.select-all-label {
  font-size: 10px;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Performance Info with optimized rendering */
.performance-info {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-family: monospace;
  font-size: 12px;
  z-index: 1000;
  /* Performance optimizations */
  will-change: transform, opacity;
  transform: translateZ(0);
  transition: opacity 0.3s ease;
  contain: content;
  pointer-events: none;
}

/* No Virtual Scrolling Needed */

/* Responsive Design */
@media (max-width: 768px) {
  .search-container-enhanced {
    padding: 16px;
    margin: 16px 0;
  }

  .search-input-wrapper-enhanced {
    max-width: 100%;
  }

  .search-input-enhanced {
    padding: 14px 45px 14px 40px;
    font-size: 14px;
  }

  .table-container-enhanced {
    max-height: 60vh;
    border-radius: 12px;
  }

  .column-header-enhanced {
    padding: 12px 16px;
    font-size: 12px;
  }

  .data-row-enhanced td {
    padding: 12px 16px;
    font-size: 13px;
  }

  /* Responsive pagination */
  .pagination-wrapper {
    flex-direction: column;
    gap: 16px;
  }

  .pagination-navigation {
    order: 2;
  }

  .page-info-section {
    order: 1;
  }

  .page-size-section {
    order: 3;
  }

  .page-numbers {
    margin: 0 4px;
  }

  .nav-btn,
  .page-btn {
    width: 36px;
    height: 36px;
    font-size: 12px;
  }
}

@media (max-width: 576px) {
  .pagination-container-enhanced {
    padding: 16px;
  }

  .page-numbers {
    display: none;
    /* Hide page numbers on very small screens */
  }

  .pagination-navigation {
    gap: 4px;
  }

  .nav-btn {
    width: 32px;
    height: 32px;
  }

  .page-info-text {
    font-size: 12px;
  }

  .current-range,
  .total-items {
    font-size: 14px;
  }
}

/* Loading States with optimized animations */
@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.search-loading .loading-spinner {
  animation: spin 1s linear infinite, pulse 1.5s infinite;
  will-change: transform, opacity;
  transform: translateZ(0);
}

/* Lazy loading optimization */
.table-wrapper {
  position: relative;
  min-height: 400px;
  contain: strict;
  will-change: contents;
  margin-bottom: 0;
  /* Remove bottom margin */
}

/* Optimize pagination buttons */
.pagination-controls button {
  will-change: transform, background-color;
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.pagination-controls button:hover {
  transform: translateY(-1px);
}

/* Optimize rendering performance */
.data-table-enhanced {
  contain: layout style;
  will-change: contents;
  transform: translateZ(0);
}

/* Optimized Smooth Scrolling */
.table-container-enhanced {
  scroll-behavior: smooth;
  /* Prevent paint during scrolling */
  contain: paint;
  /* Optimize layer composition */
  isolation: isolate;
  margin-bottom: 0;
  /* Remove bottom margin */
  border-bottom: none;
  /* Remove bottom border */
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

/* Link cell styles */
.link-cell {
  color: #667eea;
  cursor: pointer;
  font-weight: 600;
  transition: color 0.2s ease;
}

.link-cell:hover {
  color: #5a67d8;
}
</style>
