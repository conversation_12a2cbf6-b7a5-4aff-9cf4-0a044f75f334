<template>
  <div>
    <div class="search-container">
      <div class="search-label">
        <i class="fa fa-filter"></i> Filter Data:
      </div>
      <div class="search-input-wrapper">
        <i class="search-icon fa fa-search"></i>
        <input
          type="text"
          class="search-input"
          placeholder="Search in all fields..."
          v-model="localSearchTerm"
          @input="handleSearch"
        />
        <button v-if="localSearchTerm" class="clear-search-btn" @click="clearSearch">×</button>
      </div>
      <div class="search-results" v-if="localSearchTerm">
        Found {{ filteredItems.length }} of {{ items.length }} items
      </div>
    </div>
    <div class="table-container">
      <table class="data-table" id="print">
      <thead>
        <tr>
          <th colspan="100%" class="total-header">
            <strong>Total:</strong> {{ items.length }}
          </th>
        </tr>
        <tr>
          <th v-for="field in fields" :key="field"
              class="column-header sortable-header"
              @click="field === 's' ? null : handleSort(field)"
              :class="{ 'sorted-asc': sortColumn === field && sortDirection === 'asc',
                        'sorted-desc': sortColumn === field && sortDirection === 'desc',
                        'select-all-header': field === 's' }">
            <!-- Special case for 's' column - show checkbox instead of text -->
            <template v-if="field === 's'">
              <input
                type="checkbox"
                v-model="selectAllModel"
                title="Select All Items"
                class="select-all-checkbox"
              />
            </template>
            <template v-else>
              {{ formatFieldName(field) }}
              <span class="sort-icon" v-if="!isActionColumn(field)">
                <span v-if="sortColumn === field">
                  {{ sortDirection === 'asc' ? '▲' : '▼' }}
                </span>
                <span v-else>
                  ↕
                </span>
              </span>
            </template>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, index) in filteredItems" :key="`item-${index}`" class="data-row">
          <!-- Render each field -->
          <td v-for="field in fields" :key="`${item.id}-${field}`" :class="getCellClass(item, field)">
            <!-- Comment field with show more/less functionality -->
            <div v-if="field === 'comment'">
              <div v-if="!readMore[item.id]" @click="$emit('showMore', item.id)">
                {{ item.comment && item.comment.length > 30 ? item.comment.substring(0, 30) + ".." : item.comment }}
                <br v-if="item.comment && item.comment.length > 30" />
                <span v-if="item.comment && item.comment.length > 30" style="color: blue; cursor: pointer">show more</span>
              </div>
              <div v-else @click="$emit('showLess', item.id)">
                {{ item.comment }}
                <br />
                <span style="color: blue; cursor: pointer">show less</span>
              </div>
            </div>

            <!-- Division field -->
            <strong v-else-if="field === 'division'" style="color: green">{{ item.division }}</strong>

            <!-- Date fields -->
            <span v-else-if="field === 'start' || field === 'end'">{{ formatDate(item[field]) }}</span>

            <!-- Account field -->
            <strong v-else-if="field === 'account'" style="color: blue; cursor: pointer">
              <a @click="$root.$account('Account Data', item.account_id)">{{ item.account }}</a>
            </strong>

            <!-- Doctor field -->
            <strong v-else-if="field === 'doctor'" style="color: blue; cursor: pointer">
              <a @click="$root.$doctor('Doctor Data', item.doctor_id)">{{ item.doctor }}</a>
            </strong>

            <!-- Attachments field -->
            <div v-else-if="field === 'attachments'">
              <span v-if="item.attachments">
                <span v-for="(path, pathIndex) in item.attachments.split(', ')" :key="pathIndex">
                  <a :href="path" target="_blank">{{ path.split('/').pop() }}</a>
                  <span v-if="pathIndex < item.attachments.split(', ').length - 1">, </span>
                </span>
              </span>
              <span v-else>-</span>
            </div>

            <!-- Type field -->
            <div v-else-if="field === 'type'">
              <strong v-if="item.is_automatic == 1" style="color: #f1c40f; cursor: pointer">{{ item.type }}</strong>
              <strong v-else>{{ item.type }}</strong>
            </div>

            <!-- Account type field -->
            <div v-else-if="field === 'acc_type'">
              <strong v-if="item.acc_shift_id == 1" style="color: #efa609">{{ item.acc_type }}</strong>
              <strong v-if="item.acc_shift_id == 2" style="color: #ef09c2">{{ item.acc_type }}</strong>
              <strong v-if="item.acc_shift_id == 3" style="color: #09efde">{{ item.acc_type }}</strong>
            </div>

            <!-- Visit shift field -->
            <div v-else-if="field === 'visit_shift_id'">
              <strong v-if="item.visit_shift_id == 1" style="color: #efa609">{{ item.acc_type }}</strong>
              <strong v-if="item.visit_shift_id == 2" style="color: #ef09c2">{{ item.acc_type }}</strong>
              <strong v-if="item.visit_shift_id == 3" style="color: #09efde">{{ item.acc_type }}</strong>
            </div>

            <!-- Status field -->
            <div v-else-if="field === 'status'">
              <strong v-if="item.status == 'Approved'" style="color: green">Approved</strong>
              <strong v-if="item.status == 'Disapproved'" style="color: red">Disapproved</strong>
              <strong v-if="item.status == 'Pending'" style="color: blue">Pending</strong>
            </div>

            <!-- Details button -->
            <div v-else-if="field === 'v_details'" class="py-2">
              <CButton color="primary" variant="outline" square size="sm" class="mt-3"
                @click="$emit('toggleDetails', item, index)">
                {{ Boolean(item._toggled) ? "Hide" : "Details" }}
              </CButton>
            </div>

            <!-- Map button -->
            <div v-else-if="field === 'map'">
              <c-button v-if="checkPermission('show_single_actual_visits_locations')" color="primary"
                class="btn-sm mt-2 mr-1" @click="$emit('getLocation', item)">
                <CIcon class="text-white" name="marker" />
              </c-button>
            </div>

            <!-- Plan actions -->
            <div v-else-if="field === 'plan_actions'">
              <c-button color="success" class="btn-sm mt-2 mr-1" v-if="checkPermission('edit_plan_visits')"
                :to="{ name: 'EditPlanVisit', params: { id: item.id } }">
                <CIcon name="cil-pencil" />
              </c-button>
              <c-button color="danger" class="btn-sm mt-2 mr-1" v-if="checkPermission('delete_plan_visits')" @click="
                $root
                  .$confirm('Delete', 'Do you want to delete this record?', {
                    color: 'red',
                    width: 290,
                    zIndex: 200,
                  })
                  .then((confirmed) => {
                    if (confirmed) {
                      $emit('deletePlan', item);
                    }
                  })
                "><c-icon name="cil-trash" /></c-button>
            </div>

            <!-- Actual actions -->
            <div v-else-if="field === 'actual_actions'">
              <CButton color="primary" class="btn-sm mt-2 mr-1" v-if="checkPermission('show_single_actual_visits')"
                :to="{ name: 'ActualVisit', params: { id: item.id } }" target="_blank">
                <CIcon name="cil-magnifying-glass" />
              </CButton>
              <CButton color="warning" class="btn-sm mt-2 mr-1 text-white" v-if="checkPermission('create_pv_request')"
                :to="{ name: 'PV', params: { id: item.id } }" target="_blank">
                PV
              </CButton>
              <CButton color="success" class="btn-sm mt-2 mr-1" v-if="checkPermission('edit_actual_visits')"
                :to="{ name: 'EditActualVisit', params: { id: item.id } }" target="_blank">
                <CIcon name="cil-pencil" />
              </CButton>
              <c-button color="danger" v-if="checkPermission('delete_actual_visits')" class="btn-sm mt-2 mr-1" @click="
                $root
                  .$confirm('Delete', 'Do you want to delete this record?', {
                    color: 'red',
                    width: 290,
                    zIndex: 200,
                  })
                  .then((confirmed) => {
                    if (confirmed) {
                      $emit('deleteActualVisit', item);
                    }
                  })
                "><c-icon name="cil-trash" /></c-button>
            </div>

            <!-- Checkbox for selection -->
            <div v-else-if="field === 's'" class="checkbox-container">
              <input
                class="row-checkbox"
                type="checkbox"
                v-model="selectedItems"
                :value="item"
                title="Check One Plan Visit"
                @change="$emit('item-selection-changed', selectedItems)"
              />
            </div>

            <!-- OW actions -->
            <div v-else-if="field === 'ow_actions'">
              <CButton color="success" class="btn-sm mt-2 mr-1" v-if="checkPermission('edit_ow_actual_visits')" :to="{
                name: 'EditOwActualVisit',
                params: { id: item.id },
              }"><c-icon name="cil-pencil" /></CButton>
              <c-button color="danger" class="btn-sm mt-2 mr-1" v-if="checkPermission('delete_ow_actual_visits')"
                @click="
                  $root
                    .$confirm('Delete', 'Do you want to delete this record?', {
                      color: 'red',
                      width: 290,
                      zIndex: 200,
                    })
                    .then((confirmed) => {
                      if (confirmed) {
                        $emit('deleteOwActual', item);
                      }
                    })
                  "><c-icon name="cil-trash" /></c-button>
            </div>

            <!-- Default field rendering -->
            <span v-else>{{ item[field] }}</span>
          </td>
        </tr>
      </tbody>
    </table>
    </div>
  </div>
</template>

<script>
import moment from "moment";

export default {
  name: 'VisitsReportTable',
  props: {
    items: {
      type: Array,
      required: true
    },
    fields: {
      type: Array,
      required: true
    },
    readMore: {
      type: Object,
      required: true
    },
    searchTerm: {
      type: String,
      default: ''
    },
    selectAll: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedItems: [],
      sortColumn: null,
      sortDirection: 'asc',
      localSearchTerm: ''
    };
  },
  mounted() {
    // Set a default sort column on mount if none is specified
    if (!this.sortColumn && this.fields.length > 0) {
      // Find the first non-action column to use as default sort
      const defaultSortColumn = this.fields.find(field => !this.isActionColumn(field));
      if (defaultSortColumn) {
        this.sortColumn = defaultSortColumn;
        this.$emit('sort', { column: this.sortColumn, direction: this.sortDirection });
      }
    }
  },
  computed: {
    filteredItems() {
      // If no search term, return all items
      if (!this.localSearchTerm && !this.searchTerm) {
        return this.sortedItems;
      }

      const term = (this.localSearchTerm || this.searchTerm).toLowerCase();

      // Filter items based on search term
      const filtered = this.items.filter(item => {
        // Search in all fields
        return Object.keys(item).some(key => {
          const value = item[key];
          if (value === null || value === undefined) return false;
          return String(value).toLowerCase().includes(term);
        });
      });

      // Apply sorting to filtered items
      return this.applySorting(filtered);
    },
    sortedItems() {
      return this.applySorting(this.items);
    },
    isAllSelected() {
      // Check if all filtered items are selected
      return this.filteredItems.length > 0 &&
        this.filteredItems.every(item =>
          this.selectedItems.some(selected => selected.id === item.id)
        );
    },
    selectAllModel: {
      get() {
        return this.isAllSelected;
      },
      set(value) {
        if (value) {
          // Select all filtered items
          this.selectedItems = [...this.filteredItems];
        } else {
          // Deselect all items
          this.selectedItems = [];
        }
        // Emit the selection change event
        this.$emit('item-selection-changed', this.selectedItems);
        // Update the selectAll prop in the parent component
        this.$emit('update:selectAll', value);
      }
    }
  },
  watch: {
    selectedItems: {
      handler(newVal) {
        this.$emit('update:selected', newVal);
      },
      deep: true
    },
    searchTerm: {
      handler(newVal) {
        this.localSearchTerm = newVal;
      },
      immediate: true
    },
    selectAll: {
      handler(newVal) {
        // Only apply select all if the 's' field is present (plan filter)
        if (this.fields.includes('s') && newVal !== this.isAllSelected) {
          // Use the setter to handle the change
          this.selectAllModel = newVal;
        }
      },
      immediate: true
    }
  },
  methods: {
    applySorting(itemsToSort) {
      if (!this.sortColumn) return itemsToSort;

      return [...itemsToSort].sort((a, b) => {
        const modifier = this.sortDirection === 'asc' ? 1 : -1;
        const aValue = a[this.sortColumn];
        const bValue = b[this.sortColumn];

        // Handle numeric values
        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return (aValue - bValue) * modifier;
        }

        // Handle date values
        if (this.isDateField(this.sortColumn)) {
          const dateA = new Date(aValue || 0);
          const dateB = new Date(bValue || 0);
          return (dateA - dateB) * modifier;
        }

        // Handle string values
        const strA = String(aValue === null || aValue === undefined ? '' : aValue);
        const strB = String(bValue === null || bValue === undefined ? '' : bValue);
        return strA.localeCompare(strB) * modifier;
      });
    },
    isActionColumn(field) {
      // Identify action columns that shouldn't be sortable
      return ['plan_actions', 'actual_actions', 'ow_actions', 'map', 'v_details', 's'].includes(field);
    },

    handleSort(field) {
      // Skip sorting for action columns
      if (this.isActionColumn(field)) {
        return;
      }

      if (this.sortColumn === field) {
        // Toggle direction if same column
        this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        // Set new column and default to ascending
        this.sortColumn = field;
        this.sortDirection = 'asc';
      }

      // Emit sort event for parent component
      this.$emit('sort', { column: this.sortColumn, direction: this.sortDirection });
    },
    handleSearch(event) {
      this.localSearchTerm = event.target.value;
      this.$emit('search', this.localSearchTerm);
    },
    clearSearch() {
      this.localSearchTerm = '';
      this.$emit('search', '');
    },
    formatFieldName(field) {
      if (!field) return '';
      const withSpaces = field.replace(/[_-]/g, ' ');
      return withSpaces.replace(/\b\w/g, c => c.toUpperCase());
    },
    formatDate(value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD HH:mm:ss");
      }
      return '';
    },
    getCellClass(item, field) {
      // Add conditional classes based on field and item values
      const classes = {};

      if (field === 'division') {
        classes['division-cell'] = true;
      } else if (field === 'account' || field === 'doctor') {
        classes['link-cell'] = true;
      } else if (field === 'status') {
        if (item.status === 'Approved') classes['status-approved'] = true;
        if (item.status === 'Disapproved') classes['status-disapproved'] = true;
        if (item.status === 'Pending') classes['status-pending'] = true;
      }

      return classes;
    },
    isDateField(field) {
      // Identify date fields for proper sorting
      return ['start', 'end', 'created_at', 'updated_at', 'visit_date'].includes(field);
    },
    checkPermission(permission) {
      // Use the same permission check method as the parent component
      return this.$parent.checkPermission(permission);
    },
    toggleSelectAll() {
      // Toggle the selectAllModel value
      this.selectAllModel = !this.isAllSelected;
    }
  },
  emits: [
    'showMore',
    'showLess',
    'toggleDetails',
    'getLocation',
    'deletePlan',
    'deleteActualVisit',
    'deleteOwActual',
    'update:selected',
    'update:selectAll',
    'sort',
    'search',
    'item-selection-changed'
  ]
};
</script>

<style scoped>
/* Search container styles */
.search-container {
  margin-top: 20px;
  margin-bottom: 16px;
}

.search-label {
  font-weight: 600;
  margin-bottom: 8px;
  margin-left: 10px;
  color: #2d3748;
  font-size: 14px;
}

.search-input-wrapper {
  position: relative;
  max-width: 500px;
  margin-bottom: 8px;
  margin-left: 10px;
  padding: 5px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #718096;
}

.search-input {
  width: 100%;
  padding: 10px 40px 10px 36px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  background-color: white;
}

.search-input:focus {
  outline: none;
  border-color: #0056b3;
  box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.2);
  background-color: white;
}

.clear-search-btn {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #718096;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.clear-search-btn:hover {
  background-color: #f0f0f0;
}

.search-results {
  font-size: 13px;
  color: #718096;
  margin-bottom: 8px;
  margin-left: 15px;
  font-style: italic;
}

/* Table container styles */
.table-container {
  overflow-x: auto;
  overflow-y: auto;
  max-height: 70vh;
  padding: 0;
  margin: 0;
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.data-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 14px;
}

.data-table thead th {
  position: sticky;
  top: 0;
  background: #333333; /* Changed from blue to dark gray */
  color: white;
  padding: 12px 16px;
  text-align: center;
  font-weight: 600;
  white-space: nowrap;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.data-table thead tr:first-child th {
  top: 0;
  z-index: 11;
}

.data-table thead tr:nth-child(2) th {
  top: 48px; /* Adjust based on the height of the first header row */
  z-index: 10;
}

.total-header {
  background-color: white !important;
  color: #333 !important;
  text-align: left !important;
  padding-left: 20px !important;
}

.column-header {
  background-color: #333333; /* Changed from blue to dark gray to match thead th */
  color: white;
}

/* Sortable header styles */
.sortable-header {
  cursor: pointer;
  transition: background-color 0.2s ease;
  user-select: none;
  position: relative;
}

.sortable-header:hover {
  background-color: #444444; /* Darker gray for hover state */
}

.sortable-header:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: rgba(255, 255, 255, 0.2);
}

.sortable-header.sorted-asc,
.sortable-header.sorted-desc {
  background-color: #222222; /* Even darker gray for sorted state */
}

.sortable-header.sorted-asc:after,
.sortable-header.sorted-desc:after {
  background-color: rgba(255, 255, 255, 0.5);
}

.sort-icon {
  margin-left: 5px;
  font-size: 12px;
  display: inline-block;
  width: 12px;
  opacity: 0.7;
}

.sorted-asc .sort-icon,
.sorted-desc .sort-icon {
  opacity: 1;
  font-weight: bold;
}

/* Select all checkbox styling */
.select-all-header {
  text-align: center;
  width: 50px;
  min-width: 50px;
  max-width: 50px;
}

.select-all-checkbox {
  cursor: pointer;
  width: 20px;
  height: 20px;
  margin: 0;
  padding: 0;
  border: 2px solid white;
  border-radius: 3px;
  background-color: transparent;
  position: relative;
  z-index: 20;
  vertical-align: middle;
}

.select-all-checkbox:checked {
  background-color: #ffcc00; /* Changed to yellow for better contrast with blue header */
  position: relative;
}

.select-all-checkbox:checked:after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 6px;
  height: 12px;
  border: solid #333; /* Darker checkmark for better visibility */
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.data-table tbody tr:hover {
  background-color: rgba(0, 92, 200, 0.05);
}

/* Row checkbox styling */
.checkbox-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.row-checkbox {
  cursor: pointer;
  width: 18px;
  height: 18px;
  margin: 0;
  padding: 0;
  border: 2px solid #333333; /* Changed to match header color */
  border-radius: 3px;
  position: relative;
  vertical-align: middle;
}

.row-checkbox:checked {
  background-color: #ffcc00; /* Changed to yellow to match select-all checkbox */
  position: relative;
}

.row-checkbox:checked:after {
  content: '';
  position: absolute;
  left: 5px;
  top: 1px;
  width: 5px;
  height: 10px;
  border: solid #333; /* Changed to dark gray to match select-all checkbox */
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.data-table td {
  padding: 10px 16px;
  border-bottom: 1px solid #e2e8f0;
  vertical-align: middle;
}

/* Status cell styles */
.status-approved {
  color: #0cce6b;
  font-weight: 600;
}

.status-disapproved {
  color: #ff4757;
  font-weight: 600;
}

.status-pending {
  color: #0056b3;
  font-weight: 600;
}

/* Division cell styles */
.division-cell {
  color: #198520;
  font-weight: 600;
}

/* Link cell styles */
.link-cell {
  color: #0056b3;
  cursor: pointer;
}
</style>
