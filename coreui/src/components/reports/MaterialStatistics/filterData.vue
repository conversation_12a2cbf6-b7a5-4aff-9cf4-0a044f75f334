<template>

    <c-card>
      <c-card-header>Material Report</c-card-header>
      <c-card-body>
        <div class="row">
          <div class="col-lg-4 col-md-4 col-sm-8">
            <c-form-group>
            
              <template #input>
                <v-select
                  v-model="line_id"
                  :options="lines"
                  label="name"
                  :value="0"
                  :reduce="(line) => line.id"
                  placeholder="Select Line"
                  class="mt-2"
                  @input="getLineData"
                />
              </template>
            </c-form-group>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-8">
            <c-form-group>
           
              <template #input>
                <v-select
                  v-model="filter"
                  :options="filters"
                  label="name"
                  :value="0"
                  :reduce="(line) => line.id"
                  placeholder="Select Filter"
                  class="mt-2"
                  @input="
                    filter == 1 ? (user_id = []) : (division_id = [])
                  "
                />
              </template>
            </c-form-group>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-8" v-if="filter == 1">
            <c-form-group>
              <template #label> Division </template>
              <template #input>
                <input
                  label="All"
                  id="division"
                  v-if="divisions.length != 0"
                  class="m-1"
                  type="checkbox"
                  v-model="checkAllDivisions"
                  title="Check All Divisions"
                  @change="checkAllDivs"
                />
                <label
                  for="division"
                  v-if="divisions.length != 0"
                  style="font-weight: bold"
                  >All</label
                >
                <v-select
                  v-model="division_id"
                  :options="divisions"
                  label="name"
                  :value="0"
                  :reduce="(division) => division.id"
                  placeholder="Select Division"
                  class="mt-2"
                  multiple
                />
              </template>
            </c-form-group>
          </div>
          <div class="col-lg-4 col-md-4 col-sm-8" v-if="filter == 2">
            <c-form-group>
              <template #label> Employee </template>
              <template #input>
                <input
                  label="All"
                  v-if="users.length != 0"
                  id="user"
                  class="m-1"
                  type="checkbox"
                  v-model="checkAllUsers"
                  title="Check All Users"
                  @change="checkAllEmployees"
                />
                <label
                  for="user"
                  v-if="users.length != 0"
                  style="font-weight: bold"
                  >All</label
                >
                <v-select
                  v-model="user_id"
                  :options="users"
                  label="name"
                  :value="0"
                  :reduce="(user) => user.id"
                  placeholder="Select Employee"
                  class="mt-2"
                  multiple
                />
              </template>
            </c-form-group>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-3 col-md-3 col-sm-8">
            <c-form-group>
              <template #label> Material Types </template>
              <template #input>
                <v-select
                  v-model="material_type_id"
                  :options="material_types"
                  label="name"
                  :reduce="(material_type) => material_type.id"
                  placeholder="Select Approval Types"
                  class="mt-2"
                />
              </template>
            </c-form-group>
          </div>

          <div class="col-lg-3 col-md-3 col-sm-8">
            <c-input
              label="From"
              type="date"
              placeholder="From"
              v-model="from_date"
            ></c-input>
          </div>

          <div class="col-lg-3 col-md-3 col-sm-8">
            <c-input
              label="To"
              type="date"
              placeholder="To"
              v-model="to_date"
            ></c-input>
          </div>
        </div>
      </c-card-body>
      <c-card-footer>
        <c-button
          v-if="line_id != null"
          color="primary"
          class="text-white"
          @click="show"
          style="float: right"
          >Show</c-button
        >
      </c-card-footer>
    </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      division_id: [],
      product_id: [],
      type_id: [],
      user_id: [],
      line_id: null,
      divisions: [],
      material_types: [],
      material_type_id: null,

      products: [],
      types: [],
      lines: [],
      users: [],
      filters: [
        { id: 1, name: "Division" },
        { id: 2, name: "Employee" },
      ],
      filter: 2,
      checkAllDivisions: false,
      allRequestTypes: true,
      checkAllUsers: false,
      checkAllProducts: false,
      from_date: new Date().toISOString().slice(0, 10),
      to_date: new Date().toISOString().slice(0, 10),
    };
  },
  methods: {
    initialize() {
      axios
        .get("/api/material-report-lines")
        .then((response) => {
          this.lines = response.data.data.lines;
          this.material_types = response.data.data.materialTypes;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .get(`/api/get-line-request-data/${this.line_id}`)
        .then((response) => {
          this.divisions = response.data.data.divisions;
          this.users = response.data.data.users;
          this.products = response.data.data.products;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },

    checkAllRequestType() {
      if (this.allRequestTypes)
        this.type_id = this.types.map((item) => item.id);
      if (this.allRequestTypes == false) this.type_id = null;
    },
    checkAllProduct() {
      if (this.checkAllProducts)
        this.product_id = this.products.map((item) => item.id);
      if (this.checkAllProducts == false) this.product_id = null;
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = null;
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    show() {
      let materialFilter = {
        line: this.line_id,
        filter: this.filter,
        divisions: this.division_id,
        material_type_id: this.material_type_id,
        users: this.user_id,
        fromDate: this.from_date,
        toDate: this.to_date,
      };
      this.$emit("getSchedule", { materialFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>