<template>
  <c-card>
    <c-card-header>Custody Report</c-card-header>
    <c-card-body>

      <c-card>
        <c-card-body>
          <div class="row">
            <div class="col-lg-3 col-md-3 col-sm-8">
              <c-form-group>
                <template #label>
                  Line <span style="color: red">*</span></template>
                <template #input>
                  <input label="All" v-if="lines.length != 0" id="line" class="m-1" type="checkbox"
                    v-model="checkAllLines" title="Check All lines" @change="checkAllLine" />
                  <label for="line" v-if="lines.length != 0" style="font-weight: bold">All</label>
                  <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                    placeholder="Select Line" class="mt-2" multiple @input="getLineData" />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-8">
              <c-form-group>
                <template #label> Employee </template>
                <template #input>
                  <input label="All" v-if="users.length != 0" id="user" class="m-1" type="checkbox"
                    v-model="checkAllUsers" title="Check All Users" @change="checkAllEmployees" />
                  <label for="user" v-if="users.length != 0" style="font-weight: bold">All</label>
                  <v-select v-model="user_id" :options="users" label="fullname" :value="0" :reduce="(user) => user.id"
                    placeholder="Select Employee" class="mt-2" multiple />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-8">
              <c-input label="From" type="date" placeholder="From" v-model="from_date"></c-input>
            </div>

            <div class="col-lg-3 col-md-3 col-sm-8">
              <c-input label="To" type="date" placeholder="To" v-model="to_date"></c-input>
            </div>
          </div>
        </c-card-body>
      </c-card>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" class="text-white" @click="show" style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      user_id: [],
      line_id: [],
      lines: [],
      users: [],
      checkAllUsers: false,
      checkAllLines: false,
      from_date: moment().startOf("month").format("YYYY-MM-DD"),
      to_date: moment().endOf("month").format("YYYY-MM-DD"),
    };
  },
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: null,
          to: null,
          data: ['lines']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post(`/api/custody-report-data`, { lines: this.line_id })
        .then((response) => {
          this.users = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) {
        this.line_id = null;
        this.user_id = null;
      }
      this.getLineData();
    },

    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    show() {
      let custodyFilter = {
        lines: this.line_id,
        users: this.user_id,
        fromDate: this.from_date,
        toDate: this.to_date,
      };
      this.$emit("getSchedule", { custodyFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>