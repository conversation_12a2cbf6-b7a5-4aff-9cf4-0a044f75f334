<template>
  <c-card>
    <c-card-header>Product Frequency Report</c-card-header>
    <c-card-body>
      <c-tabs>
        <c-tab active>
          <template slot="title">
            <c-icon name="cil-chart-pie" /> Line
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Line </template>
                    <template #input>
                      <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                        placeholder="Select option" class="mt-2" @input="getDivisionsAndProducts" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Division </template>
                    <template #input>
                      <input label="All" v-if="divisions.length != 0" class="m-1" type="checkbox"
                        v-model="checkAllDivisions" title="Check All Divisions" @change="checkAllDivs" />
                      <label v-if="divisions.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="division_ids" :options="divisions" label="name" :value="0"
                        :reduce="(division) => division.id" placeholder="Select option" class="mt-2" multiple
                        @input="getEmployees" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Employee </template>
                    <template #input>
                      <input label="All" v-if="users.length != 0" class="m-1" type="checkbox" v-model="checkAllUsers"
                        title="Check All Employees" @change="checkAllusers" />
                      <label v-if="users.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="user_ids" :options="users" label="name" :value="0" :reduce="(user) => user.id"
                        placeholder="Select option" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label>
                      <strong>From Date</strong>
                    </template>
                    <template #input>
                      <c-input type="date" class="mt-2" placeholder="From Date" v-model="fromDate"></c-input>
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label>
                      <strong>To Date</strong>
                    </template>
                    <template #input>
                      <c-input type="date" class="mt-2" placeholder="To Date" v-model="toDate"></c-input>
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon name="cil-chart-pie" /> Products
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Product </template>
                    <template #input>
                      <input label="All" v-if="products.length != 0" class="m-1" type="checkbox"
                        v-model="checkAllProducts" title="Check All Products" checked @change="checkAllProduct" />
                      <label v-if="products.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="product_ids" :options="products" label="name" :value="0"
                        :reduce="(product) => product.id" placeholder="Select option" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
      </c-tabs>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" class="text-white" @click="show" style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["getProduct"],
  data() {
    return {
      divisions: [],
      division_ids: [],
      checkAllDivisions: false,
      users: [],
      user_ids: [],
      checkAllUsers: true,
      products: [],
      product_ids: [],
      checkAllProducts: true,
      lines: [],
      line_id: null,
      fromDate: new Date().toISOString().slice(0, 10),
      toDate: new Date().toISOString().slice(0, 10),
    };
  },
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: null,
          to: null,
          data: ['lines']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getDivisionsAndProducts() {
      axios
        .get(`/api/get-divisions-products/${this.line_id}`)
        .then((response) => {
          this.divisions = response.data.divisions;
          this.products = response.data.products;
          this.product_ids = this.products.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getEmployees() {
      axios
        .post(`/api/get-users/`, { divisions: this.division_ids })
        .then((response) => {
          this.users = response.data.users;
          this.user_ids = this.users.map((item) => item.id);
        })
        .catch((error) => {
          showErrorMessage(error);
        });
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_ids = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_ids = null;
    },
    checkAllProduct() {
      if (this.checkAllProducts)
        this.product_ids = this.products.map((item) => item.id);
      if (this.checkAllProducts == false) this.product_ids = null;
    },
    checkAllusers() {
      if (this.checkAllUsers) this.user_ids = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_ids = null;
    },
    show() {
      this.$emit("getProduct", {
        line: this.line_id,
        products: this.product_ids,
        divisions: this.division_ids,
        users: this.user_ids,
        fromDate: this.fromDate,
        toDate: this.toDate,
      });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
