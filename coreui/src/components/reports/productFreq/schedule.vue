<template>
  <c-card>
    <c-card-body>
      <div class="productFrequency" ref="productFrequency" style="height: auto" id="print">
        <div style="height: 60px" class="text-center">
          <h3>Product Frequency Report</h3>
        </div>
        <div style="height: 60px" class="text-center">
          <h3>{{ fromDate }} To {{ toDate }}</h3>
        </div>
        <table ref="productFrequency_table" id="productFrequency" class="table table-striped">
          <tr>
            <th scope="col" v-if="divisions.length!=0">
              <div style="width: 100px" class="text-center text-white">
                <label>Divisions</label>
              </div>
            </th>
            <th scope="col" v-if="users.length!=0">
              <div style="width: 100px" class="text-center text-white">
                <label>Employees</label>
              </div>
            </th>
            <th scope="col">
              <div style="width: 100px" class="text-center text-white">
                <label>No.of <br /> Calls</label>
              </div>
            </th>
            <th scope="col" v-for="(product, index) in products" :key="index">
              <div style="width: 100px" class="text-center text-white">
                <label>{{ product.name }}</label>
              </div>
            </th>
          </tr>
          <tr scope="col" v-for="(division, index) in divisions" :key="index">
            
            <td>
              <div
                :style="{color:division.color}"
                style="width: 100px"
                class="text-center"
              >
                <label>{{ division.name }}</label>
              </div>
            </td>
            <td style="width: 100px" v-if="users.length!=0">
              <div class="text-center">
                <label>{{ users[index] ? users[index].name : " "  }}</label>
              </div>
            </td>
            <td style="width: 100px">
              <div class="text-center">
                <label>{{ calls[index] }}</label>
              </div>
            </td>
            <td
              v-for="(countProd, countProdIndex) in countprods"
              :key="countProdIndex"
            >
              <div style="width: 100px" class="text-center">
                <label>{{countProd[division.id]}} </label>
              </div>
            </td>
          </tr> 
        </table>
      </div>
    </c-card-body>
    <c-card-footer>
      <download
        @getPrint="print"
        @getxlsx="download"
        @getcsv="downloadCsv"
        @getpdf="createPDF"
        :name="name"
      />
    </c-card-footer>
  </c-card>
</template>
<script>
import jsPDF from "jspdf";
import "jspdf-autotable";
import * as XLSX from "xlsx";
import download from "../../download-reports/download.vue";
export default {
  components: { download },
  props: {
    divisions: {
      type: Array,
      required: true,
    },
    products: {
      type: Array,
      required: true,
    },
    calls: {
      type: Array,
      required: true,
    },
    // percentages: {
    //   type: Array,
    //   required: true,
    // },
    users: {
      type: Array,
      required: true,
    },
    fromDate: {
      type: String,
      required: true,
    },
    toDate: {
      type: String,
      required: true,
    },
    countProducts: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      percentages: [],
      countprods:[],
      name: "Product Frequency Report",
    };
  },
  emits: ["downloaded"],
  methods: {
    download(type, fn, dl) {
      var elt = this.$refs.productFrequency;
      var wb = XLSX.utils.table_to_book(elt, { sheet: "Product Frequency" });
      return dl
        ? XLSX.write(wb, { bookType: type, bookSST: true, type: "base64" })
        : XLSX.writeFile(
            wb,
            fn || (this.name + "." || "SheetJSTableExport.") + (type || "xlsx")
          );
    },
    downloadCsv(type, fn, dl) {
      var elt = this.$refs.productFrequency;
      var wb = XLSX.utils.table_to_book(elt, { sheet: "Coverage" });
      return dl
        ? XLSX.write(wb, { bookType: type, bookSST: true, type: "base64" })
        : XLSX.writeFile(
            wb,
            fn || (this.name + "." || "SheetJSTableExport.") + (type || "csv")
          );
    },
    print() {
      this.$htmlToPaper("print");
    },
    createPDF() {
      let pdfName = "Product Frequency Report";
      const doc = new jsPDF('l', 'mm', 'a3');
      doc.autoTable({
        html: "#productFrequency",
        margin: { top: 10 },
        showHead: "firstPage",
        useCss: true,
      });
      doc.save(pdfName + ".pdf");
    },
    arrayCountProducts(countProducts) {
      this.countprods = Object.keys(countProducts).reduce(function (r, k) {
        countProducts[k].forEach(function (a, i) {
          r[i] = r[i] || {};
          r[i][k] = a;
        });
        return r;
      }, []);
    },
    arraySpecialities(countSpecialities) {
      this.specialities = Object.keys(countSpecialities).reduce(function (
        r,
        k
      ) {
        countSpecialities[k].forEach(function (a, i) {
          r[i] = r[i] || {};
          r[i][k] = a;
        });
        return r;
      },
      []);
    },
  },
  created() {
    var arr1 = this.countProducts;
    // var arr2 = this.countSpecialities;
    // var arr3 = this.countClasses;
    this.arrayCountProducts(arr1);
    // this.arraySpecialities(arr2);
  },
};
</script>
<style scoped>
.productFrequency {
  overflow-y: auto;
  height: 600px;
}
table {
  display: table;
  white-space: nowrap;
  width: 100%;
}

th {
  opacity: 1;
  position: sticky;
  top: 0;
  background: #acd5d6;
}
.form-group {
  margin-bottom: 0;
}
th label {
  color: rgb(27, 27, 29);
}
label {
  font-weight: bolder;
  margin-bottom: 0;
}
</style>