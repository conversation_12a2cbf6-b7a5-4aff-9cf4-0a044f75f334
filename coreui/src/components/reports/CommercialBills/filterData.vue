<template>
  <c-card>
    <c-card-header>Commercial & Branding Report</c-card-header>
    <c-card-body>
      <c-tabs>
        <c-tab active>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Main
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>

                    <template #label>
                      Line <span style="color: red">*</span></template>

                    <template #input>
                      <input label="All" v-if="lines.length != 0" id="line" class="m-1" type="checkbox"
                        v-model="checkAllLines" title="Check All lines" @change="checkAllLine" />
                      <label for="line" v-if="lines.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                        placeholder="Select Line" class="mt-2" @input="getLineData" multiple />
                    </template>
                  </c-form-group>
                </div>
                <!-- <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Approval Types </template>
                    <template #input>
                      <v-select v-model="approval_id" :options="approvals" label="name" :value="0"
                        :reduce="(approval) => approval.value" placeholder="Select Approval Types" class="mt-2" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Payments </template>
                    <template #input>
                      <v-select v-model="payment_id" :options="payments" label="name" :value="0"
                        :reduce="(payment) => payment.id" placeholder="Select Payment Type" class="mt-2" />
                    </template>
                  </c-form-group>
                </div> -->
                <!-- <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>

                    <template #label> Products </template>

                    <template #input>
                      <input label="All" v-if="products.length != 0" id="product" class="m-1" type="checkbox"
                        v-model="checkAllProducts" title="Check All Products" @change="checkAllProduct" />
                      <label for="product" v-if="products.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="product_id" :options="products" label="name" :value="0"
                        :reduce="(product) => product.id" placeholder="Select Product" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div> -->
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>

                    <template #label> Request Type </template>

                    <template #input>
                      <input label="All" id="type" v-if="types.length != 0" class="m-1" type="checkbox"
                        v-model="allRequestTypes" title="Check All Types" @change="checkAllRequestType" />
                      <label for="type" v-if="types.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="type_id" :options="types" label="name" :value="0" :reduce="(type) => type.id"
                        placeholder="Select Commercial Type" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>

                    <template #label> Vendor </template>

                    <template #input>
                      <input label="All" id="type" v-if="vendors.length != 0" class="m-1" type="checkbox"
                        v-model="checkAllVendors" title="Check All Vendor" @change="checkAllVendor" />
                      <label for="type" v-if="vendors.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="vendor_id" :options="vendors" label="name" :value="0"
                        :reduce="(vendor) => vendor.id" placeholder="Select Vendor" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-input label="From" type="date" placeholder="From" v-model="from_date"></c-input>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-input label="To" type="date" placeholder="To" v-model="to_date"></c-input>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Actions
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>

                    <template #label>
                      Action</template>

                    <template #input>
                      <v-select v-model="action_id" :options="actions" label="name" :value="0"
                        :reduce="(action) => action.id" placeholder="Select Line" class="mt-2" />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
      </c-tabs>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" class="text-white" @click="show" style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";

export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      // division_id: [],
      // product_id: [],
      type_id: [],
      // user_id: [],
      line_id: [],
      types: [],
      vendors: [],
      vendor_id: [],
      lines: [],
      checkAllLines: false,
      checkAllVendors: false,
      allRequestTypes: true,
      action_id: null,
      actions: [
        { id: 1, name: 'Show Bills' },
      ],
      from_date: moment().startOf('month').format("YYYY-MM-DD"),
      to_date: moment().endOf('month').format("YYYY-MM-DD"),
    };
  },
  methods: {
    initialize() {
      axios
        .get("/api/commercial-report-lines")
        .then((response) => {
          this.lines = response.data.data.lines;
          this.vendors = response.data.data.categoriesTypes;
          // this.payments = response.data.data.payments;
          this.types = response.data.data.requestTypes;
          this.type_id = this.types.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post(`/api/visitsReport`, { lines: this.line_id })
        .then((response) => {
          // this.divisions = response.data.data.divisions;
          // this.users = response.data.data.users;
          this.products = response.data.products;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) {
        this.line_id = [];
        this.product_id = [];
      }
      this.getLineData();
    },
    checkAllRequestType() {
      if (this.allRequestTypes)
        this.type_id = this.types.map((item) => item.id);
      if (this.allRequestTypes == false) this.type_id = null;
    },
    checkAllVendor() {
      if (this.checkAllVendors)
        this.vendor_id = this.vendors.map((item) => item.id);
      if (this.checkAllVendors == false) this.vendor_id = [];
    },
    show() {
      let commercialFilter = {
        lines: this.line_id,
        types: this.type_id,
        vendors: this.vendor_id,
        action_id: this.action_id,
        fromDate: this.from_date,
        toDate: this.to_date,
      };
      this.$emit("getSchedule", { commercialFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>