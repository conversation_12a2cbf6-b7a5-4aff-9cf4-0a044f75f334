<template>
  <c-card>
    <c-card-header>Product Frequency Report</c-card-header>
    <c-card-body>
      <c-tabs>
        <c-tab active>
          <template slot="title">
            <c-icon name="cil-chart-pie" /> Line
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <lines @lines="selectedLine" />
                <divisions v-if="line" @divisions="selectedDivisions" :line="line" />
                <users v-if="divisions.length!=0" @users="selectedUsers" :divisions="divisions" />
                <div class="col-4">
                  <c-form-group>
                    <template #label>
                      <strong>From Date</strong>
                    </template>
                    <template #input>
                      <c-input
                        type="date"
                        class="mt-2"
                        placeholder="From Date"
                        v-model="fromDate"
                      ></c-input>
                    </template>
                  </c-form-group>
                </div>
                <div class="col-4">
                  <c-form-group>
                    <template #label>
                      <strong>To Date</strong>
                    </template>
                    <template #input>
                      <c-input
                        type="date"
                        class="mt-2"
                        placeholder="To Date"
                        v-model="toDate"
                      ></c-input>
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon name="cil-chart-pie" /> Products
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <products v-if="line" @products="selectedProducts" :line="line" />
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
      </c-tabs>
    </c-card-body>
    <c-card-footer>
      <c-button
        color="primary"
        class="text-white"
        @click="show"
        style="float: right"
        >Show</c-button
      >
    </c-card-footer>
  </c-card>
</template>
<script>
import lines from "../../reports/filters/line.vue";
import divisions from "../../reports/filters/divisions.vue";
import users from "../../reports/filters/employeeDivision.vue";
import products from "../../reports/filters/lineProducts.vue";
export default {
  components: {
    lines,
    divisions,
    users,
    products,
  },
  emits: ["getProduct"],
  data() {
    return {
      line: null,
      divisions:[],
      users:[],
      products:[],
      fromDate: new Date().toISOString().slice(0, 10),
      toDate: new Date().toISOString().slice(0, 10),
    };
  },
  methods: {
    selectedLine(line) {
      this.line = line;
    },
    selectedDivisions(divisions){
      this.divisions=[...divisions];
    },
    selectedUsers(users){
      this.users=[...users];
    },
    selectedProducts(products){
      this.products=[...products];
    },
    show() {
      this.$emit("getProduct", {
        line:this.line,
        products:this.products,
        divisions:this.divisions,
        users:this.users,
        fromDate: this.fromDate,
        toDate: this.toDate,
      });
    },
  },
};
</script>
