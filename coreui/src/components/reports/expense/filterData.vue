<template>
  <c-card>
    <c-card-header>Expense Report</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="mt-2 col-lg-4 col-md-4 col-sm-6">
          <c-form-group>
            <template #label> Line </template>
            <template #input>   
              <v-select
                v-model="line_id"
                :options="lines"
                label="name"
                :value="0"
                :reduce="(line) => line.id"
                placeholder="Select option"
                class="mt-2"
                @input="getLineData"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-6">
          <c-form-group>
            <template #label> Division </template>
            <template #input>
              <input
                label="All"
                v-if="divisions.length != 0"
                class="m-1"
                type="checkbox"
                v-model="checkAllDivisions"
                title="Check All Divisions"
                @change="checkAllDivs"
              />
              <label v-if="divisions.length != 0" style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="division_id"
                :options="divisions"
                label="name"
                :value="0"
                :reduce="(division) => division.id"
                placeholder="Select option"
                class="mt-2"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-6">
          <c-form-group>
            <template #label> Employee </template>
            <template #input>
              <input
                label="All"
                v-if="employees.length != 0"
                class="m-1"
                type="checkbox"
                v-model="checkAllemployees"
                title="Check All employees"
                @change="checkAllemployee"
              />
              <label v-if="employees.length != 0" style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="employee_id"
                :options="employees"
                label="name"
                :value="0"
                :reduce="(employee) => employee.id"
                placeholder="Select option"
                class="mt-2"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-6">
          <c-form-group>
            <template #label> product </template>
            <template #input>
              <input
                label="All"
                v-if="products.length != 0"
                class="m-1"
                type="checkbox"
                v-model="checkAllproducts"
                title="Check All products"
                @change="checkAllproduct"
              />
              <label v-if="products.length != 0" style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="product_id"
                :options="products"
                label="name"
                :value="0"
                :reduce="(product) => product.id"
                placeholder="Select option"
                class="mt-2"
                multiple
              />
            </template>
          </c-form-group>
        </div>
 
        <div class="mt-2 col-lg-4 col-md-4 col-sm-6">
          <c-input
            label="From"
            type="date"
            placeholder="date"
            v-model="date_from"
          ></c-input>
        </div>
        <div class="mt-2 col-lg-4 col-md-4 col-sm-6">
          <c-input
            label="To"
            type="date"
            placeholder="date"
            v-model="date_to"
          ></c-input>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button
        v-if="line_id"
        color="primary"
        class="text-white"
        @click="show"
        style="float: right"
        >Show</c-button
      >
    </c-card-footer>
  </c-card>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";

export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      lines: [],
      employees: [],
      products: [],
      divisions: [],
      line_id: null,
      product_id: [],
      employee_id: [],
      division_id: [],
      checkAllLines: false,
      checkAllDivisions: false,
      checkAllproducts: false,
      checkAllemployees: false,
      date_from: moment().startOf('month').format("YYYY-MM-DD"),
      date_to: moment().endOf('month').format("YYYY-MM-DD"),
    };
  },
  methods: {
    initialize() {
      this.getlines();
    },
    getlines() {
        axios
          .get("/api/commercial-lines")
          .then((response) => {
            this.lines = response.data.data;
          })
          .catch((error) => {
            this.showErrorMessage(error);
          });
      },
    getLineData() {
      axios
        .get(`/api/get-line-data/${this.line_id}`)
        .then((response) => {
          this.employees = response.data.data.users;
          this.products = response.data.data.products;
          this.divisions = response.data.data.divisions;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = null;
    },
    checkAllemployee() {
      if (this.checkAllemployees)
        this.employee_id = this.employees.map((item) => item.id);
      if (this.checkAllemployees == false) this.employee_id = null;
    },
    checkAllproduct() {
      if (this.checkAllproducts)
        this.product_id = this.products.map((item) => item.id);
      if (this.checkAllproducts == false) this.product_id = null;
    },

    show() {
      let expenseFilter = {
        line: this.line_id,
        divisions: this.division_id,
        products: this.product_id,
        employees: this.employee_id,
        date_from: this.date_from,
        date_to: this.date_to,
      };
      this.$emit("getSchedule", { expenseFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
