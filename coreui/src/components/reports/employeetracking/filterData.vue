<template>
  <c-card>
    <c-card-header>Employee Tracking Report</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-form-group>
            <template #label> Line </template>
            <template #input>
              <input label="All" v-if="lines.length != 0" id="line" class="m-1" type="checkbox" v-model="checkAllLines"
                title="Check All lines" @change="checkAllLine" />
              <label for="line" v-if="lines.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                placeholder="Select Line" class="mt-2" multiple @input="getLineData" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Employee </template>
            <template #input>
              <input label="All" v-if="users.length != 0" id="user" class="m-1" type="checkbox" v-model="checkAllUsers"
                title="Check All Users" @change="checkAllEmployees" />
              <label for="user" v-if="users.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="user_id" :options="users" label="fullname" :value="0" :reduce="(user) => user.id"
                placeholder="Select Employee" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Shift </template>
            <template #input>
              <input label="All" id="shift" v-if="shifts.length != 0" class="m-1" type="checkbox"
                v-model="checkAllShifts" title="Check All Shifts" @change="checkAllShift" />
              <label for="shift" v-if="shifts.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="shift_id" :options="shifts" label="name" placeholder="Select Shift"
                :reduce="(option) => option.id" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-input label="From" type="date" placeholder="From" v-model="from_date"></c-input>
        </div>

        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-input label="To" type="date" placeholder="To" v-model="to_date"></c-input>
        </div>
      </div>
      <div class="row mt-3">
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Filter Options </template>
            <template #input>
              <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" id="showNoShow" v-model="showNoShow">
                <label class="custom-control-label" for="showNoShow">Show No Show Only</label>
              </div>
            </template>
          </c-form-group>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button v-if="line_id != null" color="primary" class="text-white" @click="show"
        style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      user_id: [],
      line_id: [],
      lines: [],
      shift_id: [],
      shifts: [],
      users: [],
      shift_id: [],
      checkAllDivisions: false,
      checkAllShifts: true,
      checkAllLines: false,
      checkAllUsers: false,
      showNoShow: false,
      from_date: new Date().toISOString().slice(0, 10),
      to_date: new Date().toISOString().slice(0, 10),
    };
  },
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: null,
          to: null,
          data: ['lines', 'users', 'shifts']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.users = response.data.data.users;
          this.shifts = response.data.data.shifts;
          this.shift_id = this.shifts.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post(`/api/visitsReport`, { lines: this.line_id })
        .then((response) => {
          this.users = response.data.users;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = null;
      this.getLineData();
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    checkAllShift() {
      if (this.checkAllShifts) {
        this.shift_id = this.shifts.map((item) => item.id);
        this.checkAllAccounts();
      }
      if (this.checkAllShifts == false) this.shift_id = null;
    },
    show() {
      let trackingFilter = {
        lines: this.line_id,
        users: this.user_id,
        shifts: this.shift_id,
        fromDate: this.from_date,
        toDate: this.to_date,
        showNoShow: this.showNoShow,
      };
      this.$emit("getSchedule", { trackingFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>