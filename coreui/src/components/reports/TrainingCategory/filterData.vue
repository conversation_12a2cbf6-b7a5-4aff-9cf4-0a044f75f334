<template>
  <c-card>
    <c-card-header>Question Quiz Report</c-card-header>
    <c-card-body>
      <c-card>
        <c-card-body>
          <div class="row">
            <div class="col-lg-3 col-md-3 col-sm-8">
              <c-form-group>
                <template #label> Line <span style="color:red">*</span></template>
                <template #input>
                  <input label="All" v-if="lines.length != 0" id="line" class="m-1" type="checkbox"
                    v-model="checkAllLines" title="Check All Lines" @change="checkAllLine" />
                  <label for="line" v-if="lines.length != 0" style="font-weight: bold">All</label>
                  <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                    placeholder="Select Line" class="mt-2" @input="getLineData" multiple />
                </template>
              </c-form-group>
            </div>

            <div class="col-lg-3 col-md-3 col-sm-8">
              <c-form-group>
                <template #label> View <span style="color:red">*</span></template>
                <template #input>
                  <v-select v-model="view" :options="['Details', 'Statistics']" :value="0" placeholder="Select Line"
                    class="mt-2" />
                </template>
              </c-form-group>
            </div>

            <div class="col-lg-3 col-md-3 col-sm-8">
              <c-form-group>
                <template #label> Products </template>
                <template #input>
                  <input label="All" v-if="products.length != 0" id="product" class="m-1" type="checkbox"
                    v-model="checkAllProducts" title="Check All Products" @change="checkAllProduct" />
                  <label for="product" v-if="products.length != 0" style="font-weight: bold">All</label>
                  <v-select v-model="product_id" :options="products" label="name" :value="0"
                    :reduce="(product) => product.id" placeholder="Select Product" class="mt-2" multiple />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-8">
              <c-form-group>
                <template #label> Categories </template>
                <template #input>
                  <input label="All" id="category" class="m-1" type="checkbox" v-model="checkAllCategories"
                    title="Check All Categories" @change="checkAllCategory" />
                  <label for="category" style="font-weight: bold">All</label>
                  <v-select v-model="category_id" :options="categories" label="name" :value="0"
                    :reduce="(Category) => Category.id" placeholder="Select Category" class="mt-2" multiple />
                </template>
              </c-form-group>
            </div>

          </div>
        </c-card-body>
      </c-card>
    </c-card-body>
    <c-card-footer>
      <c-button v-if="line_id != null" color="primary" class="text-white" @click="show"
        style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      product_id: [],
      category_id: [],
      line_id: [],
      products: [],
      categories: [],
      lines: [],
      view: 'Statistics',
      checkAllCategories: false,
      checkAllProducts: false,
      checkAllLines: false,
    };
  },
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: null,
          to: null,
          data: ['lines']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post(`/api/get-line-data-training-reports`, {
          lines: this.line_id,
        })
        .then((response) => {
          this.products = response.data.products;
          this.categories = response.data.categories;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines)
        this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = null;
      this.getLineData();
    },
    checkAllProduct() {
      if (this.checkAllProducts)
        this.product_id = this.products.map((item) => item.id);
      if (this.checkAllProducts == false) this.product_id = null;
    },
    checkAllCategory() {
      if (this.checkAllCategories)
        this.category_id = this.categories.map((item) => item.id);
      if (this.checkAllCategories == false) this.category_id = null;
    },

    show() {
      let trainingCategoryFilter = {
        line: this.line_id,
        products: this.product_id,
        categories: this.category_id,
        view: this.view,
      };
      this.$emit("getSchedule", { trainingCategoryFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>