<template>
  <c-card>
    <c-card-header>Product Mapping Report</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Line </template>
            <template #input>
              <input
                label="All"
                v-if="lines.length != 0"
                class="m-1"
                id="lines"
                type="checkbox"
                v-model="checkAllLines"
                title="Check All Lines"
                @change="checkAllLine"
              />
              <label
                for="lines"
                v-if="lines.length != 0"
                style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="line_id"
                :options="lines"
                label="name"
                :value="0"
                :reduce="(line) => line.id"
                placeholder="Select option"
                class="mt-2"
                @input="getLineData"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Distributor </template>
            <template #input>
              <input
                label="All"
                v-if="distributors.length != 0"
                class="m-1"
                type="checkbox"
                v-model="checkAllDistributors"
                title="Check All Distributors"
                @change="checkAllDistributor"
              />
              <label v-if="distributors.length != 0" style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="distributor_id"
                :options="distributors"
                label="name"
                :value="0"
                :reduce="(distributor) => distributor.id"
                placeholder="Select option"
                class="mt-2"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-input
            label="Date"
            type="date"
            placeholder="Date"
            v-model="date"
          ></c-input>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button
        v-if="line_id.length > 0"
        color="primary"
        class="text-white"
        @click="show"
        style="float: right"
        >Show</c-button
      >
    </c-card-footer>
  </c-card>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      lines: [],
      distributors: [],
      line_id: [],
      distributor_id: [],
      checkAllLines: false,
      checkAllDistributors: false,
      date: new Date().toISOString().slice(0, 10),
    };
  },
  methods: {
    initialize() {
      axios
        .get("/api/lines-product-mapping/")
        .then((response) => {
          this.lines = response.data.data.lines;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post("/api/line-data-product-mapping/", { lines: this.line_id })
        .then((response) => {
          this.distributors = response.data.data.distributors;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = null;
      this.getLineData();
    },
    checkAllDistributor() {
      if (this.checkAllDistributors)
        this.distributor_id = this.distributors.map((item) => item.id);
      if (this.checkAllDistributors == false) this.distributor_id = null;
    },
    show() {
      let mappingFilter = {
        lines: this.line_id,
        distributors: this.distributor_id,
        date: this.date,
      };
      this.$emit("getSchedule", { mappingFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
