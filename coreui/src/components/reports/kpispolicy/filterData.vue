<template>
  <c-card>
    <c-card-header>Kpis Policy Report</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Line </template>
            <template #input>
              <input label="All" v-if="lines.length != 0" id="line" class="m-1" type="checkbox" v-model="checkAllLines"
                title="Check All lines" @change="checkAllLine" />
              <label for="line" v-if="lines.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                placeholder="Select Lines" class="mt-2" multiple @input="getLineData" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Role </template>
            <template #input>
              <input label="All" v-if="roles.length != 0" id="user" class="m-1" type="checkbox" v-model="checkAllroles"
                title="Check All roles" @change="checkAllRole" />
              <label for="user" v-if="roles.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="role" :options="roles" label="name" placeholder="Select Role" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Kpi </template>
            <template #input>
              <input label="All" v-if="kpis.length != 0" id="kpi" class="m-1" type="checkbox" v-model="checkAllKpis"
                title="Check All kpis" @change="checkAllKpi" />
              <label for="kpi" v-if="kpis.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="kpi_id" :options="kpis" label="name" :value="0" :reduce="(user) => user.id"
                placeholder="Select Kpi" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button v-if="line_id.length > 0" color="primary" class="text-white" @click="show"
        style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      kpi_id: [],
      line_id: [],
      roles: [],
      role: [],
      lines: [],
      kpis: [],
      checkAllLines: false,
      checkAllKpis: false,
      checkAllroles: false,
    };
  },
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: null,
          to: null,
          data: ['lines']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post(`/api/get-roles`, {
          lines: this.line_id,
        })
        .then((response) => {
          this.roles = response.data.data.roles;
          this.kpis = response.data.data.kpis;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = null;
      this.getLineData();
    },
    checkAllKpi() {
      if (this.checkAllKpis) this.kpi_id = this.kpis.map((item) => item.id);
      if (this.checkAllKpis == false) this.kpi_id = null;
    },
    checkAllRole() {
      if (this.checkAllroles) this.role = this.roles.map((item) => item);
      if (this.checkAllroles == false) this.role = null;
    },
    show() {
      let roles = [];
      let kpisFilter = {
        lines: this.line_id,
        kpis: this.kpi_id,
        role: this.role.map((item) => ({
          roleable_id: item.id.split("_")[0], //this.kpiRatio.role,
          roleable_type: item.id.split("_")[1],
          name: item.name,
        })),
      };
      this.$emit("getSchedule", { kpisFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>