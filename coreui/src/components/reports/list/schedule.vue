<template>
  <c-card>
    <c-card-body>
      <c-data-table
        id="print"
        ref="content"
        hover
        striped
        sorter
        tableFilter
        footer
        :items="accounts"
        :fields="fields"
        :items-per-page="1000"
        :active-page="1"
        :responsive="true"
        pagination
        thead-top
      >
        <template slot="thead-top">
          <td style="border-top: none"><strong>Total</strong></td>
          <td style="border-top: none" class="text-xs-right">
            {{ accounts.length }}
          </td>
          <td colspan="7" style="border-top: none" class="text-center">
            <h4>List Report</h4>
          </td>
        </template>
        <template #division="{ item }">
          <td :style="{ color: item.color }" style="font-weight: bold">
            {{ item.division }}
          </td>
        </template>
        <template #actions="{ item }">
          <td>
            <c-button
              color="success"
              class="btn-sm mt-2 mr-1"
              :to="{ name: 'EditAccount', params: { id: item.id } }"
              ><i class="cil-pencil"></i><c-icon name="cil-pencil"
            /></c-button>
          </td>
        </template>
        <template v-for="field in fields" v-slot:[field]="{ item }">
          <td :key="field">
            {{ item[field] ? item[field] : "" }}
          </td>
        </template>
      </c-data-table>
      <c-card-footer>
        <download
          @getPrint="print"
          @getxlsx="download"
          @getcsv="downloadCsv"
          @getpdf="createPDF"
          :data="accounts"
          :fields="fields"
          :name="name"
        />
      </c-card-footer>
    </c-card-body>
  </c-card>
</template>
<script>

import download from "./../../download-reports/download.vue";
import { Amiri } from "../../../assets/fonts/Amiri-Regular-normal";
import jsPDF from "jspdf";
import "jspdf-autotable";
export default {
  props: {
    accounts: {
      type: Array,
      required: true,
    },
  },
  components: {
    download,
  },
  data() {
    return {
      fields: [
        "id",
        "line",
        "division",
        "brick",
        "name",
        "acc_class",
        "account_type",
        "ucode",
        "doctor",
        "doc_class",
        "address",
        "speciality",
        "active_date",
        "actions"
      ],
      name: "List Report",
    };
  },
  methods: {
    download() {
      let filteredAccounts = Object.values(this.accounts);
      filteredAccounts.forEach((element) => {
        delete element["color"];
      });
      this.downloadXlsx(filteredAccounts, "List.xlsx");
      this.$emit("downloaded");
    },
    downloadCsv() {
      // let filteredAccounts = Object.values(this.accounts).map((account) => {
      //   return {
      //                 id : account.id,
      //                 line : account.line,
      //                 division : account.division,
      //                 brick : account.brick,
      //                 name : account.name,
      //                 account_type : account.account_type,
      //                 address : account.address,
      //                 tel : account.tel,
      //                 email : account.email,
      //                 doctor : account.doctor,
      //                 speciality : account.speciality,
      //                 active_date : account.active_date,
      //   };
      // })

      let filteredAccounts = Object.values(this.accounts);
      filteredAccounts.forEach((element) => {
        delete element["color"];
      });
      this.downloadXlsx(filteredAccounts, "List.csv");
      this.$emit("downloaded");
    },
    print() {
      this.$htmlToPaper("print");
    },
    createPDF() {
      let pdfName = "ListReport";
      var columns = [
        { title: "id", dataKey: "id" },
        { title: "Line", dataKey: "line" },
        { title: "Division", dataKey: "division" },
        { title: "Brick", dataKey: "brick" },
        { title: "Name", dataKey: "name" },
        { title: "Account Type", dataKey: "account_type" },
        { title: "Doctor", dataKey: "doctor" },
        { title: "Speciality", dataKey: "speciality" },
        { title: "Active Date", dataKey: "active_date" },
      ];
      const body = this.accounts;
      const doc = new jsPDF({ filters: ["ASCIIHexEncode"] });

      doc.addFileToVFS("Amiri-Regular.ttf", Amiri);
      doc.addFont("Amiri-Regular.ttf", "Amiri", "normal");
      doc.setFont("Amiri");
      doc.setFontSize(10);
      doc.autoTable({
        columns,
        body,
        margin: { top: 10 },
        showHead: "firstPage",
        styles: {
          lineColor: "#c7c7c7",
          lineWidth: 0,
          cellPadding: 2,
          font: "Amiri",
        },
      });
      doc.save(pdfName + ".pdf");
    },
  },
};
</script>
