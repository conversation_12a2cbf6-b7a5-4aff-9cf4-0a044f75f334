<template>
  <c-card>
    <c-card-header>Call Rate Report</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-input label="From" type="date" placeholder="From" v-model="from_date"></c-input>
        </div>

        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-input label="To" type="date" placeholder="To" v-model="to_date" @input="getAllData"></c-input>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Line </template>
            <template #input>
              <input label="All" v-if="lines.length != 0" id="line" class="m-1" type="checkbox" v-model="checkAllLines"
                title="Check All lines" @change="checkAllLine" />
              <label for="line" v-if="lines.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                placeholder="Select Line" class="mt-2" @input="getLineData" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8" v-if="line_id.length == 1">
          <c-form-group>
            <template #label> Positions </template>
            <template #input>
              <v-select v-model="position_id" :options="positions" label="name" :value="0"
                :reduce="(position) => position.id" placeholder="Select Position" class="mt-2" @input="getUsers()" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Employee </template>
            <template #input>
              <input label="All" v-if="users.length != 0" id="user" class="m-1" type="checkbox" v-model="checkAllUsers"
                title="Check All Users" @change="checkAllEmployees" />
              <label for="user" v-if="users.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="user_id" :options="users" label="fullname" :value="0" :reduce="(user) => user.id"
                placeholder="Select Employee" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8 mt-2">
          <c-form-group>
            <template #label> Shift </template>
            <template #input>
              <v-select title="Search for shift" v-model="shift_id" :options="shifts" label="name" :value="0"
                class="mt-2" :reduce="(shift) => shift.id"></v-select>
            </template>
          </c-form-group>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button v-if="line_id.length > 0" color="primary" class="text-white" @click="show"
        style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";

export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      division_id: [],
      user_id: [],
      line_id: [],
      shift_id: 2,
      divisions: [],
      lines: [],
      shifts: [],
      users: [],
      positions: [],
      position_id: null,
      checkAllDivisions: false,
      checkAllLines: false,
      checkAllUsers: false,
      from_date: moment().startOf('month').format("YYYY-MM-DD"),
      to_date: moment().endOf('month').format("YYYY-MM-DD"),
    };
  },
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: this.from_date,
          to: this.to_date,
          data: ['lines', 'shifts'] 
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.shifts = response.data.data.shifts;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getpositions() {
      axios
        .post("/api/get-positions", {
          lines: this.line_id,
          from: this.from_date,
          to: this.to_date,
        })
        .then((res) => {
          this.positions = res.data.data;
        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
    getLineData() {
      axios
        .post(`/api/get-lines-data-with-dates`, {
          lines: this.line_id,
          from: this.from_date,
          to: this.to_date,
          data : ['divisions', 'users']
        })
        .then((response) => {
          this.divisions = response.data.data.divisions;
          this.users = response.data.data.users;
          this.getpositions();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getAllData() {
      this.line_id = [];
      this.position_id = null;
      this.user_id = [];
      this.initialize();
    },
    getUsers() {
      axios
        .post("/api/calendar-users", {
          line_id: this.line_id[0],
          role_id: this.position_id.split("_")[0],
          role_type: this.position_id.split("_")[1],
          from: this.from_date,
          to: this.to_date,
        })
        .then((res) => {
          this.users = res.data.users;
        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) {
        this.line_id = null;
        this.position_id = null;
        this.user_id = null;
        this.division_id = null;
      }
      this.getLineData();
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = null;
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    show() {
      let visitFilter = {
        lines: this.line_id,
        divisions: this.division_id,
        users: this.user_id,
        position: this.position_id,
        shift: this.shift_id,
        fromDate: this.from_date,
        toDate: this.to_date,
      };
      this.$emit("getSchedule", { visitFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>