<template>
  <c-card>
    <c-card-header>Budget Report</c-card-header>
    <c-card-body>
      <c-tabs>
        <c-tab active>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Main
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label>
                      Line <span style="color: red">*</span></template
                    >
                    <template #input>
                      <v-select
                        v-model="line_id"
                        :options="lines"
                        label="name"
                        :value="0"
                        :reduce="(line) => line.id"
                        placeholder="Select Line"
                        class="mt-2"
                        @input="getLineData"
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label>
                      Filter By <span style="color: red">*</span></template
                    >
                    <template #input>
                      <v-select
                        v-model="filter"
                        :options="filters"
                        label="name"
                        :value="0"
                        :reduce="(line) => line.id"
                        placeholder="Select Filter"
                        class="mt-2"
                        @input="
                          filter == 1 ? (user_id = []) : (division_id = [])
                        "
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8" v-if="filter == 1">
                  <c-form-group>
                    <template #label> Division </template>
                    <template #input>
                      <input
                        label="All"
                        id="division"
                        v-if="divisions.length != 0"
                        class="m-1"
                        type="checkbox"
                        v-model="checkAllDivisions"
                        title="Check All Divisions"
                        @change="checkAllDivs"
                      />
                      <label
                        for="division"
                        v-if="divisions.length != 0"
                        style="font-weight: bold"
                        >All</label
                      >
                      <v-select
                        v-model="division_id"
                        :options="divisions"
                        label="name"
                        :value="0"
                        :reduce="(division) => division.id"
                        placeholder="Select Division"
                        class="mt-2"
                        multiple
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8" v-if="filter == 2">
                  <c-form-group>
                    <template #label> Employee </template>
                    <template #input>
                      <input
                        label="All"
                        v-if="users.length != 0"
                        id="user"
                        class="m-1"
                        type="checkbox"
                        v-model="checkAllUsers"
                        title="Check All Users"
                        @change="checkAllEmployees"
                      />
                      <label
                        for="user"
                        v-if="users.length != 0"
                        style="font-weight: bold"
                        >All</label
                      >
                      <v-select
                        v-model="user_id"
                        :options="users"
                        label="name"
                        :value="0"
                        :reduce="(user) => user.id"
                        placeholder="Select Employee"
                        class="mt-2"
                        multiple
                      />
                    </template>
                  </c-form-group>
                </div>
              </div>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Products </template>
                    <template #input>
                      <input
                        label="All"
                        v-if="products.length != 0"
                        id="product"
                        class="m-1"
                        type="checkbox"
                        v-model="checkAllProducts"
                        title="Check All Products"
                        @change="checkAllProduct"
                      />
                      <label
                        for="product"
                        v-if="products.length != 0"
                        style="font-weight: bold"
                        >All</label
                      >
                      <v-select
                        v-model="product_id"
                        :options="products"
                        label="name"
                        :value="0"
                        :reduce="(product) => product.id"
                        placeholder="Select Product"
                        class="mt-2"
                        multiple
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-input
                    label="From"
                    type="date"
                    placeholder="From"
                    v-model="from_date"
                  ></c-input>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-input
                    label="To"
                    type="date"
                    placeholder="To"
                    v-model="to_date"
                  ></c-input>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Types
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Request Type </template>
                    <template #input>
                      <input
                        label="All"
                        id="type"
                        v-if="types.length != 0"
                        class="m-1"
                        type="checkbox"
                        v-model="checkAllTypes"
                        title="Check All Types"
                        @change="checkAllType"
                      />
                      <label
                        for="type"
                        v-if="types.length != 0"
                        style="font-weight: bold"
                        >All</label
                      >
                      <v-select
                        v-model="type_id"
                        :options="types"
                        label="name"
                        :value="0"
                        :reduce="(type) => type.id"
                        placeholder="Select Budget Type"
                        class="mt-2"
                        multiple
                        @input="getSubTypes(type_id)"
                      />
                    </template>
                  </c-form-group>
                </div>
              </div>
              <c-card class="mt-2">
                <c-card-header style="background-color: #ebedef">
                  <strong>Sub Request Types</strong>
                </c-card-header>
                <c-card-body class="row">
                  <div
                    class="col-3"
                    style="padding-bottom: 0px; padding-top: 2px"
                    v-for="(sub, index) in subTypes"
                    :key="index"
                  >
                    <sub-type-input
                      :sub="sub"
                      :key="index"
                      @setNewSub="setValue"
                    />
                  </div>
                </c-card-body>
              </c-card>
            </c-card-body>
          </c-card>
        </c-tab>
      </c-tabs>
    </c-card-body>
    <c-card-footer>
      <c-button
        v-if="line_id != null"
        color="primary"
        class="text-white"
        @click="show"
        style="float: right"
        >Show</c-button
      >
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import SubTypeInput from "./../../budget/SubTypeInput.vue";
export default {
  components: {
    vSelect,
    SubTypeInput,
  },
  emits: ["getSchedule"],
  data() {
    return {
      division_id: [],
      product_id: [],
      type_id: [],
      subTypes: [],
      user_id: [],
      line_id: null,
      divisions: [],
      products: [],
      types: [],
      lines: [],
      users: [],
      filters: [
        { id: 1, name: "Division" },
        { id: 2, name: "Employee" },
      ],
      filter: 2,
      checkAllDivisions: false,
      sub_type_id: [],
      checkAllTypes: true,
      checkAllSubTypes: false,
      checkAllUsers: false,
      checkAllProducts: false,
      from_date: new Date().toISOString().slice(0, 10),
      to_date: new Date().toISOString().slice(0, 10),
    };
  },
  methods: {
    setValue({ checked, subType }) {
      if (checked) {
        this.sub_type_id.push(subType);
      } else {
        this.sub_type_id.splice(this.sub_type_id.indexOf(subType), 1);
      }
    },
    getSubTypes(type_id) {
      axios
        .post(`/api/budget/subtypes`, {
          types: type_id,
        })
        .then((response) => {
          this.subTypes = response.data.data;
          this.sub_type_id = [];
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    initialize() {
      axios
        .get("/api/budget")
        .then((response) => {
          this.lines = response.data.data.lines;
          this.types = response.data.data.types;
          this.type_id = this.types.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .get(`/api/get-line-request-data/${this.line_id}`)
        .then((response) => {
          this.divisions = response.data.data.divisions;
          this.users = response.data.data.users;
          this.products = response.data.data.products;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllType() {
      if (this.checkAllTypes) {
        this.type_id = this.types.map((item) => item.id);
        this.getSubTypes(this.type_id);
      }
      if (this.checkAllTypes == false) {
        this.type_id = null;
        this.sub_type_id = null;
      }
    },
    checkAllProduct() {
      if (this.checkAllProducts)
        this.product_id = this.products.map((item) => item.id);
      if (this.checkAllProducts == false) this.product_id = null;
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = null;
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    show() {
      let budgetFilter = {
        line: this.line_id,
        filter: this.filter,
        divisions: this.division_id,
        types: this.type_id,
        subTypes: this.sub_type_id,
        products: this.product_id,
        users: this.user_id,
        fromDate: this.from_date,
        toDate: this.to_date,
      };
      this.$emit("getSchedule", { budgetFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>