<template>
  <c-card>
    <c-card-header>Doctor Tracing Report</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Line <span style="color: red">*</span></template>
            <template #input>
              <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                placeholder="Select Line" class="mt-2" @input="getLineData" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Division </template>
            <template #input>
              <input label="All" id="division" v-if="divisions.length != 0" class="m-1" type="checkbox"
                v-model="checkAllDivisions" title="Check All Divisions" @change="checkAllDivs" />
              <label for="division" v-if="divisions.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="division_id" :options="divisions" label="name" :value="0"
                :reduce="(division) => division.id" placeholder="Select Divisions" class="mt-2" multiple
                @input="getDoctorsData" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Doctors </template>
            <template #input>
              <input label="All" id="doctor" v-if="doctors.length != 0" class="m-1" type="checkbox"
                v-model="checkAllDoctors" title="Check All Doctors" @change="checkAllDoctor" />
              <label for="doctor" v-if="doctors.length != 0" style="font-weight: bold">All</label>
              <strong v-if="checkAllDoctors">
                (All Doctors Are Selected)
              </strong>
              <v-select v-model="doctor_id" :options="doctors" label="doctor" placeholder="Select Doctors"
                :reduce="(doctor) => doctor.doctor_id" class="mt-2" multiple :filterable="false" @search="onSearch"
                :disabled="checkAllDoctors" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Specialities </template>
            <template #input>
              <input label="All" id="speciality" v-if="specialities.length != 0" class="m-1" type="checkbox"
                v-model="checkAllSpecialities" title="Check All Specialities" @change="checkAllSpeciality" />
              <label for="speciality" v-if="specialities.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="speciality_id" :options="specialities" label="name" placeholder="Select Speciality"
                :reduce="(speciality) => speciality.id" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Account Type </template>
            <template #input>
              <input label="All" id="type" v-if="types.length != 0" class="m-1" type="checkbox"
                v-model="allAccountTypes" title="Check All Types" @change="checkAllAccounts" />
              <label for="type" v-if="types.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="type_id" :options="types" label="name" :value="0" :reduce="(type) => type.id"
                placeholder="Select Account Type" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-input label="Date" type="date" placeholder="Date" v-model="date"></c-input>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button v-if="line_id != null" color="primary" class="text-white" @click="show"
        style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import _ from "../../../mixins/debounce";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      speciality_id: [],
      doctor_id: [],
      type_id: [],
      line_id: null,
      divisions: [],
      division_id: [],
      specialities: [],
      types: [],
      lines: [],
      doctors: [],
      search: "",
      date: null,
      filter: null,
      checkAllDivisions: false,
      allAccountTypes: true,
      checkAllSpecialities: true,
      checkAllDoctors: false,
    };
  },
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: null,
          to: null,
          data: ['lines', 'accountTypes']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.types = response.data.data.accountTypes;
          this.type_id = this.types.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    onSearch(search, loading) {
      if (search.length) {
        loading(true);
        this.search = search;
        this.find(loading, this);
      }
    },
    find: _.debounce((loading, vm) => {
      vm.getDoctors().then(() => loading(false));
    }, 500),

    getLineData() {
      axios
        .get(`/api/get-line-data-reports/${this.line_id}`)
        .then((response) => {
          this.divisions = response.data.divisions;
          this.specialities = response.data.specialities;
          this.speciality_id = this.specialities.map((item) => item.id);
          this.getDoctorsData();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getDoctorsData() {
      axios
        .post(`/api/get-doctors`, {
          line: this.line_id,
          divisions: this.division_id,
        })
        .then((response) => {
          this.doctors = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = null;
      this.getDoctorsData();
    },
    checkAllAccounts() {
      if (this.allAccountTypes)
        this.type_id = this.types.map((item) => item.id);
      if (this.allAccountTypes == false) this.type_id = null;
    },
    checkAllSpeciality() {
      if (this.checkAllSpecialities)
        this.speciality_id = this.specialities.map((item) => item.id);
      if (this.checkAllSpecialities == false) this.speciality_id = null;
    },

    checkAllDoctor() {
      if (this.checkAllDoctors) this.checkAllDoctors = true;
      this.doctor_id = null;
      // this.doctor_id = this.doctors.map((item) => item.id);
      if (this.checkAllDoctors == false) this.doctor_id = null;
    },
    getDoctors() {
      return axios
        .post("/api/doctors/index?page=" + this.page, {
          query: this.search,
        })
        .then((res) => {
          this.doctors = res.data.data.data;
        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
    show() {
      let visitFilter = {
        line: this.line_id,
        filter: this.filter,
        types: this.type_id,
        specialities: this.speciality_id,
        divisions: this.division_id,
        doctors: this.doctor_id,
        date: this.date,
      };
      this.$emit("getSchedule", { visitFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>