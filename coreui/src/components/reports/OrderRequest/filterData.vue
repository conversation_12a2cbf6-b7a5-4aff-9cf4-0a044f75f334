<template>
  <c-card>
    <c-card-header>Order Request Report</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Bricks </template>
            <template #input>
              <input label="All" v-if="bricks.length != 0" id="user" class="m-1" type="checkbox"
                v-model="checkAllBricks" title="Check All Bricks" @change="checkAllBrick" />
              <label for="user" v-if="bricks.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="brick_id" :options="bricks" label="name" :value="0" :reduce="(brick) => brick.id"
                placeholder="Select Brick" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-input label="From" type="date" placeholder="From" v-model="from_date"></c-input>
        </div>

        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-input label="To" type="date" placeholder="To" v-model="to_date"></c-input>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" class="text-white" @click="show" style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";

export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      bricks: [],
      brick_id: [],
      checkAllBricks: false,
      from_date: moment().startOf('month').format("YYYY-MM-DD"),
      to_date: moment().endOf('month').format("YYYY-MM-DD"),
    };
  },
  methods: {
    initialize() {
      axios
        .get("/api/order-request-bricks")
        .then((response) => {
          this.bricks = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllBrick() {
      if (this.checkAllBricks) this.brick_id = this.bricks.map((item) => item.id);
      if (this.checkAllBricks === false) this.brick_id = [];
    },
    show() {
      let orderFilter = {
        bricks: this.brick_id,
        fromDate: this.from_date,
        toDate: this.to_date,
      };
      this.$emit("getSchedule", { orderFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
