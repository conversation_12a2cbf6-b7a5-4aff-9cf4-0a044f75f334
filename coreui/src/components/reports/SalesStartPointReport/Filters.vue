<template>
  <c-col col="12" lg="12">
    <c-card>
      <c-card-header> Start Point Report</c-card-header>
      <c-card-body>

        <c-card>
          <c-card-body>
            <div class="form-row form-group">
              <div class="col-4">
                <c-form-group>
                  <template #label>
                    <strong>Line</strong>
                  </template>
                  <template #input>
                    <input label="All" v-if="lines.length != 0" class="m-1" id="lines" type="checkbox"
                      v-model="checkAllLines" title="Check All Lines" @change="checkAllLine" />
                    <label for="lines" v-if="lines.length != 0" style="font-weight: bold">All</label>
                    <v-select v-model="line_id" :options="lines" label="name" :value="0" required
                      :reduce="(line) => line.id" placeholder="Select option" class="mt-2" @input="getLineData"
                      multiple />
                  </template>
                </c-form-group>
              </div>
              <div class="col-4">
                <c-form-group>
                  <template #label>
                    <strong>Filter By</strong>
                  </template>
                  <template #input>

                    <v-select v-model="filter" :options="filters" label="name" :value="0" :reduce="(line) => line.id"
                      placeholder="Select Filter" class="mt-2" @input="checkFilter(filter)" />
                  </template>
                </c-form-group>
              </div>
              <div class="col-lg-4 col-md-4 col-sm-8" v-if="filter == 1">
                <c-form-group>
                  <template #label>
                    <strong>Division</strong>
                  </template>
                  <template #input>
                    <input label="All" v-if="divisions.length != 0" class="m-1" type="checkbox"
                      v-model="checkAllDivisions" title="Check All Divisions" @change="checkAllDivision" />
                    <label v-if="divisions.length != 0" style="font-weight: bold">All</label>
                    <v-select v-model="division_ids" :options="divisions" label="name" :value="0"
                      :reduce="(division) => division.id" placeholder="Select Divisions" class="mt-2" multiple />
                  </template>
                </c-form-group>
              </div>
              <div class="col-lg-4 col-md-4 col-sm-8" v-if="filter == 2">
                <c-form-group>
                  <template #label>
                    <strong>Employee</strong>
                  </template>
                  <template #input>
                    <input label="All" v-if="users.length != 0" id="user" class="m-1" type="checkbox"
                      v-model="checkAllUsers" title="Check All Users" @change="checkAllEmployees" />
                    <label for="user" v-if="users.length != 0" style="font-weight: bold">All</label>
                    <v-select v-model="user_id" :options="users" label="fullname" :value="0" required
                      :reduce="(user) => user.id" placeholder="Select Employee" class="mt-2" multiple />
                  </template>
                </c-form-group>
              </div>
            </div>
            <div class="form-row form-group">
              <div class="col-lg-3 col-md-3 col-sm-8">
                <c-input label="Date" type="date" placeholder="Date" v-model="from_date"></c-input>
              </div>
            </div>
          </c-card-body>
        </c-card>

      </c-card-body>
      <c-card-footer>
        <c-button color="primary" class="text-white" @click="show" v-if="line_id != null" style="float: right">Show</c-button>
      </c-card-footer>
    </c-card>
  </c-col>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";

export default {
  components: {
    vSelect,
  },
  data() {
    return {
      line_id: [],
      lines: [],
      divisions: [],
      users: [],
      user_id: [],
      division_ids: [],
      from_date: moment().startOf('month').format("YYYY-MM-DD"),
      filters: [
        { id: 1, name: "Division" },
        { id: 2, name: "Employee" },
      ],
      filter: 1,
      checkAllUsers: false,
      checkAllLines: false,
      checkAllDivisions: false,
    };
  },
  emits: ["getList"],
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: null,
          to: null,
          data: ['lines']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post(`/api/get-lines-data-with-dates`, {
          lines: this.line_id,
          from: null,
          to: null,
          data: ['divisions', 'users']
        })
        .then((response) => {
          this.divisions = response.data.data.divisions;
          this.users = response.data.data.users;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = null;
      this.getLineData();
    },
    checkAllDivision() {
      if (this.checkAllDivisions)
        this.division_ids = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) {
        this.division_ids = null;
      };
    },

    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    checkFilter(filter) {
      if (filter == 1) {
        this.user_id = [];
      } else {
        this.division_ids = [];
      }
    },
    show() {
      let listFilter = {
        line: this.line_id,
        divisions: this.division_ids,
        users: this.user_id,
        filter: this.filter,
        fromDate: this.from_date,
      };
      this.$emit("getList", { listFilter });
    },

  },
  created() {
    this.initialize();
  },
};
</script>
