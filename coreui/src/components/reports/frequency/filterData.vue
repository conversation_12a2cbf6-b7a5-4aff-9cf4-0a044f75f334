<template>
  <c-card>
    <c-card-header>Frequency Report</c-card-header>
    <c-card-body>
      <c-tabs>
        <c-tab active>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-description" /> Line &
            Division
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-form-group>
                    <template #label>
                      <strong>From Date</strong>
                    </template>
                    <template #input>
                      <c-input type="date" class="mt-2" placeholder="Date" v-model="fromDate"></c-input>
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-form-group>
                    <template #label>
                      <strong>To Date</strong>
                    </template>
                    <template #input>
                      <c-input type="date" class="mt-2" placeholder="Date" v-model="toDate"
                        @input="getAllData"></c-input>
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-form-group>
                    <template #label> Line </template>
                    <template #input>
                      <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                        placeholder="Select Line" class="mt-2" @input="getDataWithLine" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-form-group>
                    <template #label>
                      Filter By <span style="color: red">*</span>
                    </template>
                    <template #input>
                      <v-select v-model="filter" :options="filters" label="name" :value="0" :reduce="(line) => line.id"
                        placeholder="Select Filter" class="mt-2" @input="
                          filter == 1 ? (user_ids = []) : (division_ids = [])
                          " />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8" v-if="filter == 1">
                  <c-form-group>
                    <template #label> Division </template>
                    <template #input>
                      <input label="All" v-if="divisions.length != 0" class="m-1" id="divisions" type="checkbox"
                        v-model="checkAllDivisions" title="Check All Divisions" @change="checkAllDivs" />
                      <label for="divisions" v-if="divisions.length != 0" style="font-weight: bold">All</label>
                      <v-select :disabled="user_ids.length > 0" v-model="division_ids" :options="divisions" label="name"
                        :value="0" :reduce="(division) => division.id" placeholder="Select Division" class="mt-2"
                        autoscroll multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8" v-if="filter == 2">
                  <c-form-group>
                    <template #label> Employee </template>
                    <template #input>
                      <input label="All" v-if="users.length != 0" class="m-1" type="checkbox" id="users"
                        v-model="checkAllEmployees" title="Check All Employees" @change="checkAllUsers" />
                      <label for="users" v-if="users.length != 0" style="font-weight: bold">All</label>
                      <v-select :disabled="division_ids.length > 0" v-model="user_ids" :options="users" label="fullname"
                        :value="0" :reduce="(user) => user.id" placeholder="Select Employee" class="mt-2" autoscroll
                        multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Frequency Type </template>
                    <template #input>
                      <v-select v-model="freqTypeId" :options="freqTypes" label="name" :value="0"
                        :disabled="freqTypeId != null" :reduce="(freqType) => freqType.id"
                        placeholder="Select Frequency Type" class="mt-2" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-form-group>
                    <template #label> View </template>
                    <template #input>
                      <v-select v-model="shape" :options="shapes" label="name" :value="0" :reduce="(shape) => shape.id"
                        placeholder="Select Shape" class="mt-2" @input="getData" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8" v-if="shape == 1 && freqTypeId != 2">
                  <c-form-group>
                    <template #label>
                      <span v-if="freqTypeId == 1 || freqTypeId == 3">Class</span>
                    </template>
                    <template #input>
                      <input label="All" id="types" v-if="types.length != 0 && freqTypeId == 1" class="m-1"
                        type="checkbox" v-model="checkAllClasses" title="Check All Classes" @change="checkAllClass" />
                      <label for="types" v-if="accountTypes.length != 0" style="font-weight: bold">All</label>
                      <v-select v-if="freqTypeId == 1" v-model="class_id" :options="types" label="name" :value="0"
                        :reduce="(type) => type.id" placeholder="Select type" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Account Types &
            Specialities
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Account Type </template>
                    <template #input>
                      <input label="All" id="types" v-if="accountTypes.length != 0" class="m-1" type="checkbox"
                        v-model="checkAllAccountTypes" title="Check All Account Types" @change="checkAllAccounts" />
                      <label for="types" v-if="accountTypes.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="accountType_ids" :options="accountTypes" label="name" :value="0"
                        :reduce="(type) => type.id" placeholder="Select Account Type" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Speciality </template>
                    <template #input>
                      <input label="All" v-if="specialities.length != 0" class="m-1" id="specialities" type="checkbox"
                        v-model="checkAllSpecialities" title="Check All Specialities" @change="checkAllSpech" />
                      <label v-if="specialities.length != 0" style="font-weight: bold" for="specialities">All</label>
                      <v-select v-model="speciality_ids" :options="specialities" label="name" :value="0"
                        :reduce="(speciality) => speciality.id" placeholder="Select Speciality" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Shift </template>
                    <template #input>
                      <input label="All" v-if="shifts.length != 0" id="shift" class="m-1" type="checkbox"
                        v-model="checkAllShifts" title="Check All Shifts" @change="checkAllShift" />
                      <label for="shift" v-if="shifts.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="shift_id" :options="shifts" label="name" placeholder="Select Shift"
                        :reduce="(shift) => shift.id" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
      </c-tabs>
    </c-card-body>
    <c-card-footer v-if="line_id != null">
      <c-button color="primary" class="text-white" @click="show" style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";

export default {
  components: {
    vSelect,
  },
  emits: ["getList"],
  data() {
    return {
      lines: [],
      line_id: [],
      specialities: [],
      accountTypes: [],
      divisions: [],
      users: [],
      filters: [
        { id: 1, name: "Division" },
        { id: 2, name: "Employee" },
      ],
      filter: 2,
      shapes: [
        { id: 1, name: "Classic" },
        { id: 2, name: "Advanced" },
        { id: 3, name: "Details" },
      ],
      shape: 2,
      types: [],
      class_id: [],
      user_ids: [],
      freqTypes: [],
      freqTypeId: null,
      speciality_ids: [],
      accountType_ids: [],
      division_ids: [],
      shift_id: [],
      shifts: [],
      checkAllShifts: true,
      checkAllAccountTypes: true,
      checkAllClasses: true,
      checkAllEmployees: false,
      checkAllSpecialities: true,
      checkAllDivisions: false,
      fromDate: moment().startOf('month').format("YYYY-MM-DD"),
      toDate: moment().endOf('month').format("YYYY-MM-DD"),
    };
  },
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: this.fromDate,
          to: this.toDate,
          data: ['lines', 'shifts', 'accountTypes', 'freqTypes']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.shifts = response.data.data.shifts;
          this.freqTypes = response.data.data.freqTypes;
          this.freqTypeId = this.freqTypes[0].id;
          this.accountTypes = response.data.data.accountTypes;
          this.accountType_ids = this.accountTypes.map((item) => item.id);
          this.shift_id = this.shifts.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getData() {
      axios
        .post(`/api/classic-view-data`, {
          line: this.line_id,
          freqTypeId: this.freqTypeId,
        })
        .then((response) => {
          this.types = response.data.data;
          this.class_id = this.types.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getDataWithLine() {
      axios
        .post(`/api/get-lines-data-with-dates`, {
          lines: [this.line_id],
          from: this.fromDate,
          to: this.toDate,
          data: ['divisions', 'users', 'specialities']
        })
        .then((response) => {
          this.specialities = response.data.data.specialities;
          this.divisions = response.data.data.divisions;
          this.users = response.data.data.users;
          this.speciality_ids = this.specialities.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getAllData() {
      this.line_id = [];
      this.user_id = [];
      this.division_id = [];
      this.speciality_id = [];
      this.initialize();
    },
    checkAllAccounts() {
      if (this.checkAllAccountTypes)
        this.accountType_ids = this.accountTypes.map((item) => item.id);
      if (this.checkAllAccountTypes == false) this.accountType_ids = null;
    },
    checkAllClass() {
      if (this.checkAllClasses)
        this.class_id = this.types.map((item) => item.id);
      if (this.checkAllClasses == false) this.class_id = null;
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_ids = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_ids = null;
    },
    checkAllSpech() {
      if (this.checkAllSpecialities)
        this.speciality_ids = this.specialities.map((item) => item.id);
      if (this.checkAllSpecialities == false) this.speciality_ids = null;
    },
    checkAllUsers() {
      if (this.checkAllEmployees)
        this.user_ids = this.users.map((item) => item.id);
      if (this.checkAllEmployees == false) this.user_ids = null;
    },
    checkAllShift() {
      if (this.checkAllShifts)
        this.shift_id = this.shifts.map((item) => item.id);
      if (this.checkAllShifts == false) this.shift_id = null;
    },
    show() {
      let listFilter = {
        line: this.line_id,
        divisions: this.division_ids,
        users: this.user_ids,
        filter: this.filter,
        shape: this.shape,
        classes: this.class_id,
        types: this.accountType_ids,
        shifts: this.shift_id,
        specialities: this.speciality_ids,
        fromDate: this.fromDate,
        toDate: this.toDate,
        freqType: this.freqTypeId,
      };
      this.$emit("getSchedule", { listFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>