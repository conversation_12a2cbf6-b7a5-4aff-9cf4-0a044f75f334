<template>
  <c-card>
    <c-card-header>Customer Visits Report</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Line <span style="color: red">*</span></template>
            <template #input>
              <input label="All" v-if="lines.length != 0" id="line" class="m-1" type="checkbox" v-model="checkAllLines"
                title="Check All lines" @change="checkAllLine" />
              <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                placeholder="Select Line" class="mt-2" multiple @input="getLineData" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Division </template>
            <template #input>
              <input label="All" id="division" v-if="divisions.length != 0" class="m-1" type="checkbox"
                v-model="checkAllDivisions" title="Check All Divisions" @change="checkAllDivs" />
              <label for="division" v-if="divisions.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="division_id" :options="divisions" label="name" :value="0"
                :reduce="(division) => division.id" placeholder="Select Divisions" class="mt-2" multiple
                 />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Specialities </template>
            <template #input>
              <input label="All" id="speciality" v-if="specialities.length != 0" class="m-1" type="checkbox"
                v-model="checkAllSpecialities" title="Check All Specialities" @change="checkAllSpeciality" />
              <label for="speciality" v-if="specialities.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="speciality_id" :options="specialities" label="name" placeholder="Select Speciality"
                :reduce="(speciality) => speciality.id" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Account Type </template>
            <template #input>
              <input label="All" id="type" v-if="types.length != 0" class="m-1" type="checkbox"
                v-model="allAccountTypes" title="Check All Types" @change="checkAllAccounts" />
              <label for="type" v-if="types.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="type_id" :options="types" label="name" :value="0" :reduce="(type) => type.id"
                placeholder="Select Account Type" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Approval Types </template>
            <template #input>
              <v-select v-model="approval_id" :options="approvals" label="name" :value="0"
                :reduce="(approval) => approval.value" placeholder="Select Approval Types" class="mt-2" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-input label="From" type="date" placeholder="Date" v-model="from_date"></c-input>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-input label="To" type="date" placeholder="Date" v-model="to_date"></c-input>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button v-if="line_id != null" color="primary" class="text-white" @click="show"
        style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";
import _ from "../../../mixins/debounce";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      speciality_id: [],
      doctor_id: [],
      type_id: [],
      line_id: null,
      divisions: [],
      division_id: [],
      specialities: [],
      types: [],
      lines: [],
      doctors: [],
      approvals: [
        { name: "Pending", value: 1 },
        { name: "Approved", value: 2 },
        { name: "Disapproved", value: 3 },
        { name: "Pending & Approved", value: 4 },
        { name: "Total", value: 5 },
      ],
      approval_id: 5,
      from_date: moment().startOf("month").format("YYYY-MM-DD"),
      to_date: moment().endOf("month").format("YYYY-MM-DD"),
      filter: null,
      checkAllDivisions: false,
      allAccountTypes: true,
      checkAllSpecialities: true,
      checkAllDoctors: false,
      checkAllLines: false,
      search: '',
      offset: 0,
      limit: 10,
      page: 1,
      total: 0,
      totalData: 0,
    };
  },
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: null,
          to: null,
          data: ['lines', 'accountTypes']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.types = response.data.data.accountTypes;
          this.type_id = this.types.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .get(`/api/get-line-data-reports/${this.line_id}`)
        .then((response) => {
          this.divisions = response.data.divisions;
          this.specialities = response.data.specialities;
          this.speciality_id = this.specialities.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = null;
      this.getLineData();
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = null;
    },
    checkAllAccounts() {
      if (this.allAccountTypes)
        this.type_id = this.types.map((item) => item.id);
      if (this.allAccountTypes == false) this.type_id = null;
    },
    checkAllSpeciality() {
      if (this.checkAllSpecialities)
        this.speciality_id = this.specialities.map((item) => item.id);
      if (this.checkAllSpecialities == false) this.speciality_id = null;
    },

    show() {
      let visitFilter = {
        line: this.line_id,
        filter: this.filter,
        types: this.type_id,
        specialities: this.speciality_id,
        divisions: this.division_id,
        // doctors: this.doctor_id,
        approval: this.approval_id,
        from: this.from_date,
        to: this.to_date,
      };
      this.$emit("getSchedule", { visitFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>