<template>
  <div>
    <c-card v-if="structure.length != 0">
      <c-card-header>
        <h3 class="text-center">
          Structure Report for {{ lineData.name }} at
          {{ format_date(lineData.created_at) }} to: {{ acceptedFilter.date }}
        </h3>
      </c-card-header>
      <c-card-body ref="structure">
        <c-data-table id="print" hover striped footer sorter :items="structure" :fields="fields" :items-per-page="1000"
          :active-page="1" :responsive="true" pagination thead-top>
          <template slot="thead-top">
            <td style="border-top: none"><strong>Total</strong></td>
            <td style="border-top: none" class="text-xs-right">
              {{ structure.length }}
            </td>
          </template>
          <template #division="{ item }">
            <td :style="{
              color: item.color,
              paddingLeft: item.padding + 'rem',
            }" style="font-weight: bold">
              {{ item.division }}
            </td>
          </template>
          <template #brick_name="{ item }">
            <td style="font-weight: bold; color: brown">
              {{ item.brick_name }}
            </td>
          </template>
          <template #brick_id="{ item }">
            <td style="font-weight: bold; color: brown">
              {{ item.brick_id }}
            </td>
          </template>
          <template #employee="{ item }">
            <td v-if="item.is_double == true" style="font-weight: bold; background-color: brown; color: white">
              {{ item.employee }}
            </td>
            <td v-if="item.is_double == false" :style="{
              color: item.color,
              paddingLeft: item.padding + 'rem',
            }" style="font-weight: bold">
              {{ item.employee }}
            </td>
          </template>
          <template #emp_id="{ item }">
            <td :style="{
              color: item.color,
            }" style="font-weight: bold">
              {{ item.emp_id }}
            </td>
          </template>
          <template #code="{ item }">
            <td :style="{
              color: item.color,
            }" style="font-weight: bold">
              {{ item.code }}
            </td>
          </template>
          <template #hiring_from="{ item }">
            <td :style="{
              color: item.color,
            }" style="font-weight: bold">
              {{ item.hiring_from }}
            </td>
          </template>
          <template #status="{ item }">
            <td>
              <c-badge class="text-white" style="font-weight: bold; font-size: 15px" :color="getBadge(item.status)">{{
                item.status }}</c-badge>
            </td>
          </template>
        </c-data-table>
      </c-card-body>
      <c-card-footer>
        <download @getPrint="print" @getxlsx="download" @getpdf="createPDF" @getcsv="downloadCsv" :name="name"
          :fields="fields" />
      </c-card-footer>
    </c-card>
    <c-card v-if="bricks.length > 0">
      <c-card-body>
        <c-button v-if="bricks.length > 0" class="text-white"
          style="float: right; background-color: #800080; font-weight: bold" @click="showBricks = !showBricks">
          UnLinked Bricks {{ bricks.length }}</c-button>
        <c-data-table v-if="showBricks" id="printBrick" hover striped footer sorter :items="bricks"
          :fields="bricks_fields" :items-per-page="1000" :active-page="1" :responsive="true" pagination thead-top>
          <template slot="thead-top">
            <td style="border-top: none"><strong>Total</strong></td>
            <td style="border-top: none" class="text-xs-right">
              {{ bricks.length }}
            </td>
          </template>
          <template #name="{ item }">
            <td>
              <c-badge class="text-white" style="
                  font-weight: bold;
                  font-size: 15px;
                  background-color: #800080;
                ">{{ item.name }}</c-badge>
            </td>
          </template>
        </c-data-table>
        <c-card-footer v-if="showBricks">
          <!-- <download
            @getPrint="printBrick"
            @getxlsx="downloadBricksData"
            @getpdf="createPDFBricks"
            @getcsv="downloadBricksCsv"
            :name="name"
            :fields="fields"
          /> -->
        </c-card-footer>
      </c-card-body>
    </c-card>
  </div>
</template>

<script>
import moment from "moment";
import download from "../../download-reports/download.vue";
import jsPDF from "jspdf";
import "jspdf-autotable";
import * as XLSX from "xlsx";
import { capitalize } from "../../../filters";
export default {
  components: {
    download,
  },
  props: {
    acceptedFilter: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      structure: [],
      bricks: [],
      divisionUsers: [],
      lineData: [],
      showBricks: false,
      fields: [],
      dates: [],
      bricks_fields: [],
      name: "Structure Report",
      secondName: "unLinked Brick Report",
    };
  },
  methods: {
    getBadge(status) {
      return status === "Active" || status === "active"
        ? "success"
        : status === "Inactive"
          ? "secondary"
          : status === "Pending"
            ? "warning"
            : status === "Banned"
              ? "danger"
              : "primary";
    },
    format_date: function (value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    initialize() {
      if (!this.acceptedFilter.line) {
        return;
      }
      let structure = {
        date: this.acceptedFilter.date,
        checked: this.acceptedFilter.checked,
        filter: this.acceptedFilter.filter,
        lines: this.acceptedFilter.line,
        divisions: this.acceptedFilter.division ? [this.acceptedFilter.division] : [],
        users: this.acceptedFilter.user ? [this.acceptedFilter.user] : [],
      };
      axios
        .post(`/api/structure-report/${this.acceptedFilter.line}`, {
          structure,
        })
        .then((response) => {
          this.structure = response.data.divisions;
          if (
            this.acceptedFilter.checked == 1 &&
            this.acceptedFilter.filter == 1
          ) {
            this.bricks = response.data.bricks;
            this.bricks_fields =
              this.bricks.length > 0 ? Object.keys(this.bricks[0]) : [];
          }
          this.fields = response.data.fields;
          this.dates = response.data.dates;
          this.lineData = response.data.line_data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    download() {
      this.downloadStyledExcel(
        this.dates,
        this.structure,
        this.fields,
        'Structure Report',
        'System Structure:');
    },
    downloadCsv() {
      this.downloadXlsx(this.structure, "Structure Report.csv");
      this.$emit("downloaded");
    },
    print() {
      this.$htmlToPaper("print");
    },
    createPDF() {
      let pdfName = "structureReport";
      const columns = this.fields.map((field) => {
        return {
          title: capitalize(field),
          dataKey: field,
        };
      });
      var doc = new jsPDF();
      doc.autoTable(columns, this.structure, {
        margin: { top: 10 },
        showHead: "firstPage",
      });
      doc.save(pdfName + ".pdf");
    },
    // print() {
    //   this.$htmlToPaper("printBrick");
    // },
    downloadBricksData() {
      this.downloadXlsx(this.bricks, "Unlinked Bricks.xlsx");
      this.$emit("downloaded");
    },
    downloadBricksCsv() {
      this.downloadXlsx(this.bricks, "Unlinked Bricks.csv");
      this.$emit("downloaded");
    },
    createBricksPDF() {
      let pdfName = this.secondName;
      const columns = this.bricks_fields.map((field) => {
        return {
          title: capitalize(field),
          dataKey: field,
        };
      });
      const body = this.bricks;
      const doc = new jsPDF({ filters: ["ASCIIHexEncode"] });

      doc.addFileToVFS("Amiri-Regular.ttf", Amiri);
      doc.addFont("Amiri-Regular.ttf", "Amiri", "normal");
      doc.setFont("Amiri");
      doc.setFontSize(10);
      doc.autoTable({
        columns,
        body,
        margin: { top: 10 },
        showHead: "firstPage",
        styles: {
          lineColor: "#c7c7c7",
          lineWidth: 0,
          cellPadding: 2,
          font: "Amiri",
        },
      });
      doc.save(pdfName + ".pdf");
    },
  },

  watch: {
    acceptedFilter() {
      this.initialize();
    },
  },
  created() {
    this.initialize();
  },
};
</script>