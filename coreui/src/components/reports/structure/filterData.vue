<template>
  <c-card>
    <c-card-header>Structure Report</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Viewer</strong>
            </template>
            <template #input>
              <v-select
                placeholder="Select View"
                v-model="view"
                :options="['Diagram','Default', 'Raw']"
                :value="0"
                class="mt-2"
              ></v-select>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Line</strong>
            </template>
            <template #input>
              <input
                label="All"
                v-if="lines.length != 0"
                id="line"
                class="m-1"
                type="checkbox"
                v-model="checkAllLines"
                title="Check All lines"
                @change="checkAllLine"
              />
              <label
                for="line"
                v-if="lines.length != 0"
                style="font-weight: bold"
                >All</label
              >
              <v-select
                placeholder="Search for Line"
                v-model="line_id"
                :options="lines"
                label="name"
                :value="0"
                class="mt-2"
                :reduce="(line) => line.id"
                multiple
                @input="getLineData"
              ></v-select>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8" v-if="!isDiagram">
          <c-form-group>
            <template #label>
              Filter By <span style="color: red">*</span>
            </template>
            <template #input>
              <v-select
                v-model="filter"
                :options="filters"
                label="name"
                :value="0"
                :reduce="(line) => line.id"
                placeholder="Select Filter"
                class="mt-2"
                @input="filter == 1 ? (user_id = null) : (division_id = null)"
              />
            </template>
          </c-form-group>
        </div>
        <div
          class="col-lg-4 col-md-4 col-sm-8"
          v-if="filter == 1 && !isDiagram"
        >
          <c-form-group>
            <template #label>
              <strong>Division</strong>
            </template>
            <template #input>
              <v-select
                placeholder="Search for Division"
                v-model="division_id"
                :options="divisions"
                label="name"
                :value="0"
                class="mt-2"
                :reduce="(division) => division.id"
              ></v-select>
            </template>
          </c-form-group>
        </div>
        <div
          class="col-lg-4 col-md-4 col-sm-8"
          v-if="filter == 2 && !isDiagram"
        >
          <c-form-group>
            <template #label> Employee </template>
            <template #input>
              <v-select
                v-model="user_id"
                :options="users"
                label="fullname"
                :value="0"
                :reduce="(user) => user.id"
                placeholder="Search for Employee"
                class="mt-2"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label>
              <strong>Date</strong>
            </template>
            <template #input>
              <c-input
                type="date"
                class="mt-2"
                placeholder="Date"
                v-model="date"
              ></c-input>
            </template>
          </c-form-group>
        </div>
        <div
          style="padding-top: 45px; padding-left: 60px"
          class="col-lg-3 col-md-3 col-sm-8"
          v-if="!isDiagram"
        >
          <strong>Show Bricks : </strong>
          <input class="px-4" type="checkbox" v-model="checked" />
        </div>
      </div>
    </c-card-body>
    <c-card-footer v-if="this.view != null && line_id != null">
      <c-button
        class="text-white"
        color="primary"
        @click="show"
        style="float: right"
        >Show</c-button
      >
    </c-card-footer>
  </c-card>
</template>
<script>
import axios from "axios";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["getStructure"],
  data() {
    return {
      lines: [],
      line_id: null,
      divisions: [],
      users: [],
      division_id: null,
      user_id: null,
      view: "Default",
      filters: [
        { id: 1, name: "Division" },
        { id: 2, name: "Employee" },
      ],
      filter: 1,
      checked: 0,
      checkAllLines:false,
      date: new Date().toISOString().slice(0, 10),
    };
  },
  computed: {
    isDiagram() {
      return this.view === "Diagram";
    },
  },
  methods: {
    initialize() {
      axios
        .get("/api/structureLines/")
        .then((response) => {
          this.lines = response.data.data.lines;
          this.users = response.data.data.users;
        })
        .catch((error) => {
          showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post(`/api/structure-divisions`, { lines: this.line_id })
        .then((response) => {
          this.divisions = response.data.data.divisions;
          this.users = response.data.data.users;
        })
        .catch((error) => {
          showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = null;
      this.getLineData();
    },
    show() {
      this.$emit("getStructure", {
        line: this.view === "Diagram" ? this.line_id[0] : this.line_id,
        division: this.view === "Diagram" ? null : this.division_id,
        user: this.view === "Diagram" ? null : this.user_id,
        filter: this.view === "Diagram" ? null : this.filter,
        date: this.date,
        checked: this.view === "Diagram" ? 0 : this.checked ? 1 : 0,
        view: this.view,
      });
    },
  },
  created() {
    this.initialize();
  },
};
</script>