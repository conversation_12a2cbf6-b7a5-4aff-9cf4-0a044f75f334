<template>
  <div>
    <c-card v-if="structure.length != 0">
      <c-card-header>
        <h3 class="text-center">
          Structure Report for {{ lineData.name }} at
          {{ acceptedFilter.date }}
        </h3>
      </c-card-header>
      <c-card-body ref="structure">
        <c-data-table id="print" hover striped footer sorter :items="structure" :fields="fields" :items-per-page="1000"
          :active-page="1" :responsive="true" pagination thead-top>
          <template slot="thead-top">
            <td style="border-top: none"><strong>Total</strong></td>
            <td style="border-top: none" class="text-xs-right">
              {{ structure.length }}
            </td>
          </template>
          <template #division="{ item }">
            <td :style="{
              color: item.color,
            }" style="font-weight: bold">
              {{ item.division }}
            </td>
          </template>
          <template #district="{ item }">
            <td :style="{
              color: item.manager_1_color,
            }" style="font-weight: bold">
              {{ item.district }}
            </td>
          </template>
          <template #area="{ item }">
            <td :style="{
              color: item.manager_2_color,
            }" style="font-weight: bold">
              {{ item.area }}
            </td>
          </template>
          <template #country="{ item }">
            <td :style="{
              color: item.manager_3_color,
            }" style="font-weight: bold">
              {{ item.country }}
            </td>
          </template>
          <template #employee="{ item }">
            <td :style="{
              color: item.color,
              paddingLeft: item.padding + 'rem',
            }" style="font-weight: bold">
              {{ item.employee }}
            </td>
          </template>
          <template #manager_1="{ item }">
            <td :style="{
              color: item.manager_1_color,
              paddingLeft: item.padding + 'rem',
            }" style="font-weight: bold">
              {{ item.manager_1 }}
            </td>
          </template>
          <template #manager_2="{ item }">
            <td :style="{
              color: item.manager_2_color,
              paddingLeft: item.padding + 'rem',
            }" style="font-weight: bold">
              {{ item.manager_2 }}
            </td>
          </template>
          <template #manager_3="{ item }">
            <td :style="{
              color: item.manager_3_color,
              paddingLeft: item.padding + 'rem',
            }" style="font-weight: bold">
              {{ item.manager_3 }}
            </td>
          </template>
          <template #code="{ item }">
            <td :style="{
              color: item.color,
            }" style="font-weight: bold">
              {{ item.code }}
            </td>
          </template>
          <template #manager_1_code="{ item }">
            <td :style="{
              color: item.manager_1_color,
            }" style="font-weight: bold">
              {{ item.manager_1_code }}
            </td>
          </template>
          <template #manager_2_code="{ item }">
            <td :style="{
              color: item.manager_2_color,
            }" style="font-weight: bold">
              {{ item.manager_2_code }}
            </td>
          </template>
          <template #manager_3_code="{ item }">
            <td :style="{
              color: item.manager_3_color,
            }" style="font-weight: bold">
              {{ item.manager_3_code }}
            </td>
          </template>
        </c-data-table>
      </c-card-body>
      <c-card-footer>
        <download @getPrint="print" @getxlsx="download" @getpdf="createPDF" @getcsv="downloadCsv" :name="name"
          :fields="fields" />
      </c-card-footer>
    </c-card>
  </div>
</template>

<script>
import moment from "moment";
import download from "../../download-reports/download.vue";
import jsPDF from "jspdf";
import "jspdf-autotable";
import * as XLSX from "xlsx";
import { capitalize } from "../../../filters";
export default {
  components: {
    download,
  },
  props: {
    acceptedFilter: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      structure: [],
      bricks: [],
      divisionUsers: [],
      lineData: [],
      showBricks: false,
      fields: [],
      bricks_fields: [],
      name: "Structure Report",
    };
  },
  methods: {
    format_date: function (value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    initialize() {
      if (!this.acceptedFilter.line) {
        return;
      }
      let structure = {
        date: this.acceptedFilter.date,
        checked: this.acceptedFilter.checked,
        filter: this.acceptedFilter.filter,
        lines: this.acceptedFilter.line,
        divisions: this.acceptedFilter.division ? [this.acceptedFilter.division] : [],
        users: this.acceptedFilter.user ? [this.acceptedFilter.user] : [],
      };
      axios
        .post(`/api/raw-report/${this.acceptedFilter.line}`, {
          structure,
        })
        .then((response) => {
          this.structure = response.data.divisions;
          this.fields = response.data.fields;
          this.lineData = response.data.line_data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    download() {
      let filteredData = Object.values(this.structure);
      filteredData.forEach((element) => {
        delete element["color"];
        delete element["manager_1_color"];
        delete element["manager_2_color"];
        delete element["manager_3_color"];
      });

      this.downloadXlsx(filteredData, "Structure Report.xlsx");
      this.$emit("downloaded");
    },
    downloadCsv() {
      this.downloadXlsx(this.structure, "Structure Report.csv");
      this.$emit("downloaded");
    },
    print() {
      this.$htmlToPaper("print");
    },
    createPDF() {
      let pdfName = "structureReport";
      const columns = this.fields.map((field) => {
        return {
          title: capitalize(field),
          dataKey: field,
        };
      });
      var doc = new jsPDF();
      doc.autoTable(columns, this.structure, {
        margin: { top: 10 },
        showHead: "firstPage",
      });
      doc.save(pdfName + ".pdf");
    },
    // print() {
    //   this.$htmlToPaper("printBrick");
    // },
    downloadBricksData() {
      this.downloadXlsx(this.bricks, "Unlinked Bricks.xlsx");
      this.$emit("downloaded");
    },
    downloadBricksCsv() {
      this.downloadXlsx(this.bricks, "Unlinked Bricks.csv");
      this.$emit("downloaded");
    },
    createBricksPDF() {
      let pdfName = this.secondName;
      const columns = this.bricks_fields.map((field) => {
        return {
          title: capitalize(field),
          dataKey: field,
        };
      });
      const body = this.bricks;
      const doc = new jsPDF({ filters: ["ASCIIHexEncode"] });

      doc.addFileToVFS("Amiri-Regular.ttf", Amiri);
      doc.addFont("Amiri-Regular.ttf", "Amiri", "normal");
      doc.setFont("Amiri");
      doc.setFontSize(10);
      doc.autoTable({
        columns,
        body,
        margin: { top: 10 },
        showHead: "firstPage",
        styles: {
          lineColor: "#c7c7c7",
          lineWidth: 0,
          cellPadding: 2,
          font: "Amiri",
        },
      });
      doc.save(pdfName + ".pdf");
    },
  },

  watch: {
    acceptedFilter() {
      this.initialize();
    },
  },
  created() {
    this.initialize();
  },
};
</script>