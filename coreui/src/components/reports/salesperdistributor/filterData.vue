<template>
  <c-card>
    <c-card-header>Sales Per Distributor Report</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Line </template>
            <template #input>
              <input
                label="All"
                v-if="lines.length != 0"
                class="m-1"
                id="lines"
                type="checkbox"
                v-model="checkAllLines"
                title="Check All Lines"
                @change="checkAllLine"
              />
              <label
                for="lines"
                v-if="lines.length != 0"
                style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="line_id"
                :options="lines"
                label="name"
                :value="0"
                :reduce="(line) => line.id"
                placeholder="Select Line"
                class="mt-1"
                @input="getLineData"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label>
              Filter By <span style="color: red">*</span>
            </template>
            <template #input>
              <v-select
                v-model="filter"
                :options="filters"
                label="name"
                :value="0"
                :reduce="(line) => line.id"
                placeholder="Select Filter"
                class="mt-2"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8" v-if="filter == 1">
          <c-form-group>
            <template #label> Division </template>
            <template #input>
              <input
                label="All"
                id="division"
                v-if="divisions.length != 0"
                class="m-1"
                type="checkbox"
                v-model="checkAllDivisions"
                title="Check All Divisions"
                @change="checkAllDivs"
              />
              <label
                for="division"
                v-if="divisions.length != 0"
                style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="division_id"
                :options="divisions"
                label="name"
                :value="0"
                :reduce="(division) => division.id"
                placeholder="Select Division"
                class="mt-1"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8" v-if="filter == 2">
          <c-form-group>
            <template #label> Employee </template>
            <template #input>
              <input
                label="All"
                v-if="users.length != 0"
                id="user"
                class="m-1"
                type="checkbox"
                v-model="checkAllUsers"
                title="Check All Users"
                @change="checkAllEmployees"
              />
              <label
                for="user"
                v-if="users.length != 0"
                style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="user_id"
                :options="users"
                label="fullname"
                :value="0"
                :reduce="(user) => user.id"
                placeholder="Select Employee"
                class="mt-1"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Product </template>
            <template #input>
              <input
                label="All"
                id="products"
                v-if="products.length != 0"
                class="m-1"
                type="checkbox"
                v-model="checkAllProducts"
                title="Check All Products"
                @change="checkAllProduct"
              />
              <label
                for="products"
                v-if="products.length != 0"
                style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="product_id"
                :options="products"
                label="name"
                :value="0"
                :reduce="(product) => product.id"
                placeholder="Select Product"
                class="mt-1"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Distributor </template>
            <template #input>
              <input
                label="All"
                id="distributors"
                v-if="distributors.length != 0"
                class="m-1"
                type="checkbox"
                v-model="checkAllDistributors"
                title="Check All Distributors"
                @change="checkAllDistributor"
              />
              <label
                for="distributors"
                v-if="distributors.length != 0"
                style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="distributor_id"
                :options="distributors"
                label="name"
                :value="0"
                :reduce="(distributor) => distributor.id"
                placeholder="Select Distributor"
                class="mt-1"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Mapping Types </template>
            <template #input>
              <v-select
                v-model="type_id"
                :options="types"
                label="name"
                :value="0"
                :reduce="(type) => type.id"
                placeholder="Select Mapping Type"
                class="mt-1"
              />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-input
            label="From Date"
            type="date"
            placeholder="Date"
            v-model="from_date"
          ></c-input>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-input
            label="To Date"
            type="date"
            placeholder="Date"
            v-model="to_date"
          ></c-input>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button
        v-if="line_id.length > 0"
        color="primary"
        class="text-white"
        @click="show"
        style="float: right"
        >Show</c-button
      >
    </c-card-footer>
  </c-card>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      lines: [],
      distributors: [],
      divisions: [],
      users: [],
      types: [],
      products: [],
      line_id: [],
      distributor_id: [],
      division_id: [],
      user_id: [],
      filters: [
        { id: 1, name: "Division" },
        { id: 2, name: "Employee" },
      ],
      filter: 2,
      product_id: [],
      type_id: 1,
      checkAllLines: false,
      checkAllDistributors: false,
      checkAllDivisions: false,
      checkAllUsers: false,
      checkAllProducts: false,
      from_date: moment().startOf("month").format("YYYY-MM-DD"),
      to_date: moment().endOf("month").format("YYYY-MM-DD"),
    };
  },
  methods: {
    initialize() {
      axios
        .get("/api/lines-sales-distributor")
        .then((response) => {
          this.lines = response.data.data.lines;
          this.types = response.data.data.types;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post("/api/line-data-sales-distributor/", { lines: this.line_id })
        .then((response) => {
          this.divisions = response.data.data.divisions;
          this.users = response.data.data.users;
          this.distributors = response.data.data.distributors;
          this.products = response.data.data.products;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = null;
      this.getLineData();
    },
    checkAllDistributor() {
      if (this.checkAllDistributors)
        this.distributor_id = this.distributors.map((item) => item.id);
      if (this.checkAllDistributors == false) this.distributor_id = null;
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = null;
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    checkAllProduct() {
      if (this.checkAllProducts)
        this.product_id = this.products.map((item) => item.id);
      if (this.checkAllProducts == false) this.product_id = null;
    },
    show() {
      let saleFilter = {
        lines: this.line_id,
        distributors: this.distributor_id,
        type: this.type_id,
        filter: this.filter,
        divisions: this.division_id,
        users: this.user_id,
        products: this.product_id,
        fromDate: this.from_date,
        toDate: this.to_date,
      };
      this.$emit("getSchedule", { saleFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
