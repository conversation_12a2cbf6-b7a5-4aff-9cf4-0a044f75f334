<template>
  <c-card>
    <c-card-header> Sample Consumption Report</c-card-header>
    <c-card-body>
      <c-tabs>
        <c-tab active>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Main
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-input label="From" type="date" placeholder="From" v-model="from_date"></c-input>
                </div>

                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-input label="To" type="date" placeholder="To" v-model="to_date" @input="getAllData"></c-input>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Line </template>
                    <template #input>
                      <input label="All" v-if="lines.length != 0" id="line" class="mr-1" type="checkbox"
                        v-model="checkAllLines" title="Check All lines" @change="checkAllLine" />
                      <label for="line" v-if="lines.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                        placeholder="Select Lines" class="mt-2" multiple @input="getLineData" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-form-group>
                    <template #label> Filter By <span style="color:red">*</span></template>
                    <template #input>
                      <v-select v-model="filter" :options="filters" label="name" :value="0" :reduce="(line) => line.id"
                        placeholder="Select Filter" class="mt-2" @input="
                          filter == 1 ? (user_id = []) : (division_id = [])
                          " />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8" v-if="filter == 1">
                  <c-form-group>
                    <template #label> Division </template>
                    <template #input>
                      <input label="All" id="division" v-if="divisions.length != 0" class="m-1" type="checkbox"
                        v-model="checkAllDivisions" title="Check All Divisions" @change="checkAllDivs" />
                      <label for="division" v-if="divisions.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="division_id" :options="divisions" label="name" :value="0"
                        :reduce="(division) => division.id" placeholder="Select Division" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8" v-if="filter == 2">
                  <c-form-group>
                    <template #label> Employee </template>
                    <template #input>
                      <input label="All" v-if="users.length != 0" id="user" class="m-1" type="checkbox"
                        v-model="checkAllUsers" title="Check All Users" @change="checkAllEmployees" />
                      <label for="user" v-if="users.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="user_id" :options="users" label="fullname" :value="0"
                        :reduce="(user) => user.id" placeholder="Select Employee" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Type </template>
                    <template #input>
                      <v-select v-model="report_type" :options="['Samples', 'Consumed']" placeholder="Select Type"
                        class="mt-2" @input="getProducts" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Products </template>
                    <template #input>
                      <input label="All" v-if="products.length != 0" id="product" class="m-1" type="checkbox"
                        v-model="checkAllProducts" title="Check All Products" @change="checkAllProduct" />
                      <label for="product" v-if="products.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="product_id" :options="products" label="name" :value="0"
                        :reduce="(product) => product.id" placeholder="Select Product" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> View </template>
                    <template #input>
                      <v-select v-model="view" :options="['Horizontal', 'Vertical']" placeholder="Select View"
                        class="mt-2" />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Account Types &
            Specialities
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Account Type </template>
                    <template #input>
                      <input label="All" id="type" v-if="types.length != 0" class="m-1" type="checkbox"
                        v-model="allAccountTypes" title="Check All Types" @change="checkAllAccounts" />
                      <label for="type" v-if="types.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="type_id" :options="types" label="name" :value="0" :reduce="(type) => type.id"
                        placeholder="Select Account Type" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Specialities </template>
                    <template #input>
                      <input label="All" id="speciality" v-if="specialities.length != 0" class="m-1" type="checkbox"
                        v-model="checkAllSpecialities" title="Check All Specialities" @change="checkAllSpeciality" />
                      <label for="speciality" v-if="specialities.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="speciality_id" :options="specialities" label="name"
                        placeholder="Select Speciality" :reduce="(speciality) => speciality.id" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
      </c-tabs>
    </c-card-body>
    <c-card-footer>
      <c-button v-if="line_id.length > 0" color="primary" class="text-white" @click="show"
        style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      division_id: [],
      speciality_id: [],
      product_id: [],
      type_id: [],
      user_id: [],
      line_id: [],
      divisions: [],
      products: [],
      specialities: [],
      types: [],
      lines: [],
      users: [],
      filters: [
        { id: 1, name: "Division" },
        { id: 2, name: "Employee" },
      ],
      filter: 2,
      view: "Horizontal",
      report_type: "Samples",
      checkAllDivisions: false,
      allAccountTypes: true,
      checkAllSpecialities: true,
      checkAllUsers: false,
      checkAllProducts: false,
      checkAllLines: false,
      from_date: moment().startOf("month").format("YYYY-MM-DD"),
      to_date: moment().endOf("month").format("YYYY-MM-DD"),
    };
  },
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: this.from_date,
          to: this.to_date,
          data: ['lines', 'accountTypes', 'users']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.users = response.data.data.users;
          this.types = response.data.data.accountTypes;
          this.type_id = this.types.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getAllData() {
      this.line_id = [];
      this.user_id = [];
      this.division_id = [];
      this.speciality_id = [];
      this.initialize();
    },
    getLineData() {
      axios
        .post(`/api/get-lines-data-with-dates`, {
          lines: this.line_id,
          lines: this.line_id,
          from: this.from_date,
          to: this.to_date,
          data: ['divisions', 'productSamples', 'users', 'specialities']
        })
        .then((response) => {
          this.divisions = response.data.data.divisions;
          this.products = response.data.data.productSamples;
          this.users = response.data.data.users;
          this.specialities = response.data.data.specialities;
          this.speciality_id = this.specialities.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getProducts() {
      axios
        .post(`/api/get-samples-products`, {
          lines: this.line_id,
          report_type: this.report_type,
          from: this.from_date,
          to: this.to_date,
        })
        .then((response) => {
          this.products = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = [];
      this.getLineData();
    },
    checkAllAccounts() {
      if (this.allAccountTypes)
        this.type_id = this.types.map((item) => item.id);
      if (this.allAccountTypes == false) this.type_id = [];
    },
    checkAllSpeciality() {
      if (this.checkAllSpecialities)
        this.speciality_id = this.specialities.map((item) => item.id);
      if (this.checkAllSpecialities == false) this.speciality_id = [];
    },
    checkAllProduct() {
      if (this.checkAllProducts)
        this.product_id = this.products.map((item) => item.id);
      if (this.checkAllProducts == false) this.product_id = [];
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = [];
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = [];
    },
    show() {
      let visitFilter = {
        lines: this.line_id,
        filter: this.filter,
        divisions: this.division_id,
        types: this.type_id,
        report_type: this.report_type,
        view: this.view,
        specialities: this.speciality_id,
        products: this.product_id,
        users: this.user_id,
        fromDate: this.from_date,
        toDate: this.to_date,
      };
      this.$emit("getSchedule", { visitFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>