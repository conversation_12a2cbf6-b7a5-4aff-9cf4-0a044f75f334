<template>
  <c-card>
    <c-card-header>Finance Summary Report</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-input label="From" type="date" placeholder="From" v-model="from_date"></c-input>
        </div>

        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-input label="To" type="date" placeholder="To" v-model="to_date" @input="getAllData"></c-input>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-form-group>
            <template #label>
              Line <span style="color: red">*</span></template>
            <template #input>
              <input label="All" v-if="lines.length != 0" id="line" class="m-1" type="checkbox" v-model="checkAllLines"
                title="Check All lines" @change="checkAllLine" />
              <label for="line" v-if="lines.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                placeholder="Select Line" class="mt-2" @input="getLineData" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-form-group>
            <template #label>
              Filter By <span style="color: red">*</span></template>
            <template #input>
              <v-select v-model="filter" :options="filters" label="name" :value="0" :reduce="(line) => line.id"
                placeholder="Select Filter" class="mt-2" @input="getFilteredData" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8" v-if="filter == 1">
          <c-form-group>
            <template #label> Division </template>
            <template #input>
              <input label="All" id="division" v-if="divisions.length != 0" class="m-1" type="checkbox"
                v-model="checkAllDivisions" title="Check All Divisions" @change="checkAllDivs" />
              <label for="division" v-if="divisions.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="division_id" :options="divisions" label="name" :value="0"
                :reduce="(division) => division.id" placeholder="Select Division" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8" v-if="filter == 2 || filter == 3">
          <c-form-group>
            <template #label> Employee </template>
            <template #input>
              <input label="All" v-if="users.length != 0" id="user" class="m-1" type="checkbox" v-model="checkAllUsers"
                title="Check All Users" @change="checkAllEmployees" />
              <label for="user" v-if="users.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="user_id" :options="users" label="fullname" :value="0" :reduce="(user) => user.id"
                placeholder="Select Employee" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>

        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-form-group>
            <template #label> Request Type </template>
            <template #input>
              <input label="All" id="type" v-if="types.length != 0" class="m-1" type="checkbox"
                v-model="allRequestTypes" title="Check All Types" @change="checkAllRequestType" />
              <label for="type" v-if="types.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="type_id" :options="types" label="name" :value="0" :reduce="(type) => type.id"
                placeholder="Select Commercial Type" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-form-group>
            <template #label> Approval Types </template>
            <template #input>
              <v-select v-model="approval_id" :options="approvals" label="name" :value="0"
                :reduce="(approval) => approval.value" placeholder="Select Approval Types" class="mt-2" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-form-group>
            <template #label> Partial Payment </template>
            <template #input>
              <v-select v-model="partial_payment_id" :options="partial_payments" label="name" :value="0"
                :reduce="(payment) => payment.value" placeholder="Select Payment Type" class="mt-2" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-form-group>
            <template #label> Service Done </template>
            <template #input>
              <v-select v-model="serviceDone" :options="serviceDones" label="name" :value="0"
                :reduce="(payment) => payment.value" placeholder="Select Service Done" class="mt-2" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-form-group>
            <template #label>  Paid </template>
            <template #input>
              <v-select v-model="paid" :options="paids" label="name" :value="0" :reduce="(payment) => payment.value"
                placeholder="Select Paid" class="mt-2" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-input label="Due Date" type="date" placeholder="Due Date" v-model="due_date"></c-input>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button v-if="line_id != null" color="primary" class="text-white" @click="show"
        style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";

export default {
  components: {
    vSelect,
  },
  emits: ["getServiceDone"],
  data() {
    return {
      division_id: [],
      type_id: [],
      user_id: [],
      line_id: [],
      divisions: [],
      approvals: [
        { name: "Pending", value: 1 },
        { name: "Approved", value: 2 },
        { name: "Disapproved", value: 3 },
        { name: "Pending & Approved", value: 4 },
        { name: "Total", value: 5 },
      ],
      approval_id: 5,
      partial_payments: [
        { name: "No", value: 1 },
        { name: "Yes", value: 2 },
        { name: "Total", value: 3 },
      ],
      partial_payment_id: 3,
      serviceDones: [
        { name: "No", value: 1 },
        { name: "Yes", value: 2 },
        { name: "Total", value: 3 },
      ],
      serviceDone: 3,
      paids: [
        { name: "No", value: 1 },
        { name: "Yes", value: 2 },
        { name: "Total", value: 3 },
      ],
      paid: 3,
      types: [],
      lines: [],
      users: [],
      filters: [
        { id: 1, name: "Division" },
        { id: 2, name: "Employee" },
        { id: 3, name: "All" },
      ],
      filter: 3,
      checkAllDivisions: false,
      checkAllLines: false,
      allRequestTypes: true,
      checkAllUsers: false,
      from_date: moment().startOf('month').format("YYYY-MM-DD"),
      to_date: moment().endOf('month').format("YYYY-MM-DD"),
      due_date:moment().startOf('month').format("YYYY-MM-DD"),
    };
  },
  methods: {
    getAllData() {
      this.line_id = [];
      this.division_id = [];
      this.user_id = [];
      this.checkAllUsers = false;
      this.checkAllLines = false;
      this.checkAllDivisions = false;
    },
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: this.from_date,
          to: this.to_date,
          data: ['lines', 'requestTypes']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.types = response.data.data.requestTypes;
          this.type_id = this.types.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post(`/api/get-lines-data-with-dates`, {
          lines: this.line_id,
          from: this.from_date,
          to: this.to_date,
          data: ['divisions', 'products', 'users', 'allUsers']
        })
        .then((response) => {
          this.divisions = response.data.data.divisions;
          this.users = this.filter == 2 ? response.data.data.users : response.data.data.allUsers;
          this.products = response.data.data.products;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getFilteredData() {
      this.getLineData();
      this.filter == 1 ? this.user_id = [] : this.division_id = [];
    },

    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) {
        this.line_id = [];
        this.product_id = [];
        this.user_id = [];
        this.division_id = [];
      }
      this.getLineData();
    },
    checkAllRequestType() {
      if (this.allRequestTypes)
        this.type_id = this.types.map((item) => item.id);
      if (this.allRequestTypes == false) this.type_id = null;
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = null;
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    show() {
      let commercialFilter = {
        lines: this.line_id,
        filter: this.filter,
        approval: this.approval_id,
        partial_payment: this.partial_payment_id,
        serviceDone: this.serviceDone,
        paid: this.paid,
        divisions: this.division_id,
        types: this.type_id,
        users: this.user_id,
        fromDate: this.from_date,
        toDate: this.to_date,
        due_date: this.due_date
      };
      this.$emit("getServiceDone", { commercialFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>