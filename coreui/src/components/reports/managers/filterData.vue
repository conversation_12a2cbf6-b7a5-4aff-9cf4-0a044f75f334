<template>
  <c-card>
    <c-card-header>Managers Report</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-input label="From" type="date" placeholder="From" v-model="from_date"></c-input>
        </div>

        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-input label="To" type="date" placeholder="To" v-model="to_date" @input="getAllData"></c-input>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Line </template>
            <template #input>
              <input label="All" id="line" v-if="lines.length != 0" class="m-1" type="checkbox" v-model="checkAllLines"
                title="Check All Lines" @change="checkAllLine" />
              <label for="line" v-if="lines.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                placeholder="Select option" multiple class="mt-2" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Type </template>
            <template #input>
              <v-select v-model="type" :options="types" label="name" :value="0" :reduce="(type) => type.type"
                placeholder="Select option" class="mt-2" @input="getLineData" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Position </template>
            <template #input>
              <input label="All" id="type" v-if="positions.length != 0" class="m-1" type="checkbox"
                v-model="checkAllPositions" title="Check All positions" @change="checkAllPosition" />
              <label for="type" v-if="positions.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="position_id" :options="positions" label="name" :value="0"
                :reduce="(position) => position.id" placeholder="Select option" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> View </template>
            <template #input>
              <v-select v-model="view" :options="views" label="name" :value="0" placeholder="Select View"
                class="mt-2" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8" v-if="view != 'Call Rate'">
          <c-form-group>
            <template #label> Shift </template>
            <template #input>
              <input label="All" v-if="shifts.length != 0" id="shift" class="m-1" type="checkbox"
                v-model="checkAllShifts" title="Check All Shifts" @change="checkAllShift" />
              <label for="shift" v-if="shifts.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="shift_id" :options="shifts" label="name" placeholder="Select Shift"
                :reduce="(shift) => shift.id" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8" v-else>
          <c-form-group>
            <template #label> Shift </template>
            <template #input>
              <v-select v-model="shift_id" :options="shifts" label="name" placeholder="Select Shift"
                :reduce="(shift) => shift.id" class="mt-2" />
            </template>
          </c-form-group>
        </div>

      </div>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" class="text-white" @click="show" style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";

export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      position_id: [],
      line_id: [],
      lines: [],
      types: [],
      type: null,
      positions: [],
      checkAllPositions: false,
      checkAllLines: false,
      shift_id: [],
      shifts: [],
      views: ["SOP", "Coverage", "Frequency", "Call Rate"],
      view: 'SOP',
      checkAllShifts: true,
      from_date: moment().startOf("month").format("YYYY-MM-DD"),
      to_date: moment().endOf("month").format("YYYY-MM-DD"),
    };
  },
  methods: {
    initialize() {
      axios
        .get("/api/get-lines-manager-report")
        .then((response) => {
          this.lines = response.data.data.lines;
          this.types = response.data.data.models;
          this.shifts = response.data.data.shifts;
          this.shift_id = this.view != 'Call Rate' ? this.shifts.map((item) => item.id) : [2];
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getAllData() {
      this.line_id = [];
      this.position_id = [];
      this.type = null;
      this.initialize();
    },
    getLineData() {
      axios
        .post(`/api/get-types-manager-report`, {
          lines: this.line_id,
          type: this.type,
        })
        .then((response) => {
          this.positions = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = null;
    },
    checkAllPosition() {
      if (this.checkAllPositions)
        this.position_id = this.positions.map((item) => item.id);
      if (this.checkAllPositions == false) this.position_id = null;
    },
    checkAllShift() {
      if (this.checkAllShifts)
        this.shift_id = this.shifts.map((item) => item.id);
      if (this.checkAllShifts == false) this.shift_id = null;
    },
    show() {
      let visitFilter = {
        line: this.line_id,
        type: this.type,
        shifts: this.shift_id,
        positions: this.position_id,
        fromDate: this.from_date,
        toDate: this.to_date,
        view: this.view,
      };
      this.$emit("getSchedule", { visitFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>