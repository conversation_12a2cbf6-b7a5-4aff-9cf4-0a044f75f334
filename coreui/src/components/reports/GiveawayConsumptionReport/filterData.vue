<template>
  <c-card>
    <c-card-header> Giveaway Consumption Report</c-card-header>
    <c-card-body>
      <c-tabs>
        <c-tab active>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Main
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Line <span style="color:red">*</span></template>
                    <template #input>
                      <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                        placeholder="Select Line" class="mt-2" @input="getLineData" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Filter By <span style="color:red">*</span></template>
                    <template #input>
                      <v-select v-model="filter" :options="filters" label="name" :value="0" :reduce="(line) => line.id"
                        placeholder="Select Filter" class="mt-2" @input="
                          filter == 1 ? (user_id = []) : (division_id = [])
                          " />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8" v-if="filter == 1">
                  <c-form-group>
                    <template #label> Division </template>
                    <template #input>
                      <input label="All" id="division" v-if="divisions.length != 0" class="m-1" type="checkbox"
                        v-model="checkAllDivisions" title="Check All Divisions" @change="checkAllDivs" />
                      <label for="division" v-if="divisions.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="division_id" :options="divisions" label="name" :value="0"
                        :reduce="(division) => division.id" placeholder="Select Division" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8" v-if="filter == 2">
                  <c-form-group>
                    <template #label> Employee </template>
                    <template #input>
                      <input label="All" v-if="users.length != 0" id="user" class="m-1" type="checkbox"
                        v-model="checkAllUsers" title="Check All Users" @change="checkAllEmployees" />
                      <label for="user" v-if="users.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="user_id" :options="users" label="fullname" :value="0"
                        :reduce="(user) => user.id" placeholder="Select Employee" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Giveaways </template>
                    <template #input>
                      <input label="All" v-if="giveaways.length != 0" id="giveaway" class="m-1" type="checkbox"
                        v-model="checkAllGiveaways" title="Check All Giveaways" @change="checkAllGiveaway" />
                      <label for="giveaway" v-if="giveaways.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="giveaway_id" :options="giveaways" label="name" :value="0"
                        :reduce="(giveaway) => giveaway.id" placeholder="Select Giveaway" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-input label="From" type="date" placeholder="From" v-model="from_date"></c-input>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-input label="To" type="date" placeholder="To" v-model="to_date"></c-input>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Account Types &
            Specialities
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Account Type </template>
                    <template #input>
                      <input label="All" id="type" v-if="types.length != 0" class="m-1" type="checkbox"
                        v-model="allAccountTypes" title="Check All Types" @change="checkAllAccounts" />
                      <label for="type" v-if="types.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="type_id" :options="types" label="name" :value="0" :reduce="(type) => type.id"
                        placeholder="Select Account Type" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Specialities </template>
                    <template #input>
                      <input label="All" id="speciality" v-if="specialities.length != 0" class="m-1" type="checkbox"
                        v-model="checkAllSpecialities" title="Check All Specialities" @change="checkAllSpeciality" />
                      <label for="speciality" v-if="specialities.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="speciality_id" :options="specialities" label="name"
                        placeholder="Select Speciality" :reduce="(speciality) => speciality.id" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
      </c-tabs>
    </c-card-body>
    <c-card-footer>
      <c-button v-if="line_id != null" color="primary" class="text-white" @click="show"
        style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      division_id: [],
      speciality_id: [],
      giveaway_id: [],
      type_id: [],
      user_id: [],
      line_id: null,
      divisions: [],
      giveaways: [],
      specialities: [],
      types: [],
      lines: [],
      users: [],
      filters: [
        { id: 1, name: "Division" },
        { id: 2, name: "Employee" },
      ],
      filter: 2,
      checkAllDivisions: false,
      allAccountTypes: true,
      checkAllSpecialities: true,
      checkAllUsers: false,
      checkAllGiveaways: false,
      from_date: new Date().toISOString().slice(0, 10),
      to_date: new Date().toISOString().slice(0, 10),
    };
  },
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: null,
          to: null,
          data: ['lines', 'accountTypes']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.types = response.data.data.accountTypes;
          this.type_id = this.types.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .get(`/api/get-line-data-reports/${this.line_id}`)
        .then((response) => {
          this.divisions = response.data.divisions;
          this.users = response.data.users;
          this.specialities = response.data.specialities;
          this.giveaways = response.data.giveaways;
          this.speciality_id = this.specialities.map((item) => item.id);

        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllAccounts() {
      if (this.allAccountTypes)
        this.type_id = this.types.map((item) => item.id);
      if (this.allAccountTypes == false) this.type_id = null;
    },
    checkAllSpeciality() {
      if (this.checkAllSpecialities)
        this.speciality_id = this.specialities.map((item) => item.id);
      if (this.checkAllSpecialities == false) this.speciality_id = null;
    },
    checkAllGiveaway() {
      if (this.checkAllGiveaways)
        this.giveaway_id = this.giveaways.map((item) => item.id);
      if (this.checkAllGiveaways == false) this.giveaway_id = null;
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = null;
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    show() {
      let visitFilter = {
        line: this.line_id,
        filter: this.filter,
        divisions: this.division_id,
        types: this.type_id,
        specialities: this.speciality_id,
        giveaways: this.giveaway_id,
        users: this.user_id,
        fromDate: this.from_date,
        toDate: this.to_date,
      };
      this.$emit("getSchedule", { visitFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>