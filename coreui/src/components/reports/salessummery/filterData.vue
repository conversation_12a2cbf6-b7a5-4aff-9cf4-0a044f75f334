<template>
  <c-card>
    <c-card-header>Sales Summery Report</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-input label="From Date" type="date" class="mt-2" placeholder="Date" v-model="from_date"
            :max="dateLimit"></c-input>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-input label="To Date" type="date" class="mt-2" placeholder="Date" v-model="to_date" :max="dateLimit"
            @input="getAllData"></c-input>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Mapping Types </template>
            <template #input>
              <v-select v-model="type_id" :options="types" label="name" :value="0" :reduce="(type) => type.id"
                placeholder="Select Mapping Type" class="mt-3" />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Line </template>
            <template #input>
              <input label="All" v-if="lines.length != 0" class="m-1" id="lines" type="checkbox" v-model="checkAllLines"
                title="Check All Lines" @change="checkAllLine" />
              <label for="lines" v-if="lines.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                placeholder="Select option" class="mt-2" @input="getLineData" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Division </template>
            <template #input>
              <input label="All" id="divisons" v-if="divisions.length != 0" class="m-1" type="checkbox"
                v-model="checkAllDivisions" title="Check All Divisions" @change="checkAllDivision" />
              <label for="divisions" v-if="divisions.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="division_id" :options="divisions" label="name" :value="0"
                :reduce="(division) => division.id" placeholder="Select Division" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Product </template>
            <template #input>
              <input label="All" id="products" v-if="products.length != 0" class="m-1" type="checkbox"
                v-model="checkAllProducts" title="Check All Products" @change="checkAllProduct" />
              <label for="products" v-if="products.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="product_id" :options="products" label="name" :value="0"
                :reduce="(product) => product.id" placeholder="Select Product" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-8">
          <c-form-group>
            <template #label> Distributor </template>
            <template #input>
              <input label="All" id="distributors" v-if="distributors.length != 0" class="m-1" type="checkbox"
                v-model="checkAllDistributors" title="Check All Distributors" @change="checkAllDistributor" />
              <label for="distributors" v-if="distributors.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="distributor_id" :options="distributors" label="name" :value="0"
                :reduce="(distributor) => distributor.id" placeholder="Select Distributor" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button v-if="line_id.length > 0" color="primary" class="text-white" @click="show"
        style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      lines: [],
      distributors: [],
      divisions: [],
      types: [],
      products: [],
      line_id: [],
      distributor_id: [],
      division_id: [],
      product_id: [],
      type_id: 2,
      checkAllLines: false,
      checkAllDistributors: false,
      checkAllDivisions: false,
      checkAllProducts: false,
      from_date: null,
      to_date: null,
      dateLimit: null
    };
  },
  methods: {
    getAllData() {
      this.line_id = [];
      this.division_id = [];
      this.product_id = [];
      this.checkAllLines = false;
      this.checkAllDivisions = false;
      this.checkAllProducts = false;
      this.checkAllDistributors = false;
      this.getLineAndSalesTypes();
    },
    setDates(date) {
      this.dateLimit = date
      if (!date) {
        this.from_date = moment().startOf("month").format("YYYY-MM-DD")
        this.to_date = moment().endOf("month").format("YYYY-MM-DD")
        return;
      }

      this.from_date = moment(date).startOf("month").format("YYYY-MM-DD")
      this.to_date = moment(date).endOf("month").format("YYYY-MM-DD")

    },
    async getDateLimit() {
      return axios.get("/api/sales/date/limit")
        .then((res) => this.setDates(res.data.limit))
        .catch(this.showErrorMessage);
    },
    getLineAndSalesTypes() {
      axios
        .post("/api/get-lines-with-dates", {
          from: this.from_date,
          to: this.to_date,
          data: ['lines', 'salesTypes']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.types = response.data.data.salesTypes;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    async initialize() {
      return this.getDateLimit();
    },
    getLineData() {
      axios
        .post(`/api/get-lines-data-with-dates`, {
          lines: this.line_id,
          from: this.from_date,
          to: this.to_date,
          data: ['divisions', 'products', 'distributors']
        })
        .then((response) => {
          this.divisions = response.data.data.divisions;
          this.products = response.data.data.products;
          this.distributors = response.data.data.distributors;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = [];
      this.getLineData();
    },
    checkAllDistributor() {
      if (this.checkAllDistributors)
        this.distributor_id = this.distributors.map((item) => item.id);
      if (this.checkAllDistributors == false) this.distributor_id = [];
    },
    checkAllDivision() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = [];
    },
    checkAllProduct() {
      if (this.checkAllProducts)
        this.product_id = this.products.map((item) => item.id);
      if (this.checkAllProducts == false) this.product_id = [];
    },
    show() {
      let saleFilter = {
        lines: this.line_id,
        distributors: this.distributor_id,
        type: this.type_id,
        divisions: this.division_id,
        products: this.product_id,
        fromDate: this.from_date,
        toDate: this.to_date,
      };
      this.$emit("getSchedule", { saleFilter });
    },
  },
  created() {
    this.initialize()
      .then(() => this.getLineAndSalesTypes())
  },
};
</script>
