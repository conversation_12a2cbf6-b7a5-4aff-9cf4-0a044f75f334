<template>
  <c-card>
    <c-card-header>Sales Details Report</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-input
            label="From Date"
            type="date"
            class="mt-2"
            placeholder="Date"
            v-model="from_date"
            :max="dateLimit"
          ></c-input>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-input
            label="To Date"
            type="date"
            class="mt-2"
            placeholder="Date"
            v-model="to_date"
            :max="dateLimit"
            @input="getAllData"
          ></c-input>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-form-group>
            <template #label> types</template>
            <template #input>
              <v-select
                v-model="type_id"
                :options="types"
                label="name"
                :value="0"
                :reduce="(type) => type.id"
                placeholder="Select Type"
                class="mt-3"
              />
            </template>
          </c-form-group>
        </div>
        <div :class="stylingClasses">
          <c-form-group>
            <template #label> Line</template>
            <template #input>
              <input
                label="All"
                v-if="isLinesNotEmpty"
                class="m-1"
                id="lines"
                type="checkbox"
                v-model="checkAllLines"
                title="Check All Lines"
                @change="checkAllLine"
              />
              <label
                for="lines"
                v-if="isLinesNotEmpty"
                style="font-weight: bold">
                All
              </label>
              <v-select
                v-model="line_id"
                :options="lines"
                label="name"
                :value="0"
                :reduce="(line) => line.id"
                placeholder="Select Line"
                class="mt-2"
                @input="getLineData"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div :class="stylingClasses">
          <c-form-group>
            <template #label>Report Type</template>
            <template #input>
              <v-select
                v-model="report_type"
                :options="report_types"
                label="name"
                :reduce="(type) => type.id"
                placeholder="Select Report Type"
                class="mt-2"
              />
            </template>
          </c-form-group>
        </div>
        <div :class="stylingClasses" v-if="!isAchievements">
          <c-form-group>
            <template #label>
              Filter By <span style="color: red">*</span>
            </template>
            <template #input>
              <v-select
                v-model="filter"
                :options="filters"
                label="name"
                :reduce="(by) => by.id"
                placeholder="Select Filter"
                class="mt-2"
                @input="isPerDivision ? (division_id = []) : (user_id = [])"
              />
            </template>
          </c-form-group>
        </div>
        <div :class="stylingClasses" v-if="isPerDivision && !isAchievements">
          <c-form-group>
            <template #label>Division</template>
            <template #input>
              <input
                label="All"
                id="division"
                v-if="isDivisionsNotEmpty"
                class="m-1"
                type="checkbox"
                v-model="checkAllDivisions"
                title="Check All Divisions"
                @change="checkAllDivs"
              />
              <label
                for="division"
                v-if="isDivisionsNotEmpty"
                style="font-weight: bold">
                All
              </label>
              <v-select
                v-model="division_id"
                :options="divisions"
                label="name"
                :value="0"
                :reduce="(division) => division.id"
                placeholder="Select Division"
                class="mt-2"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div :class="stylingClasses" v-if="!isPerDivision && !isAchievements">
          <c-form-group>
            <template #label>Employee</template>
            <template #input>
              <input
                label="All"
                v-if="isUsersNotEmpty"
                id="user"
                class="m-1"
                type="checkbox"
                v-model="checkAllUsers"
                title="Check All Users"
                @change="checkAllEmployees"
              />
              <label
                for="user"
                v-if="isUsersNotEmpty"
                style="font-weight: bold">
                All
              </label>
              <v-select
                v-model="user_id"
                :options="users"
                label="fullname"
                :value="0"
                :reduce="(user) => user.id"
                placeholder="Select Employee"
                class="mt-2"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div :class="stylingClasses">
          <c-form-group>
            <template #label> Product</template>
            <template #input>
              <input
                label="All"
                id="products"
                v-if="isProductsNotEmpty"
                class="m-1"
                type="checkbox"
                v-model="checkAllProducts"
                title="Check All Products"
                @change="checkAllProduct"
              />
              <label
                for="products"
                v-if="isProductsNotEmpty"
                style="font-weight: bold">
                All
              </label>
              <v-select
                v-model="product_id"
                :options="products"
                label="name"
                :value="0"
                :reduce="(product) => product.id"
                placeholder="Select Product"
                class="mt-2"
                multiple
              />
            </template>
          </c-form-group>
        </div>
        <div :class="stylingClasses" v-if="!isAchievements">
          <c-form-group>
            <template #label> Distributor</template>
            <template #input>
              <input
                label="All"
                id="distributors"
                v-if="isDistributorsNotEmpty"
                class="m-1"
                type="checkbox"
                v-model="checkAllDistributors"
                title="Check All Distributors"
                @change="checkAllDistributor"
              />
              <label
                for="distributors"
                v-if="isDistributorsNotEmpty"
                style="font-weight: bold">
                All
              </label>
              <v-select
                v-model="distributor_id"
                :options="distributors"
                label="name" :value="0"
                :reduce="(distributor) => distributor.id"
                placeholder="Select Distributor"
                class="mt-2"
                multiple
              />
            </template>
          </c-form-group>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button
        v-if="line_id.length > 0"
        color="primary"
        class="text-white"
        @click="show"
        style="float: right">
        Show
      </c-button>
    </c-card-footer>
  </c-card>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";

export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      lines: [],
      users: [],
      types: [],
      type_id: 2,
      divisions: [],
      products: [],
      line_id: [],
      distributors: [],
      distributor_id: [],
      user_id: [],
      division_id: [],
      filters: [
        {id: 1, name: "Division"},
        {id: 2, name: "Employee"},
      ],
      report_types: [
        {id: 1, name: "Details"},
        {id: 2, name: "Achievements"}
      ],
      report_type: 1,
      filter: 1,
      product_id: [],
      checkAllLines: false,
      checkAllUsers: false,
      checkAllDivisions: false,
      checkAllProducts: false,
      checkAllDistributors: false,
      from_date: null,
      to_date: null,
      quantity: null,
      dateLimit: null
    };
  },
  computed: {
    isProductsNotEmpty() {
      return this.products.length > 0;
    },
    isDistributorsNotEmpty() {
      return this.distributors.length > 0;
    },
    isLinesNotEmpty() {
      return this.lines.length > 0;
    },
    isDivisionsNotEmpty() {
      return this.divisions.length > 0
    },
    isUsersNotEmpty() {
      return this.divisions.length > 0
    },
    isPerDivision() {
      return this.filter === this.filters[0].id
    },
    isAchievements() {
      return this.report_type === this.report_types[1].id
    },
    stylingClasses() {
      return "col-lg-3 col-md-3 col-sm-8";
    }
  },
  methods: {
    getAllData() {
      this.line_id = [];
      this.division_id = [];
      this.product_id = [];
      this.checkAllUsers = false;
      this.checkAllLines = false;
      this.checkAllDivisions = false;
      this.checkAllProducts = false;
      this.checkAllDistributors = false;
      this.getLineAndSalesTypes();
    },
    setDates(date) {
      this.dateLimit = date
      if (!date) {
        this.from_date = moment().startOf("month").format("YYYY-MM-DD")
        this.to_date = moment().endOf("month").format("YYYY-MM-DD")
        return;
      }

      this.from_date = moment(date).startOf("month").format("YYYY-MM-DD")
      this.to_date = moment(date).endOf("month").format("YYYY-MM-DD")

    },
    async getDateLimit() {
      return axios.get("/api/sales/date/limit")
        .then((res) => this.setDates(res.data.limit))
        .catch(this.showErrorMessage);
    },
    getLineAndSalesTypes() {
      axios
        .post("/api/get-lines-with-dates", {
          from: this.from_date,
          to: this.to_date,
          data: ['lines', 'salesTypes']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.types = response.data.data.salesTypes;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    async initialize() {
      return this.getDateLimit();
    },
    getLineData() {
      axios
        .post(`/api/get-lines-data-with-dates`, {
          lines: this.line_id,
          from: this.from_date,
          to: this.to_date,
          data: ['divisions', 'products', 'users', 'distributors']
        })
        .then((response) => {
          this.divisions = response.data.data.divisions;
          this.products = response.data.data.products;
          this.distributors = response.data.data.distributors;
          this.users = response.data.data.users;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = [];
      this.getLineData();
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = [];
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = [];
    },
    checkAllDistributor() {
      if (this.checkAllDistributors)
        this.distributor_id = this.distributors.map((item) => item.id);
      if (this.checkAllDistributors == false) this.distributor_id = [];
    },
    checkAllProduct() {
      if (this.checkAllProducts)
        this.product_id = this.products.map((item) => item.id);
      if (this.checkAllProducts == false) this.product_id = [];
    },
    show() {

      let saleFilter = {
        lines: this.line_id,
        users: !this.isAchievements ? this.user_id : [],
        divisions: !this.isAchievements ? this.division_id : [],
        filter: !this.isAchievements ? this.filter : this.filters[0].id,
        distributors: !this.isAchievements ? this.distributor_id : [],
        report_type: this.report_type,
        products: this.product_id,
        type: this.type_id,
        view: this.view,
        fromDate: this.from_date,
        toDate: this.to_date,
        quantity: this.quantity,
      };
      this.$emit("getSchedule", {saleFilter});
    },
  },
  created() {
    this.initialize()
      .then(() => this.getLineAndSalesTypes())
  },
};
</script>
