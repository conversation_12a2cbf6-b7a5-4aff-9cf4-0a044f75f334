<template>
  <c-card>
    <c-card-header>Visits Statistics Report</c-card-header>
    <c-card-body>
      <c-tabs>
        <c-tab active>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Main
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-input label="From" type="date" placeholder="From" v-model="from_date"></c-input>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-input label="To" type="date" placeholder="To" v-model="to_date" @input="getAllData"></c-input>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label>
                      Line <span style="color: red">*</span></template>
                    <template #input>
                      <input label="All" v-if="lines.length != 0" id="line" class="m-1" type="checkbox"
                        v-model="checkAllLines" title="Check All lines" @change="checkAllLine" />
                      <label for="line" v-if="lines.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                        placeholder="Select Line" class="mt-2" multiple @input="getLineData" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label>
                      Filter By <span style="color: red">*</span>
                    </template>
                    <template #input>
                      <v-select v-model="filter" :options="filters" label="name" :value="0" :reduce="(line) => line.id"
                        placeholder="Select Filter" class="mt-3" @input="
                          filter == 1 ? (user_id = []) : (division_id = [])
                          " />
                    </template>
                  </c-form-group>
                </div>
                <!-- <div class="col-lg-2 col-md-2 col-sm-8" v-if="checkPermission('all_permissions')">
                  <c-form-group>
                    <template #label> View </template>
                    <template #input>
                      <v-select v-model="view" :options="['Active', 'Inactive']" label="name" :value="0"
                        placeholder="Select View" class="mt-3" @input="getLineData" />
                    </template>
                  </c-form-group>
                </div> -->
                <div class="col-lg-4 col-md-4 col-sm-8" v-if="filter == 1">
                  <c-form-group>
                    <template #label> Division </template>
                    <template #input>
                      <input label="All" id="division" v-if="divisions.length != 0" class="m-1" type="checkbox"
                        v-model="checkAllDivisions" title="Check All Divisions" @change="checkAllDivs" />
                      <label for="division" v-if="divisions.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="division_id" :options="divisions" label="name" :value="0"
                        :reduce="(division) => division.id" placeholder="Select Division" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8" v-if="filter == 2">
                  <c-form-group>
                    <template #label> Positions </template>
                    <template #input>
                      <v-select v-model="position_id" :options="positions" label="name" :value="0"
                        :reduce="(position) => position.id" placeholder="Select Position" class="mt-2"
                        @input="getUsers()" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8" v-if="filter == 2">
                  <c-form-group>
                    <template #label> Employee </template>
                    <template #input>
                      <input label="All" v-if="users.length != 0" id="user" class="m-1" type="checkbox"
                        v-model="checkAllUsers" title="Check All Users" @change="checkAllEmployees" />
                      <label for="user" v-if="users.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="user_id" :options="users" label="fullname" :value="0"
                        :reduce="(user) => user.id" placeholder="Select Employee" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>

              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Account Types &
            Specialities
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Shift </template>
                    <template #input>
                      <input label="All" id="shift" v-if="shifts.length != 0" class="m-1" type="checkbox"
                        v-model="checkAllShifts" title="Check All Shifts" @change="checkAllShift" />
                      <label for="shift" v-if="shifts.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="shift_id" :options="shifts" label="name" placeholder="Select Shift"
                        :reduce="(option) => option.id" class="mt-2" multiple @input="getAccountTypes(shift_id)" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Account Type </template>
                    <template #input>
                      <input label="All" id="type" v-if="types.length != 0" class="m-1" type="checkbox"
                        v-model="allAccountTypes" title="Check All Types" @change="checkAllAccounts" />
                      <label for="type" v-if="types.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="type_id" :options="types" label="name" :value="0" :reduce="(type) => type.id"
                        placeholder="Select Account Type" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label>
                      Count By <span style="color: red">*</span></template>
                    <template #input>
                      <v-select v-model="countById" :options="countBy" label="name" :value="0"
                        :reduce="(countBy) => countBy.id" placeholder="Select Count By" class="mt-3" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label>
                      Per Brick <span style="color: red">*</span></template>
                    <template #input>
                      <v-select v-model="perBrick" :options="['Yes', 'No']" placeholder="Select Per Brick"
                        class="mt-2" />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
      </c-tabs>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" class="text-white" @click="show" style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      division_id: [],
      shift_id: [],
      // view: null,
      countById: 2,
      perBrick: 'No',
      type_id: [],
      user_id: [],
      line_id: [],
      divisions: [],
      shifts: [],
      types: [],
      lines: [],
      positions: [],
      position_id: null,
      users: [],
      filters: [
        { id: 1, name: "Division" },
        { id: 2, name: "Employee" },
      ],
      countBy: [
        { id: 1, name: "Account" },
        { id: 2, name: "Doctor" },
      ],
      filter: 2,
      checkAllDivisions: false,
      allAccountTypes: true,
      checkAllShifts: true,
      checkAllUsers: false,
      checkAllLines: false,
      from_date: moment().startOf("month").format("YYYY-MM-DD"),
      to_date: moment().endOf("month").format("YYYY-MM-DD"),
    };
  },
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: this.from_date,
          to: this.to_date,
          data: ['lines', 'shifts', 'users', 'accountTypes']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.shifts = response.data.data.shifts;
          this.users = response.data.data.users;
          this.types = response.data.data.accountTypes;
          this.type_id = this.types.map((item) => item.id);
          this.shift_id = this.shifts.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getpositions() {
      axios
        .post("/api/get-positions", {
          lines: this.line_id,
          from: this.from_date,
          to: this.to_date,
        })
        .then((res) => {
          this.positions = res.data.data;
        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
    getUsers() {
      axios
        .post("/api/position-users", {
          lines: this.line_id,
          role_id: this.position_id.split("_")[0],
          role_type: this.position_id.split("_")[1],
          from: this.from_date,
          to: this.to_date,
        })
        .then((res) => {
          this.users = res.data.users;
        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
    getAllData() {
      this.line_id = [];
      this.position_id = null;
      this.user_id = [];
      this.division_id = [];
      this.speciality_id = [];
      this.initialize();
    },
    getLineData() {
      axios
        .post(`/api/get-lines-data-with-dates`, {
          lines: this.line_id,
          from: this.from_date,
          to: this.to_date,
          data: ['divisions', 'users']
        })
        .then((response) => {
          this.divisions = response.data.data.divisions;
          this.users = response.data.data.users;
          this.getpositions();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) {
        this.line_id = null;
        this.position_id = null;
        this.user_id = null;
        this.division_id = null;
      }
      this.getLineData();
    },
    getAccountTypes(shift_id) {
      axios
        .post(`/api/get-account-types`, {
          shifts: shift_id,
        })
        .then((response) => {
          this.types = response.data.data;
          this.type_id = this.types.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllAccounts() {
      if (this.allAccountTypes)
        this.type_id = this.types.map((item) => item.id);
      if (this.allAccountTypes == false) this.type_id = null;
    },
    checkAllShift() {
      if (this.checkAllShifts) {
        this.shift_id = this.shifts.map((item) => item.id);
        this.getAccountTypes(this.shift_id);
      }
      if (this.checkAllShifts == false) {
        this.shift_id = [];
        this.getAccountTypes(this.shift_id);
      }
    },

    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = null;
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    show() {
      let visitFilter = {
        lines: this.line_id,
        filter: this.filter,
        position: this.position_id,
        divisions: this.division_id,
        types: this.type_id,
        shifts: this.shift_id,
        allShifts: this.checkAllShifts,
        users: this.user_id,
        selectAllUsers: this.checkAllUsers,
        fromDate: this.from_date,
        toDate: this.to_date,
        countBy: this.countById,
        perBrick: this.perBrick,
      };
      this.$emit("getSchedule", { visitFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>