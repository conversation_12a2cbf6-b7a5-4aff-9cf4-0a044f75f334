<template>
  <c-card>
    <c-card-header>GPS Visits Report</c-card-header>
    <c-card-body>
      <c-tabs>
        <c-tab active>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Main
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Line </template>
                    <template #input>
                      <input label="All" v-if="lines.length != 0" id="line" class="m-1" type="checkbox"
                        v-model="checkAllLines" title="Check All lines" @change="checkAllLine" />
                      <label for="line" v-if="lines.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                        placeholder="Select Lines" class="mt-2" multiple @input="getLineData" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Division </template>
                    <template #input>
                      <input label="All" id="division" v-if="divisions.length != 0" class="m-1" type="checkbox"
                        v-model="checkAllDivisions" title="Check All Divisions" @change="checkAllDivs" />
                      <label for="division" v-if="divisions.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="division_id" :options="divisions" label="name" :value="0"
                        :reduce="(division) => division.id" placeholder="Select Divisions" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Employee </template>
                    <template #input>
                      <input label="All" v-if="users.length != 0" id="user" class="m-1" type="checkbox"
                        v-model="checkAllUsers" title="Check All Users" @change="checkAllEmployees" />
                      <label for="user" v-if="users.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="user_id" :options="users" label="fullname" :value="0"
                        :reduce="(user) => user.id" placeholder="Select Users" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Deviation Range </template>
                    <template #input>
                      <v-select v-model="deviation_num" :options="deviation_range" label="fullname" :value="0"
                        :reduce="(deviation_range) => deviation_range" placeholder="Select Range" class="mt-2" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-8" v-if="planSetting == 'yes' && value == 1">
                  <c-form-group>
                    <template #label> Shift </template>
                    <template #input>
                      <v-select v-model="shift_id" :options="shifts" label="name" placeholder="Select Shift"
                        :reduce="(option) => option.id" class="mt-2" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-input label="From" type="date" placeholder="From" v-model="from_date"></c-input>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-input label="To" type="date" placeholder="To" v-model="to_date"></c-input>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Account Types &
            Specialities
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Account Type </template>
                    <template #input>
                      <input label="All" id="type" v-if="types.length != 0" class="m-1" type="checkbox"
                        v-model="allAccountTypes" title="Check All Types" @change="checkAllAccounts" />
                      <label for="type" v-if="types.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="type_id" :options="types" label="name" :value="0" :reduce="(type) => type.id"
                        placeholder="Select Account Type" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Speciality </template>
                    <template #input>
                      <input label="All" id="speciality" v-if="specialities.length != 0" class="m-1" type="checkbox"
                        v-model="checkAllSpecialities" title="Check All Specialities" @change="checkAllSpech" />
                      <label for="speciality" v-if="specialities.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="speciality_id" :options="specialities" label="name" :value="0"
                        :reduce="(speciality) => speciality.id" placeholder="Select Speciality" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Bricks </template>
                    <template #input>
                      <input label="All" id="brick" v-if="bricks.length != 0" class="m-1" type="checkbox"
                        v-model="checkAllBricks" title="Check All Bricks" @change="checkAllBrick" />
                      <label for="brick" v-if="bricks.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="brick_id" :options="bricks" label="name" :value="0"
                        :reduce="(brick) => brick.id" placeholder="Select Bricks" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
      </c-tabs>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" class="text-white" @click="show" style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      type_id: [],
      speciality_id: [],
      division_id: [],
      user_id: [],
      line_id: [],
      types: [],
      specialities: [],
      divisions: [],
      bricks: [],
      lines: [],
      users: [],
      shifts: [],
      shift_id: null,
      brick_id: null,
      planSetting: null,
      allAccountTypes: true,
      checkAllSpecialities: true,
      checkAllDivisions: false,
      checkAllBricks: false,
      checkAllLines: false,
      checkAllUsers: false,
      from_date: moment().startOf('month').format("YYYY-MM-DD"),
      to_date: moment().endOf('month').format("YYYY-MM-DD"),
      deviation_range: ['0m to 200m', '201m to 500m', '501m to 1000m', 'More than 1000m'],
      deviation_num: ''
    };
  },
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: null,
          to: null,
          data: ['lines', 'users', 'planSetting', 'shifts', 'accountTypes']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.shifts = response.data.data.shifts;
          this.users = response.data.data.users;
          this.planSetting = response.data.data.planSetting;
          this.types = response.data.data.accountTypes;
          this.type_id = this.types.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post(`/api/visitsReport`, { lines: this.line_id })
        .then((response) => {
          this.divisions = response.data.divisions;
          this.bricks = response.data.bricks;
          this.users = response.data.users;
          this.specialities = response.data.specialities;
          this.speciality_id = this.specialities.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = null;
      this.getLineData();
    },
    checkAllAccounts() {
      if (this.allAccountTypes)
        this.type_id = this.types.map((item) => item.id);
      if (this.allAccountTypes == false) this.type_id = null;
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = null;
    },
    checkAllBrick() {
      if (this.checkAllBricks)
        this.brick_id = this.bricks.map((item) => item.id);
      if (this.checkAllBricks == false) this.brick_id = null;
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    checkAllSpech() {
      if (this.checkAllSpecialities)
        this.speciality_id = this.specialities.map((item) => item.id);
      if (this.checkAllSpecialities == false) this.speciality_id = null;
    },
    show() {
      let visitFilter = {
        lines: this.line_id,
        shift: this.shift_id,
        divisions: this.division_id,
        bricks: this.brick_id,
        users: this.user_id,
        types: this.type_id,
        specialities: this.speciality_id,
        visitType: this.value,
        fromDate: this.from_date,
        toDate: this.to_date,
        deviation_num: this.deviation_num
      };
      this.$emit("getSchedule", { visitFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>