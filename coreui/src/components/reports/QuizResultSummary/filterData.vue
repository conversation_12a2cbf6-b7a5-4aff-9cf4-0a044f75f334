<template>
  <c-card>
    <c-card-header>Quiz Result Summary Report</c-card-header>
    <c-card-body>
      <c-card>
        <c-card-body>
          <div class="row">
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-form-group>
                <template #label> Line <span style="color:red">*</span></template>
                <template #input>
                  <input label="All" v-if="lines.length != 0" id="line" class="m-1" type="checkbox"
                    v-model="checkAllLines" title="Check All Lines" @change="checkAllLine" />
                  <label for="line" v-if="lines.length != 0" style="font-weight: bold">All</label>
                  <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                    placeholder="Select Line" class="mt-2" @input="getLineData" multiple />
                </template>
              </c-form-group>
            </div>

            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-form-group>
                <template #label> Employee </template>
                <template #input>
                  <input label="All" v-if="users.length != 0" id="user" class="m-1" type="checkbox"
                    v-model="checkAllUsers" title="Check All Users" @change="checkAllUser" />
                  <label for="user" v-if="users.length != 0" style="font-weight: bold">All</label>
                  <v-select v-model="user_id" :options="users" label="fullname" :value="0" :reduce="(user) => user.id"
                    placeholder="Select Employee" class="mt-2" multiple />
                </template>
              </c-form-group>
            </div>

            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-input label="From Date" type="date" placeholder="Date" v-model="fromDate"></c-input>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-input label="To Date" type="date" placeholder="Date" v-model="toDate"></c-input>
            </div>
          </div>

        </c-card-body>
      </c-card>
    </c-card-body>
    <c-card-footer>
      <c-button v-if="line_id != null" color="primary" class="text-white" @click="show"
        style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import moment from "moment";
import "vue-select/dist/vue-select.css";
// const today = new Date();
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      user_id: [],
      line_id: [],
      users: [],
      lines: [],
      fromDate: moment().startOf('month').format("YYYY-MM-DD"),
      toDate: moment().endOf('month').format("YYYY-MM-DD"),
      checkAllUsers: false,
      checkAllLines: false,
    };
  },
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: null,
          to: null,
          data: ['lines']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post(`/api/get-line-data-quiz-result`, {
          lines: this.line_id
        })
        .then((response) => {
          this.users = response.data.users;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines)
        this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = null;
      this.getLineData();
    },
    checkAllUser() {
      if (this.checkAllUsers)
        this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    format(value, event) {
      return moment(value).format("YYYY-MM-DD");
    },
    format_date: function (value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    show() {
      let quizResultFilter = {
        line: this.line_id,
        users: this.user_id,
        from: this.fromDate,
        to: this.toDate,
      };
      this.$emit("getSchedule", { quizResultFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>