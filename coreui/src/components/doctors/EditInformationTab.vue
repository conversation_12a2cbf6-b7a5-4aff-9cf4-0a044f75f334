<template>
  <CCard no-header>
    <CCardBody>
      <div class="row">
        <div class="col">
          <CInput
            label="Ucode"
            type="text"
            placeholder="Ucode"
            v-model="doctor.ucode"
          >
            <template slot="label">
              Ucode <span style="color: red">*</span>
            </template>
          </CInput>
        </div>
        <div class="col">
          <CInput
            label="Name"
            type="text"
            placeholder="Name"
            v-model="doctor.name"
          >
            <template slot="label">
              Name <span style="color: red">*</span>
            </template>
          </CInput>
        </div>
      </div>

      <div class="row">
        <div class="col">
          <CFormGroup>
            <template #label> Class </template>
            <template #input>
              <v-select
                v-model="doctor.class_id"
                :options="classes"
                label="name"
                :value="0"
                :reduce="(class_item) => class_item.id"
                placeholder="Select option"
                class="mt-2"
              />
            </template>
          </CFormGroup>
        </div>
        <div class="col">
          <CFormGroup>
            <template #label> Level </template>
            <template #input>
              <v-select
                v-model="doctor.level_id"
                :options="levels"
                label="name"
                :value="0"
                :reduce="(level) => level.id"
                placeholder="Select option"
                class="mt-2"
              />
            </template>
          </CFormGroup>
        </div>
        <div class="col">
          <CFormGroup>
            <template #label> Personality Type </template>
            <template #input>
              <v-select
                v-model="doctor.personality_type_id"
                :options="personalitytypes"
                label="name"
                :value="0"
                :reduce="(personalitytype) => personalitytype.id"
                placeholder="Select option"
                class="mt-2"
              />
            </template>
          </CFormGroup>
        </div>
      </div>

      <div class="row">
        <div class="col">
          <CFormGroup>
            <template #label> Speciality </template>
            <template #input>
              <v-select
                v-model="doctor.speciality_id"
                :options="specialities"
                label="name"
                :value="0"
                :reduce="(speciality) => speciality.id"
                placeholder="Select option"
                class="mt-2"
              />
            </template>
          </CFormGroup>
        </div>
        <div class="col">
          <CFormGroup>
            <template #label> Sub Speciality </template>
            <template #input>
              <v-select
                v-model="doctor.sub_speciality_id"
                :options="sub_specialities"
                label="name"
                :value="0"
                :reduce="(sub_speciality) => sub_speciality.id"
                placeholder="Select option"
                class="mt-2"
              />
            </template>
          </CFormGroup>
        </div>
        <div class="col">
          <CFormGroup>
            <template #label> Gender </template>
            <template #input>
              <v-select
                title="Gender"
                v-model="doctor.gender"
                :options="genders"
                placeholder="Select option"
                :reduce="(gender) => gender.value"
                class="mt-2"
              />
            </template>
          </CFormGroup>
        </div>
      </div>

      <div class="row">
        <div class="col">
          <CInput
            label="Email"
            type="email"
            placeholder="Email"
            v-model="doctor.email"
          ></CInput>
        </div>
        <div class="col">
          <CFormGroup
            wrapperClasses="input-group pt-2"
            description="ex. 0 (*************"
          >
            <template #prepend-content>
              <CIcon name="cil-phone" />
            </template>
            <template #label> Mobile </template>
            <template #input>
              <masked-input
                type="mobile"
                name="mobile"
                class="form-control"
                v-model="doctor.mobile"
                :mask="[
                  '0',
                  ' ',
                  '(',
                  /[1-9]/,
                  /\d/,
                  /\d/,
                  ')',
                  ' ',
                  /\d/,
                  /\d/,
                  /\d/,
                  '-',
                  /\d/,
                  /\d/,
                  /\d/,
                  /\d/,
                ]"
                :guide="true"
                placeholderChar="#"
              />
            </template>
          </CFormGroup>
        </div>
        <div class="col">
          <CFormGroup
            wrapperClasses="input-group pt-2"
            description="ex. 02-99999999"
          >
            <template #prepend-content>
              <CIcon name="cil-phone" />
            </template>
            <template #label> Tel. </template>
            <template #input>
              <masked-input
                type="tel"
                name="tel"
                class="form-control"
                v-model="doctor.tel"
                :mask="[
                  0,
                  2,
                  '-',
                  /\d/,
                  /\d/,
                  /\d/,
                  /\d/,
                  /\d/,
                  /\d/,
                  /\d/,
                  /\d/,
                ]"
                :guide="true"
                placeholderChar="#"
              />
            </template>
          </CFormGroup>
        </div>
      </div>

      <div class="row">
        <div class="col">
          <CInput
            label="Date Of Birth"
            type="date"
            placeholder="Date Of Birth"
            v-model="doctor.dob"
          ></CInput>
        </div>
        <div class="col">
          <CInput
            label="Date Of Marriage"
            type="date"
            placeholder="Date Of Marriage"
            v-model="doctor.dom"
          ></CInput>
        </div>
        <div class="col">
          <CInput
            label="Active Date"
            type="date"
            placeholder="Active Date"
            v-model="doctor.active_date"
          ></CInput>
        </div>
        <div class="col">
          <CInput
            label="Inactive Date"
            type="date"
            placeholder="Inactive Date"
            v-model="doctor.inactive_date"
          ></CInput>
        </div>
      </div>
    </CCardBody>
    <CCardFooter>
      <CButton color="primary" @click="update">Update</CButton>
      <CButton color="default" :to="{ name: 'doctors' }">Cancel</CButton>
    </CCardFooter>
  </CCard>
</template>

<script>
import moment from "moment";
import MaskedInput from "vue-text-mask";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    MaskedInput,
    vSelect,
  },
  data() {
    return {
      doctor: {
        ucode: "",
        name: "",
        speciality_id: "",
        sub_speciality_id: "",
        personality_type_id: "",
        level_id: "",
        gender: "",
        tel: "",
        mobile: "",
        email: "",
        dob: "",
        dom: "",
        active_date: "",
        inactive_date: "",
        class_id: "",
      },
      genders: [
        { value: "male", label: "Male" },
        { value: "female", label: "Female" },
      ],
      specialities: [],
      sub_specialities: [],
      levels: [],
      socials: [],
      classes: [],
      personalitytypes: [],
    };
  },
  emits: ["getDoctors"],
  methods: {
    initialize() {
      axios
        .get(`/api/doctors/${this.$route.params.id}/edit`)
        .then((response) => {
          this.doctor_id = response.data.doctor.id;
          this.doctor.id = response.data.doctor.id;
          this.doctor.ucode = response.data.doctor.ucode;
          this.doctor.name = response.data.doctor.name;
          this.doctor.speciality_id = response.data.doctor.speciality_id;
          this.doctor.class_id = response.data.doctor.class_id;
          this.doctor.sub_speciality_id =
            response.data.doctor.sub_speciality_id;
          this.doctor.personality_type_id =
            response.data.doctor.personality_type_id;
          this.doctor.level_id = response.data.doctor.level_id;
          this.doctor.gender = response.data.doctor.gender;
          this.doctor.tel = response.data.doctor.tel;
          this.doctor.mobile = response.data.doctor.mobile;
          this.doctor.email = response.data.doctor.email;
          this.doctor.dob = this.edit_date_format(response.data.doctor.dob);
          this.doctor.dom = this.edit_date_format(response.data.doctor.dom);
          this.doctor.active_date = this.edit_date_format(
            response.data.doctor.active_date
          );
          this.doctor.inactive_date = this.edit_date_format(
            response.data.doctor.inactive_date
          );
          this.specialities = response.data.specialities;
          this.sub_specialities = response.data.sub_specialities;
          this.personalitytypes = response.data.personalitytypes;
          this.levels = response.data.levels;
          this.classes = response.data.classes;
          this.$emit("getDoctors", { doctor: this.doctor_id });
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    format_date (value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    edit_date_format (value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    update() {
      axios
        .put(`/api/doctors/${this.$route.params.id}`, {
          ucode: this.doctor.ucode,
          name: this.doctor.name,
          speciality_id: this.doctor.speciality_id,
          sub_speciality_id: this.doctor.sub_speciality_id,
          personality_type_id: this.doctor.personality_type_id,
          level_id: this.doctor.level_id,
          gender: this.doctor.gender,
          tel: this.doctor.tel,
          mobile: this.doctor.mobile,
          email: this.doctor.email,
          dob: this.doctor.dob ? this.crmDateFormat(this.doctor.dob) : "",
          dom: this.doctor.dom ? this.crmDateFormat(this.doctor.dom) : "",
          active_date: this.crmDateFormat(this.doctor.active_date),
          inactive_date: this.doctor.inactive_date
            ? this.crmDateFormat(this.doctor.inactive_date)
            : "",
          class_id: this.doctor.class_id,
        })
        .then((response) => {
          this.flash("Doctor Updated Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
