<template>
  <div>
    <CCard no-header>
      <CCardBody>
        <div class="row">
          <div class="col">
            <CFormGroup>
              <template #label> Social </template>
              <template #input>
                <v-select
                  v-model="doctor_social.social_id"
                  :options="socials"
                  label="name"
                  :value="0"
                  :reduce="(social) => social.id"
                  placeholder="Select option"
                  class="mt-2"
                />
              </template>
            </CFormGroup>
          </div>
          <div class="col">
            <CInput
              label="Link"
              type="text"
              placeholder="Link"
              v-model="doctor_social.link"
            ></CInput>
          </div>
        </div>
      </CCardBody>
      <CCardFooter>
        <CButton
          color="primary"
          v-if="!socialIsEditing"
          @click="storeDoctorSocial()"
          >Create</CButton
        >
        <CButton
          color="primary"
          v-if="socialIsEditing"
          @click="updateDoctorSocial()"
          >Update</CButton
        >
        <CButton color="default" :to="{ name: 'doctors' }">Cancel</CButton>
      </CCardFooter>
    </CCard>

    <CDataTable
      hover
      striped
      sorter
      tableFilter
      footer
      itemsPerPageSelect
      :items="doctor_socials"
      :fields="socials_fields"
      :items-per-page="1000"
      :active-page="1"
      :responsive="true"
      pagination
      thead-top
    >
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ doctor_socials.length }}
        </td>
      </template>
      <template #actions="{ item }">
        <td>
          <div class="row justify-content-center">
            <CButton
              color="success"
              class="text-white btn-sm mt-2 mr-1"
              v-if="checkPermission('edit_doctor_socials')"
              @click="editDoctorSocial(item.id)"
              ><i class="cil-pencil"></i><CIcon name="cil-pencil"
            /></CButton>
            <c-button
              color="danger"
              class="btn-sm mt-2 mr-1"
              v-if="checkPermission('delete_doctor_socials')"
              @click="
                $root
                  .$confirm('Delete', 'Do you want to delete this record?', {
                    color: 'red',
                    width: 290,
                    zIndex: 200,
                  })
                  .then((confirmed) => {
                    if (confirmed) {
                      deleteDoctorSocial(item);
                    }
                  })
              "
              ><c-icon name="cil-trash"
            /></c-button>
          </div>
        </td>
      </template>
    </CDataTable>
  </div>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  props: {
    doctor_id: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      socials: [],
      socialIsEditing: false,
      doctor_social: {
        id: 0,
        doctor_id: 0,
        social_id: "",
        link: "",
      },
      doctor_socials: [],
      socials_fields: ["id", "doctor_name", "social_name", "link", "actions"],
    };
  },
  methods: {
    initialize() {
      axios
        .get(`/api/doctor-socials`)
        .then((response) => {
          this.socials = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getDoctorSocials() {
      axios
        .get(`/api/get_doctor_socials/${this.doctor_id}`)
        .then((response) => {
          this.doctor_socials = response.data.doctor_socials;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    reset() {
      this.doctor_social = {
        id: 0,
        doctor_id: "",
        social_id: "",
        link: "",
      };
    },
    storeDoctorSocial() {
      axios
        .post("/api/doctor-socials", {
          doctor_id: this.doctor_id,
          social_id: this.doctor_social.social_id,
          link: this.doctor_social.link,
        })
        .then((response) => {
          this.reset();
          this.flash("Doctor Created successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      this.getDoctorSocials();
    },
    editDoctorSocial(id) {
      axios
        .get(`/api/doctor-socials/${id}`)
        .then((response) => {
          this.socialIsEditing = true;
          this.doctor_social.id = response.data.doctor_social.id;
          this.doctor_social.doctor_id = response.data.doctor_social.doctor_id;
          this.doctor_social.social_id = response.data.doctor_social.social_id;
          this.doctor_social.link = response.data.doctor_social.link;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    updateDoctorSocial() {
      axios
        .put(`/api/doctor-socials/${this.doctor_social.id}`, {
          id: this.doctor_social.id,
          doctor_id: this.doctor_social.doctor_id,
          social_id: this.doctor_social.social_id,
          link: this.doctor_social.link,
        })
        .then((response) => {
          this.socialIsEditing = false;
          this.reset();
          this.flash("Doctor Updated successfully");
          this.getDoctorSocials();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removeDoctorSocial(item) {
      const index = this.doctor_socials.findIndex((type) => item.id == type.id);
      this.doctor_socials.splice(index, 1);
    },
    deleteDoctorSocial(item) {
      axios
        .delete(`/api/doctor-socials/${item.id}`)
        .then((res) => {
          this.removeDoctorSocial(item);
          this.flash("Doctor Social Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
      this.initialize();
  },
};
</script>