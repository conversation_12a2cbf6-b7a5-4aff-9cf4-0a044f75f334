<template>
  <validation-observer ref="form" v-if="showReader">
    <c-card>
      <c-card-header> Upload Sales</c-card-header>
      <c-card-body>
        <div class="row">
          <div class="col-6">
            <c-form-group>
              <template #label>
                Distributor <span style="color: red">*</span>
              </template>
              <template #input>
                <validation-provider
                  v-slot="{ errors, dirty, valid }"
                  name="distributor"
                  rules="required"
                >
                  <v-select
                    title="Search for option"
                    v-model="distributor"
                    :options="distributors"
                    label="name"
                    :value="0"
                    :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                    class="mt-2"
                    :reduce="(distributor) => distributor.id"
                  >
                  </v-select>
                  <div class="invalid-feedback">{{ errors[0] }}</div>
                </validation-provider>
              </template>
            </c-form-group>
          </div>
          <div class="col-6">
            <c-form-group>
              <template #label>
                Uploader Mode
              </template>
              <template #input>
                <validation-provider
                  v-slot="{ errors, dirty, valid }"
                  name="uploader_type"
                  rules="required"
                >
                  <v-select
                    title="Search for option"
                    v-model="uploaderMode"
                    :options="uploaderModes"
                    :value="0"
                    :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                    class="mt-2"
                  >
                  </v-select>
                  <div class="invalid-feedback">{{ errors[0] }}</div>
                </validation-provider>
              </template>
            </c-form-group>
          </div>
        </div>
        <div class="row">
          <div class="col-6">
            <c-form-group>
              <template #label>
                Mapping Types <span style="color: red">*</span>
              </template>
              <template #input>
                <validation-provider
                  v-slot="{ errors, dirty, valid }"
                  name="Mapping Type"
                  rules="required"
                >
                  <v-select
                    v-model="type"
                    label="name"
                    :value="0"
                    :error-messages="errors"
                    :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                    class="mt-2"
                    :options="types"
                    :reduce="(type) => type.id"
                  >
                  </v-select>
                  <div class="invalid-feedback">{{ errors[0] }}</div>
                </validation-provider>
              </template>
            </c-form-group>
          </div>
          <div class="col-6">
            <c-form-group>
              <template #label>
                <strong>Date</strong>
              </template>
              <template #input>
                <validation-provider
                  v-slot="{ errors, dirty, valid }"
                  name="Date"
                  rules="required"
                >
                  <c-input
                    type="date"
                    placeholder="Date"
                    v-model="date"
                    :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                    class="mt-2"
                  ></c-input>
                  <div class="invalid-feedback">{{ errors[0] }}</div>
                </validation-provider>
              </template>
            </c-form-group>
          </div>
        </div>
        <div class="row">
          <div class="col-6">
            <c-form-group>
              <template #label>
                <strong>Upload</strong>
              </template>
              <template #input>
                <validation-provider
                  v-slot="{ errors, dirty, valid }"
                  name="File"
                  rules="required"
                >
                  <c-input-file
                    type="file"
                    ref="file"
                    id="file"
                    name="file_name"
                    :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                    @change="handleFileUpload"
                    placeholder="New file"
                  ></c-input-file>
                  <div class="invalid-feedback">{{ errors[0] }}</div>
                </validation-provider>
              </template>
            </c-form-group>
          </div>
        </div>
      </c-card-body>
      <c-card-footer>
        <CButton
          color="primary"
          class="text-white"
          @click="save"
        >
          Save
        </CButton>
        <CButton
          color="default"
          :to="{ name: 'sales' }"
        >
          Cancel
        </CButton>
      </c-card-footer>
    </c-card>
  </validation-observer>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import {ValidationProvider, ValidationObserver, extend} from "vee-validate";
import {required} from "vee-validate/dist/rules";

extend("required", {...required, message: "The {_field_} field is required"});
const mode = "Normal"
export default {
  emits: ["afterRead", "errors"],
  components: {
    vSelect,
    ValidationProvider,
    ValidationObserver,
  },
  props: {
    distributors: {
      type: Array,
      required: true,
    },
    types: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      distributor: null,
      type: null,
      file: null,
      file_name: null,
      date: null,
      showReader: false,
      uploaderMode: mode,
      uploaderModes:[
        mode,
        "Transfer"
      ]
    };
  },
  methods: {
    handleFileUpload(files, event) {
      this.file = event.target.files[0];
      this.file_name = event.target.files[0].name;
    },
    getFormData() {
      let formData = new FormData();
      formData.append("file", this.file);
      formData.append("distributor", this.distributor);
      formData.append("type", this.type);
      formData.append("date", this.date);
      formData.append("mode", this.uploaderMode);
      return formData;
    },
    save() {
      axios
        .post("/api/sales/save", this.getFormData(), {
          headers: {
            "content-type": "multipart/form-data",
          },
        })
        .then((res) => {
          this.flash("Sales Uploaded Successfully . ");
        })
        .catch((err) => this.showErrorMessage(err));
    },
    show() {
      this.showReader = true;
    },
    cancel() {
      this.showReader = false;
    },
    rest() {
      this.distributor = null;
      this.type = null;
      this.date = null;
      this.file = null;
      this.file_name = null;
    },
  },
  created() {
    this.show();
  },
};
</script>
