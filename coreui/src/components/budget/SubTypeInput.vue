<template>
  <div>
    <label>
      <input
        type="checkbox"
        v-model="checked"
        :id="sub.name"
        class="mr-1"
        @change="setValue"
      />
      {{ sub.name }}
    </label>
  </div>
</template>
<script>
export default {
  props: {
    sub: {
      type: Object,
      required: true,
    },
  },
  emits: ["setNewSub"],
  data() {
    return {
      checked: false,
    };
  },
  methods: {
    setValue() {
      this.$emit("setNewSub", { checked: this.checked, subType: this.sub });
    },
  },
};
</script>