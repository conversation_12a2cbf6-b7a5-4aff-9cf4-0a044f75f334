<template>
  <c-card>
    <c-card-header>Create Budget</c-card-header>
    <c-card-body>
      <c-tabs>
        <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-task" /> info
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label>
                      <strong>Line</strong>
                    </template>
                    <template #input>
                      <v-select
                        placeholder="Search for Line"
                        v-model="line_id"
                        :options="lines"
                        label="name"
                        :value="0"
                        class="mt-2"
                        :reduce="(line) => line.id"
                        @input="getLineProducts"
                      ></v-select>
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label>
                      <strong style="padding-right: 10px">Products</strong>
                    </template>
                    <template #input>
                      <input
                        label="All"
                        v-if="products.length != 0"
                        class="m-1"
                        id="product"
                        type="checkbox"
                        v-model="checkAllProducts"
                        title="Check All Products"
                        @change="checkAll"
                      />
                      <label
                        for="product"
                        v-if="products.length != 0"
                        style="font-weight: bold"
                        >All</label
                      >
                      <v-select
                      placeholder="Search for Product"
                        v-model="product_ids"
                        :options="products"
                        label="name"
                        :value="0"
                        multiple
                        class="mt-2"
                        :reduce="(product) => product.id"
                      ></v-select>
                    </template>
                  </c-form-group>
                </div>
                <div
                  v-if="budgetPeriod == 'Month'"
                  class="col-lg-3 col-md-3 col-sm-8"
                >
                  <c-form-group>
                    <template #label>
                      <strong>Months</strong>
                    </template>
                    <template #input>
                      <v-select
                      placeholder="Search for budget time"
                        v-model="month"
                        :options="months"
                        label="name"
                        class="mt-2"
                        :reduce="(month) => month"
                      ></v-select>
                    </template>
                  </c-form-group>
                </div>
                <div
                  v-if="budgetPeriod == 'Quarter' || budgetPeriod == 'Semester'"
                  class="col-lg-3 col-md-3 col-sm-8"
                >
                  <c-form-group>
                    <template #label>
                      <strong>Time</strong>
                    </template>
                    <template #input>
                      <v-select
                      placeholder="Search for budget time"
                        v-model="budgetTime"
                        :options="budgetTimes"
                        label="sub_type"
                        class="mt-2"
                        :reduce="(budgetTime) => budgetTime"
                      ></v-select>
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label>
                      <strong>Year</strong>
                    </template>
                    <template #input>
                      <v-select
                      placeholder="Search for Year"
                        v-model="year"
                        :options="years"
                        label="year"
                        :value="year"
                        class="mt-2"
                        :reduce="(year) => year"
                      ></v-select>
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-task" /> Types
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label>
                      <strong>Budget Type</strong>
                    </template>
                    <template #input>
                      <input
                        label="All"
                        id="type"
                        v-if="types.length != 0"
                        class="m-1"
                        type="checkbox"
                        v-model="checkAllTypes"
                        title="Check All Types"
                        @change="checkAllType"
                      />
                      <label
                        for="type"
                        v-if="types.length != 0"
                        style="font-weight: bold"
                        >All</label
                      >
                      <v-select
                      placeholder="Search for Type"
                        v-model="type_id"
                        :options="types"
                        label="name"
                        :value="0"
                        class="mt-2"
                        :reduce="(type) => type.id"
                        multiple
                        @input="getSubTypes(type_id)"
                      ></v-select>
                    </template>
                  </c-form-group>
                </div>
              </div>
              <c-card class="mt-2" v-if="type_id.length == 1">
                <c-card-header style="background-color: #ebedef">
                  <strong>Sub Request Types</strong>
                </c-card-header>
                <c-card-body class="row">
                  <div
                    class="col-3"
                    style="padding-bottom: 0px; padding-top: 2px"
                    v-for="(sub, index) in subTypes"
                    :key="index"
                  >
                    <sub-type-input
                      :sub="sub"
                      :key="index"
                      @setNewSub="setValue"
                    />
                  </div>
                </c-card-body>
              </c-card>
            </c-card-body>
          </c-card>
        </c-tab>
      </c-tabs>
    </c-card-body>
    <c-card-footer>
      <c-button
        color="primary"
        class="text-white"
        @click="show"
        style="float: right"
        >Show</c-button
      >
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import SubTypeInput from "./SubTypeInput.vue";
export default {
  components: {
    vSelect,
    SubTypeInput,
  },
  data() {
    return {
      lines: [],
      types: [],
      subTypes: [],
      products: [],
      budgetPeriod: null,
      budgetTimes: [],
      budgetTime: null,
      line_id: null,
      product_ids: [],
      months: [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
      ],
      month: null,
      type_id: [],
      sub_type_id: [],
      checkAllTypes: false,
      checkAllProducts: false,
      checkAllSubTypes: false,
      year: null,
    };
  },
  emits: ["getSchedule"],
  methods: {
    setValue({ checked, subType }) {
      if (checked) {
        this.sub_type_id.push(subType);
      } else {
        this.sub_type_id.splice(this.sub_type_id.indexOf(subType), 1);
      }
    },
    initialize() {
      axios
        .get("/api/budget")
        .then((response) => {
          this.lines = response.data.data.lines;
          this.types = response.data.data.types;
          this.budgetPeriod = response.data.data.budgetPeriod;
          this.budgetTimes = response.data.data.dateSettings;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getSubTypes(type_id) {
      axios
        .post(`/api/budget/subtypes`, {
          types: type_id,
        })
        .then((response) => {
          this.subTypes = response.data.data;
          this.sub_type_id = [];
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineProducts() {
      axios
        .get(`/api/get-line-products/${this.line_id}`)
        .then((response) => {
          this.products = response.data.products;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAll() {
      if (this.checkAllProducts) {
        this.product_ids = this.products.map((item) => item.id);
      }
      if (this.checkAllProducts == false) this.product_ids = null;
    },
    checkAllType() {
      if (this.checkAllTypes) {
        this.type_id = this.types.map((item) => item.id);
        this.getSubTypes(this.type_id);
      }
      if (this.checkAllTypes == false) {
        this.type_id = null;
        this.sub_type_id = null;
      }
    },
    checkAllSubType() {
      if (this.checkAllSubTypes) {
        this.sub_type_id = this.subTypes.map((item) => item.id);
      }
      if (this.checkAllSubTypes == false) this.sub_type_id = null;
    },
    show() {
      let budgetFilter = {
        line: this.line_id,
        products: this.product_ids,
        types: this.type_id,
        subTypes: this.sub_type_id,
        month: this.month,
        budgetTime: this.budgetTime,
        year: this.year,
      };
      this.$emit("getSchedule", { budgetFilter });
    },
  },
  computed: {
    years() {
      const year = new Date().getFullYear();
      return Array.from(
        { length: year - 2020 },
        (value, index) => 2021 + index + 1
      );
    },
  },
  created() {
    this.initialize();
  },
};
</script>