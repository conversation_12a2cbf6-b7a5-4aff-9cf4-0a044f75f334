<template>
  <c-card>
    <c-card-header>Create Target</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-3">
          <c-form-group>
            <template #label>
              <strong>Line</strong>
            </template>
            <template #input>
              <v-select
                placeholder="Search for Line"
                v-model="line_id"
                :options="lines"
                label="name"
                :value="0"
                class="mt-2"
                :reduce="(line) => line.id"
                @input="getLineProducts"
              ></v-select>
            </template>
          </c-form-group>
        </div>
        <div class="col-3">
          <c-form-group>
            <template #label>
              <strong style="padding-right:10px;">Products</strong>
            </template>
            <template #input>
              <input label="All" v-if="products.length!=0" class="m-1" type="checkbox" v-model="checkAllProducts" title="Check All Products" @change="checkAll"/>
              <label v-if="products.length!=0" style="font-weight:bold">All</label>
              <v-select
              placeholder="Search for Products"
                v-model="product_ids"
                :options="products"
                label="name"
                :value="0"
                multiple
                class="mt-2"
                :reduce="(product) => product.id"
              ></v-select>
            </template>
          </c-form-group>
        </div>
        <div class="col-3">
          <c-form-group>
            <template #label>
              <strong>Target Type</strong>
            </template>
            <template #input>
              <v-select
                placeholder="Search for Type"
                v-model="type_id"
                :options="types"
                label="name"
                :value="0"
                class="mt-2"
                :reduce="(type) => type.id"
              ></v-select>
            </template>
          </c-form-group>
        </div>
        <div class="col-3">
          <c-form-group>
            <template #label>
              <strong>Year</strong>
            </template>
            <template #input>
              <v-select
                title="Search for Year"
                v-model="year"
                :options="years"
                label="year"
                :value="year"
                class="mt-2"
                :reduce="(year) => year"
              ></v-select>
            </template>
          </c-form-group>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" class="text-white" @click="show" style="float: right"
        >Show</c-button
      >
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      lines: [],
      types: [],
      products:[],
      line_id: null,
      product_ids: [],
      type_id: null,
      checkAllProducts:false,
      year:null
    };
  },
  emits: ["getSchedule"],
  methods: {
    initialize() {
      axios
        .get("/api/target")
        .then((response) => {
          this.lines = response.data.lines;
          this.types = response.data.types;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineProducts(){
      axios
        .get(`/api/get-line-products/${this.line_id}`)
        .then((response) => {
          this.products = response.data.products;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAll(){
      if(this.checkAllProducts){
        this.product_ids=this.products.map(item=>item.id)
      }

      if(this.checkAllProducts==false)
        this.product_ids=null
    },
    show() {
      this.$emit("getSchedule",{line:this.line_id,products:this.product_ids,type:this.type_id,year:this.year});
    },
  },
  computed:{
    years () {
      const year = new Date().getFullYear()
      return Array.from({length: year - 2016}, (value, index) => 2017 + index+1)
    }
  },
  created() {
    this.initialize();
  },
};
</script>