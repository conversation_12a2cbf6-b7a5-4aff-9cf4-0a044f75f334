<template>
  <c-card>
    <c-card-header>
      <div class="row">
        <div class="col-md-5"><strong>Target</strong></div>
        <div class="col-md-2">
          <strong>{{ year }}</strong>
        </div>
        <div class="col-md-5">
          <c-button color="primary" @click="store()" style="float: right"
            >Save</c-button
          >
        </div>
      </div>
    </c-card-header>
    <c-card-body>
      <div class="target" style="height: auto">
        <table class="table table-striped">
          <thead>
            <th scope="col"><label class="text-white">Products</label></th>
            <th scope="col" v-for="(month, index) in months" :key="index">
              <div style="width: 70px" class="text-center text-white">
                <label>{{ formattedMonth(month) | shortFormat }}</label>
              </div>
            </th>
          </thead>
          <tbody>
            <tr v-for="(product, rowIndex) in products" :key="rowIndex">
              <td>
                <label>{{ product.name }}</label>
              </td>
              <td
                style="width: 50px"
                v-for="(month, colIndex) in months"
                :key="colIndex"
              >
                <c-input
                  type="number"
                  v-model="target[rowIndex][colIndex].quantity"
                  @change="
                    setTotal(target[rowIndex][colIndex].quantity, colIndex)
                  "
                />
              </td>
            </tr>
            <tr style="background: #2eb85c">
              <td><label class="text-white">Total</label></td>
              <td
                style="width: 50px"
                v-for="(total, colIndex) in holdData"
                :key="colIndex"
              >
                <c-input type="number" :value="total" disabled />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" class="text-white" @click="store()"
        >Save</c-button
      >
      <c-button color="default" :to="{ name: 'target' }">Cancel</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
// import moment from "moment";
import Table from "../../views/tables/Table.vue";
export default {
  components: { Table },
  props: {
    products: {
      type: Array,
      required: true,
    },
    months: {
      type: Array,
      required: true,
    },
    line: {
      type: Number,
      required: true,
    },
    type: {
      type: Number,
      required: true,
    },
    year: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      target: null,
      totals: [],
      holdData: [],
    };
  },
  methods: {
    initialize() {
      this.target = new Array(this.products.length);
      this.totals = new Array(this.months.length).fill(
        0,
        0,
        this.months.length
      );
      for (let j = 0; j < this.products.length; j++) {
        this.target[j] = [];
      }
      for (let i = 0; i < this.products.length; i++) {
        for (let j = 0; j < this.months.length; j++) {
          let date = new Date(this.months[j]);
          let currentMonth =
            date.getMonth() + 1 < 10
              ? "0" + (date.getMonth() + 1)
              : date.getMonth() + 1;
          this.target[i][j] = {
            id: null,
            line_id: this.line,
            type_id: this.type,
            date: `${date.getFullYear()}-${currentMonth}-01`, //moment(this.months[j]).format("YYYY-MM-DD HH:mm:ss"),
            product_id: this.products[i].id,
            quantity: null,
          };
        }
      }
    },
    formattedMonth(date) {
      const format = new Intl.DateTimeFormat("en-GB", { month: "short" })
        .format;
      return format(new Date(date));
    },
    filterData() {
      return this.target
        .map((item, i) => {
          return item
            .map((el) => {
              if (el.quantity == null) return null;
              return el;
            })
            .filter((item) => item != null);
        })
        .filter((item) => item.length != 0);
    },
    setTotal(value, index) {
      this.totals[index];
      this.totals[index] += +value;
      this.holdData = [];
      this.holdData = this.totals;
    },
    getTargets() {
      axios
        .post("/api/get-target-data", {
          line: this.line,
          type: this.type,
          year: this.year,
          products: this.products,
        })
        .then((response) => {
          response.data.targets.forEach((element) => {
            for (let i = 0; i < this.products.length; i++) {
              for (let j = 0; j < this.months.length; j++) {
                if (
                  element.date === this.target[i][j].date &&
                  element.product_id === this.target[i][j].product_id
                ) {
                  console.log("Data setted");
                  this.target[i][j].id = element.id;
                  this.target[i][j].quantity = element.target;
                  this.setTotal(element.target, j);
                }
              }
            }
          });
          this.show = true;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    store() {
      axios
        .post("/api/target", this.filterData())
        .then((response) => {
          this.flash("The Target is Created Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
    this.getTargets();
  },
};
</script>
<style scoped>
.target {
  overflow-y: auto;
  height: 600px;
}
table {
  display: table;
  white-space: nowrap;
  width: 100%;
}
th {
  opacity: 1;
  position: sticky;
  top: 0;
  background: #2eb85c;
}
.form-group {
  margin-bottom: 0;
}
label {
  font-weight: bolder;
  margin-bottom: 0;
}
</style>