<template>
    <div class="visit-card" :class="{ 'draggable': draggable, 'over-frequency': isOverFrequency }" draggable="true"
        @dragstart="handleDragStart">
        <div class="card-header" :style="{ background: item.group_color || '#f0f0f0' }">
            <span class="group-name">{{ item.group_name || 'No Group' }}</span>
        </div>

        <div class="card-body">
            <h4 class="account-name">{{ item.account }}</h4>
            <div v-if="item.doctor" class="doctor-name">{{ item.doctor }}</div>

            <div class="card-details">
                <div class="detail-item">
                    <span class="detail-label">Area:</span>
                    <span class="detail-value">{{ item.area || 'N/A' }}</span>
                </div>

                <div class="detail-item">
                    <span class="detail-label">Specialty:</span>
                    <span class="detail-value">{{ item.specialty || 'N/A' }}</span>
                </div>

                <div class="detail-item" v-if="item.frequency">
                    <span class="detail-label">Frequency:</span>
                    <span class="detail-value" :class="{ 'warning': isOverFrequency }">
                        {{ item.monthly_actual || 0 }}/{{ item.frequency }}
                    </span>
                </div>

                <div class="detail-item" v-if="item.pharmacies !== undefined">
                    <span class="detail-label">Pharmacies:</span>
                    <span class="detail-value" :class="{ 'warning': item.pharmacies <= 0 }">
                        {{ item.pharmacies }}
                    </span>
                </div>
            </div>
        </div>

        <div v-if="showRemove" class="remove-button" @click.stop="$emit('remove')">
            <i class="fas fa-times"></i>
        </div>
    </div>
</template>

<script>
export default {
    name: 'VisitCard',
    props: {
        item: {
            type: Object,
            required: true
        },
        draggable: {
            type: Boolean,
            default: false
        },
        showRemove: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        isOverFrequency() {
            return this.item.frequency && this.item.monthly_actual >= this.item.frequency;
        }
    },
    methods: {
        handleDragStart(event) {
            // Set data for drag operation
            event.dataTransfer.effectAllowed = 'move';
            event.dataTransfer.setData('text/plain', JSON.stringify(this.item));

            // Emit dragStart event with the item
            this.$emit('dragStart', this.item);

            // Add a class to the element being dragged
            event.target.classList.add('dragging');
        }
    },
    created() {
        console.log('VisitCard item:', this.item);
    },
}
</script>

<style scoped>
.visit-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
    transition: all 0.2s ease;
    display: block;
    width: 100%;
    min-height: 100px;
    margin-bottom: 8px;
}

.visit-card.draggable {
    cursor: grab;
}

.visit-card.draggable:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.visit-card.dragging {
    opacity: 0.6;
    transform: scale(0.95);
}

.visit-card.over-frequency {
    border-left: 3px solid var(--danger);
}

.card-header {
    padding: 8px 12px;
    color: white;
    font-weight: 500;
    font-size: 12px;
    display: block;
    width: 100%;
}

.card-body {
    padding: 12px;
    display: block;
    visibility: visible;
}

.account-name {
    margin: 0 0 4px;
    font-size: 16px;
    font-weight: 600;
    color: var(--dark);
}

.doctor-name {
    font-size: 14px;
    color: var(--gray-dark);
    margin-bottom: 8px;
}

.card-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-top: 8px;
    z-index: 1;
    position: relative;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    width: 100%;
    visibility: visible;
    margin-bottom: 4px;
}

.detail-label {
    color: var(--gray);
    display: inline-block;
    margin-right: 4px;
}

.detail-value {
    font-weight: 500;
    color: var(--dark);
    display: inline-block;
}

.detail-value.warning {
    color: var(--danger);
    font-weight: 600;
}

.remove-button {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 10px;
    transition: all 0.2s ease;
}

.remove-button:hover {
    background: rgba(0, 0, 0, 0.4);
    transform: scale(1.1);
}
</style>