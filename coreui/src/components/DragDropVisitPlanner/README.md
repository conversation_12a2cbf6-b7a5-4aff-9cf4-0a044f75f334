# Drag & Drop Visit Planner

## Overview
The Drag & Drop Visit Planner is a modern, intuitive interface for planning visits using a drag-and-drop approach. It provides a more visual and interactive way to schedule visits compared to the traditional table-based approach.

## Features
- **Intuitive Drag & Drop Interface**: Easily drag visits from the unplanned section to specific days and shifts
- **Visual Calendar View**: See your planned visits organized by day and shift (AM/PM)
- **Search Functionality**: Quickly find specific visits using the search feature
- **Time Management**: Set specific visit times for each planned visit
- **Status Indicators**: Visual indicators for completed vs pending visits
- **Frequency Tracking**: Visual indicators for visits that exceed their frequency

## Components

### Main Components
- **DragDropPlanner**: The main container component that manages the overall planner state
- **PlannerHeader**: Displays the month and save button
- **PlannerStats**: Shows statistics about planned visits
- **UnplannedVisitsList**: Displays draggable visit cards for unplanned visits
- **CalendarView**: Displays the calendar with days and shifts where visits can be dropped
- **VisitCard**: Represents individual visit cards that can be dragged and dropped

## How to Use

### Planning a Visit
1. Select a date range and line using the filter at the top of the page
2. Drag a visit card from the "Unplanned Visits" section on the left
3. Drop it into a specific day and shift (AM or PM) on the calendar
4. Optionally set a specific time for the visit
5. Click "Save" to save your plan

### Removing a Planned Visit
Click the X button on any planned visit card to remove it from the schedule and return it to the unplanned visits section.

### Searching for Visits
Use the search box in the "Unplanned Visits" section to filter the list of available visits.

## Technical Details
This component integrates with the existing visit planning system while providing a more modern user interface. It maintains compatibility with the current API schema and data structure.

### Data Flow
- The planner loads visit data from the same API endpoints as the traditional planner
- When saving, it formats the data to match the expected API format
- All existing functionality (shifts, time selection, etc.) is preserved

## Compatibility
The Drag & Drop Planner is fully compatible with the existing plan visit functionality. You can switch between the traditional table view and the drag-and-drop interface without any data loss.