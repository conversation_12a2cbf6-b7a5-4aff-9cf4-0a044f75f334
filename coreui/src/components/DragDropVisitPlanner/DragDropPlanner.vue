<template>
    <div class="drag-drop-planner">
        <filter-data @Schedule="initialize" />
        <transition name="fade">
            <div v-if="items.length != 0" class="dashboard-card">
                <planner-header :month="month" :saving="saving" @save="store" />

                <planner-stats :total-list="items.length" :planned-visits="plannedVisitsCount"
                    :filtered-visits="filteredItems.length" />

                <div class="planner-layout">
                    <unplanned-visits-list :items="filteredUnplannedItems" :searchTerm.sync="searchTerm"
                        @clearSearch="clearSearch" @update:searchTerm="handleSearchInput"
                        @dragStart="handleDragStart" />
                    <calendar-view :dates="dates" :selected="selected" :planSelectedStatus="planSelectedStatus"
                        :plan_shift="plan_shift" :plan_time="plan_time" :plan_level="plan_level"
                        @dropVisit="handleDropVisit" @removeVisit="handleRemoveVisit" @updateTime="updateTime"
                        @updateShift="updateShift" />
                </div>

                <div class="card-footer">
                    <button class="btn-save" @click="store" :disabled="saving">
                        <span v-if="!saving">Save</span>
                        <span v-else class="spinner"></span>
                    </button>
                </div>
            </div>
        </transition>

        <div v-if="items.length === 0 && initialized" class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-calendar-times"></i>
            </div>
            <h3>No Visits to Plan</h3>
            <p>Use the filters above to select a date range and line to plan visits.</p>
        </div>
    </div>
</template>

<script>
import FilterData from "../../components/dailyvisits/FilterData.vue";
import PlannerHeader from "./PlannerHeader.vue";
import PlannerStats from "./PlannerStats.vue";
import UnplannedVisitsList from "./UnplannedVisitsList.vue";
import CalendarView from "./CalendarView.vue";

export default {
    name: 'DragDropPlanner',
    components: {
        FilterData,
        PlannerHeader,
        PlannerStats,
        UnplannedVisitsList,
        CalendarView
    },
    data() {
        return {
            items: [],
            fields: [],
            fieldsWithoutDates: [],
            dates: [],
            selected: {}, // This will track planned visits by date
            planSelectedStatus: {}, // Track checkbox state independently
            plan_time: null,
            plan_shift: null,
            plan_level: { level: null },
            selectedLine: null,
            oldVisits: {},
            month: null,
            from: null,
            to: null,
            initialized: false,
            saving: false,
            searchTerm: "",
            currentDragItem: null // Track the item being dragged
        };
    },
    computed: {
        plannedVisitsCount() {
            let count = 0;
            Object.keys(this.selected).forEach(date => {
                Object.keys(this.selected[date]).forEach(index => {
                    if (this.selected[date][index] && this.selected[date][index].line_id !== null) {
                        count++;
                    }
                });
            });
            return count;
        },
        filteredItems() {
            if (!this.searchTerm.trim()) {
                return [...this.items];
            }

            const searchTermLower = this.searchTerm.toLowerCase();
            return this.items.filter(item => {
                return (
                    (item.account && item.account.toLowerCase().includes(searchTermLower)) ||
                    (item.doctor && item.doctor.toLowerCase().includes(searchTermLower)) ||
                    (item.group_name && item.group_name.toLowerCase().includes(searchTermLower)) ||
                    (item.specialty && item.specialty.toLowerCase().includes(searchTermLower)) ||
                    (item.area && item.area.toLowerCase().includes(searchTermLower))
                );
            });
        },
        filteredUnplannedItems() {
            // Filter out items that are already planned
            return this.filteredItems.filter(item => {
                const id = this.concatLevel(item);
                let isPlanned = false;

                // Check if this item is planned on any date
                for (const date of this.dates) {
                    if (this.selected[date] &&
                        this.selected[date][id] &&
                        this.selected[date][id].line_id !== null &&
                        this.planSelectedStatus[date] &&
                        this.planSelectedStatus[date][id] === true) {
                        isPlanned = true;
                        break;
                    }
                }

                return !isPlanned;
            });
        }
    },
    methods: {
        initialize(planFilter) {
            this.initialized = false;
            this.from = planFilter.fromDate;
            this.to = planFilter.toDate;
            this.selectedLine = planFilter.line;

            axios
                .post("/api/get-plan-schedule", {
                    planFilter
                })
                .then(response => {
                    this.parepareItemsAndFields(response.data);
                    this.initialized = true;
                })
                .catch(error => {
                    this.showErrorMessage(error);
                    this.initialized = true;
                });
        },

        parepareItemsAndFields(data) {
            this.items = data.data || [];
            this.plan_shift = data.plan_shift;
            this.plan_time = data.plan_time;
            this.plan_level = data.plan_level || { level: null };
            this.oldVisits = data.oldVisits || {};
            this.month = data.month;
            this.dates = data.dates || [];
            this.fieldsWithoutDates = data.fields || [];
            this.fields = [...this.fieldsWithoutDates, ...this.dates];
            const newSelected = {};
            const newPlanSelectedStatus = {};

            this.searchTerm = '';

            this.dates.forEach(date => {
                newSelected[date] = {};
                newPlanSelectedStatus[date] = {};
                this.items.forEach(item => {
                    const id = this.concatLevel(item);
                    // Initialize first to ensure entries exist
                    newSelected[date][id] = null; // Or appropriate default structure if needed
                    newPlanSelectedStatus[date][id] = false;

                    let foundOldVisit = false;
                    if (this.oldVisits[date]) {
                        this.oldVisits[date].forEach(oldVisit => {
                            if (
                                (this.plan_level.level == "Doctor" &&
                                    oldVisit.account_dr_id == item.account_dr_id) ||
                                (this.plan_level.level == "Account" &&
                                    oldVisit.account_id == item.id)
                            ) {
                                // Overwrite initialization if found in old visits
                                newSelected[date][id] = this.defaultPlan(date, oldVisit);
                                newPlanSelectedStatus[date][id] = true;
                                foundOldVisit = true;
                            }
                        });
                    }
                });
            });
            this.selected = newSelected;
            this.planSelectedStatus = newPlanSelectedStatus;
        },

        store() {
            if (this.saving) return;

            this.saving = true;
            const visits = [];

            Object.keys(this.selected).forEach(date => {
                Object.keys(this.selected[date]).forEach(id => {
                    if (this.planSelectedStatus[date][id] && this.selected[date][id].line_id !== null) {
                        visits.push({
                            ...this.selected[date][id],
                            date: date
                        });
                    }
                });
            });

            axios
                .post("/api/store-plan-schedule", {
                    visits: visits,
                    line_id: this.selectedLine,
                    from: this.from,
                    to: this.to
                })
                .then(response => {
                    this.$toaster.success("Plan saved successfully");
                    this.saving = false;
                })
                .catch(error => {
                    this.showErrorMessage(error);
                    this.saving = false;
                });
        },

        defaultPlan(date, oldVisit = null) {
            if (oldVisit) {
                return {
                    id: oldVisit.id,
                    account_id: oldVisit.account_id,
                    account_dr_id: oldVisit.account_dr_id,
                    line_id: oldVisit.line_id,
                    shift_id: oldVisit.shift_id,
                    time: oldVisit.time,
                    old: oldVisit.old || false
                };
            }

            return {
                id: null,
                account_id: null,
                account_dr_id: null,
                line_id: null,
                shift_id: "1", // Default to AM shift
                time: null,
                old: false
            };
        },

        concatLevel(item) {
            return item.account_dr_id ? `${item.id}_${item.account_dr_id}` : String(item.id);
        },

        handleSearchInput(value) {
            this.searchTerm = value;
        },

        clearSearch() {
            this.searchTerm = "";
        },

        handleDragStart(item) {
            this.currentDragItem = item;
        },

        handleDropVisit(date, shift) {
            if (!this.currentDragItem) return;

            const item = this.currentDragItem;
            const id = this.concatLevel(item);

            // Update the selected and planSelectedStatus objects
            if (!this.selected[date]) {
                this.$set(this.selected, date, {});
            }

            if (!this.planSelectedStatus[date]) {
                this.$set(this.planSelectedStatus, date, {});
            }

            // Create a new plan for this item
            const newPlan = {
                id: null,
                account_id: this.plan_level.level === "Account" ? item.id : null,
                account_dr_id: this.plan_level.level === "Doctor" ? item.account_dr_id : null,
                line_id: this.selectedLine,
                shift_id: shift || "1", // Default to AM if not specified
                time: null,
                old: false
            };

            this.$set(this.selected[date], id, newPlan);
            this.$set(this.planSelectedStatus[date], id, true);

            this.currentDragItem = null;
        },

        handleRemoveVisit(date, item) {
            const id = this.concatLevel(item);

            if (this.selected[date] && this.selected[date][id]) {
                // If it's an old visit, we can't remove it
                if (this.selected[date][id].old) return;

                // Otherwise, mark it as unplanned
                this.$set(this.planSelectedStatus[date], id, false);
                this.$set(this.selected[date], id, this.defaultPlan(date));
            }
        },

        updateTime(item, date, timeValue) {
            const id = this.concatLevel(item);
            if (this.selected[date] && this.selected[date][id]) {
                this.$set(this.selected[date][id], 'time', timeValue);
            }
        },

        updateShift(item, date, shiftValue) {
            const id = this.concatLevel(item);
            if (this.selected[date] && this.selected[date][id]) {
                this.$set(this.selected[date][id], 'shift_id', shiftValue);
            }
        },

        showErrorMessage(error) {
            let message = "An error occurred";
            if (error.response && error.response.data && error.response.data.message) {
                message = error.response.data.message;
            }
            this.$toaster.error(message);
        }
    }
};
</script>

<style scoped>
.drag-drop-planner {
    width: 100%;
    height: 100%;
}

.dashboard-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 24px;
}

.planner-layout {
    display: flex;
    height: calc(100vh - 300px);
    min-height: 500px;
}

.card-footer {
    padding: 16px 24px;
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.btn-save {
    background: var(--primary);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 24px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-save:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-save:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.empty-icon {
    font-size: 48px;
    color: var(--gray);
    margin-bottom: 16px;
}

.empty-state h3 {
    margin: 0 0 8px;
    font-size: 20px;
    color: var(--dark);
}

.empty-state p {
    margin: 0;
    color: var(--gray);
    max-width: 400px;
}

/* Fade transition */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s;
}

.fade-enter,
.fade-leave-to {
    opacity: 0;
}
</style>