<template>
    <div class="unplanned-visits-list">
        <div class="list-header">
            <h3>Unplanned Visits</h3>
            <div class="search-input-wrapper">
                <i class="fas fa-search search-icon"></i>
                <input type="text" :value="searchTerm" @input="$emit('update:searchTerm', $event.target.value)"
                    class="search-input" placeholder="Search visits..." />
                <button v-if="searchTerm" @click="$emit('clearSearch')" class="clear-search-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <div class="visits-list" v-if="items.length > 0">
            <visit-card v-for="(item, index) in items" :key="`unplanned-${index}`" :item="item" :draggable="true"
                @dragStart="$emit('dragStart', item)" />
        </div>

        <div v-else class="empty-list">
            <p v-if="searchTerm">No visits match your search</p>
            <p v-else>All visits have been planned</p>
        </div>
    </div>
</template>

<script>
import VisitCard from './VisitCard.vue';

export default {
    name: 'UnplannedVisitsList',
    components: {
        VisitCard
    },
    props: {
        items: {
            type: Array,
            required: true
        },
        searchTerm: {
            type: String,
            required: true
        }
    },
    emits: ['update:searchTerm', 'clearSearch', 'dragStart']
}
</script>

<style scoped>
.unplanned-visits-list {
    width: 350px;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.list-header {
    padding: 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.list-header h3 {
    margin: 0 0 12px;
    font-size: 16px;
    font-weight: 600;
    color: var(--dark);
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 12px;
    color: var(--gray);
    font-size: 14px;
}

.search-input {
    width: 100%;
    padding: 10px 12px 10px 36px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(58, 123, 242, 0.2);
}

.clear-search-btn {
    position: absolute;
    right: 10px;
    background: none;
    border: none;
    color: var(--gray);
    cursor: pointer;
    font-size: 14px;
    padding: 4px;
}

.clear-search-btn:hover {
    color: var(--dark);
}

.visits-list {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.empty-list {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--gray);
    font-style: italic;
    padding: 20px;
    text-align: center;
}
</style>