<template>
  <div>
    <CCard no-header>
      <CCardBody>
        <div class="row">
          <div class="col">
            <CFormGroup>
              <template #label> Line </template>
              <template #input>
                <v-select v-model="account_doctor.line_id" :options="lines" label="name" :value="0"
                  :reduce="(line) => line.id" placeholder="Select Line" class="mt-2" />
              </template>
            </CFormGroup>
          </div>
          <div class="col">
            <CFormGroup>
              <template #label> Doctor </template>
              <template #input>
                <v-select v-model="account_doctor.doctor_id" :options="doctors" label="doctor" :value="0"
                  :reduce="(doctor) => doctor.id" placeholder="Select Doctor" class="mt-2" :filterable="false"
                  @search="onSearch">
                  <!-- <template #selected-option="{ id, name }">
                    <div style="display: flex; align-items: baseline">
                      <strong>{{ name }}</strong>
                      <em style="margin-left: 0.5rem">by {{ author.firstName }} {{ author.lastName }}</em>
                    </div>
                  </template> -->
                  <!-- <li slot="list-footer" class="pagination">
                    <button :disabled="!hasPrevPage" @click="offset -= limit">Prev</button>
                    <button :disabled="!hasNextPage" @click="offset += limit">Next</button>
                  </li> -->
                </v-select>
              </template>
            </CFormGroup>
          </div>
          <div class="col">
            <CInput label="From" type="date" placeholder="From" v-model="account_doctor.from_date"></CInput>
          </div>
          <div class="col">
            <CInput label="To" type="date" placeholder="To" v-model="account_doctor.to_date" :min="fromDate"></CInput>
          </div>
        </div>
        <div class="row" v-if="AccountDoctorIsEditing">
          <div class="col">
            <CFormGroup>
              <template #label> Speciality </template>
              <template #input>
                <v-select v-model="speciality_id" :options="specialities" label="name" :value="0"
                  :reduce="(speciality) => speciality.id" placeholder="Select Speciality" class="mt-2" />
              </template>
            </CFormGroup>
          </div>
          <div class="col">
            <CFormGroup>
              <template #label> Class </template>
              <template #input>
                <v-select v-model="class_id" :options="classes" label="name" :value="0" placeholder="Select Class"
                  :reduce="(classe) => classe.id" class="mt-2" />
              </template>
            </CFormGroup>
          </div>
        </div>
      </CCardBody>
      <CCardFooter>
        <CButton class="text-white" color="primary" v-if="!AccountDoctorIsEditing" @click="store">Create</CButton>
        <CButton class="text-white" color="primary" v-if="AccountDoctorIsEditing" @click="update">Update</CButton>
        <CButton color="default" :to="{ name: 'accounts' }">Cancel</CButton>
      </CCardFooter>
    </CCard>

    <CDataTable hover striped sorter tableFilter footer itemsPerPageSelect :items="accountDoctors"
      :fields="accountDoctors_fields" :items-per-page="1000" :active-page="1" :responsive="true" pagination thead-top>
      <template #from_date="{ item }">
        <td>{{ format_date(item.from_date) }}</td>
      </template>
      <template #to_date="{ item }">
        <td>{{ format_date(item.to_date) }}</td>
      </template>
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ accountDoctors.length }}
        </td>
      </template>
      <template #actions="{ item }">
        <td>
          <div class="row justify-content-center">
            <CButton color="success" v-if="checkPermission('edit_account_doctors')" class="text-white btn-sm mt-2 mr-1"
              @click="edit(item.id)"><i class="cil-pencil"></i>
              <CIcon name="cil-pencil" />
            </CButton>
            <c-button color="danger" class="btn-sm mt-2 mr-1" v-if="checkPermission('delete_account_doctors')" @click="
              $root
                .$confirm('Delete', 'Do you want to delete this record?', {
                  color: 'red',
                  width: 290,
                  zIndex: 200,
                })
                .then((confirmed) => {
                  if (confirmed) {
                    deleteAccountDoctor(item);
                  }
                })
              "><c-icon name="cil-trash" /></c-button>
          </div>
        </td>
      </template>
    </CDataTable>
  </div>
</template>
<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import _ from '../../mixins/debounce'
export default {
  components: {
    vSelect,
  },
  props: {
    account_id: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      fromDate: new Date().toISOString().slice(0, 10),
      lines: [],
      doctors: [],
      classes: [],
      specialities: [],
      class_id: null,
      speciality_id: null,
      accountDoctors: [],
      account_doctor: {
        id: 0,
        account_id: 0,
        line_id: "",
        doctor_id: "",
        from_date: "",
        to_date: "",
      },
      AccountDoctorIsEditing: false,
      accountDoctors_fields: [
        "id",
        "account_lines_id",
        "account",
        "line",
        // "division",
        // "brick",
        "doctor",
        "speciality",
        "doc_class",
        "from_date",
        "to_date",
        "actions",
      ],
      search: '',
      offset: 0,
      limit: 10,
      page: 1,
      total: 0,
      totalData: 0,
      previousTimeout: null,
      // search: null,
    };
  },
  // computed: {
  //   filtered() {
  //     return this.doctors.filter((doctor) =>
  //       doctor.name.toLocaleLowerCase().includes(this.search.toLocaleLowerCase())
  //     )
  //   },
  //   paginated() {
  //     return this.filtered.slice(this.offset, this.limit + this.offset)
  //   },
  //   hasNextPage() {
  //     const nextOffset = this.offset + this.limit
  //     return Boolean(
  //       this.filtered.slice(nextOffset, this.limit + nextOffset).length
  //     )
  //   },
  //   hasPrevPage() {
  //     const prevOffset = this.offset - this.limit
  //     return Boolean(
  //       this.filtered.slice(prevOffset, this.limit + prevOffset).length
  //     )
  //   },
  // },
  methods: {
    onSearch(search, loading) {
      if (search.length) {
        loading(true);
        this.search = search;
        this.find(loading,this);
      }
    },
    find: _.debounce((loading,vm) => {
      vm.getDoctors().then(() => loading(false))
    }, 500),
    format_date: function (value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    edit_date_format: function (value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    initialize() {
      return axios
        .get(`/api/account_doctors`)
        .then((response) => {
          this.lines = response.data.lines;
          // this.doctors = response.data.doctors;
          this.classes = response.data.classes;
          this.specialities = response.data.specialities;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getDoctors() {
      return axios
        .post("/api/doctors/index?page=" + this.page, {
          query: this.search,
        })
        .then((res) => {
          this.doctors = res.data.data.data;
          this.total = res.data.data.last_page;
          this.totalData = res.data.data.total;
        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
    timeoutClear() {
      if (this.previousTimeout) {
        clearTimeout(this.previousTimeout);
        this.previousTimeout = null;
      }
    },
    store() {
      axios
        .post("/api/account_doctors", {
          account_id: this.account_id,
          doctor_id: this.account_doctor.doctor_id,
          line_id: this.account_doctor.line_id,
          from_date: this.crmDateFormat(this.account_doctor.from_date),
          to_date: this.account_doctor.to_date ? this.crmDateFormat(this.account_doctor.to_date) : "",
        })
        .then((response) => {
          this.account_doctor = {
            account_id: this.account_doctor.account_id,
            doctor_id: "",
            line_id: "",
            from_date: "",
            to_date: "",
          };
          this.flash("Account Created Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      //Refresh table
      this.getAccountDoctors();
    },
    getAccountDoctors() {
      axios
        .get(`/api/getaccountdoctors/${this.account_id}`)
        .then((response) => {
          this.accountDoctors = response.data.accountDoctors;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    edit(id) {
      axios
        .get(`/api/account_doctors/${id}`)
        .then((response) => {
          this.AccountDoctorIsEditing = true;
          this.account_doctor.id = response.data.account_doctor.id;
          this.account_doctor.account_id =
            response.data.account_doctor.account_id;
          this.account_doctor.line_id = response.data.account_doctor.line_id;
          this.speciality_id = response.data.account_doctor.speciality_id;
          this.class_id = response.data.account_doctor.class_id;
          this.account_doctor.doctor_id =
            response.data.account_doctor.doctor_id;
          this.account_doctor.from_date = this.edit_date_format(
            response.data.account_doctor.from_date
          );
          this.account_doctor.to_date = this.edit_date_format(
            response.data.account_doctor.to_date
          );
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    update() {
      axios
        .put(`/api/account_doctors/${this.account_doctor.id}`, {
          id: this.account_doctor.id,
          account_id: this.account_doctor.account_id,
          doctor_id: this.account_doctor.doctor_id,
          line_id: this.account_doctor.line_id,
          class_id: this.class_id,
          speciality_id: this.speciality_id,
          from_date: this.crmDateFormat(this.account_doctor.from_date),
          to_date: this.account_doctor.to_date ? this.crmDateFormat(this.account_doctor.to_date) : "",
        })
        .then((response) => {
          this.AccountDoctorIsEditing = false;
          this.flash("Accound Doctor Updated Successfully");
          this.account_doctor = {
            id: 0,
            account_id: this.account_doctor.account_id,
            doctor_id: "",
            line_id: "",
            from_date: "",
            to_date: "",
          };
          this.getAccountDoctors();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removeAccountDoctor(item) {
      const index = this.accountDoctors.findIndex((type) => item.id == type.id);
      this.accountDoctors.splice(index, 1);
    },
    deleteAccountDoctor(item) {
      axios
        .delete(`/api/account_doctors/${item.id}`)
        .then((response) => {
          this.flash("Account Doctor Deleted Successfully");
          this.removeAccountDoctor(item);
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  // watch: {
  //   search() {
  //     this.timeoutClear();
  //     const id = setTimeout(() => this.getDoctors(), 500);
  //     this.previousTimeout = id;
  //   },
  // },
  created() {
    this.initialize().then(() => this.getDoctors())
    this.getAccountDoctors();
  },
};
</script>

<style scoped>
.pagination {
  display: flex;
  margin: 0.25rem 0.25rem 0;
}

.pagination button {
  flex-grow: 1;
}

.pagination button:hover {
  cursor: pointer;
}
</style>