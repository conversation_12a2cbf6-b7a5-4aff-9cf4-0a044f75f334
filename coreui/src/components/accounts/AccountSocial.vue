<template>
  <div>
    <CCard no-header>
      <CCardBody>
        <div class="row">
          <div class="col">
            <CFormGroup>
              <template #label> Social </template>
              <template #input>
                <v-select
                  v-model="account_social.social_id"
                  :options="socials"
                  label="name"
                  :value="0"
                  :reduce="(social) => social.id"
                  placeholder="Select option"
                  class="mt-2"
                />
              </template>
            </CFormGroup>
          </div>
          <div class="col">
            <CInput
              label="Link"
              type="text"
              placeholder="Link"
              v-model="account_social.link"
            ></CInput>
          </div>
        </div>
      </CCardBody>
      <CCardFooter>
        <CButton
          class="text-white"
          color="primary"
          v-if="!socialIsEditing"
          @click="store()"
          >Create</CButton
        >
        <CButton
          class="text-white"
          color="primary"
          v-if="socialIsEditing"
          @click="update()"
          >Update</CButton
        >
        <CButton color="default" :to="{name: 'accounts' }">Cancel</CButton>
      </CCardFooter>
    </CCard>

    <CDataTable
      hover
      striped
      sorter
      tableFilter
      footer
      itemsPerPageSelect
      :items="account_socials"
      :fields="socials_fields"
      :items-per-page="1000"
      :active-page="1"
      :responsive="true"
      table-filter
      pagination
      thead-top
    >
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ account_socials.length }}
        </td>
      </template>
      <template #actions="{ item }">
        <td>
          <div class="row justify-content-center">
            <CButton
              color="success"
              v-if="checkPermission('edit_account_socials')"
              class="text-white btn-sm mt-2 mr-1"
              @click="edit(item.id)"
              ><i class="cil-pencil"></i><CIcon name="cil-pencil"
            /></CButton>
            <c-button
                  color="danger"
                  v-if="checkPermission('delete_account_socials')"
                  class="btn-sm mt-2 mr-1"
                  @click="
                    $root
                      .$confirm(
                        'Delete',
                        'Do you want to delete this record?',
                        {
                          color: 'red',
                          width: 290,
                          zIndex: 200,
                        }
                      )
                      .then((confirmed) => {
                        if (confirmed) {
                          deleteAccountSocial(item);
                        }
                      })
                  "
                  ><c-icon name="cil-trash"
                /></c-button>
          </div>
        </td>
      </template>
    </CDataTable>
  </div>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  props: {
    account_id: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      account_social: {
        id: 0,
        account_id: 0,
        social_id: "",
        link: "",
      },
      socials: [],
      account_socials: [],
      socials_fields: ["id", "account_name", "social_name", "link", "actions"],
      social_id: "",
      socialIsEditing: false,
    };
  },
  methods: {
    initialize() {
      axios
        .get(`/api/account-socials`)
        .then((response) => {
          this.socials = response.data.socials;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getAccountSocials() {
      axios
        .get(`/api/get_account_socials/${this.account_id}`)
        .then((response) => {
          this.account_socials = response.data.account_socials;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    store() {
      axios
        .post("/api/account-socials", {
          account_id: this.account_id,
          social_id: this.account_social.social_id,
          link: this.account_social.link,
        })
        .then((response) => {
          this.account_social = {
            id: 0,
            account_id: this.account_id,
            social_id: "",
            link: "",
          };
          this.flash("Account Social Created Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      //Refresh table
      this.getAccountSocials();
    },
    edit(id) {
      axios
        .get(`/api/account-socials/${id}`)
        .then((response) => {
          this.socialIsEditing = true;
          this.account_social.id = response.data.account_social.id;
          this.account_social.account_id =
            response.data.account_social.account_id;
          this.account_social.social_id =
            response.data.account_social.social_id;
          this.account_social.link = response.data.account_social.link;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    update() {
      axios
        .put(`/api/account-socials/${this.account_social.id}`, {
          id: this.account_social.id,
          account_id: this.account_social.account_id,
          social_id: this.account_social.social_id,
          link: this.account_social.link,
        })
        .then((response) => {
          this.socialIsEditing = false;
          this.account_social = {
            id: 0,
            account_id: this.account_social.account_id,
            social_id: "",
            link: "",
          };
          this.flash("Account Social Updated Successfully");
          this.getAccountSocials();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removeAccountSocial(item) {
      const index = this.account_socials.findIndex((type) => item.id == type.id);
      this.account_socials.splice(index, 1);
    },
    deleteAccountSocial(item) {
      axios
        .delete(`/api/account-socials/${item.id}`)
        .then((response) => {
          this.flash("Account Social Deleted Successfully");
          this.removeAccountSocial(item);
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
      this.initialize();
      this.getAccountSocials();
  },
};
</script>

<style>
</style>