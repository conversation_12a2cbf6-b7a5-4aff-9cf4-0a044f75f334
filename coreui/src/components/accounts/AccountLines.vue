<template>
  <div>
    <CCard no-header>
      <CCardBody>
        <div class="row">
          <div class="col">
            <CFormGroup>
              <template #label> Line </template>
              <template #input>
                <v-select
                  v-model="account_line.line_id"
                  :options="lines"
                  label="name"
                  :value="0"
                  :reduce="(line) => line.id"
                  placeholder="Select option"
                  class="mt-2"
                  @input="getLineDivisions()"
                />
              </template>
            </CFormGroup>
          </div>
          <div class="col">
            <CFormGroup>
              <template #label> Line Division </template>
              <template #input>
                <v-select
                  v-model="account_line.line_division_id"
                  :options="line_divisions"
                  label="name"
                  :value="0"
                  :reduce="(line_division) => line_division.id"
                  placeholder="Select option"
                  class="mt-2"
                  @input="getLineDivisionBricks()"
                />
              </template>
            </CFormGroup>
          </div>
          <div class="col">
            <CFormGroup>
              <template #label> Brick </template>
              <template #input>
                <v-select
                  v-model="account_line.brick_id"
                  :options="line_bricks"
                  label="name"
                  :value="0"
                  :reduce="(line_brick) => line_brick.id"
                  placeholder="Select option"
                  class="mt-2"
                />
              </template>
            </CFormGroup>
          </div>
        </div>

        <div class="row">
          <div class="col">
            <CFormGroup>
              <template #label> Class </template>
              <template #input>
                <v-select
                  v-model="account_line.class_id"
                  :options="line_classess_names"
                  label="name"
                  :value="0"
                  :reduce="(line_classess_name) => line_classess_name.id"
                  placeholder="Select option"
                  class="mt-2"
                />
              </template>
            </CFormGroup>
          </div>
          <div class="col">
            <CInput
              label="From"
              type="date"
              placeholder="From"
              v-model="account_line.from_date"
            ></CInput>
          </div>
          <div class="col">
            <CInput
              label="To"
              type="date"
              placeholder="To"
              v-model="account_line.to_date"
              :min="fromDate"
            ></CInput>
          </div>
        </div>
      </CCardBody>
      <CCardFooter>
        <CButton
          class="text-white"
          color="primary"
          v-if="!AccountLineIsEditing"
          @click="store()"
          >Create</CButton
        >
        <CButton
          class="text-white"
          color="primary"
          v-if="AccountLineIsEditing"
          @click="update()"
          >Update</CButton
        >
        <CButton color="default" :to="{ name: 'accounts' }">Cancel</CButton>
      </CCardFooter>
    </CCard>

    <CDataTable
      hover
      striped
      sorter
      tableFilter
      footer
      itemsPerPageSelect
      :items="items"
      :fields="fields"
      :items-per-page="1000"
      :active-page="1"
      :responsive="true"
      table-filter
      pagination
      thead-top
    >
      <template #from_date="{ item }">
        <td>{{ format_date(item.from_date) }}</td>
      </template>
      <template #to_date="{ item }">
        <td>{{ format_date(item.to_date) }}</td>
      </template>
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ items.length }}
        </td>
      </template>
      <template #actions="{ item }">
        <td>
          <div class="row justify-content-center">
            <CButton
              color="success"
              v-if="checkPermission('edit_account_lines')"
              class="text-white btn-sm mt-2 mr-1"
              @click="edit(item.id)"
              ><i class="cil-pencil"></i><CIcon name="cil-pencil"
            /></CButton>
            <c-button
                  color="danger"
                  v-if="checkPermission('delete_account_lines')"
                  class="btn-sm mt-2 mr-1"
                  @click="
                    $root
                      .$confirm(
                        'Delete',
                        'Do you want to delete this record?',
                        {
                          color: 'red',
                          width: 290,
                          zIndex: 200,
                        }
                      )
                      .then((confirmed) => {
                        if (confirmed) {
                          deleteAccountLine(item);
                        }
                      })
                  "
                  ><c-icon name="cil-trash"
                /></c-button>
          </div>
        </td>
      </template>
    </CDataTable>
  </div>
</template>

<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  props: {
    account_id: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      AccountLineIsEditing: false,
      items: [],
      fields: [
        "id",
        "account_name",
        "line_name",
        "line_division_name",
        "brick_name",
        "class_name",
        "from_date",
        "to_date",
        "actions",
      ],
      lines: [],
      line_divisions: [],
      line_classess_names: [],
      line_bricks: [],
      account_line: {
        id: 0,
        account_id: 0,
        line_id: "",
        line_division_id: "",
        brick_id: "",
        class_id: "",
        from_date: "",
        to_date: "",
      },
      fromDate: new Date().toISOString().slice(0, 10),
    };
  },
  methods: {
    format_date (value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    edit_date_format (value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    initialize() {
      axios
        .get("/api/account-lines")
        .then((response) => {
          this.lines = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineDivisions() {
      axios
        .get(`/api/get-line-divisions/${this.account_line.line_id}`)
        .then((response) => {
          this.line_divisions = response.data.divisions;
          this.line_classess_names = response.data.classes;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineDivisionBricks() {
      axios
        .get(
          `/api/get-line-division-bricks/${this.account_line.line_division_id}`
        )
        .then((response) => {
          this.line_bricks = response.data.bricks;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },

    store() {
      axios
        .post(`/api/account-lines`, {
          account_id: this.account_id,
          line_id: this.account_line.line_id,
          line_division_id: this.account_line.line_division_id,
          brick_id: this.account_line.brick_id,
          class_id: this.account_line.class_id,
          from_date: this.crmDateFormat(this.account_line.from_date),
          to_date: this.account_line.to_date? this.crmDateFormat(this.account_line.to_date): "",
        })
        .then((response) => {
          this.reset();
          this.flash("Account Line Created Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      this.getAccountLines();
    },
    getAccountLines() {
      axios
        .get(`/api/get-account-lines/${this.account_id}`)
        .then((response) => {
          this.items = response.data.accountLines;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    edit(id) {
      axios
        .get(`/api/account-lines/${id}`)
        .then((response) => {
          this.AccountLineIsEditing = true;
          this.line_divisions = response.data.divisions;
          this.line_bricks = response.data.bricks;
          this.line_classess_names = response.data.classes;
          this.account_line.id = response.data.account_line.id;
          this.account_line.account_id = response.data.account_line.account_id;
          this.account_line.line_id = response.data.account_line.line_id;
          this.account_line.brick_id = response.data.account_line.brick_id;
          this.account_line.class_id = response.data.account_line.class_id;
          this.account_line.line_division_id =
            response.data.account_line.line_division_id;
          this.account_line.from_date = this.edit_date_format(
            response.data.account_line.from_date
          );
          this.account_line.to_date = this.edit_date_format(
            response.data.account_line.to_date
          );
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    reset() {
      this.account_line = {
        account_id: this.account_id,
        line_id: "",
        line_division_id: "",
        brick_id: "",
        from_date: "",
        to_date: "",
      };
    },
    update() {
      axios
        .put(`/api/account-lines/${this.account_line.id}`, {
          id: this.account_line.id,
          account_id: this.account_line.account_id,
          line_id: this.account_line.line_id,
          brick_id: this.account_line.brick_id,
          class_id: this.account_line.class_id,
          line_division_id: this.account_line.line_division_id,
          from_date: this.crmDateFormat(this.account_line.from_date),
          to_date: this.account_line.to_date? this.crmDateFormat(this.account_line.to_date):"",
        })
        .then((response) => {
          this.AccountLineIsEditing = false;
          this.flash("Account Line Updated Successfully");
          this.getAccountLines();
          this.reset();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removeAccountLine(item) {
      const index = this.items.findIndex((type) => item.id == type.id);
      this.items.splice(index, 1);
    },
    deleteAccountLine(item) {
      axios
        .delete(`/api/account-lines/${item.id}`)
        .then((response) => {
          this.flash("Account Line Deleted Successfully");
          this.removeAccountLine(item);
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
    this.initialize();
    this.getAccountLines();
  },
};
</script>