<template>
  <div>
    <CCard no-header>
      <CCardBody>
        <CTabs variant="pills" vertical>
          <CTab active>
            <template slot="title">
              {{ tabs[0] }}
            </template>
            <CInput
              type="hidden"
              value="1"
              v-model="account_schedule.day"
            ></CInput>
            <div class="row">
              <div class="col">
                <CInput
                  label="Open From"
                  type="time"
                  placeholder="From"
                  v-model="account_schedule.open_from_time"
                ></CInput>
              </div>
              <div class="col">
                <CInput
                  label="To"
                  type="time"
                  placeholder="To"
                  v-model="account_schedule.open_to_time"
                  :min="fromDate"
                ></CInput>
              </div>
            </div>
            <div class="row" v-if="account_schedule.open_from_time">
              <div class="col">
                <CInput
                  label="Visit From"
                  type="time"
                  placeholder="From"
                  v-model="account_schedule.visit_from_time"
                ></CInput>
              </div>
              <div class="col">
                <CInput
                  label="To"
                  type="time"
                  placeholder="To"
                  v-model="account_schedule.visit_to_time"
                  :min="fromDate"
                ></CInput>
              </div>
            </div>
          </CTab>

          <CTab>
            <template slot="title">
              {{ tabs[1] }}
            </template>
            <CInput
              type="hidden"
              value="2"
              v-model="account_schedule.day"
            ></CInput>
            <div class="row">
              <div class="col">
                <CInput
                  label="Open From"
                  type="time"
                  placeholder="From"
                  name="open_from_time"
                  v-model="account_schedule.open_from_time"
                ></CInput>
              </div>
              <div class="col">
                <CInput
                  label="To"
                  type="time"
                  placeholder="To"
                  v-model="account_schedule.open_to_time"
                  :min="fromDate"
                ></CInput>
              </div>
            </div>
            <div class="row" v-if="account_schedule.open_from_time">
              <div class="col">
                <CInput
                  label="Visit From"
                  type="time"
                  placeholder="From"
                  v-model="account_schedule.visit_from_time"
                ></CInput>
              </div>
              <div class="col">
                <CInput
                  label="To"
                  type="time"
                  placeholder="To"
                  v-model="account_schedule.visit_to_time"
                  :min="fromDate"
                ></CInput>
              </div>
            </div>
          </CTab>

          <CTab>
            <template slot="title">
              {{ tabs[2] }}
            </template>
            <CInput
              type="hidden"
              value="3"
              v-model="account_schedule.day"
            ></CInput>
            <div class="row">
              <div class="col">
                <CInput
                  label="Open From"
                  type="time"
                  placeholder="From"
                  v-model="account_schedule.open_from_time"
                ></CInput>
              </div>
              <div class="col">
                <CInput
                  label="To"
                  type="time"
                  placeholder="To"
                  v-model="account_schedule.open_to_time"
                  :min="fromDate"
                ></CInput>
              </div>
            </div>
            <div class="row" v-if="account_schedule.open_from_time">
              <div class="col">
                <CInput
                  label="Visit From"
                  type="time"
                  placeholder="From"
                  v-model="account_schedule.visit_from_time"
                ></CInput>
              </div>
              <div class="col">
                <CInput
                  label="To"
                  type="time"
                  placeholder="To"
                  v-model="account_schedule.visit_to_time"
                  :min="fromDate"
                ></CInput>
              </div>
            </div>
          </CTab>

          <CTab>
            <template slot="title">
              {{ tabs[3] }}
            </template>
            <CInput
              type="hidden"
              value="4"
              v-model="account_schedule.day"
            ></CInput>
            <div class="row">
              <div class="col">
                <CInput
                  label="Open From"
                  type="time"
                  placeholder="From"
                  v-model="account_schedule.open_from_time"
                ></CInput>
              </div>
              <div class="col">
                <CInput
                  label="To"
                  type="time"
                  placeholder="To"
                  v-model="account_schedule.open_to_time"
                  :min="fromDate"
                ></CInput>
              </div>
            </div>
            <div class="row" v-if="account_schedule.open_from_time">
              <div class="col">
                <CInput
                  label="Visit From"
                  type="time"
                  placeholder="From"
                  v-model="account_schedule.visit_from_time"
                ></CInput>
              </div>
              <div class="col">
                <CInput
                  label="To"
                  type="time"
                  placeholder="To"
                  v-model="account_schedule.visit_to_time"
                  :min="fromDate"
                ></CInput>
              </div>
            </div>
          </CTab>

          <CTab>
            <template slot="title">
              {{ tabs[4] }}
            </template>
            <CInput
              type="hidden"
              value="5"
              v-model="account_schedule.day"
            ></CInput>
            <div class="row">
              <div class="col">
                <CInput
                  label="Open From"
                  type="time"
                  placeholder="From"
                  v-model="account_schedule.open_from_time"
                ></CInput>
              </div>
              <div class="col">
                <CInput
                  label="To"
                  type="time"
                  placeholder="To"
                  v-model="account_schedule.open_to_time"
                  :min="fromDate"
                ></CInput>
              </div>
            </div>
            <div class="row" v-if="account_schedule.open_from_time">
              <div class="col">
                <CInput
                  label="Visit From"
                  type="time"
                  placeholder="From"
                  v-model="account_schedule.visit_from_time"
                ></CInput>
              </div>
              <div class="col">
                <CInput
                  label="To"
                  type="time"
                  placeholder="To"
                  v-model="account_schedule.visit_to_time"
                  :min="fromDate"
                ></CInput>
              </div>
            </div>
          </CTab>

          <CTab>
            <template slot="title">
              {{ tabs[5] }}
            </template>
            <CInput
              type="hidden"
              value="6"
              v-model="account_schedule.day"
            ></CInput>
            <div class="row">
              <div class="col">
                <CInput
                  label="Open From"
                  type="time"
                  placeholder="From"
                  v-model="account_schedule.open_from_time"
                ></CInput>
              </div>
              <div class="col">
                <CInput
                  label="To"
                  type="time"
                  placeholder="To"
                  v-model="account_schedule.open_to_time"
                  :min="fromDate"
                ></CInput>
              </div>
            </div>
            <div class="row" v-if="account_schedule.open_from_time">
              <div class="col">
                <CInput
                  label="Visit From"
                  type="time"
                  placeholder="From"
                  v-model="account_schedule.visit_from_time"
                ></CInput>
              </div>
              <div class="col">
                <CInput
                  label="To"
                  type="time"
                  placeholder="To"
                  v-model="account_schedule.visit_to_time"
                  :min="fromDate"
                ></CInput>
              </div>
            </div>
          </CTab>

          <CTab>
            <template slot="title">
              {{ tabs[6] }}
            </template>
            <CInput
              type="hidden"
              value="7"
              v-model="account_schedule.day"
            ></CInput>
            <div class="row">
              <div class="col">
                <CInput
                  label="Open From"
                  type="time"
                  placeholder="From"
                  v-model="account_schedule.open_from_time"
                ></CInput>
              </div>
              <div class="col">
                <CInput
                  label="To"
                  type="time"
                  placeholder="To"
                  v-model="account_schedule.open_to_time"
                  :min="fromDate"
                ></CInput>
              </div>
            </div>
            <div class="row" v-if="account_schedule.open_from_time">
              <div class="col">
                <CInput
                  label="Visit From"
                  type="time"
                  placeholder="From"
                  v-model="account_schedule.visit_from_time"
                ></CInput>
              </div>
              <div class="col">
                <CInput
                  label="To"
                  type="time"
                  placeholder="To"
                  v-model="account_schedule.visit_to_time"
                  :min="fromDate"
                ></CInput>
              </div>
            </div>
          </CTab>
        </CTabs>
      </CCardBody>

      <CCardFooter>
        <CButton
          class="text-white"
          color="primary"
          v-if="!scheduleIsEditing"
          @click="store()"
          >Create</CButton
        >
        <CButton
          class="text-white"
          color="primary"
          v-if="scheduleIsEditing"
          @click="update"
          >Update</CButton
        >
        <CButton color="default" :to="{ name: 'accounts' }">Cancel</CButton>
      </CCardFooter>
    </CCard>

    <!-- <CDataTable
      hover
      striped
      sorter
      tableFilter
      footer
      itemsPerPageSelect
      :items="account_socials"
      :fields="socials_fields"
      :items-per-page="100"
      :active-page="1"
      :responsive="true"
      table-filter
      pagination
      thead-top
    >
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ account_socials.length }}
        </td>
      </template>
      <template #actions="{ item }">
        <td>
          <div class="row justify-content-center">
            <CButton
              color="success"
              class="text-white btn-sm"
              @click="editAccountSocial(item.id)"
              ><i class="cil-pencil"></i><CIcon name="cil-pencil"
            /></CButton>
            <CButton
              color="danger"
              class="text-white btn-sm"
              @click="
                dangerModal = true;
                currId = item.id;
              "
              ><CIcon name="cil-trash"
            /></CButton>
          </div>
        </td>
      </template>
    </CDataTable> -->
  </div>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  props: {
    account_id: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      scheduleIsEditing: false,
      activeTab: 1,
      tabs: [
        "Saturday",
        "Sunday",
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
      ],
      account_schedule: {
        id: 0,
        account_id: 0,
        day: "",
        open_from_time: "",
        open_to_time: "",
        visit_from_time: "",
        visit_to_time: "",
      },
      fromDate: new Date().toISOString().slice(0, 10),
    };
  },
  methods: {},
};
</script>

<style>
</style>