<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";

export default {
  name: "AssignAccountLocation",
  components: {
    vSelect
  },
  data() {
    return {
      center: {lat: 30.0444, lng: 31.2357},
      item: null,
      assignLocationModal: false,
      lines: [],
      divisions: [],
      bricks: [],
      line: null,
      division: null,
      brick: null,
    }
  },
  methods: {
    open(account) {
      this.item = account;
      this.assignLocationModal = true;
      this.getAccountLines()
    },
    close() {
      this.reset()
    },
    reset() {
      this.assignLocationModal = false;
      this.item = null;
      this.lines = [];
      this.divisions = [];
      this.bricks = [];
      this.line = null;
      this.division = null;
      this.brick = null;
    },
    getAccountLines() {
      axios.get(`/api/accounts/${this.item.id}/account-lines`)
        .then(response => {
          this.lines = response.data.lines
        })
        .catch(this.showErrorMessage)
    },
    getAccountDivisions() {
      axios.get(`/api/accounts/${this.item.id}/account-lines/${this.line}/divisions`)
        .then(response => {
          this.divisions = response.data.divisions
        })
        .catch(this.showErrorMessage)
    },
    getAccountBricks() {
      axios.get(`/api/accounts/${this.item.id}/account-divisions/${this.division}/bricks`)
        .then(response => {
          this.bricks = response.data.bricks
        })
        .catch(this.showErrorMessage)
    },
    onMarkerDragEnd(marker) {
      const newPosition = marker.latLng;
      this.center = {
        lat: newPosition.lat(),
        lng: newPosition.lng(),
      };

    },
    assignLocation() {
      axios.post(`/api/accounts/${this.item.id}/assign-location`,{
        ll: this.center.lat,
        lg: this.center.lng,
        line: this.line,
        division: this.division,
        brick: this.brick,
      })
        .then(()=>this.flash('Location Assigned Successfully.'))
        .catch(this.showErrorMessage)
    }
  },
  watch: {
    line() {
      if (this.line)
        this.getAccountDivisions()

      if (!this.line) {
        this.divisions = [];
        this.division = null
      }
    },
    division() {
      if (this.division)
        this.getAccountBricks()

      if (!this.division) {
        this.brick = []
        this.brick = null
      }
    },
  }
}
</script>

<template>
  <c-modal
    :title="`Assign Location to  ( ${item?.name} )`"
    color="success"
    :show.sync="assignLocationModal"
    :closeOnBackdrop="false"
    @close="close"
  >
    <c-form-group>
      <template #label>Line</template>
      <template #input>
        <v-select
          v-model="line"
          :options="lines"
          label="name"
          :reduce="(el) => el.id"
          placeholder="Select a Line"
          class="mt-2"
        />
      </template>
    </c-form-group>
    <c-form-group>
      <template #label>Division</template>
      <template #input>
        <v-select
          v-model="division"
          :options="divisions"
          label="name"
          :reduce="(el) => el.id"
          placeholder="Select a Division"
          class="mt-2"
        />
      </template>
    </c-form-group>
    <c-form-group>
      <template #label>Brick</template>
      <template #input>
        <v-select
          v-model="brick"
          :options="bricks"
          label="name"
          :reduce="(el) => el.id"
          placeholder="Select a Brick"
          class="mt-2"
        />
      </template>
    </c-form-group>
    <gmap-map class="mt-3" :center="center" :zoom="18" style="width: 100%; height: 340px">
      <gmap-marker
        style="color: red"
        :draggable="true"
        :position="center"
        :clickable="true"
        @dragend="onMarkerDragEnd"
      ></gmap-marker>
    </gmap-map>
    <template #footer>
      <CButton @click="close" class="text-white" color="danger">Cancel</CButton>
      <CButton type="submit" @click="assignLocation" class="text-white" color="success">Submit</CButton>
    </template>
  </c-modal>
</template>

