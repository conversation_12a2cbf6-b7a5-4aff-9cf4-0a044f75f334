<template>
  <div>
    <CCard no-header>
      <CCardBody>
        <div class="row">
          <div class="col">
            <CInput
              label="Code"
              type="text"
              placeholder="Code"
              v-model="account.code"
            ></CInput>
          </div>
          <div class="col">
            <CInput
              label="Name"
              type="text"
              placeholder="Name"
              v-model="account.name"
            ></CInput>
          </div>
          <div class="col">
            <CInput
              label="Email"
              type="email"
              placeholder="Email"
              v-model="account.email"
            ></CInput>
          </div>
        </div>

        <div class="row">
          <!-- <div class="col">
                      <CInput label="Mobile" type="text" placeholder="Mobile" v-model="account.mobile"></CInput>
                    </div> -->

          <div class="col">
            <CFormGroup
              wrapperClasses="input-group pt-2"
              description="ex. 0 (*************"
            >
              <template #prepend-content>
                <CIcon name="cil-phone" />
              </template>
              <template #label> Mobile </template>
              <template #input>
                <masked-input
                  type="tel"
                  name="mobile"
                  class="form-control"
                  v-model="account.mobile"
                  :mask="[
                    '0',
                    ' ',
                    '(',
                    /[1-9]/,
                    /\d/,
                    /\d/,
                    ')',
                    ' ',
                    /\d/,
                    /\d/,
                    /\d/,
                    '-',
                    /\d/,
                    /\d/,
                    /\d/,
                    /\d/,
                  ]"
                  :guide="true"
                  placeholderChar="#"
                />
              </template>
            </CFormGroup>
          </div>

          <!-- <div class="col">
                      <CInput label="Tel." type="text" placeholder="Tel" v-model="account.tel"></CInput>
                    </div> -->

          <div class="col">
            <CFormGroup
              wrapperClasses="input-group pt-2"
              description="ex. 02-********"
            >
              <template #prepend-content>
                <CIcon name="cil-phone" />
              </template>
              <template #label> Tel. </template>
              <template #input>
                <masked-input
                  type="tel"
                  name="tel"
                  class="form-control"
                  v-model="account.tel"
                  :mask="[
                    0,
                    2,
                    '-',
                    /\d/,
                    /\d/,
                    /\d/,
                    /\d/,
                    /\d/,
                    /\d/,
                    /\d/,
                    /\d/,
                  ]"
                  :guide="true"
                  placeholderChar="#"
                />
              </template>
            </CFormGroup>
          </div>
        </div>

        <div class="row">
          <div class="col">
            <CFormGroup>
              <template #label> Type </template>
              <template #input>
                <v-select
                  v-model="account.type_id"
                  :options="types"
                  label="name"
                  :value="0"
                  :reduce="(type) => type.id"
                  placeholder="Select Type"
                  class="mt-2"
                  @input="getSubTypes()"
                />
              </template>
            </CFormGroup>
          </div>
          <div class="col">
            <CFormGroup>
              <template #label> Sub Type </template>
              <template #input>
                <v-select
                  v-model="account.sub_type_id"
                  :options="subtypes"
                  label="name"
                  :value="0"
                  :reduce="(subtypes) => subtypes.id"
                  placeholder="Select Subtype"
                  class="mt-2"
                />
              </template>
            </CFormGroup>
          </div>
          <div class="col">
            <CFormGroup>
              <template #label> Classification </template>
              <template #input>
                <v-select
                  v-model="account.classification_id"
                  :options="classifications"
                  label="name"
                  :value="0"
                  :reduce="(classification) => classification.id"
                  placeholder="Select Classification"
                  class="mt-2"
                />
              </template>
            </CFormGroup>
          </div>
        </div>

        <div class="row">
          <div class="col">
            <CTextarea
              label="Address"
              type="text"
              placeholder="Address"
              v-model="account.address"
            ></CTextarea>
          </div>
        </div>

        <div class="row">
          <div class="col">
            <CInput
              label="Active Date"
              type="date"
              placeholder="Active Date"
              v-model="account.active_date"
            ></CInput>
          </div>
          <div class="col">
            <CInput
              label="Inactive Date"
              type="date"
              placeholder="Inactive Date"
              v-model="account.inactive_date"
            ></CInput>
          </div>
        </div>

        <div class="row">
          <div class="col">
            <CInput
              label="Website Link"
              type="text"
              placeholder="Website Link"
              v-model="account.website_link"
            ></CInput>
          </div>
        </div>

        <div class="row">
          <div class="col">
            <CTextarea
              label="Notes"
              type="text"
              placeholder="Notes"
              v-model="account.notes"
            ></CTextarea>
          </div>
        </div>
      </CCardBody>
      <CCardFooter>
        <CButton class="text-white" color="primary" @click="store()"
          >Save</CButton
        >
        <CButton color="default" :to="{ name: 'accounts' }">Cancel</CButton>
      </CCardFooter>
    </CCard>
  </div>
</template>
<script>
import MaskedInput from "vue-text-mask";
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    MaskedInput,
    vSelect,
  },
  data() {
    return {
      account_id: null,
      types: [],
      classifications: [],
      subtypes: [],
      account: {
        code: "",
        name: "",
        notes: "",
        type_id: "",
        classification_id:"",
        sub_type_id: "",
        address: "",
        tel: "",
        mobile: "",
        email: "",
        active_date: "",
        inactive_date: "",
        website_link: "",
      },
    };
  },
  emits: ["getAccount"],
  methods: {
    edit_date_format: function (value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    initialize() {
      axios
        .get("/api/accounts/create")
        .then((response) => {
          this.types = response.data.types;
          this.classifications = response.data.classifications;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    store() {
      axios
        .post("/api/accounts", {
          code: this.account.code,
          name: this.account.name,
          notes: this.account.notes,
          type_id: this.account.type_id,
          classification_id: this.account.classification_id,
          sub_type_id: this.account.sub_type_id,
          address: this.account.address,
          tel: this.account.tel,
          mobile: this.account.mobile,
          email: this.account.email,
          active_date: this.crmDateFormat(this.account.active_date),
          inactive_date:this.account.inactive_date? this.crmDateFormat(this.account.inactive_date):"",
          website_link: this.account.website_link,
        })
        .then((response) => {
         this. account_id=response.data.account.id
          this.flash("Account Created Successfully");
          this.$emit("getAccount", { account: this.account_id });
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getSubTypes() {
      axios.get(`/api/get_accounttype_subtypes/${this.account.type_id}`)
      .then( (response)=> {
        this.subtypes = response.data.subtypes;
      }).catch( (error)=> {
          this.showErrorMessage(error);
      });
    },
  },
  created() {
    this.initialize();
  },
};
</script>