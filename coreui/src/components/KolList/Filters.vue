<template>
  <c-col col="12" lg="12">
    <c-card>
      <c-card-header> {{ textHeader }}</c-card-header>
      <c-card-body>
        <c-tabs>
          <c-tab active>
            <template slot="title">
              <c-icon class="custom_icon" name="cil-chart-pie" />
              Main Data
            </template>
            <c-card>
              <c-card-body>
                <div class="form-row form-group">
                  <div class="col-lg-3 col-md-3 col-sm-8">
                    <c-form-group>
                      <template #label>
                        <strong>Action</strong>
                      </template>
                      <template #input>
                        <v-select v-model="listFilter.status" :options="statusbar" label="name"  required
                          :reduce="(status) => status.id" placeholder="Select Action" class="mt-2" @input="getData" />
                      </template>
                    </c-form-group>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-8">
                    <c-form-group>
                      <template #label>
                        <strong>Line</strong>
                      </template>
                      <template #input>
                        <v-select v-model="listFilter.line_id" :options="lines" label="name"  required
                          :reduce="(line) => line.id" placeholder="Select Line" class="mt-2" />
                      </template>
                    </c-form-group>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-8">
                    <c-form-group>
                      <template #label>
                        <strong>Division</strong>
                      </template>
                      <template #input>
                        <input label="All" v-if="divisions.length != 0" class="m-1" type="checkbox"
                          v-model="checkAllDivisions" title="Check All Divisions" @change="checkAllDivision" />
                        <label v-if="divisions.length != 0" style="font-weight: bold">All</label>
                        <v-select v-model="listFilter.div_id" :options="divisions" label="name" 
                          :reduce="(division) => division.id" placeholder="Select Division" class="mt-1" multiple
                         />
                      </template>
                    </c-form-group>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-8">
                    <c-form-group>
                      <template #label>
                        <strong>Brick</strong>
                      </template>
                      <template #input>
                        <input label="All" v-if="bricks.length != 0" class="m-1" type="checkbox"
                          v-model="checkAllBricks" title="Check All Bricks" @change="checkAllBrick" />
                        <label v-if="bricks.length != 0" style="font-weight: bold">All</label>
                        <v-select v-model="listFilter.brick_id" :options="bricks" label="name"  required
                          :reduce="(brick) => brick.id" placeholder="Select Brick" class="mt-1" multiple />
                      </template>
                    </c-form-group>
                  </div>
                </div>
              </c-card-body>
            </c-card>
          </c-tab>
          <c-tab>
            <template slot="title">
              <c-icon class="custom_icon" name="cil-description" />
              Specialities
              & Classes
            </template>
            <c-card>
              <c-card-body>
                <div class="form-row form-group">
                  <div class="col-lg-4 col-md-4 col-sm-8">
                    <c-form-group>
                      <template #label>
                        <strong>Specialities</strong>
                      </template>
                      <template #input>
                        <input label="All" v-if="specialities.length != 0" class="m-1" type="checkbox"
                          v-model="checkAllSpecialities" title="Check All Specialities" @change="checkAllSpeciality" />
                        <label v-if="specialities.length != 0" style="font-weight: bold">All</label>
                        <v-select v-model="listFilter.speciality_id" :options="specialities" label="name"
                          :reduce="(speciality) => speciality.id" placeholder="Select Speciality" class="mt-1"
                          multiple />
                      </template>
                    </c-form-group>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-8">
                    <c-form-group>
                      <template #label> Account Type</template>
                      <template #input>
                        <input label="All" id="type" v-if="types.length != 0" class="m-1" type="checkbox"
                          v-model="allAccountTypes" title="Check All Types" @change="checkAllAccounts" />
                        <label for="type" v-if="types.length != 0" style="font-weight: bold">All</label>
                        <v-select v-model="listFilter.type_id" :options="types" label="name"
                          :reduce="(type) => type.id" placeholder="Select Account Type" class="mt-1" multiple />
                      </template>
                    </c-form-group>
                  </div>
                  <div class="col-lg-4 col-md-4 col-sm-8">
                    <c-form-group>
                      <template #label>
                        <strong>Classes</strong>
                      </template>
                      <template #input>
                        <input label="All" v-if="classes.length != 0" class="m-1" type="checkbox"
                          v-model="checkAllClasses" title="Check All Classes" @change="checkAllClass" />
                        <label v-if="classes.length != 0" style="font-weight: bold">All</label>
                        <v-select v-model="listFilter.class_id" :options="classes" label="name"  required
                          :reduce="(clas) => clas.id" placeholder="Select Class" class="mt-1" multiple />
                      </template>
                    </c-form-group>
                  </div>
                </div>
              </c-card-body>
            </c-card>
          </c-tab>
        </c-tabs>
      </c-card-body>
      <c-card-footer>
        <c-button color="primary" class="text-white" @click="show()"
          :disabled="!(listFilter.line_id && listFilter.div_id)" style="float: right">Show
        </c-button>
      </c-card-footer>
    </c-card>
  </c-col>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";

export default {
  components: {
    vSelect,
  },
  data() {
    return {
      listFilter: {
        line_id: null,
        div_id: null,
        brick_id: null,
        speciality_id: null,
        class_id: null,
        type_id: null,
        status: null,
        setting: null,
        month: null,
      },
      lines: [],
      statusbar: [
        { id: 1, name: "Activation" },
        { id: 2, name: "Inactivation" },
      ],
      divisions: [],
      bricks: [],
      specialities: [],
      months: [],
      classes: [],
      types: [],
      checkAllLines: false,
      checkAllDivisions: false,
      checkAllBricks: false,
      allAccountTypes: true,
      checkAllSpecialities: true,
      checkAllClasses: true,
    };
  },
  computed: {
    filteredLines() {
      return this.listFilter.line_id;
    },
    filteredDivisions() {
      return this.listFilter.div_id;
    }
  },
  props: {
    textHeader: {
      type: String,
      default: 'Create Favourite List'
    }
  },
  emits: ["getList"],
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: null,
          to: null,
          data: ['lines', 'months', 'accountTypes', 'current_month', 'listSetting']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.listFilter.line_id = this.lines[0].id;
          this.types = response.data.data.accountTypes;
          this.listFilter.type_id = this.types.map((item) => item.id);
          this.months = response.data.data.months;
          this.listFilter.month = response.data.data.current_month;
          this.listFilter.setting = response.data.data.listSetting;
          this.getLineDivisions();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getData() {
      this.initialize();
    },
    async getLineDivisions() {
      const res = await axios
        .post(`/api/get-lines-data-with-dates`, {
          lines: [this.listFilter.line_id],
          from: null,
          to: null,
          action: this.listFilter.status,
          data: ['divisions', 'classes', 'specialities']
        });

      this.divisions = res.data.data.divisions;
      this.classes = res.data.data.classes;
      this.specialities = res.data.data.specialities;
      this.selectedByDefaults();
      // await this.getDivisionBricks();
    },
    selectedByDefaults() {
      this.listFilter.div_id = this.divisions.map((item) => item.id);
      this.listFilter.speciality_id = this.specialities.map((item) => item.id);
      this.listFilter.class_id = this.classes.map((item) => item.id);
      this.checkAllDivisions = true;
      this.checkAllBricks = true;
      this.checkAllSpecialities = true;
    },

    async getDivisionBricks() {
      try {
        const res = await axios
          .post(`/api/favourite-list-bricks`, {
            divisions: this.listFilter.div_id,
          });

        this.bricks = res.data.data;
        this.listFilter.brick_id = this.bricks.map((item) => item.id);
      } catch (error) {
        this.showErrorMessage(error);
      }

    },
    checkAllDivision() {
      if (this.checkAllDivisions)
        this.listFilter.div_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.listFilter.div_id = null;
      this.getDivisionBricks();
    },
    checkAllBrick() {
      if (this.checkAllBricks)
        this.listFilter.brick_id = this.bricks.map((item) => item.id);
      if (this.checkAllBricks == false) this.listFilter.brick_id = null;
    },
    checkAllSpeciality() {
      if (this.checkAllSpecialities)
        this.listFilter.speciality_id = this.specialities.map(
          (item) => item.id
        );
      if (this.checkAllSpecialities == false)
        this.listFilter.speciality_id = null;
    },
    checkAllAccounts() {
      if (this.allAccountTypes)
        this.listFilter.type_id = this.types.map((item) => item.id);
      if (this.allAccountTypes == false) this.listFilter.type_id = null;
    },
    checkAllClass() {
      if (this.checkAllClasses)
        this.listFilter.class_id = this.classes.map((item) => item.id);
      if (this.checkAllClasses == false) this.listFilter.class_id = null;
    },
    show() {
      this.$emit("getList", this.listFilter);
    },
  },
  watch: {
    filteredLines: {
      handler(newVal, oldVal) {
        this.getLineDivisions()
      }
    },
    filteredDivisions: {
      handler(newVal, oldVal) {
        this.getDivisionBricks()
      },
      deep: true
    }
  }
};
</script>
