<template>
  <c-col col="12" lg="12">
    <c-card>
      <c-card-header> Notifications Report</c-card-header>
      <c-card-body>

            <c-card>
              <c-card-body>
                <div class="form-row form-group">
                  <div class="col-4">
                    <c-form-group>
                      <template #label>
                        <strong>From Date</strong>
                      </template>
                      <template #input>
                        <c-input
                          type="date"
                          placeholder="From Date"
                          v-model="listFilter.from_date"
                        ></c-input>
                      </template>
                    </c-form-group>
                  </div>
                  <div class="col-4">
                    <c-form-group>
                      <template #label>
                        <strong>To Date</strong>
                      </template>
                      <template #input>
                        <c-input
                          type="date"
                          placeholder="To Date"
                          v-model="listFilter.to_date"
                        ></c-input>
                      </template>
                    </c-form-group>
                  </div>
                  <div class="col-4">
                    <c-form-group>
                      <template #label>
                        <strong>types</strong>
                      </template>
                      <template #input>
                        <!-- <input
                          label="All"
                          v-if="types.length != 0"
                          class="m-1"
                          type="checkbox"
                          v-model="checkAlltypes"
                          title="Check All types"
                          @change="checkAllType"
                        />
                        <label
                          v-if="types.length != 0"
                          style="font-weight: bold"
                          >All</label
                        > -->
                        <v-select
                          v-model="listFilter.type_id"
                          :options="types"
                          label="name"
                          :value="0"
                          required
                          :reduce="(type) => type.name"
                          placeholder="Select option"
                          class="mt-2"
                          multiple
                        />
                      </template>
                    </c-form-group>
                  </div>    
                </div>
              </c-card-body>
            </c-card>

      </c-card-body>
      <c-card-footer>
        <c-button
          color="primary"
          class="text-white"
          @click="show()"
          :disabled="!(listFilter.type_id && listFilter.from_date && listFilter.to_date)"
          style="float: right"
          >Show</c-button
        >
      </c-card-footer>
    </c-card>
  </c-col>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {

      listFilter: {
        from_date: null,
        to_date: null,
        type_id: null,
      },
      types: [],
      checkAlltypes: false,

    };
  },
  emits: ["getList"],
  methods: {
    initialize() {
      axios
        .get("/api/notifications-types")
        .then((response) => {
          this.types = response.data;
          console.log(this.types);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },

    checkAllType() {
      if (this.checkAlltypes)
        this.listFilter.type_id = this.types.map((item) => item.id);
      if (this.checkAlltypes == false) this.listFilter.type_id = null;
    },
    show() {
      this.$emit("getList", this.listFilter);
    },
  },
  created() {
    this.initialize();
  },
};
</script>
