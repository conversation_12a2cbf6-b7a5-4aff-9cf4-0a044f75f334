// export default [{
//     path: 'dashboardcharts',
//     meta: { label: 'Charts' },
//     component: {
//         render(c) { return c('router-view') }
//     },
//     children: [{
//             path: '',
//             component: () =>
//                 import ('../../../views/dashboardcharts/DashboardCharts.vue'),
//         },
//         {
//             path: 'create',
//             meta: { label: 'Chart Create' },
//             name: 'CreateDashboardChart',
//             component: () =>
//                 import ('../../../views/dashboardcharts/CreateDashboardChart.vue'),
//         },
//         {
//             path: ':id',
//             meta: { label: 'Chart Details' },
//             name: 'Dashboard<PERSON>hart',
//             component: () =>
//                 import ('../../../views/dashboardcharts/DashboardChart.vue'),
//         },
//         {
//             path: ':id/edit',
//             meta: { label: 'Edit Chart' },
//             name: 'EditDashboard<PERSON>hart',
//             component: () =>
//                 import ('../../../views/dashboardcharts/EditDashboardChart.vue')
//         },
//     ]
// }]
