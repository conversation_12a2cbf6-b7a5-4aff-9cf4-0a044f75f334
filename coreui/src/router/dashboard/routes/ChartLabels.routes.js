export default [{
    // path: 'chartlabels',
    // meta: { label: 'ChartLabels' },
    // component: {
    //     render(c) { return c('router-view') }
    // },
    // children: [{
    //         path: '',
    //         component: () =>
    //             import ('../../../views/chartlabels/ChartLabels.vue'),
    //     },
    //     {
    //         path: 'create',
    //         meta: { label: 'Chart Label Create' },
    //         name: 'CreateChartLabel',
    //         component: () =>
    //             import ('../../../views/chartlabels/CreateChartLabel.vue'),
    //     },
    //     {
    //         path: ':id',
    //         meta: { label: 'Chart Label Details' },
    //         name: 'ChartLabel',
    //         component: () =>
    //             import ('../../../views/chartlabels/ChartLabel.vue'),
    //     },
    //     {
    //         path: ':id/edit',
    //         meta: { label: 'Edit Chart Label' },
    //         name: 'EditChartLabel',
    //         component: () =>
    //             import ('../../../views/chartlabels/EditChartLabel.vue')
    //     },
    // ]
}]
