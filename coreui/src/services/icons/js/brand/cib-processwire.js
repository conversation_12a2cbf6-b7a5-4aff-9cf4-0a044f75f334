export const cibProcesswire = ["32 32","<path d='M29.25 7.026c-0.969-1.448-2.583-3.104-4.203-4.177-3.37-2.307-7.271-3.125-10.724-2.766-3.594 0.385-6.677 1.74-9.208 4.005-2.349 2.094-3.901 4.656-4.583 7.099-0.688 2.448-0.599 4.708-0.349 6.411 0.26 1.729 1.12 3.906 1.12 3.906 0.177 0.417 0.422 0.589 0.547 0.656 0.63 0.344 1.661 0.042 2.458-0.849 0.036-0.052 0.057-0.13 0.036-0.193-0.219-0.854-0.302-1.495-0.38-1.99-0.177-0.984-0.26-2.625-0.141-4.146 0.063-0.823 0.224-1.703 0.5-2.625 0.542-1.823 1.688-3.724 3.469-5.307 1.922-1.703 4.385-2.755 6.729-3.026 0.823-0.099 2.385-0.193 4.266 0.271 0.401 0.099 2.125 0.547 3.964 1.797 1.344 0.911 2.422 2.036 3.167 3.151 0.76 1.063 1.578 2.854 1.823 4.167 0.339 1.495 0.339 3.073 0.099 4.615-0.302 1.547-0.844 3.047-1.682 4.37-0.583 1.021-1.786 2.375-3.203 3.391-1.281 0.901-2.745 1.552-4.25 1.911-0.755 0.182-1.521 0.297-2.302 0.323-0.693 0.021-1.615 0-2.26-0.109-0.964-0.156-1.161-0.401-1.385-0.734 0 0-0.151-0.24-0.193-0.88 0.016-5.875 0.010-4.307 0.010-7.354 0-0.865-0.026-1.646-0.021-2.354 0.042-1.161 0.141-1.964 0.974-2.828 0.599-0.641 1.443-1.026 2.37-1.026 0.281 0 1.25 0.016 2.099 0.724 0.917 0.76 1.068 1.802 1.104 2.083 0.208 1.63-0.87 2.844-1.281 3.151-0.516 0.385-0.974 0.573-1.286 0.682-0.661 0.219-1.385 0.281-2.042 0.24-0.099-0.005-0.188 0.063-0.208 0.161l-0.219 1.141c-0.214 0.865 0.266 1.182 0.547 1.297 0.896 0.276 1.687 0.391 2.625 0.318 1.443-0.099 2.865-0.667 4.073-1.792 1.026-0.979 1.609-2.182 1.813-3.531 0.198-1.495-0.063-3.104-0.745-4.464-0.75-1.505-2.042-2.76-3.745-3.443-1.724-0.677-3.094-0.703-4.859-0.255l-0.016 0.010c-1.151 0.396-2.13 0.88-3.151 1.932-0.698 0.729-1.276 1.609-1.625 2.604-0.344 1.005-0.438 1.755-0.453 2.917-0.021 0.859 0.021 1.661 0.021 2.411v4.932c0 1.578-0.057 1.854 0 2.667 0.026 0.536 0.104 1.146 0.339 1.802 0.245 0.719 0.75 1.458 1.099 1.802 0.49 0.521 1.115 0.938 1.724 1.198 1.406 0.625 3.307 0.724 4.849 0.661 1.021-0.036 2.047-0.177 3.057-0.417 2.010-0.479 3.943-1.349 5.646-2.557 1.823-1.286 3.406-3.036 4.281-4.526 1.12-1.766 1.844-3.75 2.224-5.792 0.339-2.042 0.323-4.135-0.135-6.146-0.359-1.87-1.318-3.917-2.427-5.568z'></path>"]