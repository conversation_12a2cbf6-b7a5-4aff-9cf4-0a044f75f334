export const cibOcaml = ["32 32","<path d='M16.251 28.839c-0.115-0.224-0.251-0.699-0.339-0.901-0.095-0.177-0.365-0.672-0.496-0.833-0.291-0.333-0.359-0.359-0.452-0.807-0.157-0.767-0.537-2.115-1.011-3.057-0.251-0.5-0.656-0.901-1.016-1.267-0.312-0.312-1.031-0.828-1.167-0.807-1.192 0.224-1.557 1.396-2.115 2.317-0.317 0.516-0.629 0.943-0.88 1.485-0.224 0.495-0.203 1.057-0.583 1.484-0.381 0.421-0.667 0.921-0.833 1.464-0.048 0.113-0.136 1.239-0.251 1.504l1.755-0.109c1.647 0.115 1.172 0.74 3.715 0.609l4.025-0.135c-0.093-0.323-0.213-0.641-0.359-0.948zM27.948 2.063h-23.896c-2.224-0.005-4.031 1.801-4.031 4.025v8.776c0.588-0.203 1.395-1.437 1.667-1.733 0.448-0.516 0.541-1.193 0.765-1.595 0.516-0.948 0.609-1.619 1.776-1.619 0.541 0 0.765 0.129 1.125 0.629 0.249 0.333 0.697 0.989 0.9 1.417 0.251 0.495 0.631 1.145 0.813 1.281 0.129 0.088 0.244 0.181 0.359 0.224 0.177 0.067 0.339-0.068 0.475-0.157 0.156-0.135 0.224-0.38 0.38-0.739 0.224-0.521 0.453-1.104 0.583-1.328 0.229-0.365 0.319-0.813 0.563-1.016 0.365-0.312 0.86-0.339 0.989-0.36 0.745-0.156 1.084 0.36 1.444 0.677 0.244 0.224 0.561 0.651 0.812 1.213 0.177 0.448 0.401 0.88 0.516 1.125 0.088 0.245 0.317 0.651 0.448 1.145 0.135 0.428 0.452 0.767 0.588 0.991 0 0 0.203 0.541 1.369 1.036 0.355 0.151 0.713 0.276 1.084 0.38 0.516 0.183 1.011 0.161 1.64 0.093 0.448 0 0.699-0.656 0.901-1.171 0.109-0.313 0.245-1.193 0.312-1.439 0.068-0.249-0.109-0.427 0.048-0.656 0.181-0.244 0.291-0.271 0.38-0.583 0.229-0.699 1.489-0.719 2.208-0.719 0.604 0 0.516 0.583 1.531 0.38 0.584-0.109 1.147 0.068 1.756 0.249 0.516 0.136 1.011 0.292 1.301 0.631 0.183 0.224 0.657 1.328 0.183 1.369 0.041 0.047 0.089 0.161 0.156 0.204-0.115 0.427-0.563 0.115-0.833 0.067-0.339-0.067-0.583 0-0.921 0.157-0.584 0.249-1.417 0.229-1.932 0.656-0.428 0.359-0.428 1.145-0.636 1.593 0 0-0.563 1.423-1.749 2.297-0.319 0.229-0.928 0.765-2.229 0.968-0.589 0.089-1.147 0.089-1.756 0.068-0.291-0.020-0.583-0.020-0.88-0.020-0.177 0-0.765-0.027-0.74 0.041l-0.067 0.161c0.005 0.073 0.015 0.151 0.041 0.224 0.025 0.129 0.025 0.244 0.047 0.359 0 0.245-0.021 0.516 0 0.767 0.021 0.515 0.224 0.989 0.251 1.525 0.020 0.589 0.312 1.219 0.604 1.693 0.115 0.176 0.271 0.197 0.339 0.427 0.088 0.245 0 0.536 0.047 0.807 0.156 1.057 0.468 2.183 0.943 3.152v0.020c0.583-0.088 1.192-0.312 1.959-0.421 1.416-0.208 3.375-0.115 4.635-0.229 3.199-0.292 4.927 1.307 7.787 0.651v-23.672c-0.016-2.224-1.823-4.025-4.052-4.025zM16.021 20.964c0-0.047 0-0.047 0 0zM7.407 24.651c0.224-0.495 0.359-1.031 0.536-1.525 0.183-0.475 0.453-1.152 0.921-1.396-0.061-0.068-0.989-0.093-1.233-0.115-0.271-0.021-0.543-0.068-0.813-0.109-0.511-0.093-1.020-0.199-1.531-0.317-0.292-0.068-1.303-0.428-1.505-0.516-0.521-0.224-0.855-0.88-1.24-0.812-0.244 0.047-0.495 0.135-0.651 0.385-0.136 0.203-0.183 0.561-0.271 0.807-0.109 0.271-0.292 0.541-0.427 0.812-0.319 0.469-0.833 0.896-1.057 1.371-0.048 0.115-0.068 0.224-0.115 0.339v5.421c0.271 0.047 0.541 0.093 0.833 0.183 2.249 0.609 2.791 0.651 4.995 0.407l0.203-0.027c0.156-0.36 0.297-1.552 0.407-1.912 0.088-0.291 0.203-0.515 0.249-0.785 0.041-0.272 0-0.543-0.025-0.787-0.043-0.657 0.473-0.88 0.724-1.423z'></path>"]