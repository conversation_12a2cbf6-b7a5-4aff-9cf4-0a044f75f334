@charset "UTF-8";
/*!
 * CoreUI Icons - Flag Icons
 * @version v1.0.1
 * @link https://coreui.io/icons/flag/
 * Copyright (c) 2020 creativeLabs <PERSON><PERSON>
 * Licensed under CC0 1.0 Universal
 */
[class^="cif-"], [class*=" cif-"] {
  background-size: contain;
  background-position: 50%;
  background-repeat: no-repeat;
  position: relative;
  display: inline-block;
  width: 1.33333333em;
  line-height: 1em;
}

.cif-af {
  background-image: url(../svg/flag/cif-af.svg);
}

.cif-al {
  background-image: url(../svg/flag/cif-al.svg);
}

.cif-dz {
  background-image: url(../svg/flag/cif-dz.svg);
}

.cif-ad {
  background-image: url(../svg/flag/cif-ad.svg);
}

.cif-ao {
  background-image: url(../svg/flag/cif-ao.svg);
}

.cif-ag {
  background-image: url(../svg/flag/cif-ag.svg);
}

.cif-ar {
  background-image: url(../svg/flag/cif-ar.svg);
}

.cif-am {
  background-image: url(../svg/flag/cif-am.svg);
}

.cif-au {
  background-image: url(../svg/flag/cif-au.svg);
}

.cif-at {
  background-image: url(../svg/flag/cif-at.svg);
}

.cif-az {
  background-image: url(../svg/flag/cif-az.svg);
}

.cif-bs {
  background-image: url(../svg/flag/cif-bs.svg);
}

.cif-bh {
  background-image: url(../svg/flag/cif-bh.svg);
}

.cif-bd {
  background-image: url(../svg/flag/cif-bd.svg);
}

.cif-bb {
  background-image: url(../svg/flag/cif-bb.svg);
}

.cif-by {
  background-image: url(../svg/flag/cif-by.svg);
}

.cif-be {
  background-image: url(../svg/flag/cif-be.svg);
}

.cif-bz {
  background-image: url(../svg/flag/cif-bz.svg);
}

.cif-bj {
  background-image: url(../svg/flag/cif-bj.svg);
}

.cif-bt {
  background-image: url(../svg/flag/cif-bt.svg);
}

.cif-bo {
  background-image: url(../svg/flag/cif-bo.svg);
}

.cif-ba {
  background-image: url(../svg/flag/cif-ba.svg);
}

.cif-bw {
  background-image: url(../svg/flag/cif-bw.svg);
}

.cif-br {
  background-image: url(../svg/flag/cif-br.svg);
}

.cif-bn {
  background-image: url(../svg/flag/cif-bn.svg);
}

.cif-bg {
  background-image: url(../svg/flag/cif-bg.svg);
}

.cif-bf {
  background-image: url(../svg/flag/cif-bf.svg);
}

.cif-bi {
  background-image: url(../svg/flag/cif-bi.svg);
}

.cif-kh {
  background-image: url(../svg/flag/cif-kh.svg);
}

.cif-cm {
  background-image: url(../svg/flag/cif-cm.svg);
}

.cif-ca {
  background-image: url(../svg/flag/cif-ca.svg);
}

.cif-cv {
  background-image: url(../svg/flag/cif-cv.svg);
}

.cif-cf {
  background-image: url(../svg/flag/cif-cf.svg);
}

.cif-td {
  background-image: url(../svg/flag/cif-td.svg);
}

.cif-cl {
  background-image: url(../svg/flag/cif-cl.svg);
}

.cif-cn {
  background-image: url(../svg/flag/cif-cn.svg);
}

.cif-co {
  background-image: url(../svg/flag/cif-co.svg);
}

.cif-km {
  background-image: url(../svg/flag/cif-km.svg);
}

.cif-cg {
  background-image: url(../svg/flag/cif-cg.svg);
}

.cif-cd {
  background-image: url(../svg/flag/cif-cd.svg);
}

.cif-cr {
  background-image: url(../svg/flag/cif-cr.svg);
}

.cif-ci {
  background-image: url(../svg/flag/cif-ci.svg);
}

.cif-hr {
  background-image: url(../svg/flag/cif-hr.svg);
}

.cif-cu {
  background-image: url(../svg/flag/cif-cu.svg);
}

.cif-cy {
  background-image: url(../svg/flag/cif-cy.svg);
}

.cif-cz {
  background-image: url(../svg/flag/cif-cz.svg);
}

.cif-dk {
  background-image: url(../svg/flag/cif-dk.svg);
}

.cif-dj {
  background-image: url(../svg/flag/cif-dj.svg);
}

.cif-dm {
  background-image: url(../svg/flag/cif-dm.svg);
}

.cif-do {
  background-image: url(../svg/flag/cif-do.svg);
}

.cif-ec {
  background-image: url(../svg/flag/cif-ec.svg);
}

.cif-eg {
  background-image: url(../svg/flag/cif-eg.svg);
}

.cif-sv {
  background-image: url(../svg/flag/cif-sv.svg);
}

.cif-gq {
  background-image: url(../svg/flag/cif-gq.svg);
}

.cif-er {
  background-image: url(../svg/flag/cif-er.svg);
}

.cif-ee {
  background-image: url(../svg/flag/cif-ee.svg);
}

.cif-et {
  background-image: url(../svg/flag/cif-et.svg);
}

.cif-fj {
  background-image: url(../svg/flag/cif-fj.svg);
}

.cif-fi {
  background-image: url(../svg/flag/cif-fi.svg);
}

.cif-fr {
  background-image: url(../svg/flag/cif-fr.svg);
}

.cif-ga {
  background-image: url(../svg/flag/cif-ga.svg);
}

.cif-gm {
  background-image: url(../svg/flag/cif-gm.svg);
}

.cif-ge {
  background-image: url(../svg/flag/cif-ge.svg);
}

.cif-de {
  background-image: url(../svg/flag/cif-de.svg);
}

.cif-gh {
  background-image: url(../svg/flag/cif-gh.svg);
}

.cif-gr {
  background-image: url(../svg/flag/cif-gr.svg);
}

.cif-gd {
  background-image: url(../svg/flag/cif-gd.svg);
}

.cif-gt {
  background-image: url(../svg/flag/cif-gt.svg);
}

.cif-gn {
  background-image: url(../svg/flag/cif-gn.svg);
}

.cif-gw {
  background-image: url(../svg/flag/cif-gw.svg);
}

.cif-gy {
  background-image: url(../svg/flag/cif-gy.svg);
}

.cif-hk {
  background-image: url(../svg/flag/cif-hk.svg);
}

.cif-ht {
  background-image: url(../svg/flag/cif-ht.svg);
}

.cif-va {
  background-image: url(../svg/flag/cif-va.svg);
}

.cif-hn {
  background-image: url(../svg/flag/cif-hn.svg);
}

.cif-xk {
  background-image: url(../svg/flag/cif-xk.svg);
}

.cif-hu {
  background-image: url(../svg/flag/cif-hu.svg);
}

.cif-is {
  background-image: url(../svg/flag/cif-is.svg);
}

.cif-in {
  background-image: url(../svg/flag/cif-in.svg);
}

.cif-id {
  background-image: url(../svg/flag/cif-id.svg);
}

.cif-ir {
  background-image: url(../svg/flag/cif-ir.svg);
}

.cif-iq {
  background-image: url(../svg/flag/cif-iq.svg);
}

.cif-ie {
  background-image: url(../svg/flag/cif-ie.svg);
}

.cif-il {
  background-image: url(../svg/flag/cif-il.svg);
}

.cif-it {
  background-image: url(../svg/flag/cif-it.svg);
}

.cif-jm {
  background-image: url(../svg/flag/cif-jm.svg);
}

.cif-jp {
  background-image: url(../svg/flag/cif-jp.svg);
}

.cif-jo {
  background-image: url(../svg/flag/cif-jo.svg);
}

.cif-kz {
  background-image: url(../svg/flag/cif-kz.svg);
}

.cif-ke {
  background-image: url(../svg/flag/cif-ke.svg);
}

.cif-ki {
  background-image: url(../svg/flag/cif-ki.svg);
}

.cif-kr {
  background-image: url(../svg/flag/cif-kr.svg);
}

.cif-kp {
  background-image: url(../svg/flag/cif-kp.svg);
}

.cif-kw {
  background-image: url(../svg/flag/cif-kw.svg);
}

.cif-kg {
  background-image: url(../svg/flag/cif-kg.svg);
}

.cif-la {
  background-image: url(../svg/flag/cif-la.svg);
}

.cif-lv {
  background-image: url(../svg/flag/cif-lv.svg);
}

.cif-lb {
  background-image: url(../svg/flag/cif-lb.svg);
}

.cif-ls {
  background-image: url(../svg/flag/cif-ls.svg);
}

.cif-lr {
  background-image: url(../svg/flag/cif-lr.svg);
}

.cif-ly {
  background-image: url(../svg/flag/cif-ly.svg);
}

.cif-li {
  background-image: url(../svg/flag/cif-li.svg);
}

.cif-lt {
  background-image: url(../svg/flag/cif-lt.svg);
}

.cif-lu {
  background-image: url(../svg/flag/cif-lu.svg);
}

.cif-mk {
  background-image: url(../svg/flag/cif-mk.svg);
}

.cif-mg {
  background-image: url(../svg/flag/cif-mg.svg);
}

.cif-mw {
  background-image: url(../svg/flag/cif-mw.svg);
}

.cif-my {
  background-image: url(../svg/flag/cif-my.svg);
}

.cif-mv {
  background-image: url(../svg/flag/cif-mv.svg);
}

.cif-ml {
  background-image: url(../svg/flag/cif-ml.svg);
}

.cif-mt {
  background-image: url(../svg/flag/cif-mt.svg);
}

.cif-mh {
  background-image: url(../svg/flag/cif-mh.svg);
}

.cif-mr {
  background-image: url(../svg/flag/cif-mr.svg);
}

.cif-mu {
  background-image: url(../svg/flag/cif-mu.svg);
}

.cif-mx {
  background-image: url(../svg/flag/cif-mx.svg);
}

.cif-fm {
  background-image: url(../svg/flag/cif-fm.svg);
}

.cif-md {
  background-image: url(../svg/flag/cif-md.svg);
}

.cif-mc {
  background-image: url(../svg/flag/cif-mc.svg);
}

.cif-mn {
  background-image: url(../svg/flag/cif-mn.svg);
}

.cif-me {
  background-image: url(../svg/flag/cif-me.svg);
}

.cif-ma {
  background-image: url(../svg/flag/cif-ma.svg);
}

.cif-mz {
  background-image: url(../svg/flag/cif-mz.svg);
}

.cif-mm {
  background-image: url(../svg/flag/cif-mm.svg);
}

.cif-na {
  background-image: url(../svg/flag/cif-na.svg);
}

.cif-nr {
  background-image: url(../svg/flag/cif-nr.svg);
}

.cif-np {
  background-image: url(../svg/flag/cif-np.svg);
}

.cif-nl {
  background-image: url(../svg/flag/cif-nl.svg);
}

.cif-nz {
  background-image: url(../svg/flag/cif-nz.svg);
}

.cif-ni {
  background-image: url(../svg/flag/cif-ni.svg);
}

.cif-ne {
  background-image: url(../svg/flag/cif-ne.svg);
}

.cif-ng {
  background-image: url(../svg/flag/cif-ng.svg);
}

.cif-nu {
  background-image: url(../svg/flag/cif-nu.svg);
}

.cif-no {
  background-image: url(../svg/flag/cif-no.svg);
}

.cif-om {
  background-image: url(../svg/flag/cif-om.svg);
}

.cif-pk {
  background-image: url(../svg/flag/cif-pk.svg);
}

.cif-pw {
  background-image: url(../svg/flag/cif-pw.svg);
}

.cif-pa {
  background-image: url(../svg/flag/cif-pa.svg);
}

.cif-pg {
  background-image: url(../svg/flag/cif-pg.svg);
}

.cif-py {
  background-image: url(../svg/flag/cif-py.svg);
}

.cif-pe {
  background-image: url(../svg/flag/cif-pe.svg);
}

.cif-ph {
  background-image: url(../svg/flag/cif-ph.svg);
}

.cif-pl {
  background-image: url(../svg/flag/cif-pl.svg);
}

.cif-pt {
  background-image: url(../svg/flag/cif-pt.svg);
}

.cif-qa {
  background-image: url(../svg/flag/cif-qa.svg);
}

.cif-ro {
  background-image: url(../svg/flag/cif-ro.svg);
}

.cif-ru {
  background-image: url(../svg/flag/cif-ru.svg);
}

.cif-rw {
  background-image: url(../svg/flag/cif-rw.svg);
}

.cif-kn {
  background-image: url(../svg/flag/cif-kn.svg);
}

.cif-lc {
  background-image: url(../svg/flag/cif-lc.svg);
}

.cif-vc {
  background-image: url(../svg/flag/cif-vc.svg);
}

.cif-ws {
  background-image: url(../svg/flag/cif-ws.svg);
}

.cif-sm {
  background-image: url(../svg/flag/cif-sm.svg);
}

.cif-st {
  background-image: url(../svg/flag/cif-st.svg);
}

.cif-sa {
  background-image: url(../svg/flag/cif-sa.svg);
}

.cif-sn {
  background-image: url(../svg/flag/cif-sn.svg);
}

.cif-rs {
  background-image: url(../svg/flag/cif-rs.svg);
}

.cif-sc {
  background-image: url(../svg/flag/cif-sc.svg);
}

.cif-sl {
  background-image: url(../svg/flag/cif-sl.svg);
}

.cif-sg {
  background-image: url(../svg/flag/cif-sg.svg);
}

.cif-sk {
  background-image: url(../svg/flag/cif-sk.svg);
}

.cif-si {
  background-image: url(../svg/flag/cif-si.svg);
}

.cif-sb {
  background-image: url(../svg/flag/cif-sb.svg);
}

.cif-so {
  background-image: url(../svg/flag/cif-so.svg);
}

.cif-za {
  background-image: url(../svg/flag/cif-za.svg);
}

.cif-es {
  background-image: url(../svg/flag/cif-es.svg);
}

.cif-lk {
  background-image: url(../svg/flag/cif-lk.svg);
}

.cif-sd {
  background-image: url(../svg/flag/cif-sd.svg);
}

.cif-ss {
  background-image: url(../svg/flag/cif-ss.svg);
}

.cif-sr {
  background-image: url(../svg/flag/cif-sr.svg);
}

.cif-sz {
  background-image: url(../svg/flag/cif-sz.svg);
}

.cif-se {
  background-image: url(../svg/flag/cif-se.svg);
}

.cif-ch {
  background-image: url(../svg/flag/cif-ch.svg);
}

.cif-sy {
  background-image: url(../svg/flag/cif-sy.svg);
}

.cif-tw {
  background-image: url(../svg/flag/cif-tw.svg);
}

.cif-tj {
  background-image: url(../svg/flag/cif-tj.svg);
}

.cif-tz {
  background-image: url(../svg/flag/cif-tz.svg);
}

.cif-th {
  background-image: url(../svg/flag/cif-th.svg);
}

.cif-tl {
  background-image: url(../svg/flag/cif-tl.svg);
}

.cif-tg {
  background-image: url(../svg/flag/cif-tg.svg);
}

.cif-to {
  background-image: url(../svg/flag/cif-to.svg);
}

.cif-tt {
  background-image: url(../svg/flag/cif-tt.svg);
}

.cif-tn {
  background-image: url(../svg/flag/cif-tn.svg);
}

.cif-tr {
  background-image: url(../svg/flag/cif-tr.svg);
}

.cif-tm {
  background-image: url(../svg/flag/cif-tm.svg);
}

.cif-tv {
  background-image: url(../svg/flag/cif-tv.svg);
}

.cif-ug {
  background-image: url(../svg/flag/cif-ug.svg);
}

.cif-ua {
  background-image: url(../svg/flag/cif-ua.svg);
}

.cif-ae {
  background-image: url(../svg/flag/cif-ae.svg);
}

.cif-gb {
  background-image: url(../svg/flag/cif-gb.svg);
}

.cif-us {
  background-image: url(../svg/flag/cif-us.svg);
}

.cif-uy {
  background-image: url(../svg/flag/cif-uy.svg);
}

.cif-uz {
  background-image: url(../svg/flag/cif-uz.svg);
}

.cif-ve {
  background-image: url(../svg/flag/cif-ve.svg);
}

.cif-vn {
  background-image: url(../svg/flag/cif-vn.svg);
}

.cif-ye {
  background-image: url(../svg/flag/cif-ye.svg);
}

.cif-zm {
  background-image: url(../svg/flag/cif-zm.svg);
}

.cif-zw {
  background-image: url(../svg/flag/cif-zw.svg);
}
/*# sourceMappingURL=flag.css.map */