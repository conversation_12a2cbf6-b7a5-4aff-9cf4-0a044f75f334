{"version": 3, "sources": ["flag.css", "../scss/flag/flag-icons.scss", "../scss/flag/_core.scss"], "names": [], "mappings": "AAAA,gBAAgB;ACAhB;;;;;;EDOE;AACF;EEPE,wBAAwB;EACxB,wBAAwB;EACxB,4BAA4B;EAC5B,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;EACnB,gBAAgB;AFSlB;;AEJE;EACE,6CAAmC;AFOvC;;AERE;EACE,6CAAmC;AFWvC;;AEZE;EACE,6CAAmC;AFevC;;AEhBE;EACE,6CAAmC;AFmBvC;;AEpBE;EACE,6CAAmC;AFuBvC;;AExBE;EACE,6CAAmC;AF2BvC;;AE5BE;EACE,6CAAmC;AF+BvC;;AEhCE;EACE,6CAAmC;AFmCvC;;AEpCE;EACE,6CAAmC;AFuCvC;;AExCE;EACE,6CAAmC;AF2CvC;;AE5CE;EACE,6CAAmC;AF+CvC;;AEhDE;EACE,6CAAmC;AFmDvC;;AEpDE;EACE,6CAAmC;AFuDvC;;AExDE;EACE,6CAAmC;AF2DvC;;AE5DE;EACE,6CAAmC;AF+DvC;;AEhEE;EACE,6CAAmC;AFmEvC;;AEpEE;EACE,6CAAmC;AFuEvC;;AExEE;EACE,6CAAmC;AF2EvC;;AE5EE;EACE,6CAAmC;AF+EvC;;AEhFE;EACE,6CAAmC;AFmFvC;;AEpFE;EACE,6CAAmC;AFuFvC;;AExFE;EACE,6CAAmC;AF2FvC;;AE5FE;EACE,6CAAmC;AF+FvC;;AEhGE;EACE,6CAAmC;AFmGvC;;AEpGE;EACE,6CAAmC;AFuGvC;;AExGE;EACE,6CAAmC;AF2GvC;;AE5GE;EACE,6CAAmC;AF+GvC;;AEhHE;EACE,6CAAmC;AFmHvC;;AEpHE;EACE,6CAAmC;AFuHvC;;AExHE;EACE,6CAAmC;AF2HvC;;AE5HE;EACE,6CAAmC;AF+HvC;;AEhIE;EACE,6CAAmC;AFmIvC;;AEpIE;EACE,6CAAmC;AFuIvC;;AExIE;EACE,6CAAmC;AF2IvC;;AE5IE;EACE,6CAAmC;AF+IvC;;AEhJE;EACE,6CAAmC;AFmJvC;;AEpJE;EACE,6CAAmC;AFuJvC;;AExJE;EACE,6CAAmC;AF2JvC;;AE5JE;EACE,6CAAmC;AF+JvC;;AEhKE;EACE,6CAAmC;AFmKvC;;AEpKE;EACE,6CAAmC;AFuKvC;;AExKE;EACE,6CAAmC;AF2KvC;;AE5KE;EACE,6CAAmC;AF+KvC;;AEhLE;EACE,6CAAmC;AFmLvC;;AEpLE;EACE,6CAAmC;AFuLvC;;AExLE;EACE,6CAAmC;AF2LvC;;AE5LE;EACE,6CAAmC;AF+LvC;;AEhME;EACE,6CAAmC;AFmMvC;;AEpME;EACE,6CAAmC;AFuMvC;;AExME;EACE,6CAAmC;AF2MvC;;AE5ME;EACE,6CAAmC;AF+MvC;;AEhNE;EACE,6CAAmC;AFmNvC;;AEpNE;EACE,6CAAmC;AFuNvC;;AExNE;EACE,6CAAmC;AF2NvC;;AE5NE;EACE,6CAAmC;AF+NvC;;AEhOE;EACE,6CAAmC;AFmOvC;;AEpOE;EACE,6CAAmC;AFuOvC;;AExOE;EACE,6CAAmC;AF2OvC;;AE5OE;EACE,6CAAmC;AF+OvC;;AEhPE;EACE,6CAAmC;AFmPvC;;AEpPE;EACE,6CAAmC;AFuPvC;;AExPE;EACE,6CAAmC;AF2PvC;;AE5PE;EACE,6CAAmC;AF+PvC;;AEhQE;EACE,6CAAmC;AFmQvC;;AEpQE;EACE,6CAAmC;AFuQvC;;AExQE;EACE,6CAAmC;AF2QvC;;AE5QE;EACE,6CAAmC;AF+QvC;;AEhRE;EACE,6CAAmC;AFmRvC;;AEpRE;EACE,6CAAmC;AFuRvC;;AExRE;EACE,6CAAmC;AF2RvC;;AE5RE;EACE,6CAAmC;AF+RvC;;AEhSE;EACE,6CAAmC;AFmSvC;;AEpSE;EACE,6CAAmC;AFuSvC;;AExSE;EACE,6CAAmC;AF2SvC;;AE5SE;EACE,6CAAmC;AF+SvC;;AEhTE;EACE,6CAAmC;AFmTvC;;AEpTE;EACE,6CAAmC;AFuTvC;;AExTE;EACE,6CAAmC;AF2TvC;;AE5TE;EACE,6CAAmC;AF+TvC;;AEhUE;EACE,6CAAmC;AFmUvC;;AEpUE;EACE,6CAAmC;AFuUvC;;AExUE;EACE,6CAAmC;AF2UvC;;AE5UE;EACE,6CAAmC;AF+UvC;;AEhVE;EACE,6CAAmC;AFmVvC;;AEpVE;EACE,6CAAmC;AFuVvC;;AExVE;EACE,6CAAmC;AF2VvC;;AE5VE;EACE,6CAAmC;AF+VvC;;AEhWE;EACE,6CAAmC;AFmWvC;;AEpWE;EACE,6CAAmC;AFuWvC;;AExWE;EACE,6CAAmC;AF2WvC;;AE5WE;EACE,6CAAmC;AF+WvC;;AEhXE;EACE,6CAAmC;AFmXvC;;AEpXE;EACE,6CAAmC;AFuXvC;;AExXE;EACE,6CAAmC;AF2XvC;;AE5XE;EACE,6CAAmC;AF+XvC;;AEhYE;EACE,6CAAmC;AFmYvC;;AEpYE;EACE,6CAAmC;AFuYvC;;AExYE;EACE,6CAAmC;AF2YvC;;AE5YE;EACE,6CAAmC;AF+YvC;;AEhZE;EACE,6CAAmC;AFmZvC;;AEpZE;EACE,6CAAmC;AFuZvC;;AExZE;EACE,6CAAmC;AF2ZvC;;AE5ZE;EACE,6CAAmC;AF+ZvC;;AEhaE;EACE,6CAAmC;AFmavC;;AEpaE;EACE,6CAAmC;AFuavC;;AExaE;EACE,6CAAmC;AF2avC;;AE5aE;EACE,6CAAmC;AF+avC;;AEhbE;EACE,6CAAmC;AFmbvC;;AEpbE;EACE,6CAAmC;AFubvC;;AExbE;EACE,6CAAmC;AF2bvC;;AE5bE;EACE,6CAAmC;AF+bvC;;AEhcE;EACE,6CAAmC;AFmcvC;;AEpcE;EACE,6CAAmC;AFucvC;;AExcE;EACE,6CAAmC;AF2cvC;;AE5cE;EACE,6CAAmC;AF+cvC;;AEhdE;EACE,6CAAmC;AFmdvC;;AEpdE;EACE,6CAAmC;AFudvC;;AExdE;EACE,6CAAmC;AF2dvC;;AE5dE;EACE,6CAAmC;AF+dvC;;AEheE;EACE,6CAAmC;AFmevC;;AEpeE;EACE,6CAAmC;AFuevC;;AExeE;EACE,6CAAmC;AF2evC;;AE5eE;EACE,6CAAmC;AF+evC;;AEhfE;EACE,6CAAmC;AFmfvC;;AEpfE;EACE,6CAAmC;AFufvC;;AExfE;EACE,6CAAmC;AF2fvC;;AE5fE;EACE,6CAAmC;AF+fvC;;AEhgBE;EACE,6CAAmC;AFmgBvC;;AEpgBE;EACE,6CAAmC;AFugBvC;;AExgBE;EACE,6CAAmC;AF2gBvC;;AE5gBE;EACE,6CAAmC;AF+gBvC;;AEhhBE;EACE,6CAAmC;AFmhBvC;;AEphBE;EACE,6CAAmC;AFuhBvC;;AExhBE;EACE,6CAAmC;AF2hBvC;;AE5hBE;EACE,6CAAmC;AF+hBvC;;AEhiBE;EACE,6CAAmC;AFmiBvC;;AEpiBE;EACE,6CAAmC;AFuiBvC;;AExiBE;EACE,6CAAmC;AF2iBvC;;AE5iBE;EACE,6CAAmC;AF+iBvC;;AEhjBE;EACE,6CAAmC;AFmjBvC;;AEpjBE;EACE,6CAAmC;AFujBvC;;AExjBE;EACE,6CAAmC;AF2jBvC;;AE5jBE;EACE,6CAAmC;AF+jBvC;;AEhkBE;EACE,6CAAmC;AFmkBvC;;AEpkBE;EACE,6CAAmC;AFukBvC;;AExkBE;EACE,6CAAmC;AF2kBvC;;AE5kBE;EACE,6CAAmC;AF+kBvC;;AEhlBE;EACE,6CAAmC;AFmlBvC;;AEplBE;EACE,6CAAmC;AFulBvC;;AExlBE;EACE,6CAAmC;AF2lBvC;;AE5lBE;EACE,6CAAmC;AF+lBvC;;AEhmBE;EACE,6CAAmC;AFmmBvC;;AEpmBE;EACE,6CAAmC;AFumBvC;;AExmBE;EACE,6CAAmC;AF2mBvC;;AE5mBE;EACE,6CAAmC;AF+mBvC;;AEhnBE;EACE,6CAAmC;AFmnBvC;;AEpnBE;EACE,6CAAmC;AFunBvC;;AExnBE;EACE,6CAAmC;AF2nBvC;;AE5nBE;EACE,6CAAmC;AF+nBvC;;AEhoBE;EACE,6CAAmC;AFmoBvC;;AEpoBE;EACE,6CAAmC;AFuoBvC;;AExoBE;EACE,6CAAmC;AF2oBvC;;AE5oBE;EACE,6CAAmC;AF+oBvC;;AEhpBE;EACE,6CAAmC;AFmpBvC;;AEppBE;EACE,6CAAmC;AFupBvC;;AExpBE;EACE,6CAAmC;AF2pBvC;;AE5pBE;EACE,6CAAmC;AF+pBvC;;AEhqBE;EACE,6CAAmC;AFmqBvC;;AEpqBE;EACE,6CAAmC;AFuqBvC;;AExqBE;EACE,6CAAmC;AF2qBvC;;AE5qBE;EACE,6CAAmC;AF+qBvC;;AEhrBE;EACE,6CAAmC;AFmrBvC;;AEprBE;EACE,6CAAmC;AFurBvC;;AExrBE;EACE,6CAAmC;AF2rBvC;;AE5rBE;EACE,6CAAmC;AF+rBvC;;AEhsBE;EACE,6CAAmC;AFmsBvC;;AEpsBE;EACE,6CAAmC;AFusBvC;;AExsBE;EACE,6CAAmC;AF2sBvC;;AE5sBE;EACE,6CAAmC;AF+sBvC;;AEhtBE;EACE,6CAAmC;AFmtBvC;;AEptBE;EACE,6CAAmC;AFutBvC;;AExtBE;EACE,6CAAmC;AF2tBvC;;AE5tBE;EACE,6CAAmC;AF+tBvC;;AEhuBE;EACE,6CAAmC;AFmuBvC;;AEpuBE;EACE,6CAAmC;AFuuBvC;;AExuBE;EACE,6CAAmC;AF2uBvC;;AE5uBE;EACE,6CAAmC;AF+uBvC;;AEhvBE;EACE,6CAAmC;AFmvBvC;;AEpvBE;EACE,6CAAmC;AFuvBvC;;AExvBE;EACE,6CAAmC;AF2vBvC;;AE5vBE;EACE,6CAAmC;AF+vBvC;;AEhwBE;EACE,6CAAmC;AFmwBvC;;AEpwBE;EACE,6CAAmC;AFuwBvC;;AExwBE;EACE,6CAAmC;AF2wBvC;;AE5wBE;EACE,6CAAmC;AF+wBvC;;AEhxBE;EACE,6CAAmC;AFmxBvC;;AEpxBE;EACE,6CAAmC;AFuxBvC", "file": "flag.css", "sourcesContent": ["@charset \"UTF-8\";\n/*!\n * CoreUI Icons - Flag Icons\n * @version v1.0.1\n * @link https://coreui.io/icons/flag/\n * Copyright (c) 2020 creativeLabs <PERSON><PERSON>\n * Licensed under CC0 1.0 Universal\n */\n[class^=\"cif-\"], [class*=\" cif-\"] {\n  background-size: contain;\n  background-position: 50%;\n  background-repeat: no-repeat;\n  position: relative;\n  display: inline-block;\n  width: 1.33333333em;\n  line-height: 1em;\n}\n\n.cif-af {\n  background-image: url(../svg/flag/cif-af.svg);\n}\n\n.cif-al {\n  background-image: url(../svg/flag/cif-al.svg);\n}\n\n.cif-dz {\n  background-image: url(../svg/flag/cif-dz.svg);\n}\n\n.cif-ad {\n  background-image: url(../svg/flag/cif-ad.svg);\n}\n\n.cif-ao {\n  background-image: url(../svg/flag/cif-ao.svg);\n}\n\n.cif-ag {\n  background-image: url(../svg/flag/cif-ag.svg);\n}\n\n.cif-ar {\n  background-image: url(../svg/flag/cif-ar.svg);\n}\n\n.cif-am {\n  background-image: url(../svg/flag/cif-am.svg);\n}\n\n.cif-au {\n  background-image: url(../svg/flag/cif-au.svg);\n}\n\n.cif-at {\n  background-image: url(../svg/flag/cif-at.svg);\n}\n\n.cif-az {\n  background-image: url(../svg/flag/cif-az.svg);\n}\n\n.cif-bs {\n  background-image: url(../svg/flag/cif-bs.svg);\n}\n\n.cif-bh {\n  background-image: url(../svg/flag/cif-bh.svg);\n}\n\n.cif-bd {\n  background-image: url(../svg/flag/cif-bd.svg);\n}\n\n.cif-bb {\n  background-image: url(../svg/flag/cif-bb.svg);\n}\n\n.cif-by {\n  background-image: url(../svg/flag/cif-by.svg);\n}\n\n.cif-be {\n  background-image: url(../svg/flag/cif-be.svg);\n}\n\n.cif-bz {\n  background-image: url(../svg/flag/cif-bz.svg);\n}\n\n.cif-bj {\n  background-image: url(../svg/flag/cif-bj.svg);\n}\n\n.cif-bt {\n  background-image: url(../svg/flag/cif-bt.svg);\n}\n\n.cif-bo {\n  background-image: url(../svg/flag/cif-bo.svg);\n}\n\n.cif-ba {\n  background-image: url(../svg/flag/cif-ba.svg);\n}\n\n.cif-bw {\n  background-image: url(../svg/flag/cif-bw.svg);\n}\n\n.cif-br {\n  background-image: url(../svg/flag/cif-br.svg);\n}\n\n.cif-bn {\n  background-image: url(../svg/flag/cif-bn.svg);\n}\n\n.cif-bg {\n  background-image: url(../svg/flag/cif-bg.svg);\n}\n\n.cif-bf {\n  background-image: url(../svg/flag/cif-bf.svg);\n}\n\n.cif-bi {\n  background-image: url(../svg/flag/cif-bi.svg);\n}\n\n.cif-kh {\n  background-image: url(../svg/flag/cif-kh.svg);\n}\n\n.cif-cm {\n  background-image: url(../svg/flag/cif-cm.svg);\n}\n\n.cif-ca {\n  background-image: url(../svg/flag/cif-ca.svg);\n}\n\n.cif-cv {\n  background-image: url(../svg/flag/cif-cv.svg);\n}\n\n.cif-cf {\n  background-image: url(../svg/flag/cif-cf.svg);\n}\n\n.cif-td {\n  background-image: url(../svg/flag/cif-td.svg);\n}\n\n.cif-cl {\n  background-image: url(../svg/flag/cif-cl.svg);\n}\n\n.cif-cn {\n  background-image: url(../svg/flag/cif-cn.svg);\n}\n\n.cif-co {\n  background-image: url(../svg/flag/cif-co.svg);\n}\n\n.cif-km {\n  background-image: url(../svg/flag/cif-km.svg);\n}\n\n.cif-cg {\n  background-image: url(../svg/flag/cif-cg.svg);\n}\n\n.cif-cd {\n  background-image: url(../svg/flag/cif-cd.svg);\n}\n\n.cif-cr {\n  background-image: url(../svg/flag/cif-cr.svg);\n}\n\n.cif-ci {\n  background-image: url(../svg/flag/cif-ci.svg);\n}\n\n.cif-hr {\n  background-image: url(../svg/flag/cif-hr.svg);\n}\n\n.cif-cu {\n  background-image: url(../svg/flag/cif-cu.svg);\n}\n\n.cif-cy {\n  background-image: url(../svg/flag/cif-cy.svg);\n}\n\n.cif-cz {\n  background-image: url(../svg/flag/cif-cz.svg);\n}\n\n.cif-dk {\n  background-image: url(../svg/flag/cif-dk.svg);\n}\n\n.cif-dj {\n  background-image: url(../svg/flag/cif-dj.svg);\n}\n\n.cif-dm {\n  background-image: url(../svg/flag/cif-dm.svg);\n}\n\n.cif-do {\n  background-image: url(../svg/flag/cif-do.svg);\n}\n\n.cif-ec {\n  background-image: url(../svg/flag/cif-ec.svg);\n}\n\n.cif-eg {\n  background-image: url(../svg/flag/cif-eg.svg);\n}\n\n.cif-sv {\n  background-image: url(../svg/flag/cif-sv.svg);\n}\n\n.cif-gq {\n  background-image: url(../svg/flag/cif-gq.svg);\n}\n\n.cif-er {\n  background-image: url(../svg/flag/cif-er.svg);\n}\n\n.cif-ee {\n  background-image: url(../svg/flag/cif-ee.svg);\n}\n\n.cif-et {\n  background-image: url(../svg/flag/cif-et.svg);\n}\n\n.cif-fj {\n  background-image: url(../svg/flag/cif-fj.svg);\n}\n\n.cif-fi {\n  background-image: url(../svg/flag/cif-fi.svg);\n}\n\n.cif-fr {\n  background-image: url(../svg/flag/cif-fr.svg);\n}\n\n.cif-ga {\n  background-image: url(../svg/flag/cif-ga.svg);\n}\n\n.cif-gm {\n  background-image: url(../svg/flag/cif-gm.svg);\n}\n\n.cif-ge {\n  background-image: url(../svg/flag/cif-ge.svg);\n}\n\n.cif-de {\n  background-image: url(../svg/flag/cif-de.svg);\n}\n\n.cif-gh {\n  background-image: url(../svg/flag/cif-gh.svg);\n}\n\n.cif-gr {\n  background-image: url(../svg/flag/cif-gr.svg);\n}\n\n.cif-gd {\n  background-image: url(../svg/flag/cif-gd.svg);\n}\n\n.cif-gt {\n  background-image: url(../svg/flag/cif-gt.svg);\n}\n\n.cif-gn {\n  background-image: url(../svg/flag/cif-gn.svg);\n}\n\n.cif-gw {\n  background-image: url(../svg/flag/cif-gw.svg);\n}\n\n.cif-gy {\n  background-image: url(../svg/flag/cif-gy.svg);\n}\n\n.cif-hk {\n  background-image: url(../svg/flag/cif-hk.svg);\n}\n\n.cif-ht {\n  background-image: url(../svg/flag/cif-ht.svg);\n}\n\n.cif-va {\n  background-image: url(../svg/flag/cif-va.svg);\n}\n\n.cif-hn {\n  background-image: url(../svg/flag/cif-hn.svg);\n}\n\n.cif-xk {\n  background-image: url(../svg/flag/cif-xk.svg);\n}\n\n.cif-hu {\n  background-image: url(../svg/flag/cif-hu.svg);\n}\n\n.cif-is {\n  background-image: url(../svg/flag/cif-is.svg);\n}\n\n.cif-in {\n  background-image: url(../svg/flag/cif-in.svg);\n}\n\n.cif-id {\n  background-image: url(../svg/flag/cif-id.svg);\n}\n\n.cif-ir {\n  background-image: url(../svg/flag/cif-ir.svg);\n}\n\n.cif-iq {\n  background-image: url(../svg/flag/cif-iq.svg);\n}\n\n.cif-ie {\n  background-image: url(../svg/flag/cif-ie.svg);\n}\n\n.cif-il {\n  background-image: url(../svg/flag/cif-il.svg);\n}\n\n.cif-it {\n  background-image: url(../svg/flag/cif-it.svg);\n}\n\n.cif-jm {\n  background-image: url(../svg/flag/cif-jm.svg);\n}\n\n.cif-jp {\n  background-image: url(../svg/flag/cif-jp.svg);\n}\n\n.cif-jo {\n  background-image: url(../svg/flag/cif-jo.svg);\n}\n\n.cif-kz {\n  background-image: url(../svg/flag/cif-kz.svg);\n}\n\n.cif-ke {\n  background-image: url(../svg/flag/cif-ke.svg);\n}\n\n.cif-ki {\n  background-image: url(../svg/flag/cif-ki.svg);\n}\n\n.cif-kr {\n  background-image: url(../svg/flag/cif-kr.svg);\n}\n\n.cif-kp {\n  background-image: url(../svg/flag/cif-kp.svg);\n}\n\n.cif-kw {\n  background-image: url(../svg/flag/cif-kw.svg);\n}\n\n.cif-kg {\n  background-image: url(../svg/flag/cif-kg.svg);\n}\n\n.cif-la {\n  background-image: url(../svg/flag/cif-la.svg);\n}\n\n.cif-lv {\n  background-image: url(../svg/flag/cif-lv.svg);\n}\n\n.cif-lb {\n  background-image: url(../svg/flag/cif-lb.svg);\n}\n\n.cif-ls {\n  background-image: url(../svg/flag/cif-ls.svg);\n}\n\n.cif-lr {\n  background-image: url(../svg/flag/cif-lr.svg);\n}\n\n.cif-ly {\n  background-image: url(../svg/flag/cif-ly.svg);\n}\n\n.cif-li {\n  background-image: url(../svg/flag/cif-li.svg);\n}\n\n.cif-lt {\n  background-image: url(../svg/flag/cif-lt.svg);\n}\n\n.cif-lu {\n  background-image: url(../svg/flag/cif-lu.svg);\n}\n\n.cif-mk {\n  background-image: url(../svg/flag/cif-mk.svg);\n}\n\n.cif-mg {\n  background-image: url(../svg/flag/cif-mg.svg);\n}\n\n.cif-mw {\n  background-image: url(../svg/flag/cif-mw.svg);\n}\n\n.cif-my {\n  background-image: url(../svg/flag/cif-my.svg);\n}\n\n.cif-mv {\n  background-image: url(../svg/flag/cif-mv.svg);\n}\n\n.cif-ml {\n  background-image: url(../svg/flag/cif-ml.svg);\n}\n\n.cif-mt {\n  background-image: url(../svg/flag/cif-mt.svg);\n}\n\n.cif-mh {\n  background-image: url(../svg/flag/cif-mh.svg);\n}\n\n.cif-mr {\n  background-image: url(../svg/flag/cif-mr.svg);\n}\n\n.cif-mu {\n  background-image: url(../svg/flag/cif-mu.svg);\n}\n\n.cif-mx {\n  background-image: url(../svg/flag/cif-mx.svg);\n}\n\n.cif-fm {\n  background-image: url(../svg/flag/cif-fm.svg);\n}\n\n.cif-md {\n  background-image: url(../svg/flag/cif-md.svg);\n}\n\n.cif-mc {\n  background-image: url(../svg/flag/cif-mc.svg);\n}\n\n.cif-mn {\n  background-image: url(../svg/flag/cif-mn.svg);\n}\n\n.cif-me {\n  background-image: url(../svg/flag/cif-me.svg);\n}\n\n.cif-ma {\n  background-image: url(../svg/flag/cif-ma.svg);\n}\n\n.cif-mz {\n  background-image: url(../svg/flag/cif-mz.svg);\n}\n\n.cif-mm {\n  background-image: url(../svg/flag/cif-mm.svg);\n}\n\n.cif-na {\n  background-image: url(../svg/flag/cif-na.svg);\n}\n\n.cif-nr {\n  background-image: url(../svg/flag/cif-nr.svg);\n}\n\n.cif-np {\n  background-image: url(../svg/flag/cif-np.svg);\n}\n\n.cif-nl {\n  background-image: url(../svg/flag/cif-nl.svg);\n}\n\n.cif-nz {\n  background-image: url(../svg/flag/cif-nz.svg);\n}\n\n.cif-ni {\n  background-image: url(../svg/flag/cif-ni.svg);\n}\n\n.cif-ne {\n  background-image: url(../svg/flag/cif-ne.svg);\n}\n\n.cif-ng {\n  background-image: url(../svg/flag/cif-ng.svg);\n}\n\n.cif-nu {\n  background-image: url(../svg/flag/cif-nu.svg);\n}\n\n.cif-no {\n  background-image: url(../svg/flag/cif-no.svg);\n}\n\n.cif-om {\n  background-image: url(../svg/flag/cif-om.svg);\n}\n\n.cif-pk {\n  background-image: url(../svg/flag/cif-pk.svg);\n}\n\n.cif-pw {\n  background-image: url(../svg/flag/cif-pw.svg);\n}\n\n.cif-pa {\n  background-image: url(../svg/flag/cif-pa.svg);\n}\n\n.cif-pg {\n  background-image: url(../svg/flag/cif-pg.svg);\n}\n\n.cif-py {\n  background-image: url(../svg/flag/cif-py.svg);\n}\n\n.cif-pe {\n  background-image: url(../svg/flag/cif-pe.svg);\n}\n\n.cif-ph {\n  background-image: url(../svg/flag/cif-ph.svg);\n}\n\n.cif-pl {\n  background-image: url(../svg/flag/cif-pl.svg);\n}\n\n.cif-pt {\n  background-image: url(../svg/flag/cif-pt.svg);\n}\n\n.cif-qa {\n  background-image: url(../svg/flag/cif-qa.svg);\n}\n\n.cif-ro {\n  background-image: url(../svg/flag/cif-ro.svg);\n}\n\n.cif-ru {\n  background-image: url(../svg/flag/cif-ru.svg);\n}\n\n.cif-rw {\n  background-image: url(../svg/flag/cif-rw.svg);\n}\n\n.cif-kn {\n  background-image: url(../svg/flag/cif-kn.svg);\n}\n\n.cif-lc {\n  background-image: url(../svg/flag/cif-lc.svg);\n}\n\n.cif-vc {\n  background-image: url(../svg/flag/cif-vc.svg);\n}\n\n.cif-ws {\n  background-image: url(../svg/flag/cif-ws.svg);\n}\n\n.cif-sm {\n  background-image: url(../svg/flag/cif-sm.svg);\n}\n\n.cif-st {\n  background-image: url(../svg/flag/cif-st.svg);\n}\n\n.cif-sa {\n  background-image: url(../svg/flag/cif-sa.svg);\n}\n\n.cif-sn {\n  background-image: url(../svg/flag/cif-sn.svg);\n}\n\n.cif-rs {\n  background-image: url(../svg/flag/cif-rs.svg);\n}\n\n.cif-sc {\n  background-image: url(../svg/flag/cif-sc.svg);\n}\n\n.cif-sl {\n  background-image: url(../svg/flag/cif-sl.svg);\n}\n\n.cif-sg {\n  background-image: url(../svg/flag/cif-sg.svg);\n}\n\n.cif-sk {\n  background-image: url(../svg/flag/cif-sk.svg);\n}\n\n.cif-si {\n  background-image: url(../svg/flag/cif-si.svg);\n}\n\n.cif-sb {\n  background-image: url(../svg/flag/cif-sb.svg);\n}\n\n.cif-so {\n  background-image: url(../svg/flag/cif-so.svg);\n}\n\n.cif-za {\n  background-image: url(../svg/flag/cif-za.svg);\n}\n\n.cif-es {\n  background-image: url(../svg/flag/cif-es.svg);\n}\n\n.cif-lk {\n  background-image: url(../svg/flag/cif-lk.svg);\n}\n\n.cif-sd {\n  background-image: url(../svg/flag/cif-sd.svg);\n}\n\n.cif-ss {\n  background-image: url(../svg/flag/cif-ss.svg);\n}\n\n.cif-sr {\n  background-image: url(../svg/flag/cif-sr.svg);\n}\n\n.cif-sz {\n  background-image: url(../svg/flag/cif-sz.svg);\n}\n\n.cif-se {\n  background-image: url(../svg/flag/cif-se.svg);\n}\n\n.cif-ch {\n  background-image: url(../svg/flag/cif-ch.svg);\n}\n\n.cif-sy {\n  background-image: url(../svg/flag/cif-sy.svg);\n}\n\n.cif-tw {\n  background-image: url(../svg/flag/cif-tw.svg);\n}\n\n.cif-tj {\n  background-image: url(../svg/flag/cif-tj.svg);\n}\n\n.cif-tz {\n  background-image: url(../svg/flag/cif-tz.svg);\n}\n\n.cif-th {\n  background-image: url(../svg/flag/cif-th.svg);\n}\n\n.cif-tl {\n  background-image: url(../svg/flag/cif-tl.svg);\n}\n\n.cif-tg {\n  background-image: url(../svg/flag/cif-tg.svg);\n}\n\n.cif-to {\n  background-image: url(../svg/flag/cif-to.svg);\n}\n\n.cif-tt {\n  background-image: url(../svg/flag/cif-tt.svg);\n}\n\n.cif-tn {\n  background-image: url(../svg/flag/cif-tn.svg);\n}\n\n.cif-tr {\n  background-image: url(../svg/flag/cif-tr.svg);\n}\n\n.cif-tm {\n  background-image: url(../svg/flag/cif-tm.svg);\n}\n\n.cif-tv {\n  background-image: url(../svg/flag/cif-tv.svg);\n}\n\n.cif-ug {\n  background-image: url(../svg/flag/cif-ug.svg);\n}\n\n.cif-ua {\n  background-image: url(../svg/flag/cif-ua.svg);\n}\n\n.cif-ae {\n  background-image: url(../svg/flag/cif-ae.svg);\n}\n\n.cif-gb {\n  background-image: url(../svg/flag/cif-gb.svg);\n}\n\n.cif-us {\n  background-image: url(../svg/flag/cif-us.svg);\n}\n\n.cif-uy {\n  background-image: url(../svg/flag/cif-uy.svg);\n}\n\n.cif-uz {\n  background-image: url(../svg/flag/cif-uz.svg);\n}\n\n.cif-ve {\n  background-image: url(../svg/flag/cif-ve.svg);\n}\n\n.cif-vn {\n  background-image: url(../svg/flag/cif-vn.svg);\n}\n\n.cif-ye {\n  background-image: url(../svg/flag/cif-ye.svg);\n}\n\n.cif-zm {\n  background-image: url(../svg/flag/cif-zm.svg);\n}\n\n.cif-zw {\n  background-image: url(../svg/flag/cif-zw.svg);\n}\n\n/*# sourceMappingURL=flag.css.map */", "/*!\n * CoreUI Icons - Flag Icons\n * @version v1.0.1\n * @link https://coreui.io/icons/flag/\n * Copyright (c) 2020 creativeLabs <PERSON><PERSON>\n * Licensed under CC0 1.0 Universal\n */\n\n@import \"variables\";\n@import \"functions\";\n@import \"core\";\n", "[class^=\"#{$prefix}\"], [class*=\" #{$prefix}\"] {\n  background-size: contain;\n  background-position: 50%;\n  background-repeat: no-repeat;\n  position: relative;\n  display: inline-block;\n  width: 1.33333333em;\n  line-height: 1em;\n}\n\n@each $icon, $unicode in $icons {\n  $icon-lower: to-lower-case(#{$icon});\n  .#{$prefix}#{$icon-lower} {\n    background-image: url(../svg/flag/#{$prefix}#{$icon}.svg);\n  }\n}\n"]}