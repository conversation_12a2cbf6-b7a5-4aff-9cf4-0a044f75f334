import {use} from "echarts/core";
import {<PERSON>vas<PERSON><PERSON><PERSON>} from "echarts/renderers";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>} from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  PolarComponent,
  ToolboxComponent,
  DatasetComponent,
  VisualMapComponent,
  TimelineComponent,
  TransformComponent
} from "echarts/components";

use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  TitleComponent,
  Tooltip<PERSON>omponent,
  LegendComponent,
  GridComponent,
  PolarComponent,
  ToolboxComponent,
  DatasetComponent,
  VisualMapComponent,
  TimelineComponent,
  TransformComponent
]);
