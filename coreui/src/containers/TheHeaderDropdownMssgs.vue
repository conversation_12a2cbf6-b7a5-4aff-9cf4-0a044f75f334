<template>
  <CDropdown
    :caret="false"
    placement="bottom-end"
    in-nav
    class="c-header-nav-item mx-2"
    add-menu-classes="pt-0"
    :disabled="accountLock"
  >
    <template #toggler>
      <CHeaderNavLink :disabled="accountLock">
        <CIcon name="cil-envelope-closed" />
        <CBadge shape="pill" color="info">{{
          getMessageNotifications.length
        }}</CBadge>
      </CHeaderNavLink>
    </template>
    <CDropdownHeader tag="div" class="text-center bg-light">
      <strong>You have {{ getMessageNotifications.length }} messages</strong>
    </CDropdownHeader>
    <div style="overflow-y: scroll; height: 500px">
      <template v-for="(notification, key) in getMessageNotifications">
        <CDropdownItem
          :key="key"
          :disabled="accountLock"
          @click="goToLink(notification)"
          :to="{
            name: notification.data.type == 'message' ? 'Show_Message' : '',
            params: { id: notification.data.id },
          }"
          class="w-20"
        >
          <div class="notification">
            <div class="avatar">
              <div class="c-avatar">
                <img
                  :src="notification.data.sender.url"
                  class="c-avatar-img"
                  :alt="notification.data.sender.name"
                />
              </div>
            </div>
            <div class="notification-body">
              <small class="sender">{{ notification.data.sender.name }}</small>
              <small class="subject">{{ notification.data.subject }}</small>
            </div>
          </div>
        </CDropdownItem>
      </template>
    </div>
  </CDropdown>
</template>
<script>
import { mapState, mapGetters, mapActions } from "vuex";
export default {
  name: "TheHeaderDropdownMssgs",
  data() {
    return { messageNotifications: [] };
  },
  computed: {
    ...mapState("authentication", ["accountLock"]),
    ...mapGetters("notifications", ["getMessageNotifications"]),
  },
  methods: {
    ...mapActions("notifications", ["removeNotification"]),
    async goToLink(notification) {
      try {
        await axios.get(`/api/notifications/read/${notification.id}`);
        this.removeNotification(notification);
      } catch (e) {
        this.showErrorMessage(e);
      }
    },
  },
};
</script>
<style scoped>
.notification {
  display: flex;
  justify-content: space-between;
}
.notification .notification-body {
  display: flex;
  flex-direction: column;
}
.w-20 {
  width: 20em;
}
</style>
