<template>
  <c-dropdown
    :disabled="accountLock"
    inNav
    class="c-header-nav-items"
    placement="bottom-end"
    add-menu-classes="pt-0"
  >
    <template #toggler>
      <c-header-nav-link :disabled="accountLock">
        <div class="c-avatar avatar-lg">
          <img :src="authUser.url" class="c-avatar-img" />
        </div>
      </c-header-nav-link>
    </template>
    <c-dropdown-header tag="div" class="text-center" color="light">
      <strong>{{authUser.fullname}}</strong>
    </c-dropdown-header>
    <c-dropdown-item
      v-if="checkPermission('show_single_user_profile')"
      :to="{ name: 'profile' }"
      :disabled="accountLock"
    >
      <c-icon class="custom_icon" name="cil-user" /> Profile
    </c-dropdown-item>

    <c-dropdown-divider />
    <c-dropdown-item
      v-if="checkPermission('change_profile_pic')"
      :to="{ name: 'changeProfilePic' }"
      :disabled="accountLock"
    >
      <c-icon class="custom_icon" name="cil-user" /> Change Profile Picture
    </c-dropdown-item>
    <c-dropdown-item
      v-if="checkPermission('lock_account')"
      @click="lock"
      :disabled="accountLock"
    >
      <c-icon class="custom_icon" name="cil-shield-alt" /> Lock Account
    </c-dropdown-item>
    <c-dropdown-item
      v-if="checkPermission('change_password')"
      :to="{ name: 'changePassword' }"
      :disabled="accountLock"
    >
      <c-icon class="custom_icon" name="cil-lock-locked" /> Change Password
    </c-dropdown-item>
    <c-dropdown-item
      v-if="checkPermission('logout')"
      @click="logout"
      :disabled="accountLock"
    >
      <c-icon class="custom_icon" name="cil-lock-locked" /> Logout
    </c-dropdown-item>
  </c-dropdown>
</template>

<script>
import { mapActions, mapState } from "vuex";
export default {
  name: "TheHeaderDropdownAccnt",
  props: {
    imageUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      user: {
        gender: localStorage.getItem("user_gender"),
      },
    };
  },
  computed: {
    ...mapState("authentication", ["accountLock","authUser"]),
  },
  methods: {
    ...mapActions("authentication", ["removeAuthUser", "lockAccount"]),
    logout() {
      axios
        .post("/api/logout", {})
        .then((response) => {
          this.$store.commit("app/setApiToken", null);
          this.removeAuthUser();
          localStorage.clear();
          this.$router.push({ path: "/login" });
        })
        .catch((error) => {
          console.log(error);
          this.showErrorMessage(error);
        });
    },
    lock() {
      axios
        .get("/api/lockscreen")
        .then(() => {
          this.$router.push({ path: "/lockscreen" });
          this.lockAccount();
          this.flash("Account is locked .");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
};
</script>

<style scoped>
.c-icon {
  margin-right: 0.3rem;
}
</style>
