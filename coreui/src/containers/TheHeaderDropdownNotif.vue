<template>
  <CDropdown
    placement="bottom-end"
    :caret="false"
    in-nav
    class="c-header-nav-item mx-2"
    add-menu-classes="pt-0"
    :disabled="accountLock"
  >
    <template #toggler>
      <CHeaderNavLink :disabled="accountLock">
        <CIcon name="cil-bell" />
        <CBadge shape="pill" color="danger">{{
          getNormalNotifications.length
        }}</CBadge>
      </CHeaderNavLink>
    </template>
    <CDropdownHeader tag="div" class="text-center bg-light">
      <strong
        >You have {{ getNormalNotifications.length }} notifications</strong
      >
    </CDropdownHeader>
    <div style="overflow-y: scroll; height: 500px">
      <CDropdownItem
        v-for="(notification, index) in getNormalNotifications"
        :key="index"
        :disabled="accountLock"
        :to="notification.data.link"
        @click="goToLink(notification)"
      >
        <div class="notification">
          <div class="avatar">
            <div class="c-avatar">
              <img
                :src="notification.data.user.url"
                class="c-avatar-img"
                :alt="notification.data.user.name"
              />
            </div>
          </div>
          <div class="notification-body">
            <small class="sender">{{ notification.data.user.name }}</small>
            <small class="subject">{{ notification.data.message }}</small>
          </div>
        </div>
      </CDropdownItem>
    </div>
  </CDropdown>
</template>
<script>
import { mapState, mapGetters, mapActions } from "vuex";
export default {
  name: "TheHeaderDropdownNotif",
  data() {
    return { itemsCount: 5 };
  },
  computed: {
    ...mapState("authentication", ["accountLock"]),
    ...mapGetters("notifications", ["getNormalNotifications"]),
  },
  methods: {
    ...mapActions("notifications", ["removeNotification"]),
    async goToLink(notification) {
      try {
        console.log(notification);
        await axios.get(`/api/notifications/read/${notification.id}`);
        this.removeNotification(notification);
      } catch (e) {
        this.showErrorMessage(e);
      }
    },
  },
};
</script>

<style scoped>
.c-icon {
  margin-right: 0.3rem;
}
.notification {
  display: flex;
  justify-content: space-between;
}
.notification .notification-body {
  display: flex;
  flex-direction: column;
}
.w-20 {
  width: 20em;
}
</style>
