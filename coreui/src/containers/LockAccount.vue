<template>
    <CModal
      :ref="dialogRef"
      :show.sync="lockModal"
      :closeOnBackdrop="false"
      :centered="true"
      title="Modal title 2"
      size="lg"
      color="dark"
    >
      <p class="text-muted">Enter your password to unlock again</p>
      <CInput
        v-model="password"
        prependHtml="<i class='cui-lock-locked'></i>"
        placeholder="Password"
        type="password"
        autocomplete="curent-password"
      >
        <template #prepend-content><CIcon name="cil-lock-locked"/></template>
      </CInput>
      <template #header>
        <h6 class="modal-title">Lock Screen</h6>
        <!-- <CButtonClose @click="lockModal = false" class="text-white"/> -->
      </template>
      <template #footer>
        <!-- <CButton @click="closeMyModal()" color="danger">Discard</CButton> -->
        <CButton @click="lockscreen()" color="success">Unlock</CButton>
      </template>
    </CModal>
</template>

<script>
import axios from 'axios'
export default {
  name: 'LockAccount',
  data () {
    return {
      dialogRef: 'myDialog',
      message: '',
      showMessage: false,
      dismissSecs: 7,
      dismissCountDown: 0,
      showDismissibleAlert: false,
      successModal: false,
      lockModal: false,
      modalCode:'',
      modalMessage:'',
      modalException:'',
      modalStatusText:'',
    }
  },
  methods:{
    openMyModal() {
        this.lockModal = true;
    },
    closeMyModal() {
        this.lockModal = false;
    },
    countDownChanged (dismissCountDown) {
      this.dismissCountDown = dismissCountDown
    },
    showAlert () {
      this.dismissCountDown = this.dismissSecs
    },
    modalAlert () {
      this.dangerModalAlert= true
    },
    lockscreen() {
      let self = this;
      axios.post('/api/lockscreenunlock?token=' + localStorage.getItem("api_token"),
      {
        password: self.password,
      }).then(function (response) {
        self.password = '';
        // self.hasHistory()?$router.go(-1): $router.push('/');
        // self.$router.back();
        self.closeMyModal();
      })
      .catch(function (error) {
        if (typeof error.response !== 'undefined') {
          if(error.response.data.exception){
            self.modalCode = '';
            self.modalMessage = '';
            self.modalException = '';
            self.modalStatusText = '';
            self.modalCode = error.response.status;
            self.modalMessage = error.response.data.message;
            self.modalStatusText = error.response.data.statusText;
            self.modalException = error.response.data.exception;
            self.modalAlert();
          }
          else if(error.response.data.statusText == 'password does not match.'){
            self.message = 'password does not match.';
            self.showAlert();}
          else if(error.response.data.statusText == 'The given data was invalid.'){
            self.$router.push({ path: '/login' });
            // self.message = self.failed_login;
            self.showAlert();}
            // }else if(error.response.data.statusText == 'The given data was invalid.'){
            //       self.message = '';
            //       for (let key in error.response.data.errors) {
            //         if (error.response.data.errors.hasOwnProperty(key)) {
            //      // self.message += '\n' + error.response.data.errors[key][0] + '';
            //         }
            //       }
            //       self.showAlert();
            // }
          else{
            console.log(error);
          }
        }else{
          if(error){
            self.modalCode = '';
            self.modalException = '';
            self.modalStatusText = '';
            self.modalMessage = '';
            self.modalMessage = 'Error:' + error;
            self.modalAlert();
          }else{
            console.log(error);
          }
        }
      });

    },
    logout(){
      let self = this;
      axios.post('/api/logout?token=' + localStorage.getItem("api_token"),{})
      .then(function (response) {
        self.$router.push({ path: '/login' });
      }).catch(function (error) {
            if (typeof error.response !== 'undefined') {
              if(error.response.data.exception){
                self.modalCode = '';
                self.modalMessage = '';
                self.modalException = '';
                self.modalStatusText = '';
                self.modalCode = error.response.status;
                self.modalMessage = error.response.data.message;
                self.modalStatusText = error.response.data.statusText;
                self.modalException = error.response.data.exception;
                self.modalAlert();
              }else if(error.response.data.message == 'The given data was invalid.'){
                    self.message = '';
                    for (let key in error.response.data.errors) {
                      if (error.response.data.errors.hasOwnProperty(key)) {
                        self.message += error.response.data.errors[key][0] + '  ';
                      }
                    }
                    self.showAlert();
              }else{
                console.log(error);
              }
            }else{
              if(error){
                self.modalCode = '';
                self.modalException = '';
                self.modalStatusText = '';
                self.modalMessage = '';
                self.modalMessage = 'Error:' + error;
                self.modalAlert();
              }else{
                console.log(error);
              }
            }
      });
    },
    changePassword(){
      this.$router.push({path: '/changepassword'});
    },
    changeProfilePic(){
      this.$router.push({path: '/changeprofilepic'});
    }
  }
}
</script>
