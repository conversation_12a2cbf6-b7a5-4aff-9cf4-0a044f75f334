<template>
  <CDropdown
    :caret="false"
    placement="bottom-end"
    in-nav
    class="c-header-nav-item d-md-down-none mx-2"
    add-menu-classes="pt-0"
    :disabled="accountLock"
  >
    <template #toggler>
      <CHeaderNavLink :disabled="accountLock">
        <CIcon name="cil-list-rich"/>
        <CBadge shape="pill" color="warning">{{itemsCount}}</CBadge>
      </CHeaderNavLink>
    </template>
    <CDropdownHeader
      tag="div"
      class="text-center bg-light"
    >
      <strong>You have {{itemsCount}} pending tasks</strong>
    </CDropdownHeader>

    <CDropdownItem :disabled="accountLock" class="d-block">
      <div class="small mb-1">
        Upgrade NPM &amp; Bower <span class="float-right"><strong>0%</strong></span>
      </div>
      <CProgress class="progress-xs" color="info"/>
    </CDropdownItem>

    <CDropdownItem :disabled="accountLock" class="d-block">
      <div class="small mb-1">
        ReactJS Version <span class="float-right"><strong>25%</strong></span>
      </div>
      <CProgress class="progress-xs" color="danger" :value="25"/>
    </CDropdownItem>

    <CDropdownItem :disabled="accountLock" class="d-block">
      <div class="small mb-1">
        VueJS Version <span class="float-right"><strong>50%</strong></span>
      </div>
      <CProgress class="progress-xs" color="warning" :value="50"/>
    </CDropdownItem>

    <CDropdownItem :disabled="accountLock" class="d-block">
      <div class="small mb-1">
        Add new layouts <span class="float-right"><strong>75%</strong></span>
      </div>
      <CProgress class="progress-xs" color="info" :value="75"/>
    </CDropdownItem>

    <CDropdownItem :disabled="accountLock" class="d-block">
      <div class="small mb-1">
        Angular 2 Cli Version <span class="float-right"><strong>100%</strong></span>
      </div>
      <CProgress class="progress-xs" color="success" :value="100"/>
    </CDropdownItem>

    <CDropdownItem :disabled="accountLock" class="text-center border-top">
      <strong>View all tasks</strong>
    </CDropdownItem>

  </CDropdown>
</template>
<script>
import {mapState} from 'vuex'
export default {
  name: 'TheHeaderDropdownTasks',
  data () {
    return { itemsCount: 15 }
  },
  computed: {
    ...mapState('authentication',['accountLock'])
  }
}
</script>
