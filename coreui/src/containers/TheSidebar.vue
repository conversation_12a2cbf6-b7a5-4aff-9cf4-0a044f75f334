<template>
  <c-sidebar
    :minimize="minimize"
    :show="show"
    :unfoldable="unfoldable"
    dropdownMode="close"
    @update:show="(value) => $store.commit('set', ['sidebarShow', value])"
  >
    <sidebar-brand
      :minimize="minimize"
      :logo-url="url"
    />

    <sidebar-search
      :minimize="minimize"
      v-model="searchQuery"
    />

    <c-sidebar-nav>
      <nav-item
        v-for="item in filteredNavItems"
        :key="item.id"
        :item="item"
        :search-query="searchQuery"
      />
    </c-sidebar-nav>

    <c-sidebar-minimizer
      class="c-brand-minimizer"
      @click.native="$store.commit('set', ['sidebarMinimize', !minimize])"
    />
  </c-sidebar>
</template>

<script>
import {mapActions} from "vuex";
import nav from "./_nav.js";
import NavItem from "../components/sidebar/NavItem";
import SidebarSearch from "../components/sidebar/SidebarSearch";
import SidebarBrand from "../components/sidebar/SidebarBrand";

export default {
  name: "TheSidebar",
  components: {
    NavItem,
    SidebarSearch,
    SidebarBrand
  },
  props: ["locale"],
  data() {
    return {
      unfoldable: false,
      url: null,
      searchQuery: '',
    };
  },
  computed: {
    show() {
      return this.$store.state.sidebarShow;
    },
    minimize() {
      return this.$store.state.sidebarMinimize;
    },
    filteredNavItems() {
      if (!this.searchQuery) {
        return nav;
      }

      const query = this.searchQuery.toLowerCase().trim();
      return this.filterNavigationData(nav, query);
    }
  },
  methods: {
    ...mapActions("company", ["saveCompanyName"]),
    getLogo() {
      axios
        .get(`/api/companies/1`)
        .then((response) => {
          this.url = response.data.data.company.url;
          this.saveCompanyName(response.data.data.company.name);
          document.body.style.fontSize = response.data.data.font_size;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    filterNavigationData(data, query) {
      const filteredItems = [];

      for (let i = 0; i < data.length; i++) {
        const item = JSON.parse(JSON.stringify(data[i]));

        // Check permissions first
        if (item.permission && !this.checkPermission(item.permission)) {
          continue;
        }

        // Check if the item name matches the query
        const nameMatches = item.name && item.name.toLowerCase().includes(query);

        // For dropdown items, also check and filter the children
        if (item.type === 'dropdown' && item.children) {
          const filteredChildren = this.filterNavigationData(item.children, query);

          // If this dropdown has matching children or its name matches, include it
          if (filteredChildren.length > 0 || nameMatches) {
            item.children = filteredChildren;
            filteredItems.push(item);
          }
        }
        // For non-dropdown items (links and titles), include if name matches
        else if (nameMatches) {
          filteredItems.push(item);
        }
      }

      return filteredItems;
    },
  },
  created() {
    this.$root.$on("toggle-sidebar", () => {
      const sidebarOpened = this.show === true || this.show === "responsive";
      this.show = sidebarOpened ? false : "responsive";
    });
    this.$root.$on("toggle-sidebar-mobile", () => {
      const sidebarClosed = this.show === "responsive" || this.show === false;
      this.show = sidebarClosed ? true : "responsive";
    });
    this.getLogo();
  },
  watch: {
    minimize(newValue) {
      // Clear search when minimizing sidebar
      if (newValue && this.searchQuery) {
        this.searchQuery = '';
      }
    }
  }
};
</script>
