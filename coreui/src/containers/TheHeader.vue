<template>
  <c-header with-subheader>
    <c-toggler in-header class="ml-3 d-lg-none" @click="$store.commit('toggleSidebarMobile')" />
    <c-toggler in-header class="ml-3 d-md-down-none" @click="$store.commit('toggleSidebarDesktop')" />
    <c-button v-if="checkPermission('show_all_logos')" class="c-sidebar-brand d-md-down-none router-link-active"><img
        :src="urlLogo" class="c-sidebar-brand-full" /></c-button>
    <!-- @click="$root.$ramadan('ramadan.wav')" -->
    <c-header-nav class="mt-2">
      <c-header-nav-item class="px-1">
        <button :disabled="accountLock" @click="() => $store.commit('toggle', 'darkMode')" class="c-header-nav-btn">
          <c-icon v-if="$store.state.darkMode" name="cil-sun" />
          <c-icon v-else name="cil-moon" />
        </button>
      </c-header-nav-item>
      <c-header-nav-item class="px-1">
        <button v-if="automatic == 'No'" :disabled="accountLock" @click="checkLocation" class="c-header-nav-btn">
          <c-icon v-if="location === null || location.checkout_date" name="cil-location-pin" size="lg"
            style="color: blue" />
          <c-icon v-if="location && location.checkout_date === null" name="cil-location-pin" size="lg"
            style="color: red" />
        </button>
        <button v-if="automatic == 'Yes'" :disabled="accountLock" @click="addAutomaticExpense" class="c-header-nav-btn">
          <c-icon v-if="location === null || location.checkout_date" name="cil-location-pin" size="lg"
            style="color: blue" />
          <c-icon v-if="location && location.checkout_date === null" name="cil-location-pin" size="lg"
            style="color: red" />
        </button>
        <automaticDialog ref="automaticDialog" @getVisitDivision="saveVisitedDivision"></automaticDialog>
      </c-header-nav-item>
      <the-header-dropdown-notif />
      <!-- <the-header-dropdown-tasks /> -->
      <the-header-dropdown-mssgs />
      <the-header-dropdown-accnt class="px-3" v-if="checkPermission('show_user_profile_image')"
        :imageUrl="imageUrl || 'img/avatars/male.png'" />
      <c-header-nav-item>
        <!-- <button
          in-header
          class="c-header-nav-btn"
          @click="$store.commit('toggle', 'asideShow')"
          :disabled="accountLock"
        >
          <c-icon size="lg" name="cil-applications-settings" class="mr-2" />
        </button> -->
      </c-header-nav-item>
    </c-header-nav>

    <c-subheader class="px-3 flex" v-if="!accountLock">
      <c-breadcrumb-router class="border-0 mb-0" />
      <!-- <button
        :disabled="accountLock"
        @click="checkLocation"
        class="c-header-nav-btn flex mt-2"
        style="display: flex; justify-content: right"
      >
        <c-icon
          v-if="location === null || location.checkout_date"
          name="cil-location-pin"
          size="xl"
          style="color: blue"
        />
        <c-icon
          v-if="location && location.checkout_date === null"
          name="cil-location-pin"
          size="xl"
          style="color: red"
        />
      </button> -->
    </c-subheader>
  </c-header>
</template>

<script>
import AutomaticDialog from "../components/common/AutomaticDialog.vue";
import TheHeaderDropdownAccnt from "./TheHeaderDropdownAccnt.vue";
import TheHeaderDropdownNotif from "./TheHeaderDropdownNotif.vue";
import TheHeaderDropdownTasks from "./TheHeaderDropdownTasks.vue";
import TheHeaderDropdownMssgs from "./TheHeaderDropdownMssgs.vue";
import { mapState } from "vuex";

export default {
  name: "TheHeader",
  components: {
    TheHeaderDropdownAccnt,
    TheHeaderDropdownNotif,
    TheHeaderDropdownTasks,
    TheHeaderDropdownMssgs,
    AutomaticDialog
  },
  data() {
    return {
      langs: [],
      locale: "en",
      url: null,
      urlLogo: null,
      automatic: null,
      imageUrl: null,
      divisions: [],
      location: null,
      marker: { position: { lat: 0, lng: 0 } },
      visible: true,
    };
  },
  computed: {
    ...mapState("authentication", ["accountLock"]),
    ...mapState("authentication", ["authUser"]),
  },
  methods: {
    setElapsedTime() {
      const millis = Date.now() - this.startTime;
      this.elapsedTime = `seconds elapsed = ${Math.floor(millis / 1000)}`;
    },
    getImageProfile() {
      axios
        .get("/api/users/null/profile/image")
        .then((response) => {
          this.imageUrl = response.data.url;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    selectLocale(option) {
      localStorage.setItem("locale", option);
      this.$i18n.set(option);
      this.$emit("change-locale", option);
    },
    getItgatesLogo() {
      axios
        .get(`/api/logo/3`)
        .then((response) => {
          this.urlLogo = response.data.data.url;
          this.visible = true;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    async checkLocation() {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition((position) => {
          this.marker.position = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
        });
        await axios
          .post("/api/check-location", {
            location: this.location ? this.location : null,
            position: this.marker.position,
          })
          .then((response) => {
            this.flash(response.data.status);
            if (response.data.message) {
              this.flash(response.data.message, "error");
            }
            this.getLocation();
          })
          .catch((error) => {
            this.showErrorMessage(error);
          });
      } else {
        this.showErrorMessage("Location is not supported");
      }
      console.log(this.marker.position);
    },
    addAutomaticExpense() {
      if (this.location === null || this.location.checkout_date) {
        this.$refs.automaticDialog.open('Add Visits Division: ', this.divisions);
      } else {
        if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition((position) => {
          this.marker.position = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
        });      
        axios
          .post("/api/add-automatic-expense", {
            location: this.location ? this.location : null,
            position: this.marker.position,
          })
          .then((response) => {
            this.flash(response.data.status);
            if (response.data.message) {
              this.flash(response.data.message, "error");
            }
            this.getLocation();
          })
          .catch((error) => {
            this.showErrorMessage(error);
          });
        }
      }

    },
    saveVisitedDivision(division) {
      axios
        .post("/api/add-automatic-expense", {
          location: this.location ? this.location : null,
          division: division,
        })
        .then((response) => {
          this.flash(response.data.status);
          if (response.data.message) {
            this.flash(response.data.message, "error");
          }
          this.$refs.automaticDialog.cancel();
          this.getLocation();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLocation() {
      axios
        .get("/api/show-check-location")
        .then((response) => {
          this.location = response.data.data.location;
          this.divisions = response.data.data.divisions;
          this.automatic = response.data.data.automatic_setting;
        })
        .catch((error) => {
          this.showErrorMessage("Check Location has wrong number", "error");
        });
    },
  },
  created() {
    this.getLocation();
    if (typeof localStorage.locale !== "undefined") {
      this.locale = localStorage.getItem("locale");
    }
    axios
      .get("/api/langlist")
      .then((response) => {
        this.langs = [];
        for (let i = 0; i < response.data.length; i++) {
          this.langs.push({
            value: response.data[i].short_name,
            label: response.data[i].name,
          });
        }
      })
      .catch((error) => {
        this.showErrorMessage(error);
      });
    // this.getLogo();
    this.getImageProfile();
    this.getItgatesLogo();
  },
};
</script>
