export default [
  {
    id: 1,
    type: "dropdown",
    name: "Dashboards",
    icon: "cil-dashboard",
    permission: "show_menu_dashboards",
    children: [
      {
        id: 2,
        type: "link",
        name: "Main",
        to: "/dashboards/main",
        icon: "cil-home",
        permission: "show_menu_main"
      },
      {
        id: 3,
        type: "link",
        name: "KPIs",
        to: "/dashboards/kpis",
        icon: "cil-kpis",
        permission: "show_menu_kpis"
      },
      {
        id: 4,
        type: "link",
        name: "Sales",
        to: "/dashboards/sales",
        icon: "cil-money",
        permission: "show_menu_sales"
      },
      {
        id: 5,
        type: "link",
        name: "Statistics",
        to: "/dashboards/statistics",
        icon: "cil-calculator",
        permission: "show_menu_statistics"
      },
      {
        id: 6,
        type: "link",
        name: "M.R Statistics",
        to: "/dashboards/m.r-statistics",
        icon: "cib-superuser",
        permission: "show_menu_m.r-statistics"
      }
    ]
  },
  {
    id: 7,
    type: "link",
    name: "Calendar",
    to: "/calendar",
    icon: "cil-calendar",
    permission: "show_menu_calendar"
  },
  {
    id: 8,
    type: "dropdown",
    name: "<PERSON>",
    icon: "cil-phone",
    permission: "show_menu_communication",
    children: [
      {
        id: 9,
        type: "link",
        name: "Internal Messaging",
        to: "/internal-messaging/inbox",
        icon: "cil-speech",
        permission: "show_menu_internal_messaging"
      },
      {
        id: 10,
        type: "link",
        name: "Tasks",
        to: "/tasks",
        icon: "cil-task",
        permission: "show_menu_tasks"
      },
      {
        id: 11,
        type: "link",
        name: "Announcement",
        to: "/announcements",
        icon: "cil-microphone",
        permission: "show_menu_announcement"
      },
      {
        id: 12,
        type: "link",
        name: "Live",
        to: "/live",
        icon: "cil-video",
        permission: "show_menu_live"
      }
    ]
  },
  {
    id: 13,
    type: "dropdown",
    name: "Visits",
    icon: "cilColumns",
    permission: "show_menu_visits",
    children: [
      {
        id: 14,
        type: "dropdown",
        name: "Plan",
        icon: "cilBraille",
        permission: "show_menu_plan",
        children: [
          {
            id: 15,
            type: "link",
            name: "Periodical Plan Visits",
            to: "/plans",
            icon: "cil-loop",
            permission: "show_menu_periodical_plan_visits"
          },
          {
            id: 16,
            type: "link",
            name: "Plan Scheduler",
            to: "/plans/scheduler",
            icon: "cil-drag-and-drop",
            permission: "show_menu_plan_scheduler"
          },
          {
            id: 17,
            type: "link",
            name: "Plan Visits (Brick View)",
            to: "/plans/bricks",
            icon: "cil-list-low-priority",
            permission: "show_menu_plan_visits_brick_view"
          },
          {
            id: 18,
            type: "link",
            name: "Change Plan",
            to: "/change-plans",
            icon: "cib-plex",
            permission: "show_menu_change_plan"
          },
          {
            id: 19,
            type: "link",
            name: "Plan OW & Activities",
            to: "/owplanvisits",
            icon: "cil-description",
            permission: "show_menu_plan_ow_activities"
          },
          {
            id: 20,
            type: "link",
            name: "Start Point",
            to: "/start-point",
            icon: "cil-arrow-thick-from-left",
            permission: "show_menu_start_point"
          },
          {
            id: 21,
            type: "link",
            name: "Plan Approvals",
            to: "/planapprovals",
            icon: "cil-check",
            permission: "show_menu_plan_approvals"
          },
          {
            id: 22,
            type: "link",
            name: "Change Plan Approvals",
            to: "/change-plan-approvals",
            icon: "cib-verizon",
            permission: "show_menu_change_plan_approvals"
          },
          {
            id: 23,
            type: "link",
            name: "Plan Double Visit",
            to: "/double_plan",
            icon: "cil-clone",
            permission: "show_menu_plan_double_visit"
          },
          {
            id: 24,
            type: "link",
            name: "Automatic Plan Visit",
            to: "/automatic_plan",
            icon: "cib-autotask",
            permission: "show_menu_automatic_plan_visit"
          },
          {
            id: 25,
            type: "link",
            name: "Plan Visit Settings",
            to: "/plansettings",
            icon: "cil-settings",
            permission: "show_menu_plan_visit_settings"
          }
        ],
        to: "/"
      },
      {
        id: 26,
        type: "dropdown",
        name: "Actual",
        icon: "cil-task",
        permission: "show_menu_actual",
        children: [
          {
            id: 27,
            type: "link",
            name: "Actual Visits",
            to: "/actual_visits",
            icon: "cil-calendar-check",
            permission: "show_menu_actual_visits"
          },
          {
            id: 28,
            type: "link",
            name: "Actual OW & Activities",
            to: "/owactualvisits",
            icon: "cil-description",
            permission: "show_menu_actual_ow_activities"
          },
          {
            id: 29,
            type: "link",
            name: "Actual Approvals",
            to: "/actualapprovals",
            icon: "cil-cloudy",
            permission: "show_menu_actual_approvals"
          },
          {
            id: 30,
            type: "link",
            name: "Double Visit Feedbacks",
            to: "/actualdoublefeedbacks",
            icon: "cil-speech",
            permission: "show_menu_double_visit_feedbacks"
          },
          {
            id: 31,
            type: "link",
            name: "Actual Visit Settings",
            to: "/actualvisitsettings",
            icon: "cil-settings",
            permission: "show_menu_actual_visit_settings"
          },
          {
            id: 32,
            type: "link",
            name: "Favourite List",
            to: "/favourite-lists",
            icon: "cil-fax",
            permission: "show_menu_favourite_list"
          },
          {
            id: 33,
            type: "link",
            name: "Favourite Approvals",
            to: "/favourite-list-approvals",
            icon: "cil-check",
            permission: "show_menu_favourite_approvals"
          },
          {
            id: 34,
            type: "link",
            name: "Customize Your List",
            to: "/kol-lists",
            icon: "cil-fax",
            permission: "show_menu_customize_your_list"
          },
          {
            id: 35,
            type: "link",
            name: "List Management",
            to: "/list-management",
            icon: "cil-briefcase",
            permission: "show_menu_list_management"
          },
          {
            id: 36,
            type: "link",
            name: "Pv Approvals",
            to: "/pv-approvals",
            icon: "cil-fax",
            permission: "show_menu_pv_approvals"
          },
          {
            id: 37,
            type: "link",
            name: "Doctor Frequency",
            to: "/doctorfrequencies/create",
            icon: "cil-fax",
            permission: "show_menu_doctor_frequency"
          }
        ],
        to: "/"
      }
    ],
    to: "/",
  },
  {
    id: 38,
    type: "dropdown",
    name: "Requests",
    icon: "cil-color-border",
    children: [
      {
        id: 39,
        type: "link",
        name: "Commercial and Brandings",
        to: "/commercial",
        icon: "cil-color-palette",
        permission: "show_menu_commercial_and_brandings"
      },
      {
        id: 40,
        type: "link",
        name: "Commercial Approvals",
        to: "/commercial-approvals",
        icon: "cil-check",
        permission: "show_menu_commercial_approvals",
      },
      {
        id: 41,
        type: "link",
        name: "Commercial Bills",
        to: "/commercial-bills",
        icon: "cil-list",
        permission: "show_menu_commercial_bills"
      },
      {
        id: 42,
        type: "link",
        name: "Commercial Bills Approvals",
        to: "/commercial-bills-approvals",
        icon: "cil-check",
        permission: "show_menu_commercial_bills_approvals"
      },
      {
        id: 43,
        type: "link",
        name: "Custody",
        to: "/custody",
        icon: "cil-calculator",
        permission: "show_menu_custody"
      },
      {
        id: 44,
        type: "link",
        name: "Expenses",
        to: "/expenses",
        icon: "cil-money",
        permission: "show_menu_expenses"
      },
      {
        id: 45,
        type: "link",
        name: "Expense Approvals",
        to: "/expense-approvals",
        icon: "cil-check",
        permission: "show_menu_expense_approvals"
      },
      {
        id: 46,
        type: "link",
        name: "Location Price",
        to: "/expense-location-prices",
        icon: "cil-monitor",
        permission: "show_menu_location_price"
      },
      {
        id: 47,
        type: "link",
        name: "Material",
        to: "/material",
        icon: "cil-cart",
        permission: "show_menu_material"
      },
      {
        id: 48,
        type: "link",
        name: "Material Approvals",
        to: "/material-approvals",
        icon: "cil-check",
        permission: "show_menu_material_approvals"
      },
      {
        id: 49,
        type: "link",
        name: "Material Types",
        to: "/promotional-material-types",
        icon: "cil-cart",
        permission: "show_menu_material_types"
      },
      {
        id: 50,
        type: "link",
        name: "Vacations",
        to: "/vacations",
        icon: "cil-beach-access",
        permission: "show_menu_vacations"
      },
      {
        id: 51,
        type: "link",
        name: "Vacation Approvals",
        to: "/vacation-approvals",
        icon: "cil-check",
        permission: "show_menu_vacation_approvals"
      },
      {
        id: 52,
        type: "link",
        name: "Personal Request",
        to: "/personal-request",
        icon: "cil-user",
        permission: "show_menu_personal_request"
      },
      {
        id: 53,
        type: "link",
        name: "Account Requests",
        to: "/account-request",
        icon: "cil-color-palette",
        permission: "show_menu_account_requests"
      },
      {
        id: 54,
        type: "link",
        name: "Account Request Approvals",
        to: "/account-request-approvals",
        icon: "cil-check",
        permission: "show_menu_account_request_approvals"
      },
      {
        id: 55,
        type: "link",
        name: "Linked Pharmacies",
        to: "/linked-pharmacies",
        icon: "cil-clipboard",
        permission: "show_menu_linked_pharmacies"
      },
      {
        id: 56,
        type: "link",
        name: "Linked Pharmacies Per Brands",
        to: "/linked-pharmacies-per-brand",
        icon: "cil-clipboard",
        permission: "show_menu_linked_pharmacies_per_brands"
      },
      {
        id: 57,
        type: "link",
        name: "Budget Setup",
        to: "/budget-setup",
        icon: "cil-bank",
        permission: "show_menu_budget_setup"
      }
    ],
    to: "/",
    permission: "show_menu_requests"
  },
  {
    id: 58,
    type: "dropdown",
    name: "Sales",
    icon: "cil-dollar",
    children: [
      {
        id: 59,
        type: "link",
        name: "Sales Importer",
        to: "/upload",
        icon: "cil-data-transfer-down",
        permission: "show_menu_sales_importer"
      },
      {
        id: 60,
        type: "link",
        name: "Incentive Calculations",
        to: "/",
        icon: "cil-calculator",
        permission: "show_menu_incentive_calculations"
      }
    ],
    to: "/",
    permission: "show_menu_sales"
  },
  {
    id: 61,
    type: "dropdown",
    name: "Training",
    icon: "cil-people",
    children: [
      {
        id: 62,
        type: "link",
        name: "Files",
        to: "/",
        icon: "cil-clipboard",
        permission: "show_menu_files"
      },
      {
        id: 63,
        type: "link",
        name: "Videos",
        to: "/",
        icon: "cil-movie",
        permission: "show_menu_videos"
      },
      {
        id: 64,
        type: "link",
        name: "Live Sessions",
        to: "/",
        icon: "cil-video",
        permission: "show_menu_live_sessions"
      },
      {
        id: 65,
        type: "link",
        name: "Coaching",
        to: "/coaching",
        icon: "cil-list",
        permission: "show_menu_coaching"
      },
      {
        id: 66,
        type: "link",
        name: "Quizzes",
        to: "/quiz",
        icon: "cil-info",
        permission: "show_menu_quizzes"
      }
    ],
    to: "/",
    permission: "show_menu_training"
  },
  {
    id: 67,
    type: "dropdown",
    name: "Tools",
    icon: "cil-briefcase",
    children: [
      {
        id: 68,
        type: "link",
        name: "Log Activity",
        to: "/logActivities",
        icon: "cil-list",
        permission: "show_menu_log_activity"
      },
      {
        id: 69,
        type: "link",
        name: "Files Imported",
        to: "/imports",
        icon: "cil-file",
        permission: "show_menu_files_imported"
      }
    ],
    to: "/",
    permission: "show_menu_tools"
  },
  {
    id: 70,
    type: "link",
    name: "Settings",
    to: "/setting-links",
    icon: "cil-settings",
    permission: "show_menu_settings"
  },
  {
    id: 71,
    type: "link",
    name: "Reports",
    to: "/reports",
    icon: "cil-align-left",
    permission: "show_menu_reports"
  }
];

