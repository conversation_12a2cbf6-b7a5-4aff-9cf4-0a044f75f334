/* Global styles for tables with frozen headers */

/* Wrapper for tables with scrollable content */
.table-scroll-wrapper {
  position: relative;
  max-height: 70vh; /* Adjust this value based on your needs */
  overflow: auto;
}

/* Sticky header styles for all tables with the frozen-header class */
.frozen-header-table > thead > tr > th,
.table-class > thead > tr > th {
  position: sticky !important;
  top: 0 !important;
  z-index: 10 !important;
  background-color: #005cc8 !important;
  color: white !important;
  text-align: center !important;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.1) !important;
}

/* Make sure the thead-top row is also sticky if present */
.frozen-header-table > thead > tr:first-child > td,
.table-class > thead > tr:first-child > td {
  position: sticky !important;
  top: 0 !important;
  z-index: 10 !important;
  background-color: white !important;
}

/* If there's a thead-top, adjust the position of the actual headers */
.frozen-header-table > thead > tr:nth-child(2) > th,
.table-class > thead > tr:nth-child(2) > th {
  top: 41px !important; /* Adjust based on the height of your thead-top row */
}

/* Hover effect for rows */
.frozen-header-table > tbody > tr:hover,
.table-class > tbody > tr:hover {
  background-color: rgba(0, 92, 200, 0.05) !important;
}
