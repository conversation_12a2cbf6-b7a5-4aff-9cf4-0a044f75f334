/* Global styles for tables with sticky headers */

/* Table container with fixed height and scrolling */
.table-container {
  position: relative;
  max-height: 70vh;
  overflow: auto;
  width: 100%;
}

/* Basic table styling */
.data-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 14px;
}

/* Sticky header styles */
.data-table thead th {
  position: sticky;
  top: 0;
  background: #0056b3;
  color: white;
  padding: 12px 16px;
  text-align: center;
  font-weight: 600;
  white-space: nowrap;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* First row in thead (if there are multiple rows) */
.data-table thead tr:first-child th {
  top: 0;
  z-index: 11;
}

/* Second row in thead (if there are multiple rows) */
.data-table thead tr:nth-child(2) th {
  top: 48px; /* Adjust based on the height of the first header row */
  z-index: 10;
}

/* Total header styling */
.total-header {
  background-color: white !important;
  color: #333 !important;
  text-align: left !important;
  padding-left: 20px !important;
}

/* Column header styling */
.column-header {
  background-color: #0056b3;
  color: white;
}

/* Row hover effect */
.data-table tbody tr:hover {
  background-color: rgba(0, 92, 200, 0.05);
}

/* Cell styling */
.data-table td {
  padding: 10px 16px;
  border-bottom: 1px solid #e2e8f0;
  vertical-align: middle;
}

/* Apply sticky headers to CoreUI data tables as well */
.c-data-table thead th {
  position: sticky !important;
  top: 0 !important;
  background: #0056b3 !important;
  color: white !important;
  z-index: 10 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

/* First row in CoreUI data table thead */
.c-data-table thead tr:first-child th {
  top: 0 !important;
  z-index: 11 !important;
}

/* Second row in CoreUI data table thead */
.c-data-table thead tr:nth-child(2) th {
  top: 48px !important; /* Adjust based on the height of the first header row */
  z-index: 10 !important;
}

/* Make sure the thead-top row is also sticky if present */
.c-data-table thead tr:first-child td {
  position: sticky !important;
  top: 0 !important;
  z-index: 10 !important;
  background-color: white !important;
}
