// Variable overrides

// // $font-size-root affects the value of `rem`, which is used for as well font sizes, paddings, and margins
// // $font-size-base affects the font size of the body text
// $font-size-root:              null;
// $font-size-base:              0.8rem; // Assumes the browser default, typically `16px`
// $font-size-sm:                $font-size-base * .875;
// $font-size-lg:                $font-size-base * 1.25;

// $font-weight-lighter:         lighter;
// $font-weight-light:           300;
// $font-weight-normal:          400;
// $font-weight-medium:          500;
// $font-weight-semibold:        600;
// $font-weight-bold:            700;
// $font-weight-bolder:          bolder;

// $font-weight-base:            $font-weight-normal;

// $line-height-base:            1.5;
// $line-height-sm:              1.25;
// $line-height-lg:              2;

// $h1-font-size:                $font-size-base * 2.5;
// $h2-font-size:                $font-size-base * 2;
// $h3-font-size:                $font-size-base * 1.75;
// $h4-font-size:                $font-size-base * 1.5;
// $h5-font-size:                $font-size-base * 1.25;
// $h6-font-size:                $font-size-base;