// Here you can add other styles
.select-lang-menu {
    max-width: 128px;
    cursor: pointer;
}

.card-header>.c-icon:first-child {
    margin-right: 0.1rem;
    margin-top: 0.1rem;
    vertical-align: top;
    width: 1.2rem;
    height: 1.2rem;
    font-size: 1.2rem;
}

.click-file {
    cursor: pointer;
}

#cropp-img-img {
    max-width: 500px;
    max-height: 500px;
}

.c-header-nav {
    display: flex;
    flex-direction: row;
    align-items: center;
    min-height: $header-height;
    padding: 0;
    margin-bottom: 0;
    list-style: none;
    margin-left: auto;
}

.btn.btn-primary {
    background-color: #005cc8 !important;
    color: white !important;
}

.btn.btn-danger {
    color: white;
}

.btn.btn-primary:hover {
    background-color: #4189de !important;
}

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
    color: white !important;
    background-color: #005cc8 !important;
}

.c-sidebar-nav-dropdown-items .c-sidebar-nav-item {
    padding-left: 20px;
}

// .c-sidebar-nav-dropdown .c-sidebar-nav-icon {
//     color: #e09898 !important;
// }
// .c-sidebar-nav-item .c-sidebar-nav-icon {
//     color: #e09898 !important;
// }
.custom_icon {
    margin-bottom: 3px !important;
    color: #e09898 !important;
}

.custom-file-upload {
    margin-top: 15px !important;
    margin-bottom: 3px !important;
}

.custom-container {
    display: flex;
    gap: 0.2rem;
    flex-wrap: wrap;
}

.custom-container>* {
    flex-basis: calc(calc(20rem - 5%));
    flex-grow: 1;
}

.custom-table {
    display: table;
    white-space: nowrap;
    width: 100%;
}

.custom-thead {
    opacity: 1;
    position: sticky;
    top: 0;
    color: white;
    background: #2eb85c;
}

.custom-label {
    font-weight: bolder;
    margin-bottom: 0;
}

// /* Custom dark mode styles for vue-select */
// .vue-select-dark .vs__dropdown-menu {
//     @include themes($c-multi-select-theme-map) {
//         color: themes-get-value("c-multi-select-color");
//         background: themes-get-value("c-multi-select-bg") themes-get-value("c-multi-select-background");
//         border-color: themes-get-value("c-multi-select-border-color");
//       }
// }

// .vue-select-dark .vs__dropdown-option--selected,
// .vue-select-dark .vs__dropdown-option--highlight {
//     @include themes($c-multi-select-theme-map) {
//         color: themes-get-value("c-multi-select-color");
//         background: themes-get-value("c-multi-select-bg") themes-get-value("c-multi-select-background");
//         border-color: themes-get-value("c-multi-select-border-color");
//       }
// }

/* Optionally apply a border and adjust focus outline for consistency */
// .vue-select-dark .vs__dropdown-menu {
//     border-color: themes-get-value("c-multi-select-border-color");
// }

.vs__selected-options {
    max-height: 80px !important;
    overflow: auto !important;
}

/* Light mode styles */
.vs__dropdown-menu {
    background-color: #ffffff;
    /* Light background */
    color: #333333;
    /* Dark text color */
    border-color: #cccccc;
    /* Light border */
}

/* Dark mode styles (when CoreUI's dark theme is active) */
.c-dark-theme .vs__dropdown-menu {
    background-color: #2a2a2a;
    /* Dark background */
    color: #f0f0f0;
    /* Light text color */
    border-color: #333333;
    /* Dark border */
}

/* Highlight and selection styles */
// .vs--single .vs__selected ,
.vs__dropdown-option--selected,
.vs__dropdown-option--highlight {
    background-color: #e0e0e0;
    /* Light mode highlight */
    color: #333333;
}

.c-dark-theme .vs__clear,
.c-dark-theme .vs--single .vs__selected,
.c-dark-theme .vs__dropdown-option--selected,
.c-dark-theme .vs__dropdown-option--highlight {
    background-color: #444444;
    /* Dark mode highlight */
    color: #ffffff;
}


/* Dark mode search input background in v-select */
.c-dark-theme .vs__dropdown-menu,
.c-dark-theme .vs__search ,
.c-dark-theme .vs--searchable .vs__dropdown-toggle,
.c-dark-theme .vs__selected-options {
    background-color: #2f303a;
    /* Dark background */
    color: #f0f0f0;
    /* Light text color */
    border-color: #333333;
    /* Border color for dark mode */
}


::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    border-radius: 8px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
}