<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/laravel/app" isTestSource="false" packagePrefix="App\" />
      <sourceFolder url="file://$MODULE_DIR$/laravel/database/factories" isTestSource="false" packagePrefix="Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/laravel/database/seeders" isTestSource="false" packagePrefix="Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/laravel/database/seeders/permissions" isTestSource="false" packagePrefix="Database\Seeders\Permissions\" />
      <sourceFolder url="file://$MODULE_DIR$/laravel/tests" isTestSource="true" packagePrefix="Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/laravel/tests/Unit" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/carbonphp/carbon-doctrine-types" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/laravel/horizon" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/laravel/prompts" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/mpdf/psr-http-message-shim" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/webklex/laravel-imap" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/webklex/php-imap" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>