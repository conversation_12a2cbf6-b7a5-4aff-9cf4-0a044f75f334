<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MessDetectorOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCSFixerOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCodeSnifferOptionsConfiguration">
    <option name="highlightLevel" value="WARNING" />
    <option name="transferred" value="true" />
  </component>
  <component name="PhpIncludePathManager">
    <include_path>
      <path value="$PROJECT_DIR$/laravel/vendor/nikic/php-parser" />
      <path value="$PROJECT_DIR$/laravel/vendor/dragonmantank/cron-expression" />
      <path value="$PROJECT_DIR$/laravel/vendor/staudenmeir/eloquent-has-many-deep" />
      <path value="$PROJECT_DIR$/laravel/vendor/staudenmeir/belongs-to-through" />
      <path value="$PROJECT_DIR$/laravel/vendor/staudenmeir/laravel-adjacency-list" />
      <path value="$PROJECT_DIR$/laravel/vendor/staudenmeir/laravel-cte" />
      <path value="$PROJECT_DIR$/laravel/vendor/filp/whoops" />
      <path value="$PROJECT_DIR$/laravel/vendor/phpoption/phpoption" />
      <path value="$PROJECT_DIR$/laravel/vendor/carlos-meneses/laravel-mpdf" />
      <path value="$PROJECT_DIR$/laravel/vendor/phpseclib/phpseclib" />
      <path value="$PROJECT_DIR$/laravel/vendor/corbosman/laravel-passport-claims" />
      <path value="$PROJECT_DIR$/laravel/vendor/phenx/php-font-lib" />
      <path value="$PROJECT_DIR$/laravel/vendor/brick/math" />
      <path value="$PROJECT_DIR$/laravel/vendor/phenx/php-svg-lib" />
      <path value="$PROJECT_DIR$/laravel/vendor/psy/psysh" />
      <path value="$PROJECT_DIR$/laravel/vendor/laravel/ui" />
      <path value="$PROJECT_DIR$/laravel/vendor/graham-campbell/result-type" />
      <path value="$PROJECT_DIR$/laravel/vendor/laravel/passport" />
      <path value="$PROJECT_DIR$/laravel/vendor/laravel/serializable-closure" />
      <path value="$PROJECT_DIR$/laravel/vendor/laravel/tinker" />
      <path value="$PROJECT_DIR$/laravel/vendor/laravel/octane" />
      <path value="$PROJECT_DIR$/laravel/vendor/laravel/framework" />
      <path value="$PROJECT_DIR$/laravel/vendor/intervention/image" />
      <path value="$PROJECT_DIR$/laravel/vendor/nyholm/psr7" />
      <path value="$PROJECT_DIR$/laravel/vendor/laravel/sail" />
      <path value="$PROJECT_DIR$/laravel/vendor/maatwebsite/excel" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/temporary-directory" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/image" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/laravel-permission" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/laravel-medialibrary" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/laravel-collection-macros" />
      <path value="$PROJECT_DIR$/laravel/vendor/gedmo/doctrine-extensions" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/image-optimizer" />
      <path value="$PROJECT_DIR$/laravel/vendor/predis/predis" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/laravel-activitylog" />
      <path value="$PROJECT_DIR$/laravel/vendor/fideloper/proxy" />
      <path value="$PROJECT_DIR$/laravel/vendor/mpdf/mpdf" />
      <path value="$PROJECT_DIR$/laravel/vendor/ezyang/htmlpurifier" />
      <path value="$PROJECT_DIR$/laravel/vendor/vlucas/phpdotenv" />
      <path value="$PROJECT_DIR$/laravel/vendor/mockery/mockery" />
      <path value="$PROJECT_DIR$/laravel/vendor/fruitcake/laravel-cors" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/cache-contracts" />
      <path value="$PROJECT_DIR$/laravel/vendor/fakerphp/faker" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/cache" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/var-exporter" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/css-selector" />
      <path value="$PROJECT_DIR$/laravel/vendor/maennchen/zipstream-php" />
      <path value="$PROJECT_DIR$/laravel/vendor/swiftmailer/swiftmailer" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/psr-http-message-bridge" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/error-handler" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/laravel/vendor/asm89/stack-cors" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/polyfill-php73" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/console" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/polyfill-iconv" />
      <path value="$PROJECT_DIR$/laravel/vendor/myclabs/deep-copy" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/http-kernel" />
      <path value="$PROJECT_DIR$/laravel/vendor/behat/transliterator" />
      <path value="$PROJECT_DIR$/laravel/vendor/myclabs/php-enum" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/polyfill-php81" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/translation-contracts" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/translation" />
      <path value="$PROJECT_DIR$/laravel/vendor/lcobucci/clock" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/laravel/vendor/lcobucci/jwt" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/process" />
      <path value="$PROJECT_DIR$/laravel/vendor/opis/closure" />
      <path value="$PROJECT_DIR$/laravel/vendor/dflydev/dot-access-data" />
      <path value="$PROJECT_DIR$/laravel/vendor/nesbot/carbon" />
      <path value="$PROJECT_DIR$/laravel/vendor/ramsey/uuid" />
      <path value="$PROJECT_DIR$/laravel/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/laravel/vendor/ramsey/collection" />
      <path value="$PROJECT_DIR$/laravel/vendor/theseer/tokenizer" />
      <path value="$PROJECT_DIR$/laravel/vendor/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/laravel/vendor/markbaker/complex" />
      <path value="$PROJECT_DIR$/laravel/vendor/markbaker/matrix" />
      <path value="$PROJECT_DIR$/laravel/vendor/egulias/email-validator" />
      <path value="$PROJECT_DIR$/laravel/vendor/voku/portable-ascii" />
      <path value="$PROJECT_DIR$/laravel/vendor/defuse/php-encryption" />
      <path value="$PROJECT_DIR$/laravel/vendor/twilio/sdk" />
      <path value="$PROJECT_DIR$/laravel/vendor/barryvdh/laravel-dompdf" />
      <path value="$PROJECT_DIR$/laravel/vendor/phar-io/version" />
      <path value="$PROJECT_DIR$/laravel/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/laravel/vendor/phar-io/manifest" />
      <path value="$PROJECT_DIR$/laravel/vendor/composer" />
      <path value="$PROJECT_DIR$/laravel/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/laravel/vendor/phpoffice/phpspreadsheet" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/type" />
      <path value="$PROJECT_DIR$/laravel/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/lines-of-code" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/diff" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/comparator" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/object-enumerator" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/cli-parser" />
      <path value="$PROJECT_DIR$/laravel/vendor/firebase/php-jwt" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/code-unit" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/version" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/exporter" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/recursion-context" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/complexity" />
      <path value="$PROJECT_DIR$/laravel/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/environment" />
      <path value="$PROJECT_DIR$/laravel/vendor/doctrine/lexer" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/resource-operations" />
      <path value="$PROJECT_DIR$/laravel/vendor/doctrine/collections" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/code-unit-reverse-lookup" />
      <path value="$PROJECT_DIR$/laravel/vendor/doctrine/instantiator" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/global-state" />
      <path value="$PROJECT_DIR$/laravel/vendor/doctrine/annotations" />
      <path value="$PROJECT_DIR$/laravel/vendor/doctrine/persistence" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/object-reflector" />
      <path value="$PROJECT_DIR$/laravel/vendor/doctrine/dbal" />
      <path value="$PROJECT_DIR$/laravel/vendor/hamcrest/hamcrest-php" />
      <path value="$PROJECT_DIR$/laravel/vendor/facade/ignition" />
      <path value="$PROJECT_DIR$/laravel/vendor/doctrine/inflector" />
      <path value="$PROJECT_DIR$/laravel/vendor/openspout/openspout" />
      <path value="$PROJECT_DIR$/laravel/vendor/doctrine/common" />
      <path value="$PROJECT_DIR$/laravel/vendor/dompdf/dompdf" />
      <path value="$PROJECT_DIR$/laravel/vendor/doctrine/cache" />
      <path value="$PROJECT_DIR$/laravel/vendor/doctrine/event-manager" />
      <path value="$PROJECT_DIR$/laravel/vendor/stella-maris/clock" />
      <path value="$PROJECT_DIR$/laravel/vendor/tijsverkoyen/css-to-inline-styles" />
      <path value="$PROJECT_DIR$/laravel/vendor/league/glide" />
      <path value="$PROJECT_DIR$/laravel/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/laravel/vendor/nette/schema" />
      <path value="$PROJECT_DIR$/laravel/vendor/league/mime-type-detection" />
      <path value="$PROJECT_DIR$/laravel/vendor/league/config" />
      <path value="$PROJECT_DIR$/laravel/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/laravel/vendor/league/event" />
      <path value="$PROJECT_DIR$/laravel/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/laravel/vendor/nette/utils" />
      <path value="$PROJECT_DIR$/laravel/vendor/league/uri" />
      <path value="$PROJECT_DIR$/laravel/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/laravel/vendor/league/oauth2-server" />
      <path value="$PROJECT_DIR$/laravel/vendor/psr/container" />
      <path value="$PROJECT_DIR$/laravel/vendor/league/commonmark" />
      <path value="$PROJECT_DIR$/laravel/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/laravel/vendor/league/uri-interfaces" />
      <path value="$PROJECT_DIR$/laravel/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/laravel/vendor/league/flysystem" />
      <path value="$PROJECT_DIR$/laravel/vendor/nunomaduro/collision" />
      <path value="$PROJECT_DIR$/laravel/vendor/psr/log" />
      <path value="$PROJECT_DIR$/laravel/vendor/psr/cache" />
      <path value="$PROJECT_DIR$/laravel/vendor/php-http/message-factory" />
      <path value="$PROJECT_DIR$/laravel/vendor/laminas/laminas-diactoros" />
      <path value="$PROJECT_DIR$/laravel/vendor/setasign/fpdi" />
      <path value="$PROJECT_DIR$/laravel/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/laravel/vendor/rap2hpoutre/fast-excel" />
      <path value="$PROJECT_DIR$/laravel/vendor/facade/flare-client-php" />
      <path value="$PROJECT_DIR$/laravel/vendor/facade/ignition-contracts" />
      <path value="$PROJECT_DIR$/laravel/vendor/phpunit/php-file-iterator" />
      <path value="$PROJECT_DIR$/laravel/vendor/phpunit/php-timer" />
      <path value="$PROJECT_DIR$/laravel/vendor/phpunit/php-text-template" />
      <path value="$PROJECT_DIR$/laravel/vendor/phpunit/php-invoker" />
      <path value="$PROJECT_DIR$/laravel/vendor/phpunit/php-code-coverage" />
      <path value="$PROJECT_DIR$/laravel/vendor/phpunit/phpunit" />
      <path value="$PROJECT_DIR$/laravel/vendor/sabberworm/php-css-parser" />
      <path value="$PROJECT_DIR$/laravel/vendor/nunomaduro/termwind" />
      <path value="$PROJECT_DIR$/laravel/vendor/league/flysystem-local" />
      <path value="$PROJECT_DIR$/laravel/vendor/fruitcake/php-cors" />
      <path value="$PROJECT_DIR$/laravel/vendor/staudenmeir/eloquent-has-many-deep-contracts" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/backtrace" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/flare-client-php" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/ignition" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/laravel-ignition" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/laravel-package-tools" />
      <path value="$PROJECT_DIR$/laravel/vendor/guzzlehttp/uri-template" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/mailgun-mailer" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/postmark-mailer" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/yaml" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/mailer" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/http-client-contracts" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/laravel/vendor/mpdf/psr-log-aware-trait" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/http-client" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/laravel/vendor/league/flysystem-sftp-v3" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/laravel-signal-aware-command" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/laravel-backup" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/db-dumper" />
      <path value="$PROJECT_DIR$/laravel/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/laravel/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/laravel/vendor/webklex/laravel-imap" />
      <path value="$PROJECT_DIR$/laravel/vendor/webklex/php-imap" />
      <path value="$PROJECT_DIR$/laravel/vendor/laravel/prompts" />
      <path value="$PROJECT_DIR$/laravel/vendor/carbonphp/carbon-doctrine-types" />
      <path value="$PROJECT_DIR$/laravel/vendor/mpdf/psr-http-message-shim" />
      <path value="$PROJECT_DIR$/laravel/vendor/laravel/horizon" />
    </include_path>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="8.1" />
  <component name="PhpStanOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PhpUnit">
    <phpunit_settings>
      <PhpUnitSettings custom_loader_path="$PROJECT_DIR$/laravel/vendor/autoload.php" />
    </phpunit_settings>
  </component>
  <component name="PsalmOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
</project>